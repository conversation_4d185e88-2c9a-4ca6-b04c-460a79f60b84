<template>
  <div class="professional-dashboard">
    <!-- 背景装饰 -->
    <div class="background-decoration"></div>
    
    <!-- 头部信息 -->
    <div class="dashboard-header">
      <div class="header-content">
        <div class="breadcrumb-section">
          <t-breadcrumb>
            <t-breadcrumb-item>专业工作台</t-breadcrumb-item>
            <t-breadcrumb-item>{{ currentMajor.name || '仪表盘' }}</t-breadcrumb-item>
          </t-breadcrumb>
        </div>
        <div class="major-info" v-if="currentMajor.name">
          <div class="title-container">
            <h1 class="dashboard-title">{{ currentMajor.name }}</h1>
            <t-tag theme="success" size="small" class="major-tag">
              {{ currentMajor.type }}
            </t-tag>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="dashboard-content">
      <!-- 总体统计模块 -->
      <div class="statistics-section">
        <div class="section-header">
          <div class="header-left">
            <h2 class="section-title">
              <t-icon name="chart-line" class="title-icon" />
              总体统计
            </h2>
            <p class="section-subtitle">专业教学数据概览</p>
          </div>
        </div>
        
        <div class="stats-grid">
          <div class="stat-card" v-for="(stat, index) in statsData" :key="stat.key" :style="{ '--delay': `${index * 0.1}s` }">
            <div class="stat-card-inner">
              <div class="stat-icon-wrapper" :class="stat.iconClass">
                <t-icon :name="stat.icon" size="28px" />
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ stat.value }}</div>
                <div class="stat-label">{{ stat.label }}</div>
                <div class="stat-trend" :class="stat.trendClass" v-if="stat.trend">
                  <t-icon :name="stat.trendIcon" size="12px" />
                  <span>{{ stat.trend }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 当前学期课程模块 -->
      <div class="current-courses-section">
        <div class="section-header">
          <div class="header-left">
            <h2 class="section-title">
              <t-icon name="book" class="title-icon" />
              当前学期课程
            </h2>
          </div>
          <div class="header-right">
            <t-select 
              v-model="currentSemester" 
              placeholder="选择学期"
              style="width: 200px;"
              :options="semesterOptions"
              @change="handleSemesterChange"
            />
          </div>
        </div>
        
        <div class="courses-grid">
          <div class="course-card" v-for="(course, index) in currentCourses" :key="course.id" :style="{ '--delay': `${(index + 5) * 0.1}s` }">
            <div class="course-card-inner">
              <!-- 课程状态标签 -->
              <div class="course-status" :class="course.statusClass">
                {{ course.status }}
              </div>
              
                             <!-- 课程基本信息 -->
               <div class="course-header">
                 <h3 class="course-name">{{ course.name }}</h3>
                 <div class="course-code">{{ course.code }}</div>
               </div>
              
                             <!-- 授课班级信息 -->
               <div class="course-classes">
                 <div class="class-stats">
                   <div class="stat-item">
                     <div class="stat-icon">
                       <t-icon name="user-group" size="14px" />
                     </div>
                     <div class="stat-info">
                       <div class="stat-value">{{ course.classCount }}</div>
                       <div class="stat-desc">授课班级</div>
                     </div>
                   </div>
                   <div class="stat-item">
                     <div class="stat-icon">
                       <t-icon name="user" size="14px" />
                     </div>
                     <div class="stat-info">
                       <div class="stat-value">{{ course.studentCount }}</div>
                       <div class="stat-desc">学生统计</div>
                     </div>
                   </div>
                 </div>
                <div class="class-details">
                  <t-tooltip 
                    :content="formatClassList(course.classDetails).allClassesText"
                    placement="top"
                    theme="light"
                    :show-arrow="true"
                    :disabled="!formatClassList(course.classDetails).hasMore"
                    :delay="300"
                    :destroy-on-close="false"
                    overlay-class-name="professional-dashboard-tooltip"
                  >
                    <span class="details-label">班级详情：</span>
                  </t-tooltip>
                  <t-tooltip 
                    :content="formatClassList(course.classDetails).allClassesText"
                    placement="top"
                    theme="light"
                    :show-arrow="true"
                    :disabled="!formatClassList(course.classDetails).hasMore"
                    :delay="300"
                    :destroy-on-close="false"
                    overlay-class-name="professional-dashboard-tooltip"
                  >
                    <div class="class-list">
                      <span 
                        v-for="classInfo in formatClassList(course.classDetails).displayClasses" 
                        :key="classInfo.name" 
                        class="class-item"
                      >
                        {{ classInfo.name }}{{ classInfo.count }}人
                      </span>
                      <span 
                        v-if="formatClassList(course.classDetails).hasMore" 
                        class="class-item class-more"
                      >
                        ...等{{ formatClassList(course.classDetails).remainingCount }}个班级
                      </span>
                    </div>
                  </t-tooltip>
                </div>
              </div>
              
                             <!-- 操作按钮 -->
               <div class="course-actions">
                 <t-button theme="primary" variant="outline" size="small">
                   <template #icon>
                     <t-icon name="view-module" size="14px" />
                   </template>
                   课程详情
                 </t-button>
                 <t-button theme="primary" variant="outline" size="small">
                   <template #icon>
                     <t-icon name="chart-bar" size="14px" />
                   </template>
                   课程分析
                 </t-button>
               </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 课程授课统计模块 -->
      <div class="teaching-stats-section">
        <div class="section-header">
          <div class="header-left">
            <h2 class="section-title">
              <t-icon name="chart-bar" class="title-icon" />
              课程授课统计
            </h2>
            <p class="section-subtitle">历史课程教学统计分析</p>
          </div>
          <div class="header-right">
            <t-input
              v-model="searchKeyword"
              placeholder="搜索课程（课程号/课程名称）"
              style="width: 280px;"
              clearable
              @input="handleSearch"
            >
              <template #prefix-icon>
                <t-icon name="search" />
              </template>
            </t-input>
          </div>
        </div>
        
                 <div class="teaching-grid">
           <div class="teaching-card" v-for="(course, index) in filteredTeachingStats" :key="course.id" :style="{ '--delay': `${(index + 8) * 0.1}s` }">
            <div class="teaching-card-inner">
              <!-- 学分标签 -->
              <div class="course-credits">
                {{ course.credits }}学分
              </div>
              
              <!-- 课程基本信息 -->
              <div class="teaching-header">
                <h3 class="teaching-name">{{ course.name }}</h3>
                <div class="course-code">{{ course.code }}</div>
              </div>
              
              <!-- 统计信息 -->
              <div class="teaching-stats">
                <div class="stat-item">
                  <div class="stat-icon">
                    <t-icon name="calendar" size="16px" />
                  </div>
                  <div class="stat-info">
                    <div class="stat-value">{{ course.semesterCount }}</div>
                    <div class="stat-desc">学期统计</div>
                  </div>
                </div>
                <div class="stat-item">
                  <div class="stat-icon">
                    <t-icon name="user-group" size="16px" />
                  </div>
                  <div class="stat-info">
                    <div class="stat-value">{{ course.totalStudents }}</div>
                    <div class="stat-desc">学生统计</div>
                  </div>
                </div>
                <div class="stat-item">
                  <div class="stat-icon">
                    <t-icon name="chart-line" size="16px" />
                  </div>
                  <div class="stat-info">
                    <div class="stat-value">{{ course.avgScore }}%</div>
                    <div class="stat-desc">平均成绩</div>
                  </div>
                </div>
              </div>
              
                             <!-- 操作按钮 -->
               <div class="teaching-actions">
                 <t-button theme="primary" variant="outline" size="small">
                   <template #icon>
                     <t-icon name="chart-bar" size="14px" />
                   </template>
                   课程分析
                 </t-button>
                 <t-button theme="primary" variant="outline" size="small">
                   <template #icon>
                     <t-icon name="setting" size="14px" />
                   </template>
                   内容管理
                 </t-button>
               </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRoute } from 'vue-router'
import {
  Tag as TTag,
  Icon as TIcon,
  Breadcrumb as TBreadcrumb,
  BreadcrumbItem as TBreadcrumbItem,
  Button as TButton,
  Select as TSelect,
  Option as TOption,
  Input as TInput,
  Tooltip as TTooltip
} from 'tdesign-vue-next'

// 类型定义
interface CurrentMajor {
  id?: string;
  name?: string;
  type?: '本科' | '专科';
}

interface StatItem {
  key: string;
  label: string;
  value: string;
  icon: string;
  iconClass: string;
  trend?: string;
  trendIcon?: string;
  trendClass?: string;
}

interface CourseInfo {
  id: string;
  name: string;
  code: string;
  classCount: number;
  studentCount: number;
  classDetails: Array<{name: string; count: number}>;
  status: string;
  statusClass: string;
}

interface TeachingCourse {
  id: string;
  name: string;
  code: string;
  credits: number;
  semesterCount: number;
  totalStudents: number;
  avgScore: number;
}

const route = useRoute()

// 当前专业信息
const currentMajor = ref<CurrentMajor>({})

// 当前学期
const currentSemester = ref('')

// 学期选项
const semesterOptions = ref<Array<{label: string; value: string}>>([])

// 搜索关键词
const searchKeyword = ref('')

// 过滤后的课程统计数据
const filteredTeachingStats = computed(() => {
  if (!searchKeyword.value) {
    return teachingStats.value
  }
  
  const keyword = searchKeyword.value.toLowerCase()
  return teachingStats.value.filter(course => 
    course.name.toLowerCase().includes(keyword) || 
    course.code.toLowerCase().includes(keyword)
  )
})

// 总体统计数据
const statsData = ref<StatItem[]>([
  {
    key: 'total-courses',
    label: '授课总门数',
    value: '24',
    icon: 'book',
    iconClass: 'stat-icon-primary'
  },
  {
    key: 'total-students',
    label: '授课总学生数',
    value: '1,286',
    icon: 'user-group',
    iconClass: 'stat-icon-success'
  },
  {
    key: 'current-courses',
    label: '当前课程门数',
    value: '8',
    icon: 'calendar',
    iconClass: 'stat-icon-warning',
  },
  {
    key: 'current-classes',
    label: '当前班级数',
    value: '15',
    icon: 'layers',
    iconClass: 'stat-icon-error',
  },
  {
    key: 'current-students',
    label: '当前学生人数',
    value: '486',
    icon: 'user',
    iconClass: 'stat-icon-info',
  }
])

// 当前学期课程
const currentCourses = ref<CourseInfo[]>([
  {
    id: '1',
    name: '数据结构与算法',
    code: 'CS101',
    classCount: 3,
    studentCount: 156,
    classDetails: [
      { name: '计科21-1班', count: 52 },
      { name: '计科21-2班', count: 48 },
      { name: '计科21-3班', count: 56 }
    ],
    status: '进行中',
    statusClass: 'status-active'
  },
  {
    id: '2',
    name: '操作系统原理',
    code: 'CS201',
    classCount: 2,
    studentCount: 98,
    classDetails: [
      { name: '计科20-1班', count: 45 },
      { name: '计科20-2班', count: 53 }
    ],
    status: '已结课',
    statusClass: 'status-completed'
  },
  {
    id: '3',
    name: '软件工程',
    code: 'SE301',
    classCount: 6,
    studentCount: 352,
    classDetails: [
      { name: '软工21-1班', count: 58 },
      { name: '软工21-2班', count: 56 },
      { name: '软工21-3班', count: 59 },
      { name: '软工21-4班', count: 59 },
      { name: '软工21-5班', count: 62 },
      { name: '软工21-6班', count: 58 }
    ],
    status: '准备中',
    statusClass: 'status-preparing'
  }
])

// 课程授课统计
const teachingStats = ref<TeachingCourse[]>([
  {
    id: '1',
    name: '数据结构与算法',
    code: 'CS101',
    credits: 4,
    semesterCount: 6,
    totalStudents: 624,
    avgScore: 87
  },
  {
    id: '2',
    name: '操作系统原理',
    code: 'CS201',
    credits: 3,
    semesterCount: 4,
    totalStudents: 392,
    avgScore: 82
  },
  {
    id: '3',
    name: '软件工程',
    code: 'SE301',
    credits: 3,
    semesterCount: 3,
    totalStudents: 270,
    avgScore: 89
  },
  {
    id: '4',
    name: '计算机网络',
    code: 'CS301',
    credits: 3,
    semesterCount: 5,
    totalStudents: 485,
    avgScore: 85
  }
])

// 获取当前学期
const getCurrentSemester = () => {
  const now = new Date()
  const year = now.getFullYear()
  const month = now.getMonth() + 1
  
  // 春季学期：2-7月，秋季学期：8月-次年1月
  if (month >= 2 && month <= 7) {
    return `${year}春`
  } else {
    return month >= 8 ? `${year}秋` : `${year - 1}秋`
  }
}

// 生成学期选项列表
const generateSemesterOptions = () => {
  const options: Array<{label: string; value: string}> = []
  const currentYear = new Date().getFullYear()
  
  // 生成从当前年份开始，往前推3年的学期选项
  for (let year = currentYear; year >= currentYear - 3; year--) {
    // 每年两个学期：春季和秋季
    options.push({ label: `${year}春`, value: `${year}春` })
    if (year > currentYear - 3) { // 避免最后一年添加重复的秋季学期
      options.push({ label: `${year - 1}秋`, value: `${year - 1}秋` })
    }
  }
  
  // 按时间倒序排列（最新的在前面）
  options.sort((a, b) => {
    const parseYear = (semester: string) => {
      const year = parseInt(semester.slice(0, 4))
      const season = semester.slice(4)
      return year * 10 + (season === '春' ? 1 : 0) // 春季=1, 秋季=0，这样春季会排在同年秋季前面
    }
    return parseYear(b.value) - parseYear(a.value)
  })
  
  return options
}

// 学期变更处理
const handleSemesterChange = (value: any) => {
  console.log('学期变更:', value)
  // 这里可以添加学期变更后的数据刷新逻辑
}

// 搜索处理
const handleSearch = (value: string) => {
  console.log('搜索关键词:', value)
  // 搜索逻辑已通过computed实现
}

// 初始化页面数据
const initializeData = () => {
  // 从路由参数获取专业信息
  const { majorId, majorName } = route.query
  
  if (majorId && majorName) {
    currentMajor.value = {
      id: majorId as string,
      name: majorName as string,
      type: '本科' // 可以根据专业ID确定类型
    }
  }
  
  // 生成学期选项列表
  semesterOptions.value = generateSemesterOptions()
  
  // 设置当前学期
  const currentSem = getCurrentSemester()
  
  // 确保当前学期在选项列表中，如果不在则添加到列表开头
  const existingSemester = semesterOptions.value.find(option => option.value === currentSem)
  if (!existingSemester) {
    semesterOptions.value.unshift({ label: currentSem, value: currentSem })
  }
  
  currentSemester.value = currentSem
  
  console.log('当前专业:', currentMajor.value)
  console.log('当前学期:', currentSemester.value)
  console.log('学期选项:', semesterOptions.value)
}

// 格式化班级列表显示
const formatClassList = (classDetails: Array<{name: string; count: number}>) => {
  const maxDisplay = 2 // 最多显示2个班级
  
  if (classDetails.length <= 3) {
    // 班级数量少于等于3个时，显示所有班级
    return {
      displayClasses: classDetails,
      hasMore: false,
      remainingCount: 0,
      allClassesText: formatTooltipContent(classDetails)
    }
  } else {
    // 班级数量多于3个时，显示前2个 + 省略
    return {
      displayClasses: classDetails.slice(0, maxDisplay),
      hasMore: true,
      remainingCount: classDetails.length - maxDisplay,
      allClassesText: formatTooltipContent(classDetails)
    }
  }
}

// 格式化Tooltip内容
const formatTooltipContent = (classDetails: Array<{name: string; count: number}>) => {
  const totalStudents = classDetails.reduce((sum, c) => sum + c.count, 0)
  const classListText = classDetails.map(c => `${c.name} ${c.count}人`).join('\n')
  
  return `共${classDetails.length}个班级，${totalStudents}名学生\n\n${classListText}`
}

onMounted(() => {
  initializeData()
})
</script>

<style lang="less" scoped>
.professional-dashboard {
  min-height: 100%;
  background: linear-gradient(135deg, var(--td-brand-color) 0%, var(--td-brand-color-8) 100%);
  position: relative;
  overflow: hidden;

  // 恢复原始的多层渐变背景装饰
  .background-decoration {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
      radial-gradient(circle at 20% 80%, var(--td-brand-color-1) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, var(--td-brand-color-2) 0%, transparent 50%),
      radial-gradient(circle at 40% 40%, var(--td-brand-color-3) 0%, transparent 50%);
    animation: backgroundFloat 20s ease-in-out infinite;
    pointer-events: none;
  }

  .dashboard-header {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding: 24px;
    position: relative;
    z-index: 2;
    
    .header-content {
      margin: 0 auto;
      width: 100%;
      
      .breadcrumb-section {
        margin-bottom: 16px;
        
        :deep(.t-breadcrumb) {
          .t-breadcrumb__item {
            color: var(--td-text-color-secondary);
            
            &:last-child {
              color: var(--td-text-color-anti);
              font-weight: 500;
            }
          }
          
          .t-breadcrumb__separator {
            color: var(--td-text-color-placeholder);
          }
        }
      }
      
      .major-info {
        .title-container {
          position: relative;
          display: inline-block;
        }
        
        .dashboard-title {
          font-size: 32px;
          font-weight: 700;
          color: var(--td-text-color-anti);
          margin: 0;
          text-shadow: 0 2px 10px var(--td-shadow-1);
          padding-right: 60px; // 为右上角标签留出空间
        }
        
        .major-tag {
          position: absolute;
          top: -4px;
          right: 0;
          backdrop-filter: blur(10px);
          font-size: 10px;
          
          :deep(.t-tag__text) {
            color: var(--td-brand-color);
            font-weight: 600;
            font-size: 10px;
          }
        }
      }
    }
  }
  
  .dashboard-content {
    margin: 0 auto;
    width: 100%;
    padding: 32px 24px;
    
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-end;
      margin-bottom: 32px;
      
      .header-left {
        flex: 1;
        
        .section-title {
          font-size: 24px;
          font-weight: 700;
          color: var(--td-text-color-anti);
          margin-bottom: 8px;
          display: flex;
          align-items: center;
          gap: 12px;
          
          .title-icon {
            color: var(--td-text-color-anti);
            filter: drop-shadow(0 2px 4px var(--td-shadow-1));
          }
        }
        
        .section-subtitle {
          font-size: 16px;
          color: rgba(255, 255, 255, 0.85);
          margin: 0;
        }
      }
      
      .header-right {
        flex-shrink: 0;
        
        :deep(.t-select) {
          .t-input {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            
            .t-input__inner {
              color: var(--td-text-color-anti);
              
              &::placeholder {
                color: var(--td-text-color-placeholder);
              }
            }
            
            .t-input__suffix {
              color: var(--td-text-color-secondary);
            }
          }
          
          &:hover .t-input {
            border-color: rgba(255, 255, 255, 0.5);
          }
        }
        
        :deep(.t-input) {
          background: rgba(255, 255, 255, 0.1);
          border: 1px solid rgba(255, 255, 255, 0.3);
          
          .t-input__inner {
            color: var(--td-text-color-anti);
            
            &::placeholder {
              color: var(--td-text-color-placeholder);
            }
          }
          
          .t-input__prefix,
          .t-input__suffix {
            color: var(--td-text-color-secondary);
          }
          
          &:hover {
            border-color: rgba(255, 255, 255, 0.5);
          }
          
          &:focus-within {
            border-color: rgba(255, 255, 255, 0.5);
            box-shadow: none;
          }
        }
      }
    }
    
    // 总体统计样式
    .statistics-section {
      margin-bottom: 48px;
      
      .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
        gap: 20px;
        
        .stat-card {
          opacity: 0;
          transform: translateY(30px);
          animation: slideInUp 0.6s ease-out var(--delay) both;
          
          .stat-card-inner {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 16px;
            padding: 20px;
            display: flex;
            align-items: center;
            gap: 16px;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            
            &:hover {
              transform: translateY(-4px);
              box-shadow: 0 12px 32px var(--td-shadow-3);
              background: rgba(255, 255, 255, 0.18);
              border-color: rgba(255, 255, 255, 0.5);
            }
          }
          
          .stat-icon-wrapper {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
            
            &.stat-icon-primary {
              background: var(--td-brand-color);
            }
            
            &.stat-icon-success {
              background: var(--td-success-color);
            }
            
            &.stat-icon-warning {
              background: var(--td-warning-color);
            }
            
            &.stat-icon-error {
              background: var(--td-error-color);
            }
            
            &.stat-icon-info {
              background: var(--td-brand-color-6);
            }
            
            :deep(.t-icon) {
              color: var(--td-text-color-anti);
            }
          }
          
          .stat-content {
            flex: 1;
            
            .stat-number {
              font-size: 24px;
              font-weight: 700;
              color: var(--td-text-color-anti);
              line-height: 1;
              margin-bottom: 4px;
            }
            
            .stat-label {
              font-size: 14px;
              color: rgba(255, 255, 255, 0.9);
              margin-bottom: 4px;
            }
            
            .stat-trend {
              display: flex;
              align-items: center;
              gap: 4px;
              font-size: 12px;
              font-weight: 600;
              
              &.trend-up {
                color: var(--td-success-color);
              }
              
              &.trend-down {
                color: var(--td-error-color);
              }
            }
          }
        }
      }
    }
    
    // 当前学期课程样式
    .current-courses-section {
      margin-bottom: 48px;
      
      .courses-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
        gap: 24px;
        
        .course-card {
          opacity: 0;
          transform: translateY(30px);
          animation: slideInUp 0.6s ease-out var(--delay) both;
          
          .course-card-inner {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 16px;
            padding: 24px;
            position: relative;
            transition: all 0.3s ease;
            
            &:hover {
              background: rgba(255, 255, 255, 0.18);
              border-color: rgba(255, 255, 255, 0.5);
              transform: translateY(-2px);
              box-shadow: 0 8px 32px var(--td-shadow-2);
            }
          }
          
          .course-status {
            position: absolute;
            top: 16px;
            right: 16px;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
            
            &.status-active {
              background: var(--td-success-color);
              color: var(--td-text-color-anti);
            }
            
            &.status-completed {
              background: var(--td-brand-color);
              color: var(--td-text-color-anti);
            }
            
            &.status-preparing {
              background: var(--td-warning-color);
              color: var(--td-text-color-anti);
            }
          }
          
          .course-header {
            margin-bottom: 16px;
            
            .course-name {
              font-size: 18px;
              font-weight: 600;
              color: var(--td-text-color-anti);
              margin: 0 0 8px 0;
            }
            
            .course-code {
              display: inline-block;
              font-size: 12px;
              color: rgba(255, 255, 255, 0.9);
              background: rgba(255, 255, 255, 0.2);
              padding: 4px 8px;
              border-radius: 6px;
              margin-top: 8px;
              font-weight: 500;
            }
          }
          
                      .course-classes {
              margin-bottom: 20px;
              
              .class-stats {
                display: flex;
                gap: 16px;
                margin-bottom: 16px;
                
                .stat-item {
                  display: flex;
                  align-items: center;
                  gap: 8px;
                  background: rgba(255, 255, 255, 0.1);
                  padding: 8px 12px;
                  border-radius: 8px;
                  flex: 1;
                  
                  .stat-icon {
                    :deep(.t-icon) {
                      color: rgba(255, 255, 255, 0.8);
                    }
                  }
                  
                  .stat-info {
                    flex: 1;
                    
                    .stat-value {
                      font-size: 16px;
                      font-weight: 600;
                      color: var(--td-text-color-anti);
                      line-height: 1;
                      margin-bottom: 2px;
                    }
                    
                    .stat-desc {
                      font-size: 11px;
                      color: rgba(255, 255, 255, 0.7);
                      line-height: 1;
                    }
                  }
                }
              }
            
            .class-details {
              margin-top: 12px;
              
              .details-label {
                font-size: 14px;
                color: rgba(255, 255, 255, 0.8);
                display: block;
                margin-bottom: 8px;
                cursor: help;
                transition: color 0.2s ease;
                
                &:hover {
                  color: rgba(255, 255, 255, 0.95);
                }
              }
              
              .class-list {
                display: flex;
                flex-wrap: wrap;
                gap: 8px;
                cursor: help;
                
                .class-item {
                  background: rgba(255, 255, 255, 0.1);
                  padding: 4px 8px;
                  border-radius: 8px;
                  font-size: 12px;
                  color: rgba(255, 255, 255, 0.8);
                  border: 1px solid rgba(255, 255, 255, 0.3);
                  transition: all 0.2s ease;
                  
                  &:hover {
                    background: rgba(255, 255, 255, 0.15);
                    color: rgba(255, 255, 255, 0.95);
                    border-color: rgba(255, 255, 255, 0.4);
                  }
                  
                  &.class-more {
                    background: rgba(255, 255, 255, 0.05);
                    color: rgba(255, 255, 255, 0.6);
                    border-style: dashed;
                    font-style: italic;
                    
                    &:hover {
                      background: rgba(255, 255, 255, 0.1);
                      color: rgba(255, 255, 255, 0.8);
                    }
                  }
                }
              }
            }
          }
          
          .course-actions {
            display: flex;
            gap: 12px;
            
            :deep(.t-button) {
              background: rgba(255, 255, 255, 0.1);
              border: 1px solid rgba(255, 255, 255, 0.3);
              color: var(--td-text-color-anti);
              
              &:hover {
                background: rgba(255, 255, 255, 0.2);
                border-color: rgba(255, 255, 255, 0.5);
              }
            }
          }
        }
      }
    }
    
    // Tooltip样式优化 - 与页面毛玻璃风格保持一致
    :deep(.t-tooltip) {
      .t-tooltip__content {
        background: rgba(255, 255, 255, 0.15) !important;
        backdrop-filter: blur(20px) saturate(1.2) !important;
        border: 1px solid rgba(255, 255, 255, 0.4) !important;
        border-radius: 12px !important;
        box-shadow: 
          0 8px 32px rgba(0, 0, 0, 0.15),
          0 2px 8px rgba(0, 0, 0, 0.1),
          inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
        color: rgba(255, 255, 255, 0.95) !important;
        font-size: 12px !important;
        font-weight: 500 !important;
        max-width: 280px !important;
        white-space: pre-line !important;
        word-break: keep-all !important;
        line-height: 1.6 !important;
        padding: 14px 18px !important;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
        
        // 优化多行文本显示
        .tooltip-header {
          font-weight: 600 !important;
          margin-bottom: 8px !important;
          color: rgba(255, 255, 255, 1) !important;
        }
        
        .tooltip-class-list {
          font-size: 11px !important;
          opacity: 0.9 !important;
        }
        
        // 添加淡入动画
        animation: tooltipFadeIn 0.2s ease-out;
        
        // 添加光效
        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          height: 1px;
          background: linear-gradient(90deg, 
            transparent 0%, 
            rgba(255, 255, 255, 0.6) 50%, 
            transparent 100%);
          border-radius: 12px 12px 0 0;
        }
      }
      
      .t-tooltip__arrow {
        &::before {
          border-top-color: rgba(255, 255, 255, 0.4);
        }
        
        &::after {
          border-top-color: rgba(255, 255, 255, 0.15);
          filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
        }
      }
      
        // 不同方向的箭头适配
  &[data-popper-placement^="bottom"] .t-popup__arrow {
        &::before {
          border-bottom-color: rgba(255, 255, 255, 0.4);
          border-top-color: transparent;
        }
        
        &::after {
          border-bottom-color: rgba(255, 255, 255, 0.15);
          border-top-color: transparent;
        }
      }
      
      &[data-popper-placement^="left"] .t-popup__arrow {
        &::before {
          border-left-color: rgba(255, 255, 255, 0.4);
          border-top-color: transparent;
        }
        
        &::after {
          border-left-color: rgba(255, 255, 255, 0.15);
          border-top-color: transparent;
        }
      }
      
      &[data-popper-placement^="right"] .t-popup__arrow {
        &::before {
          border-right-color: rgba(255, 255, 255, 0.4);
          border-top-color: transparent;
        }
        
        &::after {
          border-right-color: rgba(255, 255, 255, 0.15);
          border-top-color: transparent;
        }
      }
    }
    
    // Tooltip淡入动画
    @keyframes tooltipFadeIn {
      from {
        opacity: 0;
        transform: translateY(-4px) scale(0.95);
      }
      to {
        opacity: 1;
        transform: translateY(0) scale(1);
      }
    }
    
    // 课程授课统计样式
    .teaching-stats-section {
      .teaching-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
        gap: 24px;
        
        .teaching-card {
          opacity: 0;
          transform: translateY(30px);
          animation: slideInUp 0.6s ease-out var(--delay) both;
          
          .teaching-card-inner {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 16px;
            padding: 24px;
            position: relative;
            transition: all 0.3s ease;
            
            &:hover {
              background: rgba(255, 255, 255, 0.18);
              border-color: rgba(255, 255, 255, 0.5);
              transform: translateY(-2px);
              box-shadow: 0 8px 32px var(--td-shadow-2);
            }
          }
          
          .course-credits {
            position: absolute;
            top: 16px;
            right: 16px;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
            background: var(--td-warning-color);
            color: var(--td-text-color-anti);
          }
          
          .teaching-header {
            margin-bottom: 20px;
            
            .teaching-name {
              font-size: 18px;
              font-weight: 600;
              color: var(--td-text-color-anti);
              margin: 0 0 8px 0;
            }
            
            .course-code {
              display: inline-block;
              font-size: 12px;
              color: rgba(255, 255, 255, 0.9);
              background: rgba(255, 255, 255, 0.2);
              padding: 4px 8px;
              border-radius: 6px;
              margin-top: 8px;
              font-weight: 500;
            }
          }
          
          .teaching-stats {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            
            .stat-item {
              text-align: center;
              flex: 1;
              
              .stat-icon {
                margin-bottom: 8px;
                
                :deep(.t-icon) {
                  color: rgba(255, 255, 255, 0.6);
                }
              }
              
              .stat-value {
                font-size: 20px;
                font-weight: 700;
                color: var(--td-text-color-anti);
                margin-bottom: 4px;
              }
              
              .stat-desc {
                font-size: 12px;
                color: rgba(255, 255, 255, 0.7);
              }
            }
          }
          
          .teaching-actions {
            display: flex;
            gap: 12px;
            
            :deep(.t-button) {
              flex: 1;
              background: rgba(255, 255, 255, 0.1);
              border: 1px solid rgba(255, 255, 255, 0.3);
              color: var(--td-text-color-anti);
              
              &:hover {
                background: rgba(255, 255, 255, 0.2);
                border-color: rgba(255, 255, 255, 0.5);
              }
            }
          }
        }
      }
    }
  }
  
  // 动画定义
  @keyframes slideInUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  @keyframes backgroundFloat {
    0%, 100% {
      transform: translate(0, 0) rotate(0deg);
    }
    33% {
      transform: translate(20px, -20px) rotate(120deg);
    }
    66% {
      transform: translate(-20px, 10px) rotate(240deg);
    }
  }
  
  // 响应式设计
  @media (max-width: 768px) {
    .dashboard-header {
      padding: 16px;
      
      .header-content .major-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
        
        .dashboard-title {
          font-size: 24px;
        }
      }
    }
    
    .dashboard-content {
      padding: 24px 16px;
      
      .stats-grid {
        grid-template-columns: 1fr;
        gap: 16px;
      }
      
      .courses-grid {
        grid-template-columns: 1fr;
        gap: 20px;
      }
      
      .teaching-grid {
        grid-template-columns: 1fr;
        gap: 20px;
      }
    }
  }
}


</style>

<!-- 专业工作台Tooltip样式 - 现代化风格优化 -->
<style lang="less">
// 专业工作台页面的Tooltip现代化风格 - 与页面卡片保持一致的设计语言
.professional-dashboard-tooltip {
  // Tooltip容器优化 - 主题色渐变设计
  .t-popup__content {
    // 优化主题色渐变背景 - 使用中等深度的颜色
    background: 
      linear-gradient(135deg, 
        var(--td-brand-color-3) 0%, 
        var(--td-brand-color-4) 50%, 
        var(--td-brand-color-5) 100%
      ) !important;
    backdrop-filter: blur(12px) saturate(1.2) !important;
    
    // 简化边框系统
    border: none !important;
    border-radius: 12px !important;
    
    // 简化阴影系统 - 无边框
    box-shadow: 
      0 12px 32px rgba(0, 0, 0, 0.2),
      0 6px 16px rgba(0, 0, 0, 0.12),
      0 2px 8px rgba(0, 0, 0, 0.08) !important;
    
    // 统一白色字体
    color: rgba(255, 255, 255, 1) !important;
    font-size: 13px !important;
    font-weight: 500 !important;
    max-width: 300px !important;
    white-space: pre-line !important;
    word-break: keep-all !important;
    line-height: 1.6 !important;
    padding: 16px 20px !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
    
    // 现代化动画
    transform-origin: center bottom !important;
    animation: modernTooltipAppear 0.4s cubic-bezier(0.34, 1.56, 0.64, 1) !important;
    
    // 简化顶部光效
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 1px;
      background: linear-gradient(90deg, 
        transparent 0%, 
        rgba(255, 255, 255, 0.6) 50%, 
        transparent 100%);
      border-radius: 12px 12px 0 0;
    }
    
    // 简化内容样式
    .tooltip-header {
      font-weight: 600 !important;
      font-size: 14px !important;
      margin-bottom: 10px !important;
      color: rgba(255, 255, 255, 1) !important;
      padding-bottom: 8px !important;
      border-bottom: 1px solid rgba(255, 255, 255, 0.2) !important;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
    }
    
    .tooltip-class-list {
      font-size: 12px !important;
      line-height: 1.6 !important;
      color: rgba(255, 255, 255, 0.95) !important;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
    }
  }

  // 简化箭头设计 - 主题色风格，优化位置
  .t-popup__arrow {
    // 调整箭头位置，使其更贴近边框
    transform: translateY(13px) !important;
    
    &::before {
      border-top-color: var(--td-brand-color-4) !important;
      filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
    }
    
    &::after {
      border-top-color: var(--td-brand-color-5) !important;
      filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
    }
  }

  // 不同方向的箭头适配 - 主题色，优化位置
  &[data-popper-placement^="bottom"] .t-popup__arrow {
    // bottom方向箭头向上调整
    transform: translateY(-2px) !important;
    
    &::before {
      border-bottom-color: var(--td-brand-color-4) !important;
      border-top-color: transparent !important;
    }
    
    &::after {
      border-bottom-color: var(--td-brand-color-5) !important;
      border-top-color: transparent !important;
    }
  }

  &[data-popper-placement^="left"] .t-popup__arrow {
    // left方向箭头向右调整
    transform: translateX(2px) !important;
    
    &::before {
      border-left-color: var(--td-brand-color-4) !important;
      border-top-color: transparent !important;
    }
    
    &::after {
      border-left-color: var(--td-brand-color-5) !important;
      border-top-color: transparent !important;
    }
  }

  &[data-popper-placement^="right"] .t-popup__arrow {
    // right方向箭头向左调整
    transform: translateX(-2px) !important;
    
    &::before {
      border-right-color: var(--td-brand-color-4) !important;
      border-top-color: transparent !important;
    }
    
    &::after {
      border-right-color: var(--td-brand-color-5) !important;
      border-top-color: transparent !important;
    }
  }
}

// 现代化Tooltip出现动画
@keyframes modernTooltipAppear {
  0% {
    opacity: 0;
    transform: translateY(8px) scale(0.9);
    filter: blur(4px);
  }
  60% {
    opacity: 0.8;
    transform: translateY(-2px) scale(1.02);
    filter: blur(1px);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
    filter: blur(0);
  }
}

// 光效流动动画
@keyframes lightFlow {
  0%, 100% {
    opacity: 0.6;
    transform: translateX(-10px);
  }
  50% {
    opacity: 1;
    transform: translateX(10px);
  }
}

// 暗黑模式适配
:root[theme-mode='dark'] {
  .professional-dashboard-tooltip .t-popup__content {
    // 暗黑模式下使用稍深的主题色
    background: 
      linear-gradient(135deg, 
        var(--td-brand-color-5) 0%, 
        var(--td-brand-color-6) 50%, 
        var(--td-brand-color-7) 100%
      ) !important;
    backdrop-filter: blur(12px) saturate(1.2) !important;
    
    // 暗黑模式下的简化阴影 - 无边框
    box-shadow: 
      0 12px 32px rgba(0, 0, 0, 0.4),
      0 6px 16px rgba(0, 0, 0, 0.25),
      0 2px 8px rgba(0, 0, 0, 0.15) !important;
  }
}
</style>
