<template>
  <div class="dynamic-side-nav">
    <!-- 导航头部 -->
    <div class="nav-header">
      <div class="parent-route-info" v-if="parentRouteInfo">
        <div class="parent-icon">
          <t-icon :name="(parentRouteInfo.meta?.icon as string) || 'folder'" size="20px" />
        </div>
        <div class="parent-text">
          <div class="parent-title">{{ getRouteTitle(parentRouteInfo) }}</div>
          <div class="parent-subtitle">{{ menuList.length }} 个子模块</div>
        </div>
      </div>
    </div>
    
    <!-- 导航菜单 -->
    <div class="nav-menu">
      <t-menu
        :value="activeMenu"
        :collapsed="isCollapsed"
        @change="handleMenuChange"
        class="dynamic-menu"
      >
        <template v-for="route in menuList" :key="route.name">
          <!-- 有子路由的菜单项 -->
          <t-submenu
            v-if="route.children && route.children.length > 0 && hasVisibleChildren(route)"
            :value="route.name"
            :title="getRouteTitle(route)"
          >
            <template #icon>
              <t-icon :name="route.meta?.icon || 'folder'" />
            </template>
            
            <t-menu-item
              v-for="child in route.children"
              :key="child.name"
              :value="child.name"
              v-show="!child.meta?.hidden"
              @click="navigateToRoute(child)"
            >
              <template #icon>
                <t-icon :name="child.meta?.icon || 'file'" />
              </template>
              {{ getRouteTitle(child) }}
            </t-menu-item>
          </t-submenu>
          
          <!-- 单级菜单项 -->
          <t-menu-item
            v-else-if="!route.meta?.hidden"
            :value="route.name"
            @click="navigateToRoute(route)"
          >
            <template #icon>
              <t-icon :name="route.meta?.icon || 'file'" />
            </template>
            {{ getRouteTitle(route) }}
          </t-menu-item>
        </template>
      </t-menu>
    </div>
    
    <!-- 导航底部 -->
    <div class="nav-footer">
      <t-button
        variant="text"
        size="small"
        @click="toggleCollapse"
        class="collapse-btn"
      >
        <template #icon>
          <t-icon :name="isCollapsed ? 'chevron-right' : 'chevron-left'" />
        </template>
        <span v-if="!isCollapsed">收起</span>
      </t-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { 
  Menu as TMenu, 
  MenuItem as TMenuItem, 
  Submenu as TSubmenu,
  Icon as TIcon,
  Button as TButton 
} from 'tdesign-vue-next'
import { useRoutesStore } from '@/store/modules/routes'
import type { ObeRouteRecord } from '@/types/router'

// Props
interface Props {
  parentRoute: string
  menuList: ObeRouteRecord[]
}

const props = withDefaults(defineProps<Props>(), {
  parentRoute: '',
  menuList: () => []
})

// 路由相关
const route = useRoute()
const router = useRouter()
const routesStore = useRoutesStore()

// 状态
const isCollapsed = ref(false)
const activeMenu = ref('')

// 父路由信息
const parentRouteInfo = computed(() => {
  if (!props.parentRoute) return null
  
  const allRoutes = routesStore.routes
  return findRouteByPath(allRoutes, props.parentRoute)
})

// 递归查找路由
const findRouteByPath = (routes: ObeRouteRecord[], targetPath: string): ObeRouteRecord | null => {
  for (const routeItem of routes) {
    if (routeItem.path === targetPath || routeItem.name === targetPath) {
      return routeItem
    }
    if (routeItem.children) {
      const found = findRouteByPath(routeItem.children, targetPath)
      if (found) return routeItem
    }
  }
  return null
}

// 获取路由标题
const getRouteTitle = (routeItem: ObeRouteRecord) => {
  if (!routeItem.meta?.title) return routeItem.name || '未命名'
  
  const title = routeItem.meta.title
  if (typeof title === 'string') return title
  if (typeof title === 'object' && title.zh_CN) return title.zh_CN
  
  return routeItem.name || '未命名'
}

// 检查是否有可见的子路由
const hasVisibleChildren = (routeItem: ObeRouteRecord) => {
  if (!routeItem.children) return false
  return routeItem.children.some(child => !child.meta?.hidden)
}

// 导航到指定路由
const navigateToRoute = (targetRoute: ObeRouteRecord) => {
  let targetPath = targetRoute.path
  
  // 如果路径不是绝对路径，需要拼接父路径
  if (!targetPath.startsWith('/')) {
    const parentPath = props.parentRoute.endsWith('/') 
      ? props.parentRoute.slice(0, -1) 
      : props.parentRoute
    targetPath = `${parentPath}/${targetPath}`
  }
  
  // 检查是否包含路由参数
  if (targetPath.includes(':')) {
    // 如果有路由参数，需要从当前路由中获取对应的参数值
    const currentParams = route.params
    Object.keys(currentParams).forEach(key => {
      targetPath = targetPath.replace(`:${key}`, currentParams[key] as string)
    })
  }
  
  router.push(targetPath)
}

// 菜单变化处理
const handleMenuChange = (value: string | number) => {
  activeMenu.value = String(value)
}

// 切换折叠状态
const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value
}

// 更新当前激活的菜单项
const updateActiveMenu = () => {
  const currentPath = route.path
  const currentName = route.name as string
  
  // 优先使用路由名称匹配
  if (currentName) {
    activeMenu.value = currentName
    return
  }
  
  // 根据路径匹配最相近的菜单项
  for (const menuItem of props.menuList) {
    if (menuItem.path && currentPath.includes(menuItem.path)) {
      activeMenu.value = menuItem.name || ''
      return
    }
    
    // 检查子路由
    if (menuItem.children) {
      for (const child of menuItem.children) {
        if (child.path && currentPath.includes(child.path)) {
          activeMenu.value = child.name || ''
          return
        }
      }
    }
  }
}

// 监听路由变化
watch(() => route.path, updateActiveMenu, { immediate: true })

// 监听菜单列表变化
watch(() => props.menuList, updateActiveMenu, { immediate: true })

onMounted(() => {
  updateActiveMenu()
})
</script>

<style lang="less" scoped>
.dynamic-side-nav {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--td-bg-color-container);
  
  .nav-header {
    padding: 16px;
    border-bottom: 1px solid var(--td-component-border);
    
    .parent-route-info {
      display: flex;
      align-items: center;
      gap: 12px;
      
      .parent-icon {
        width: 40px;
        height: 40px;
        border-radius: 8px;
        background: var(--td-brand-color-1);
        display: flex;
        align-items: center;
        justify-content: center;
        
        :deep(.t-icon) {
          color: var(--td-brand-color);
        }
      }
      
      .parent-text {
        flex: 1;
        min-width: 0;
        
        .parent-title {
          font-size: 16px;
          font-weight: 600;
          color: var(--td-text-color-primary);
          margin-bottom: 4px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        
        .parent-subtitle {
          font-size: 12px;
          color: var(--td-text-color-secondary);
        }
      }
    }
  }
  
  .nav-menu {
    flex: 1;
    overflow-y: auto;
    padding: 8px 0;
    
    .dynamic-menu {
      border: none;
      background: transparent;
      
      :deep(.t-menu__item) {
        margin: 2px 8px;
        border-radius: 6px;
        
        &:hover {
          background: var(--td-bg-color-container-hover);
        }
        
        &.t-is-active {
          background: var(--td-brand-color-1);
          color: var(--td-brand-color);
          
          .t-icon {
            color: var(--td-brand-color);
          }
        }
      }
      
      :deep(.t-submenu__title) {
        margin: 2px 8px;
        border-radius: 6px;
        
        &:hover {
          background: var(--td-bg-color-container-hover);
        }
      }
      
      :deep(.t-submenu__content) {
        padding-left: 16px;
        
        .t-menu__item {
          margin: 2px 0;
          margin-left: 20px;
          border-left: 2px solid transparent;
          border-radius: 0 6px 6px 0;
          
          &.t-is-active {
            border-left-color: var(--td-brand-color);
            background: linear-gradient(90deg, var(--td-brand-color-1), transparent);
          }
        }
      }
    }
  }
  
  .nav-footer {
    padding: 8px;
    border-top: 1px solid var(--td-component-border);
    
    .collapse-btn {
      width: 100%;
      justify-content: flex-start;
      color: var(--td-text-color-secondary);
      
      &:hover {
        background: var(--td-bg-color-container-hover);
        color: var(--td-text-color-primary);
      }
    }
  }
}

// 折叠状态样式
.dynamic-side-nav.collapsed {
  .nav-header {
    padding: 12px 8px;
    
    .parent-route-info {
      justify-content: center;
      
      .parent-text {
        display: none;
      }
    }
  }
  
  .nav-footer {
    .collapse-btn {
      justify-content: center;
      
      span {
        display: none;
      }
    }
  }
}

// 暗黑模式适配
:root[theme-mode='dark'] {
  .dynamic-side-nav {
    .parent-icon {
      background: var(--td-brand-color-2);
    }
  }
}
</style> 