<template>
  <div class="class-management-container">
    <t-card>
      <template #title>
        <div class="page-header">
          <t-button theme="default" variant="text" @click="backToMajorList">
            <template #icon><t-icon name="chevron-left" /></template>
            返回专业管理
          </t-button>
          <span class="divider">|</span>
          <span>{{ majorName }} - 班级管理</span>
        </div>
      </template>

      <!-- 搜索区域 -->
      <div class="search-area">
        <t-form ref="searchForm" :data="searchFormData" layout="inline">
          <t-form-item label="班级名称" class="form-item-small">
            <t-input
              v-model="searchFormData.className"
              placeholder="请输入班级名称"
              clearable
            />
          </t-form-item>
          <t-form-item label="入学年份" class="form-item-small">
            <t-select
              v-model="searchFormData.entranceYear"
              placeholder="请选择入学年份"
              clearable
              :options="yearOptions"
            />
          </t-form-item>
          <t-form-item label="状态" class="form-item-small">
            <t-select
              v-model="searchFormData.status"
              placeholder="请选择状态"
              clearable
              :options="statusOptions"
            />
          </t-form-item>
          <t-form-item>
            <t-space>
              <t-button theme="primary" @click="handleSearch">搜索</t-button>
              <t-button theme="default" @click="resetSearch">重置</t-button>
            </t-space>
          </t-form-item>
        </t-form>
      </div>

      <!-- 操作按钮 -->
      <template #actions>
        <t-space>
          <t-button theme="primary" @click="handleAdd">
            <template #icon><t-icon name="add" /></template>
            新增班级
          </t-button>
          <t-button theme="default" @click="handleSyncStudentCount">
            <template #icon><t-icon name="refresh" /></template>
            同步班级人数
          </t-button>
          <t-button theme="default" @click="handleImport">
            <template #icon><t-icon name="upload" /></template>
            导入
          </t-button>
          <t-button theme="default" @click="handleExport">
            <template #icon><t-icon name="download" /></template>
            导出
          </t-button>
        </t-space>
      </template>

      <!-- 数据表格 -->
      <t-loading :loading="loading">
        <t-table
          :data="tableData"
          :columns="columns"
          :row-key="rowKey"
          :pagination="pagination"
          :max-height="600"
          stripe
          bordered
          hover
          @page-change="onPageChange"
        >
          <template #serial-number="{ rowIndex }">
            {{ (pagination.current - 1) * pagination.pageSize + rowIndex + 1 }}
          </template>

          <template #status="{ row }">
            <t-tag :theme="row.status === 0 ? 'success' : row.status === 1 ? 'warning' : 'danger'" variant="light">
              {{ row.status === 0 ? '正常' : row.status === 1 ? '停用' : '删除' }}
            </t-tag>
          </template>

          <template #classStatus="{ row }">
            <t-tag :theme="row.classStatus === 0 ? 'success' : 'danger'" variant="light">
              {{ row.classStatus === 0 ? '在读' : '毕业' }}
            </t-tag>
          </template>

          <template #headteacher="{ row }">
            <div v-if="row.user">
              <t-tooltip
                :content="getTeacherTooltipContent(row.user)"
                placement="top"
                :show-arrow="true"
                :dangerously-use-html-string="true"
              >
                <span class="teacher-name">{{ row.user.name || row.user.username }}</span>
              </t-tooltip>
            </div>
            <span v-else class="no-teacher-table">未设置</span>
          </template>

          <template #createTime="{ row }">
            {{ formatDate(row.createTime) }}
          </template>

          <template #operation="{ row }">
            <t-space size="small">
              <t-button theme="primary" variant="text" size="small" @click="handleViewDetail(row)">
                详情
              </t-button>
              <t-button theme="primary" variant="text" size="small" @click="handleEdit(row)">
                编辑
              </t-button>
              <t-button theme="danger" variant="text" size="small" @click="handleDelete(row)">
                删除
              </t-button>
            </t-space>
          </template>
        </t-table>
      </t-loading>
    </t-card>

    <!-- 新增/编辑对话框 -->
    <t-dialog
      v-model:visible="dialogVisible"
      :header="dialogTitle"
      :width="500"
      :footer="false"
      @close="dialogVisible = false"
    >
      <t-form
        ref="formRef"
        :data="formData"
        :rules="rules"
        label-width="100px"
        @submit="handleSubmit"
      >
        <t-form-item label="班级名称" name="className">
          <t-input v-model="formData.className" placeholder="请输入班级名称"></t-input>
        </t-form-item>

        <t-form-item label="入学年份" name="entranceYear">
          <t-select
            v-model="formData.entranceYear"
            placeholder="请选择入学年份"
            :options="yearOptions"
          ></t-select>
        </t-form-item>

        <t-form-item label="班主任" name="headteacherId">
          <t-select
            v-model="formData.headteacherId"
            placeholder="请选择班主任（可选）"
            :options="teacherOptions"
            clearable
          ></t-select>
        </t-form-item>

        <t-form-item label="班级状态" name="classStatus">
          <t-radio-group v-model="formData.classStatus">
            <t-radio :value="0">在读</t-radio>
            <t-radio :value="-1">毕业</t-radio>
          </t-radio-group>
        </t-form-item>

        <t-form-item label="记录状态" name="status">
          <t-radio-group v-model="formData.status">
            <t-radio :value="0">正常</t-radio>
            <t-radio :value="1">停用</t-radio>
            <t-radio :value="-1">删除</t-radio>
          </t-radio-group>
        </t-form-item>

        <t-form-item>
          <t-space>
            <t-button theme="primary" type="submit">确认</t-button>
            <t-button theme="default" variant="base" @click="dialogVisible = false">取消</t-button>
          </t-space>
        </t-form-item>
      </t-form>
    </t-dialog>

    <!-- 详情对话框 -->
    <t-dialog
      v-model:visible="detailDialogVisible"
      header="班级详情"
      :width="700"
      top="70"
      @close="detailDialogVisible = false"
    >
      <t-loading :loading="detailLoading">
        <div class="detail-container" v-if="detailData">
          <div class="detail-grid">
            <!-- 班主任信息 - 一行显示 -->
            <div class="detail-section">
              <div class="section-title">班主任信息</div>
              <div class="detail-row full-width">
                <div class="detail-content">
                    <div v-if="detailData.user" class="teacher-detail-info">
                      <div class="teacher-info-row">
                        <div class="teacher-item">
                          <t-icon name="user" size="14px" />
                          <span class="teacher-name">{{ detailData.user.name || detailData.user.username }}</span>
                        </div>
                        <div class="teacher-item" v-if="detailData.user.gender !== undefined && detailData.user.gender !== null">
                          <t-icon name="user-circle" size="14px" />
                          <t-tag :theme="detailData.user.gender === 1 ? 'primary' : 'success'" variant="light" size="small">
                            {{ detailData.user.gender === 1 ? '男' : '女' }}
                          </t-tag>
                        </div>
                        <div class="teacher-item" v-if="detailData.user.phone">
                          <t-icon name="call" size="14px" />
                          <span>{{ detailData.user.phone }}</span>
                        </div>
                        <div class="teacher-item" v-if="detailData.user.email">
                          <t-icon name="mail" size="14px" />
                          <span>{{ detailData.user.email }}</span>
                        </div>
                      </div>
                    </div>
                    <span v-else class="no-teacher">未设置班主任</span>
                  </div>
              </div>
            </div>
            
            <!-- 基本信息 -->
            <div class="detail-section">
              <div class="section-title">基本信息</div>
              <div class="detail-row">
                <div class="detail-item">
                  <div class="detail-label">
                    <t-icon name="browse" size="14px" />
                    班级ID
                  </div>
                  <div class="detail-content">{{ detailData.classId }}</div>
                </div>
                <div class="detail-item">
                  <div class="detail-label">
                    <t-icon name="file" size="14px" />
                    班级名称
                  </div>
                  <div class="detail-content">{{ detailData.className }}</div>
                </div>
              </div>
              <div class="detail-row">
                <div class="detail-item">
                  <div class="detail-label">
                    <t-icon name="calendar" size="14px" />
                    入学年份
                  </div>
                  <div class="detail-content">{{ detailData.entranceYear }}</div>
                </div>
                <div class="detail-item">
                  <div class="detail-label">
                    <t-icon name="book" size="14px" />
                    所属专业
                  </div>
                  <div class="detail-content">{{ majorName }}</div>
                </div>
              </div>
              <div class="detail-row full-width">
                <div class="detail-item">
                  <div class="detail-label">
                    <t-icon name="user-avatar" size="14px" />
                    学生数量
                  </div>
                  <div class="detail-content">{{ detailData.studentNumber || 0 }}</div>
                </div>
              </div>
            </div>
            
            <!-- 状态信息 -->
            <div class="detail-section">
              <div class="section-title">状态信息</div>
              <div class="detail-row">
                <div class="detail-item">
                  <div class="detail-label">
                    <t-icon name="check" size="14px" />
                    班级状态
                  </div>
                  <div class="detail-content">
                    <t-tag :theme="detailData.classStatus === 0 ? 'success' : 'danger'" variant="light">
                      {{ detailData.classStatus === 0 ? '在读' : '毕业' }}
                    </t-tag>
                  </div>
                </div>
                <div class="detail-item">
                  <div class="detail-label">
                    <t-icon name="setting" size="14px" />
                    记录状态
                  </div>
                  <div class="detail-content">
                    <t-tag :theme="detailData.status === 0 ? 'success' : detailData.status === 1 ? 'warning' : 'danger'" variant="light">
                      {{ detailData.status === 0 ? '正常' : detailData.status === 1 ? '停用' : '删除' }}
                    </t-tag>
                  </div>
                </div>
              </div>
              <div class="detail-row">
                <div class="detail-item">
                  <div class="detail-label">
                    <t-icon name="time" size="14px" />
                    创建时间
                  </div>
                  <div class="detail-content">{{ formatDate(detailData.createTime) }}</div>
                </div>
                <div class="detail-item">
                  <div class="detail-label">
                    <t-icon name="user-add" size="14px" />
                    创建人
                  </div>
                  <div class="detail-content">{{ detailData.creator }}</div>
                </div>
              </div>
              <div class="detail-row">
                <div class="detail-item">
                  <div class="detail-label">
                    <t-icon name="edit" size="14px" />
                    更新时间
                  </div>
                  <div class="detail-content">{{ formatDate(detailData.modifyTime) }}</div>
                </div>
                <div class="detail-item">
                  <div class="detail-label">
                    <t-icon name="user-add" size="14px" />
                    更新人
                  </div>
                  <div class="detail-content">{{ detailData.modifier }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <t-empty v-else description="暂无详情数据" />
      </t-loading>
      <template #footer>
        <t-space>
          <t-button theme="default" @click="detailDialogVisible = false">关闭</t-button>
        </t-space>
      </template>
    </t-dialog>

    <!-- 导入对话框 -->
    <ImportDialog
      v-model:visible="importDialogVisible"
      :config="importConfig"
      :callbacks="importCallbacks"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch, getCurrentInstance } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { formatDate } from '@/utils/date';
import ImportDialog from '@/components/ImportDialog/index.vue';
import type { ImportConfig, ImportCallbacks } from '@/components/ImportDialog/types';

const { proxy } = getCurrentInstance();
import { 
  getClassesList, 
  getClassDetail, 
  addClass, 
  updateClass, 
  deleteClass,
  stopClass,
  getTeacherOptions,
  importClasses,
  syncStudentCount,
  type ClassItem,
  type ClassQueryParams,
  type ClassFormData,
  type SyncStudentCountParams
} from '@/api/base/classes';

// 路由相关
const route = useRoute();
const router = useRouter();
const majorId = ref(Number(route.params.majorId) || 0);
const majorName = ref((route.query.majorName as string) || '专业管理');

// 返回专业管理列表
const backToMajorList = () => {
  router.push('/base/major');
};

// 页面状态
const loading = ref(false);
const rowKey = 'classId';

// 搜索表单数据
const searchFormData = reactive<ClassQueryParams>({
  className: '',
  entranceYear: '',
  status: 0, // 默认查询正常状态的班级
  majorId: majorId.value,
  current: 1,
  size: 10
});

// 状态选项
const statusOptions = [
  { label: '正常', value: 0 },
  { label: '停用', value: 1 },
  { label: '删除', value: -1 },
];

// 班级状态选项
const classStatusOptions = [
  { label: '在读', value: 0 },
  { label: '毕业', value: -1 },
];

// 年份选项 - 从2000年到当前年
const currentYear = new Date().getFullYear();
const yearOptions = Array.from({ length: currentYear - 1999 }, (_, i) => {
  const year = currentYear - i;
  return { label: `${year}年`, value: `${year}` };
});

// 班主任选项
const teacherOptions = ref<{ label: string; value: number }[]>([]);

// 获取教师选项列表
const fetchTeacherOptions = async () => {
  try {
    const response = await getTeacherOptions();
    if (response.data) {
      teacherOptions.value = response.data.map((teacher: any) => ({
        label: teacher.name || teacher.teacherName || teacher.user?.name || '未知教师',
        value: teacher.user?.id
      }));
    }
  } catch (error) {
    console.error('获取教师列表失败:', error);
  }
};

// 表格数据
const tableData = ref<ClassItem[]>([]);

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showJumper: true,
  showPageSize: true,
  pageSizeOptions: [10, 20, 50],
  onChange: (pageInfo: any) => {
    pagination.current = pageInfo.current;
    pagination.pageSize = pageInfo.pageSize;
    fetchClassList();
  },
});

// 表格列配置
const columns = [
  { colKey: 'serial-number', title: '序号', width: 80, align: 'center' as const },
  { colKey: 'className', title: '班级名称', width: 180, align: 'left' as const },
  { colKey: 'entranceYear', title: '入学年份', width: 100, align: 'center' as const },
  { colKey: 'headteacher', title: '班主任', width: 120, align: 'center' as const },
  { colKey: 'studentNumber', title: '学生数', width: 90, align: 'center' as const },
  { colKey: 'classStatus', title: '班级状态', width: 100, align: 'center' as const },
  { colKey: 'status', title: '记录状态', width: 100, align: 'center' as const },
  { colKey: 'createTime', title: '创建时间', width: 160, align: 'center' as const },
  { colKey: 'operation', title: '操作', width: 180, align: 'center' as const },
];

// 表单数据
const formData = reactive<ClassFormData>({
  classId: undefined,
  majorId: majorId.value,
  className: '',
  entranceYear: `${currentYear}`,
  headteacherId: undefined,
  studentNumber: 0,
  classStatus: 0,
  status: 0,
});

// 表单校验规则
const rules = {
  className: [
    { required: true, message: '请输入班级名称', trigger: 'blur' as const },
    { max: 50, message: '班级名称不能超过50个字符', trigger: 'blur' as const }
  ],
  entranceYear: [
    { required: true, message: '请选择入学年份', trigger: 'change' as const }
  ],
  headteacherId: [
    { required: false, message: '请选择班主任', trigger: 'change' as const }
  ],
  classStatus: [
    { required: true, message: '请选择班级状态', trigger: 'change' as const }
  ],
  status: [
    { required: true, message: '请选择记录状态', trigger: 'change' as const }
  ],
};

// 对话框控制
const dialogVisible = ref(false);
const dialogTitle = ref('新增班级');
const formRef = ref(null);

// 详情对话框控制
const detailDialogVisible = ref(false);
const detailLoading = ref(false);
const detailData = ref<ClassItem | null>(null);

// 导入对话框控制
const importDialogVisible = ref(false);

// 导入配置
const importConfig: ImportConfig = {
  title: '导入班级信息',
  tips: '请按照模板格式填写班级信息，支持批量导入',
  templateFileName: '班级导入模板.xlsx',
  templateData: [
    ['班级名称', '入学年份', '班主任姓名', '班级状态', '备注'],
    ['计算机科学与技术1班', '2024', '张老师', '在读', '示例数据'],
    ['软件工程1班', '2024', '李老师', '在读', '示例数据'],
  ],
  acceptTypes: ['.xlsx', '.xls'],
  maxFileSize: 5
};

// 导入回调函数
const importCallbacks: ImportCallbacks = {
  onImport: async (file: File) => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('majorId', majorId.value.toString());
    return await importClasses(formData);
  },
  onSuccess: () => {
    fetchClassList();
  },
  onError: (error: Error) => {
    console.error('导入失败:', error);
  },
  onComplete: () => {
    // 导入完成后的处理
  }
};

// 搜索处理
const handleSearch = () => {
  pagination.current = 1;
  searchFormData.current = 1;
  searchFormData.size = pagination.pageSize;
  fetchClassList();
};

// 重置搜索
const resetSearch = () => {
  searchFormData.className = '';
  searchFormData.entranceYear = '';
  searchFormData.status = 0;
  searchFormData.current = 1;
  searchFormData.size = pagination.pageSize;
  pagination.current = 1;
  fetchClassList();
};

// 获取班级列表
const fetchClassList = async () => {
  loading.value = true;
  try {
    // 构建查询参数
    const queryParams: ClassQueryParams = {
      ...searchFormData,
      current: pagination.current,
      size: pagination.pageSize
    };
    
    const {data} = await getClassesList(queryParams);
    if (data) {
      tableData.value = data.records || [];
      pagination.total = data.total || 0;
    }
  } catch (error) {
    console.error('获取班级列表失败:', error);
    proxy.$baseMessage('获取班级列表失败', 'error');
  } finally {
    loading.value = false;
  }
};

// 分页变化处理
const onPageChange = (pageInfo: any) => {
  pagination.current = pageInfo.current;
  pagination.pageSize = pageInfo.pageSize;
  searchFormData.current = pageInfo.current;
  searchFormData.size = pageInfo.pageSize;
  fetchClassList();
};

// 新增班级
const handleAdd = () => {
  formData.classId = undefined;
  formData.className = '';
  formData.entranceYear = `${currentYear}`;
  formData.headteacherId = undefined;
  formData.studentNumber = 0;
  formData.classStatus = 0;
  formData.status = 0;
  
  dialogTitle.value = '新增班级';
  dialogVisible.value = true;
};

// 编辑班级
const handleEdit = (row: ClassItem) => {
  formData.classId = row.classId;
  formData.className = row.className;
  formData.entranceYear = row.entranceYear;
  formData.headteacherId = row.headteacherId;
  formData.studentNumber = row.studentNumber;
  formData.classStatus = row.classStatus;
  formData.status = row.status;
  
  dialogTitle.value = '编辑班级';
  dialogVisible.value = true;
};

// 查看班级详情
const handleViewDetail = async (row: ClassItem) => {
  detailLoading.value = true;
  detailDialogVisible.value = true;
  
  try {
    const response = await getClassDetail(row.classId);
    if (response.data) {
      detailData.value = response.data;
    } else {
      detailData.value = null;
    }
  } catch (error) {
    console.error('获取详情失败:', error);
    proxy.$baseMessage('获取详情失败', 'error');
    detailData.value = null;
  } finally {
    detailLoading.value = false;
  }
};

// 生成班主任tooltip内容
const getTeacherTooltipContent = (user: any) => {
  const content = [];
  
  if (user.name || user.username) {
    content.push(`姓名：${user.name || user.username}`);
  }
  
  if (user.gender !== undefined && user.gender !== null) {
    content.push(`性别：${user.gender === 1 ? '男' : user.gender === 0 ? '女' : '保密'}`);
  }
  
  if (user.phone) {
    content.push(`手机：${user.phone}`);
  }
  
  if (user.email) {
    content.push(`邮箱：${user.email}`);
  }
  
  return content.join('<br/>');
};



// 删除班级
const handleDelete = async (row: ClassItem) => {
  try {
    await proxy.$baseConfirm(`确定要删除班级"${row.className}"吗？`, '删除确认', '确认删除', '取消', async () => {
      const response = await deleteClass(row.classId);
      if (response.data) {
        proxy.$baseMessage('删除成功', 'success');
        fetchClassList(); // 刷新列表
      }
    });
  } catch (error) {
    console.error('删除失败:', error);
    proxy.$baseMessage('删除失败，请重试', 'error');
  }
};

// 提交表单
const handleSubmit = async () => {
  try {
    const formDataToSubmit: ClassFormData = {
      ...formData,
      majorId: majorId.value
    };
    
    if (formData.classId) {
      // 编辑
      const response = await updateClass(formDataToSubmit);
      if (response.data) {
        proxy.$baseMessage('编辑成功', 'success');
        dialogVisible.value = false;
        fetchClassList(); // 刷新列表
      }
    } else {
      // 新增
      const response = await addClass(formDataToSubmit);
      if (response.data) {
        proxy.$baseMessage('新增成功', 'success');
        dialogVisible.value = false;
        fetchClassList(); // 刷新列表
      }
    }
  } catch (error) {
    console.error('提交失败:', error);
    proxy.$baseMessage('操作失败，请重试', 'error');
  }
};

// 导入班级
const handleImport = () => {
  importDialogVisible.value = true;
};

// 导出班级
const handleExport = () => {
  proxy.$baseMessage('导出功能开发中...', 'info');
};

// 同步班级人数
const handleSyncStudentCount = async () => {
  try {
    // 构建同步参数，使用当前搜索条件
    const syncParams: SyncStudentCountParams = {
      majorId: majorId.value || undefined,
      entranceYear: searchFormData.entranceYear || undefined,
      studentStatus: searchFormData.status !== 0 ? searchFormData.status : undefined
    };

    // 显示确认对话框
    await proxy.$baseConfirm(
      '确定要同步班级人数吗？这将根据当前筛选条件统计各班级的实际学生人数并更新班级表中的冗余字段。',
      '同步班级人数',
      '确认同步',
      '取消',
      async () => {
        try {
          loading.value = true;
          const response = await syncStudentCount(syncParams);
          
          if (response.data && response.data.success) {
            const result = response.data;
            proxy.$baseMessage(
              `同步完成！共处理 ${result.totalClasses} 个班级，成功更新 ${result.updatedClasses} 个，失败 ${result.errorCount} 个，耗时 ${result.duration} ms`,
              'success'
            );
            // 刷新班级列表
            fetchClassList();
          } else {
            proxy.$baseMessage(response.data?.message || '同步失败', 'error');
          }
        } catch (error) {
          console.error('同步班级人数失败:', error);
          proxy.$baseMessage('同步失败，请重试', 'error');
        } finally {
          loading.value = false;
        }
      }
    );
  } catch (error) {
    console.error('同步班级人数失败:', error);
    proxy.$baseMessage('同步失败，请重试', 'error');
  }
};

// 文件上传前处理 - 已移除，使用ImportDialog组件处理

// 初始化
onMounted(async () => {
  // 初始化路由参数 - 从路径参数获取majorId，从查询参数获取majorName
  majorId.value = Number(route.params.majorId) || 0;
  majorName.value = (route.query.majorName as string) || '专业管理';
  
  // 设置搜索表单的专业ID
  searchFormData.majorId = majorId.value;
  formData.majorId = majorId.value;
  
  // 获取教师选项列表
  await fetchTeacherOptions();
  
  // 班级管理页面可以独立访问，不强制要求majorId
  fetchClassList();
});

// 监听路由参数变化
watch(
  () => route.params.majorId,
  (newMajorId) => {
    majorId.value = Number(newMajorId) || 0;
    searchFormData.majorId = majorId.value;
    formData.majorId = majorId.value;
    fetchClassList();
  }
);

// 监听查询参数变化
watch(
  () => route.query.majorName,
  (newMajorName) => {
    majorName.value = (newMajorName as string) || '专业管理';
  }
);
</script>

<style scoped lang="less">
.class-management-container {
  padding: 20px;
}

.page-header {
  display: flex;
  align-items: center;
  
  .divider {
    margin: 0 12px;
    color: var(--td-text-color-placeholder);
  }
}

.search-area {
  margin-bottom: 20px;
  
  .form-item-small {
    width: 180px;
  }
}

.operation-container {
  display: flex;
  justify-content: center;
}

.detail-container {
  padding: 16px;
  background: var(--td-bg-color-container);
  border-radius: 8px;
}

.detail-grid {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.detail-section {
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding: 14px;
  background: var(--td-bg-color-page);
  border-radius: 8px;
  border: 1px solid var(--td-border-level-1-color);
  transition: all 0.2s ease;
}

.detail-section:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.section-title {
  font-weight: 600;
  color: var(--td-brand-color);
  font-size: 15px;
  padding-bottom: 6px;
  border-bottom: 2px solid var(--td-brand-color);
  margin-bottom: 10px;
  position: relative;
}

.section-title::before {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 30px;
  height: 2px;
  background: linear-gradient(90deg, var(--td-brand-color) 0%, transparent 100%);
}

.detail-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.detail-row.full-width {
  grid-template-columns: 1fr;
}

.detail-item.full-width-item {
  max-width: none;
  width: 100%;
}

.detail-item {
  display: flex;
  flex-direction: row;
  gap: 8px;
  padding: 10px;
  background: var(--td-bg-color-container);
  border-radius: 6px;
  border: 1px solid var(--td-border-level-1-color);
  transition: all 0.2s ease;
  align-items: center;
}

.detail-item:hover {
  border-color: var(--td-brand-color-light);
  box-shadow: 0 1px 4px rgba(0, 82, 217, 0.1);
}

.detail-label {
  font-weight: 600;
  width: 80px;
  color: var(--td-text-color-primary);
  font-size: 13px;
  display: flex;
  align-items: center;
  gap: 4px;
  flex-shrink: 0;
}

.detail-content {
  color: var(--td-text-color-secondary);
  font-size: 13px;
  line-height: 1.4;
  font-weight: 500;
  flex: 1;
}

.teacher-name {
  font-weight: 500;
  color: var(--td-text-color-primary);
  cursor: pointer;
  transition: all 0.2s ease;
  padding: 4px 8px;
  border-radius: 4px;
}

.teacher-name:hover {
  color: var(--td-brand-color);
  background-color: var(--td-brand-color-light);
  text-decoration: none;
}

.no-teacher {
  color: var(--td-text-color-placeholder);
  font-style: italic;
  padding: 8px;
  background: linear-gradient(135deg, var(--td-bg-color-page) 0%, var(--td-bg-color-container) 100%);
  border-radius: 4px;
  border: 1px dashed var(--td-border-level-2-color);
  margin-top: 2px;
  text-align: center;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  transition: all 0.2s ease;
}

.no-teacher::before {
  content: '⚠️';
  font-size: 12px;
  opacity: 0.5;
}

.teacher-detail-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 12px;
  background: linear-gradient(135deg, var(--td-brand-color-light) 0%, var(--td-bg-color-container) 100%);
  border-radius: 6px;
  border: 1px solid var(--td-brand-color-light);
  margin-top: 2px;
  box-shadow: 0 1px 4px rgba(0, 82, 217, 0.08);
  position: relative;
  overflow: hidden;
}


.teacher-info-row {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.teacher-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: var(--td-text-color-secondary);
  padding: 6px 10px;
  background-color: var(--td-bg-color-container);
  border-radius: 4px;
  border: 1px solid var(--td-border-level-1-color);
  transition: all 0.2s ease;
}

.teacher-item:hover {
  border-color: var(--td-brand-color-light);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.06);
}

.teacher-main {
  display: flex;
  align-items: center;
  gap: 12px;
}

.teacher-contact {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: var(--td-text-color-secondary);
}

/* 表格中的未设置样式 */
.no-teacher-table {
  color: var(--td-text-color-placeholder);
  font-style: italic;
  font-size: 12px;
}
</style> 