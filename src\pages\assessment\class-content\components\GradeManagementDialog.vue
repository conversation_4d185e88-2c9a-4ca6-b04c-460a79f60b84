<template>
  <div>
    <!-- 直接录入模式 - 普通弹窗 -->
    <t-dialog
      v-model:visible="directDialogVisible"
      :header="directDialogTitle"
      width="90vw"
      :top="50"
      class="grade-dialog-minwidth"
      :footer="false"
      @close="handleClose"
    >
      <div class="grade-management-dialog" @mousedown="handleDialogMouseDown">
        <!-- 考核内容信息 -->
        <div class="assessment-info">
          <div class="info-content">
            <div class="info-meta">
              <div class="meta-item">
                <span class="label">班级：</span>
                <span class="value">{{ classInfo?.className }}</span>
              </div>
              <div class="meta-item">
                <span class="label">人数：</span>
                <span class="value">{{ classInfo?.studentCount }}人</span>
              </div>
              <div class="meta-item">
                <span class="label">录入方式：</span>
                <t-tag size="small" theme="primary">直接录入</t-tag>
              </div>
            </div>
          </div>
          <div class="info-actions">
            <t-button theme="primary" @click="handleImportGrades">
              <template #icon>
                <t-icon name="upload" />
              </template>
              导入成绩
            </t-button>
            <t-button theme="default" variant="outline" @click="handleExportGrades">
              <template #icon>
                <t-icon name="file-export" />
              </template>
              导出成绩
            </t-button>
            <t-button theme="default" variant="outline" @click="handleRefresh">
              <template #icon>
                <t-icon name="refresh" />
              </template>
              刷新
            </t-button>
          </div>
        </div>

        <!-- 成绩统计卡片 -->
        <div class="stats-cards" v-if="classInfo">
          <div class="stat-card">
            <div class="stat-icon average">
              <t-icon name="chart-line" />
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ gradeStats.averageScore }}</div>
              <div class="stat-label">平均分</div>
            </div>
          </div>
          
          <div class="stat-card">
            <div class="stat-icon highest">
              <t-icon name="arrow-up" />
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ gradeStats.maxScore }}</div>
              <div class="stat-label">最高分</div>
            </div>
          </div>
          
          <div class="stat-card">
            <div class="stat-icon lowest">
              <t-icon name="arrow-down" />
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ gradeStats.minScore }}</div>
              <div class="stat-label">最低分</div>
            </div>
          </div>
          
          <div class="stat-card">
            <div class="stat-icon submitted">
              <t-icon name="check-circle" />
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ gradeStats.submittedCount }}</div>
              <div class="stat-label">已录入</div>
            </div>
          </div>
          
          <div class="stat-card">
            <div class="stat-icon pending">
              <t-icon name="time" />
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ gradeStats.pendingCount }}</div>
              <div class="stat-label">待录入</div>
            </div>
          </div>
        </div>

        <!-- 搜索工具栏 -->
        <div class="search-toolbar">
          <div class="search-section">
            <t-input
              v-model="searchKeyword"
              placeholder="搜索学号或姓名"
              style="width: 200px"
              clearable
            >
              <template #prefix-icon>
                <t-icon name="search" />
              </template>
            </t-input>
            <span class="status-hint">{{ statusDescription }}</span>
          </div>
          <div class="toolbar-actions">
            <t-button 
              theme="warning" 
              :disabled="!hasChanges"
              @click="handleSaveChanges"
            >
              <template #icon>
                <t-icon name="save" />
              </template>
              保存修改{{ changedCellsCount > 0 ? ` (${changedCellsCount})` : '' }}
            </t-button>
            <t-button 
              theme="default" 
              variant="outline"
              :disabled="!hasChanges"
              @click="handleCancelChanges"
            >
              <template #icon>
                <t-icon name="close" />
              </template>
              取消保存
            </t-button>
            <t-button 
              theme="success" 
              :disabled="hasChanges"
              @click="handleSubmitGrades"
            >
              <template #icon>
                <t-icon name="check" />
              </template>
              提交成绩
            </t-button>
          </div>
        </div>

        <!-- 学生成绩表格 -->
        <div class="grade-table">
          <t-table
            :data="filteredDirectGradeList"
            :columns="directTableColumns"
            row-key="studentId"
            stripe
            hover
            :loading="loading"
            :pagination="directPagination"
            @page-change="handleDirectPageChange"
          >
            <!-- 课程目标列的行内编辑 -->
            <template v-for="objective in courseObjectives" :key="objective.id" #[`courseTargetScores.${objective.id}`]="{ row }">
              <div
                class="inline-edit-cell"
                :data-student-id="row.studentId"
                :data-target-no="objective.id"
                :class="{ 'score-changed': hasScoreChanged(row.studentId, objective.id) }"
              >
                <t-input-number
                  v-if="editingCell?.studentId === row.studentId && editingCell?.targetNo === objective.id && scoreStatus === 1"
                  ref="inputNumberRef"
                  :model-value="getCourseTargetScore(row, objective.id)"
                  :min="0"
                  :max="row.courseTargetScores.find((ts: CourseTargetScore) => ts.courseTargetNo === objective.id)?.fullScore || 100"
                  :decimal-places="1"
                  placeholder="请输入分数"
                  style="width: 120px"
                  @blur="handleCellBlur"
                  @enter="handleCellBlur"
                  @update:model-value="(value: number) => updateCourseTargetScore(row, objective.id, value)"
                />
                <t-tooltip theme="light" v-else-if="hasScoreChanged(row.studentId, objective.id)" :content="`原始成绩: ${getOriginalScore(row.studentId, objective.id)}`">
                  <span
                    class="score-display"
                    :class="{ 'score-changed': hasScoreChanged(row.studentId, objective.id) }"
                    :style="{
                      cursor: scoreStatus === 1 ? 'pointer' : 'default',
                      backgroundColor: hasScoreChanged(row.studentId, objective.id) ? 'var(--td-warning-color-light)' : 'transparent',
                      color: hasScoreChanged(row.studentId, objective.id) ? 'var(--td-warning-color)' : 'inherit'
                    }"
                    @click="scoreStatus === 1 && handleCellClick(row.studentId, objective.id)"
                  >
                    {{ getCourseTargetScoreDisplay(row, objective.id) }}
                  </span>
                </t-tooltip>
                <span
                  v-else
                  class="score-display"
                  :class="{ 'score-changed': hasScoreChanged(row.studentId, objective.id) }"
                  :style="{
                    cursor: scoreStatus === 1 ? 'pointer' : 'default',
                    backgroundColor: hasScoreChanged(row.studentId, objective.id) ? 'var(--td-warning-color-light)' : 'transparent',
                    color: hasScoreChanged(row.studentId, objective.id) ? 'var(--td-warning-color)' : 'inherit'
                  }"
                  @click="scoreStatus === 1 && handleCellClick(row.studentId, objective.id)"
                >
                  {{ getCourseTargetScoreDisplay(row, objective.id) }}
                </span>
              </div>
            </template>

            <template #totalScore="{ row }">
              <t-tooltip theme="light" v-if="hasTotalScoreChanged(row.studentId)" :content="`原始总分: ${getOriginalTotalScore(row.studentId)}`">
                <span 
                class="total-score" 
                :class="[getTotalScoreClass(row.totalScore), { 'score-changed': hasTotalScoreChanged(row.studentId) } ]"
                :style="{
                  backgroundColor: hasTotalScoreChanged(row.studentId) ? 'var(--td-warning-color-light)' : 'transparent',
                  color: hasTotalScoreChanged(row.studentId) ? 'var(--td-warning-color)' : 'inherit'
                }"
              >
                {{ getTotalScoreDisplay(row.totalScore) }}
              </span>
              </t-tooltip>
              <span 
                v-else
                class="total-score" 
                :class="[getTotalScoreClass(row.totalScore), { 'score-changed': hasTotalScoreChanged(row.studentId) } ]"
                :style="{
                  backgroundColor: hasTotalScoreChanged(row.studentId) ? 'var(--td-warning-color-light)' : 'transparent',
                  color: hasTotalScoreChanged(row.studentId) ? 'var(--td-warning-color)' : 'inherit'
                }"
              >
                {{ getTotalScoreDisplay(row.totalScore) }}
              </span>
            </template>
          </t-table>
        </div>
      </div>
    </t-dialog>

    <!-- 文件导入对话框 -->
    <ImportDialog
      v-model:visible="importDialogVisible"
      :config="importConfig"
      :callbacks="importCallbacks"
      :dialog-props="{ zIndex: 3100 }"
    />

    <!-- 详细录入模式 - 全屏弹窗 -->
    <FullScreenDialog
      v-model:visible="detailedDialogVisible"
      :header="detailedDialogTitle"
      @close="handleClose"
    >
      <div class="detailed-grade-management" @mousedown="handleDetailedDialogMouseDown">
        <!-- 考核内容信息 -->
        <div class="assessment-info">
          <div class="info-content">
            <div class="info-meta">
              <div class="meta-item">
                <span class="label">班级：</span>
                <span class="value">{{ classInfo?.className }}</span>
              </div>
              <div class="meta-item">
                <span class="label">人数：</span>
                <span class="value">{{ classInfo?.studentCount }}人</span>
              </div>
              <div class="meta-item">
                <span class="label">录入方式：</span>
                <t-tag size="small" theme="success">详细录入</t-tag>
              </div>
            </div>
          </div>
          <div class="info-actions">
            <t-button theme="primary" @click="handleImportDetailedGrades">
              <template #icon>
                <t-icon name="upload" />
              </template>
              导入成绩
            </t-button>
            <t-button theme="default" variant="outline" @click="handleExportDetailedGrades">
              <template #icon>
                <t-icon name="file-export" />
              </template>
              导出成绩
            </t-button>
            <t-button theme="default" variant="outline" @click="handleRefresh">
              <template #icon>
                <t-icon name="refresh" />
              </template>
              刷新
            </t-button>
          </div>
        </div>

        <!-- 成绩统计卡片 -->
        <div class="stats-cards">
          <div class="stat-card">
            <div class="stat-icon average">
              <t-icon name="chart-line" />
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ detailedGradeStats.averageScore }}</div>
              <div class="stat-label">平均分</div>
            </div>
          </div>
          
          <div class="stat-card">
            <div class="stat-icon highest">
              <t-icon name="arrow-up" />
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ detailedGradeStats.maxScore }}</div>
              <div class="stat-label">最高分</div>
            </div>
          </div>
          
          <div class="stat-card">
            <div class="stat-icon lowest">
              <t-icon name="arrow-down" />
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ detailedGradeStats.minScore }}</div>
              <div class="stat-label">最低分</div>
            </div>
          </div>
          
          <div class="stat-card">
            <div class="stat-icon submitted">
              <t-icon name="check-circle" />
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ detailedGradeStats.submittedCount }}</div>
              <div class="stat-label">已录入</div>
            </div>
          </div>
          
          <div class="stat-card">
            <div class="stat-icon pending">
              <t-icon name="time" />
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ detailedGradeStats.pendingCount }}</div>
              <div class="stat-label">待录入</div>
            </div>
          </div>
        </div>

        <!-- 搜索和筛选工具栏 -->
        <div class="search-toolbar">
          <div class="search-section">
            <t-input v-model="searchKeyword" placeholder="搜索学号或姓名" style="width: 200px" clearable>
              <template #prefix-icon>
                <t-icon name="search" />
              </template>
            </t-input>
            <span class="status-hint">{{ statusDescription }}</span>
          </div>
          <div class="toolbar-actions">
            
            <t-button 
              theme="warning" 
              :disabled="!hasDetailedChanges"
              @click="handleSaveDetailedChanges"
            >
              <template #icon>
                <t-icon name="save" />
              </template>
              保存修改{{ changedDetailedCellsCount > 0 ? ` (${changedDetailedCellsCount})` : '' }}
            </t-button>
            <t-button 
              theme="default" 
              variant="outline"
              :disabled="!hasDetailedChanges"
              @click="handleCancelDetailedChanges"
            >
              <template #icon>
                <t-icon name="close" />
              </template>
              取消保存
            </t-button>
            <t-button 
              theme="success" 
              :disabled="hasDetailedChanges"
              @click="handleSubmitDetailedGrades"
            >
              <template #icon>
                <t-icon name="check" />
              </template>
              提交成绩
            </t-button>
            <t-popup
              v-model:visible="columnSettingsPopupVisible"
              trigger="click"
              placement="bottom"
              :overlay-style="{ minWidth: '180px', padding: '8px 0' }"
            >
              <t-button variant="outline">
                <template #icon>
                  <t-icon name="view-list" />
                </template>
                列设置
              </t-button>
              <template #content>
                <div style="max-height: 260px; overflow-y: auto;">
                  <div v-for="type in props.allQuestionTypes" :key="type" style="display: flex; align-items: center; padding: 4px 16px;">
                    <t-checkbox
                      :checked="props.visibleQuestionTypes.includes(type)"
                      @change="(checked) => handleQuestionTypeVisibilityChange(type, checked)"
                    >
                      {{ props.allQuestionTypeNames[type] || type }}
                    </t-checkbox>
                  </div>
                </div>
              </template>
            </t-popup>
          </div>
        </div>

        <!-- 详细成绩表格 -->
        <div class="detailed-grade-table">
          <t-table
            :data="filteredDetailedGradeList"
            :columns="detailedTableColumns"
            row-key="studentId"
            stripe
            hover
            :loading="loading"
            bordered
            :pagination="detailedPagination"
            @page-change="handleDetailedPageChange"
          >
            <template
              v-for="slot in detailedScoreSlots"
              :key="slot.repositoryAnswerId"
              #[`score.${slot.repositoryAnswerId}`]="{ row }"
            >
              <div
                class="detailed-inline-edit-cell"
                :data-student-id="row.studentId"
                :data-repository-answer-id="slot.repositoryAnswerId"
                :class="{ 'score-changed': hasDetailedScoreChanged(row.studentId, slot.repositoryAnswerId) }"
              >
                <t-input-number
                  v-if="editingDetailedCell?.studentId === row.studentId && editingDetailedCell?.repositoryAnswerId === slot.repositoryAnswerId && scoreStatus === 1"
                  ref="detailedInputNumberRef"
                  :model-value="row.detailScores?.find((ds: any) => ds.repositoryAnswerId === slot.repositoryAnswerId)?.score || 0"
                  :min="0"
                  :max="slot.sub.maxScore || 100"
                  :decimal-places="1"
                  placeholder="请输入分数"
                  style="width: 60px; padding: 0 2px; vertical-align: middle;"
                  :theme="'normal'"
                  :auto-width="false"
                  :controls="false"
                  align="center"
                  @blur="handleDetailedCellBlur"
                  @enter="handleDetailedCellBlur"
                  @update:model-value="(value: number) => updateDetailedScore(row, slot.repositoryAnswerId, value)"
                />
                <t-tooltip theme="light" v-else-if="hasDetailedScoreChanged(row.studentId, slot.repositoryAnswerId)" :content="`原始成绩: ${getOriginalDetailedScore(row.studentId, slot.repositoryAnswerId)}`">
                  <span
                    class="score-display"
                    :class="{ 'score-changed': hasDetailedScoreChanged(row.studentId, slot.repositoryAnswerId) }"
                    :style="{
                      cursor: scoreStatus === 1 ? 'pointer' : 'default',
                      backgroundColor: hasDetailedScoreChanged(row.studentId, slot.repositoryAnswerId) ? 'var(--td-warning-color-light)' : 'transparent',
                      color: hasDetailedScoreChanged(row.studentId, slot.repositoryAnswerId) ? 'var(--td-warning-color)' : 'inherit'
                    }"
                    @click="scoreStatus === 1 && handleDetailedCellClick(row.studentId, slot.repositoryAnswerId)"
                  >
                    {{
                      (row.detailScores?.find((ds: any) => ds.repositoryAnswerId === slot.repositoryAnswerId)?.score ?? '') === '' ||
                      row.detailScores?.find((ds: any) => ds.repositoryAnswerId === slot.repositoryAnswerId)?.score === null ||
                      row.detailScores?.find((ds: any) => ds.repositoryAnswerId === slot.repositoryAnswerId)?.score === undefined
                        ? '-' 
                        : row.detailScores?.find((ds: any) => ds.repositoryAnswerId === slot.repositoryAnswerId)?.score
                    }}
                  </span>
                </t-tooltip>
                <span
                  v-else
                  class="score-display"
                  :class="{ 'score-changed': hasDetailedScoreChanged(row.studentId, slot.repositoryAnswerId) }"
                  :style="{
                    cursor: scoreStatus === 1 ? 'pointer' : 'default',
                    backgroundColor: hasDetailedScoreChanged(row.studentId, slot.repositoryAnswerId) ? 'var(--td-warning-color-light)' : 'transparent',
                    color: hasDetailedScoreChanged(row.studentId, slot.repositoryAnswerId) ? 'var(--td-warning-color)' : 'inherit'
                  }"
                  @click="scoreStatus === 1 && handleDetailedCellClick(row.studentId, slot.repositoryAnswerId)"
                >
                  {{
                    (row.detailScores?.find((ds: any) => ds.repositoryAnswerId === slot.repositoryAnswerId)?.score ?? '') === '' ||
                    row.detailScores?.find((ds: any) => ds.repositoryAnswerId === slot.repositoryAnswerId)?.score === null ||
                    row.detailScores?.find((ds: any) => ds.repositoryAnswerId === slot.repositoryAnswerId)?.score === undefined
                      ? '-' 
                      : row.detailScores?.find((ds: any) => ds.repositoryAnswerId === slot.repositoryAnswerId)?.score
                  }}
                </span>
              </div>
            </template>
            <template #totalScore="{ row }">
              <t-tooltip theme="light" v-if="hasDetailedTotalScoreChanged(row.studentId)" :content="`原始总分: ${getOriginalDetailedTotalScore(row.studentId)}`">
                <span 
                  class="total-score" 
                  :class="[getTotalScoreClass(row.totalScore), { 'score-changed': hasDetailedTotalScoreChanged(row.studentId) } ]"
                  :style="{
                    backgroundColor: hasDetailedTotalScoreChanged(row.studentId) ? 'var(--td-warning-color-light)' : 'transparent',
                    color: hasDetailedTotalScoreChanged(row.studentId) ? 'var(--td-warning-color)' : 'inherit'
                  }"
                >
                  {{ getTotalScoreDisplay(row.totalScore) }}
                </span>
              </t-tooltip>
              <span 
                v-else
                class="total-score" 
                :class="[getTotalScoreClass(row.totalScore), { 'score-changed': hasDetailedTotalScoreChanged(row.studentId) } ]"
                :style="{
                  backgroundColor: hasDetailedTotalScoreChanged(row.studentId) ? 'var(--td-warning-color-light)' : 'transparent',
                  color: hasDetailedTotalScoreChanged(row.studentId) ? 'var(--td-warning-color)' : 'inherit'
                }"
              >
                {{ getTotalScoreDisplay(row.totalScore) }}
              </span>
            </template>
          </t-table>
        </div>
      </div>
    </FullScreenDialog>

    <!-- 直接录入编辑弹窗 -->
    <t-dialog
      v-model:visible="directEditVisible"
      title="编辑成绩"
      width="600px"
      @confirm="handleSaveDirectGrade"
      @close="handleCloseDirectEdit"
    >
      <div class="direct-grade-edit" v-if="editingDirectGrade">
        <div class="student-info">
          <h4>{{ editingDirectGrade.studentName }} ({{ editingDirectGrade.studentId }})</h4>
        </div>
        <div class="grade-form">
          <div class="form-section">
            <h5>课程目标成绩</h5>
            <div class="objective-grades">
              <div
                v-for="(objective, index) in courseObjectives"
                :key="objective.id"
                class="objective-item"
              >
                <div class="objective-label">
                  {{ objective.name }}
                </div>
                <t-input-number
                  v-model="editingDirectGrade.objectives[objective.id]"
                  :min="0"
                  :max="100"
                  :decimal-places="1"
                  placeholder="请输入分数"
                  style="width: 120px"
                />
              </div>
            </div>
          </div>
          <div class="total-score-section">
            <div class="total-label">总分：</div>
            <div class="total-value">{{ calculateDirectTotal(editingDirectGrade) }}</div>
          </div>
        </div>
      </div>
    </t-dialog>

    <!-- 详细录入编辑弹窗 -->
    <t-dialog
      v-model:visible="detailedEditVisible"
      title="编辑详细成绩"
      width="800px"
      @confirm="handleSaveDetailedGrade"
      @close="handleCloseDetailedEdit"
    >
      <div class="detailed-grade-edit" v-if="editingDetailedGrade">
        <div class="student-info">
          <h4>{{ editingDetailedGrade.studentName }} ({{ editingDetailedGrade.studentId }})</h4>
        </div>
        <div class="grade-form">
          <div class="form-section">
            <h5>题目成绩</h5>
            <div class="question-grades">
              <div
                v-for="question in questionStructure"
                :key="question.id"
                class="question-group"
              >
                <div class="question-header">
                  <span class="question-number">{{ question.questionNumber }}</span>
                  <span class="question-type">{{ question.questionType }}</span>
                </div>
                <div class="sub-items">
                  <div
                    v-for="subItem in question.subItems"
                    :key="subItem.id"
                    class="sub-item"
                  >
                    <div class="sub-label">
                      {{ subItem.subNumber }}
                    </div>
                    <t-input-number
                      v-model="editingDetailedGrade.questions[subItem.id]"
                      :min="0"
                      :max="subItem.maxScore"
                      :decimal-places="1"
                      :placeholder="`满分${subItem.maxScore}`"
                      style="width: 100px"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="total-score-section">
            <div class="total-label">总分：</div>
            <div class="total-value">{{ calculateDetailedTotal(editingDetailedGrade) }}</div>
          </div>
        </div>
      </div>
    </t-dialog>

    <!-- 详细录入导入对话框 -->
    <ImportDialog
      v-model:visible="detailedImportDialogVisible"
      :config="detailedImportConfig"
      :callbacks="detailedImportCallbacks"
      :dialog-props="{ zIndex: 3300 }"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, reactive, nextTick } from 'vue'
import { MessagePlugin } from 'tdesign-vue-next'
import FullScreenDialog from '@/components/FullScreenDialog/index.vue'
import ImportDialog from '@/components/ImportDialog/index.vue'
import type { ImportConfig, ImportCallbacks } from '@/components/ImportDialog/types'
import * as XLSX from 'xlsx'
import {
  Button as TButton,
  Icon as TIcon,
  Tag as TTag,
  Dialog as TDialog,
  Input as TInput,
  InputNumber as TInputNumber,
  Select as TSelect,
  Option as TOption,
  Table as TTable,
  Space as TSpace,
  Checkbox as TCheckbox,
  Popup as TPopup
} from 'tdesign-vue-next'
import { type ComponentPublicInstance } from 'vue'

// 引入样式文件
import './styles/grade-management.less'

// 引入 dictUtil
import * as dictUtil from '@/utils/dictUtil'
// 引入 API
import { getAssessmentTaskScores } from '@/api/assessment/assessmentScore'

// 定义Props和Emits
interface Props {
  visible: boolean
  assessmentContent: any
  classInfo: any
  scoreStatus: number // 0: 未开始, 1: 进行中, 2: 已提交
  scoreParams?: { taskId: number; assessmentId: number } // 成绩参数
  visibleQuestionTypes: string[] // 只存编号
  allQuestionTypes: string[] // 所有编号
  allQuestionTypeNames: Record<string, string> // 编号->名称
}

const props = defineProps<Props>()

// 记录当前编辑单元格的信息
const editingCell = ref<{ studentId: number; targetNo: number } | null>(null)

// 详细录入模式的编辑状态
const editingDetailedCell = ref<{ studentId: number; repositoryAnswerId: number } | null>(null)

const inputNumberRef = ref<any>(null)
const detailedInputNumberRef = ref<any>(null)

let lastInput: HTMLInputElement | null = null;
let lastDetailedInput: HTMLInputElement | null = null;

const isInputReallyFocused = ref(false);
const isDetailedInputReallyFocused = ref(false);

let globalKeydownListener: ((e: KeyboardEvent) => void) | null = null;
let detailedGlobalKeydownListener: ((e: KeyboardEvent) => void) | null = null;

function addGlobalKeydownListener() {
  if (globalKeydownListener) return;
  globalKeydownListener = (e: KeyboardEvent) => {
    if (!editingCell.value) return;
    // Shift+方向键
    if (["ArrowUp", "ArrowDown", "ArrowLeft", "ArrowRight"].includes(e.key) && e.shiftKey) {
      e.preventDefault();
      const { studentId, targetNo } = editingCell.value;
      const next = findEdgeCellByDirection(studentId, targetNo, e.key);
      if (next) {
        handleCellClick(next.studentId, next.targetNo);
      }
      return;
    }
    // Shift+Tab
    if (e.key === 'Tab' && e.shiftKey) {
      e.preventDefault();
      const { studentId, targetNo } = editingCell.value;
      const next = findPrevCell(studentId, targetNo);
      if (next) {
        handleCellClick(next.studentId, next.targetNo);
      } else {
        isInputReallyFocused.value = false;
        exitEditMode();
      }
      return;
    }
    // 普通Tab
    if (e.key === 'Tab') {
      e.preventDefault();
      const { studentId, targetNo } = editingCell.value;
      const next = findNextCell(studentId, targetNo);
      if (next) {
        handleCellClick(next.studentId, next.targetNo);
      } else {
        isInputReallyFocused.value = false;
        exitEditMode();
      }
      return;
    }
    // 普通方向键
    if (["ArrowUp", "ArrowDown", "ArrowLeft", "ArrowRight"].includes(e.key)) {
      e.preventDefault();
      const { studentId, targetNo } = editingCell.value;
      const next = findNextCellByDirection(studentId, targetNo, e.key);
      if (next) {
        handleCellClick(next.studentId, next.targetNo);
      }
      return;
    }
    // Enter
    if (e.key === 'Enter') {
      e.preventDefault();
      isInputReallyFocused.value = false;
      exitEditMode();
      return;
    }
  };
  window.addEventListener('keydown', globalKeydownListener, true);
}

// 详细录入模式的全局键盘监听
function addDetailedGlobalKeydownListener() {
  if (detailedGlobalKeydownListener) return;
  detailedGlobalKeydownListener = (e: KeyboardEvent) => {
    if (!editingDetailedCell.value) return;
    // Shift+方向键
    if (["ArrowUp", "ArrowDown", "ArrowLeft", "ArrowRight"].includes(e.key) && e.shiftKey) {
      e.preventDefault();
      const { studentId, repositoryAnswerId } = editingDetailedCell.value;
      const next = findDetailedEdgeCellByDirection(studentId, repositoryAnswerId, e.key);
      if (next) {
        handleDetailedCellClick(next.studentId, next.repositoryAnswerId);
      }
      return;
    }
    // Shift+Tab
    if (e.key === 'Tab' && e.shiftKey) {
      e.preventDefault();
      const { studentId, repositoryAnswerId } = editingDetailedCell.value;
      const next = findDetailedPrevCell(studentId, repositoryAnswerId);
      if (next) {
        handleDetailedCellClick(next.studentId, next.repositoryAnswerId);
      } else {
        isDetailedInputReallyFocused.value = false;
        exitDetailedEditMode();
      }
      return;
    }
    // 普通Tab
    if (e.key === 'Tab') {
      e.preventDefault();
      const { studentId, repositoryAnswerId } = editingDetailedCell.value;
      const next = findDetailedNextCell(studentId, repositoryAnswerId);
      if (next) {
        handleDetailedCellClick(next.studentId, next.repositoryAnswerId);
      } else {
        isDetailedInputReallyFocused.value = false;
        exitDetailedEditMode();
      }
      return;
    }
    // 普通方向键
    if (["ArrowUp", "ArrowDown", "ArrowLeft", "ArrowRight"].includes(e.key)) {
      e.preventDefault();
      const { studentId, repositoryAnswerId } = editingDetailedCell.value;
      const next = findDetailedNextCellByDirection(studentId, repositoryAnswerId, e.key);
      if (next) {
        handleDetailedCellClick(next.studentId, next.repositoryAnswerId);
      }
      return;
    }
    // Enter
    if (e.key === 'Enter') {
      e.preventDefault();
      isDetailedInputReallyFocused.value = false;
      exitDetailedEditMode();
      return;
    }
  };
  window.addEventListener('keydown', detailedGlobalKeydownListener, true);
}

function removeGlobalKeydownListener() {
  if (globalKeydownListener) {
    window.removeEventListener('keydown', globalKeydownListener, true);
    globalKeydownListener = null;
  }
}

function removeDetailedGlobalKeydownListener() {
  if (detailedGlobalKeydownListener) {
    window.removeEventListener('keydown', detailedGlobalKeydownListener, true);
    detailedGlobalKeydownListener = null;
  }
}

function findNextCell(studentId: number, targetNo: number) {
  // 获取当前表格的可见数据和目标列
  const rows = filteredDirectGradeList.value;
  const targets = courseObjectives.value.map(obj => obj.id);

  // 找到当前行和列的索引
  const rowIdx = rows.findIndex(row => row.studentId === studentId);
  const colIdx = targets.findIndex(id => id === targetNo);

  // 先尝试同一行下一个目标
  if (colIdx < targets.length - 1) {
    return { studentId, targetNo: targets[colIdx + 1] };
  }
  // 否则跳到下一行的第一个目标
  if (rowIdx < rows.length - 1) {
    return { studentId: rows[rowIdx + 1].studentId, targetNo: targets[0] };
  }
  // 已经是最后一个单元格，循环到第一个成绩单元格
  if (rows.length > 0 && targets.length > 0) {
    return { studentId: rows[0].studentId, targetNo: targets[0] };
  }
  return null;
}

// 方向键查找下一个单元格
function findNextCellByDirection(studentId: number, targetNo: number, direction: string) {
  const rows = filteredDirectGradeList.value;
  const targets = courseObjectives.value.map(obj => obj.id);
  const rowIdx = rows.findIndex(row => row.studentId === studentId);
  const colIdx = targets.findIndex(id => id === targetNo);
  let nextRowIdx = rowIdx, nextColIdx = colIdx;
  if (direction === 'ArrowRight') {
    nextColIdx = (colIdx + 1) % targets.length;
  } else if (direction === 'ArrowLeft') {
    nextColIdx = (colIdx - 1 + targets.length) % targets.length;
  } else if (direction === 'ArrowDown') {
    nextRowIdx = (rowIdx + 1) % rows.length;
  } else if (direction === 'ArrowUp') {
    nextRowIdx = (rowIdx - 1 + rows.length) % rows.length;
  }
  // 只变动一维，另一维保持
  if (direction === 'ArrowRight' || direction === 'ArrowLeft') {
    return { studentId, targetNo: targets[nextColIdx] };
  } else if (direction === 'ArrowUp' || direction === 'ArrowDown') {
    return { studentId: rows[nextRowIdx].studentId, targetNo };
  }
  return null;
}

// Shift+方向键跳到边界
function findEdgeCellByDirection(studentId: number, targetNo: number, direction: string) {
  const rows = filteredDirectGradeList.value;
  const targets = courseObjectives.value.map(obj => obj.id);
  const rowIdx = rows.findIndex(row => row.studentId === studentId);
  const colIdx = targets.findIndex(id => id === targetNo);
  if (direction === 'ArrowRight') {
    return { studentId, targetNo: targets[targets.length - 1] };
  } else if (direction === 'ArrowLeft') {
    return { studentId, targetNo: targets[0] };
  } else if (direction === 'ArrowDown') {
    return { studentId: rows[rows.length - 1].studentId, targetNo };
  } else if (direction === 'ArrowUp') {
    return { studentId: rows[0].studentId, targetNo };
  }
  return null;
}

// Shift+Tab 反向查找
function findPrevCell(studentId: number, targetNo: number) {
  const rows = filteredDirectGradeList.value;
  const targets = courseObjectives.value.map(obj => obj.id);
  const rowIdx = rows.findIndex(row => row.studentId === studentId);
  const colIdx = targets.findIndex(id => id === targetNo);
  // 先尝试同一行前一个目标
  if (colIdx > 0) {
    return { studentId, targetNo: targets[colIdx - 1] };
  }
  // 否则跳到上一行最后一个目标
  if (rowIdx > 0) {
    return { studentId: rows[rowIdx - 1].studentId, targetNo: targets[targets.length - 1] };
  }
  // 已经是第一个单元格，循环到最后一个
  if (rows.length > 0 && targets.length > 0) {
    return { studentId: rows[rows.length - 1].studentId, targetNo: targets[targets.length - 1] };
  }
  return null;
}

// 详细录入模式的导航函数
function findDetailedNextCell(studentId: number, repositoryAnswerId: number) {
  const rows = filteredDetailedGradeList.value;
  const allRepositoryAnswerIds = getAllRepositoryAnswerIds();
  const rowIdx = rows.findIndex(row => row.studentId === studentId);
  const colIdx = allRepositoryAnswerIds.findIndex(id => id === repositoryAnswerId);
  
  // 先尝试同一行下一个得分点
  if (colIdx < allRepositoryAnswerIds.length - 1) {
    return { studentId, repositoryAnswerId: allRepositoryAnswerIds[colIdx + 1] };
  }
  // 否则跳到下一行的第一个得分点
  if (rowIdx < rows.length - 1) {
    return { studentId: rows[rowIdx + 1].studentId, repositoryAnswerId: allRepositoryAnswerIds[0] };
  }
  // 已经是最后一个单元格，循环到第一个
  if (rows.length > 0 && allRepositoryAnswerIds.length > 0) {
    return { studentId: rows[0].studentId, repositoryAnswerId: allRepositoryAnswerIds[0] };
  }
  return null;
}

function findDetailedNextCellByDirection(studentId: number, repositoryAnswerId: number, direction: string) {
  const rows = filteredDetailedGradeList.value;
  const allRepositoryAnswerIds = getAllRepositoryAnswerIds();
  const rowIdx = rows.findIndex(row => row.studentId === studentId);
  const colIdx = allRepositoryAnswerIds.findIndex(id => id === repositoryAnswerId);
  let nextRowIdx = rowIdx, nextColIdx = colIdx;
  
  if (direction === 'ArrowRight') {
    nextColIdx = (colIdx + 1) % allRepositoryAnswerIds.length;
  } else if (direction === 'ArrowLeft') {
    nextColIdx = (colIdx - 1 + allRepositoryAnswerIds.length) % allRepositoryAnswerIds.length;
  } else if (direction === 'ArrowDown') {
    nextRowIdx = (rowIdx + 1) % rows.length;
  } else if (direction === 'ArrowUp') {
    nextRowIdx = (rowIdx - 1 + rows.length) % rows.length;
  }
  
  // 只变动一维，另一维保持
  if (direction === 'ArrowRight' || direction === 'ArrowLeft') {
    return { studentId, repositoryAnswerId: allRepositoryAnswerIds[nextColIdx] };
  } else if (direction === 'ArrowUp' || direction === 'ArrowDown') {
    return { studentId: rows[nextRowIdx].studentId, repositoryAnswerId };
  }
  return null;
}

function findDetailedEdgeCellByDirection(studentId: number, repositoryAnswerId: number, direction: string) {
  const rows = filteredDetailedGradeList.value;
  const allRepositoryAnswerIds = getAllRepositoryAnswerIds();
  const rowIdx = rows.findIndex(row => row.studentId === studentId);
  const colIdx = allRepositoryAnswerIds.findIndex(id => id === repositoryAnswerId);
  
  if (direction === 'ArrowRight') {
    return { studentId, repositoryAnswerId: allRepositoryAnswerIds[allRepositoryAnswerIds.length - 1] };
  } else if (direction === 'ArrowLeft') {
    return { studentId, repositoryAnswerId: allRepositoryAnswerIds[0] };
  } else if (direction === 'ArrowDown') {
    return { studentId: rows[rows.length - 1].studentId, repositoryAnswerId };
  } else if (direction === 'ArrowUp') {
    return { studentId: rows[0].studentId, repositoryAnswerId };
  }
  return null;
}

function findDetailedPrevCell(studentId: number, repositoryAnswerId: number) {
  const rows = filteredDetailedGradeList.value;
  const allRepositoryAnswerIds = getAllRepositoryAnswerIds();
  const rowIdx = rows.findIndex(row => row.studentId === studentId);
  const colIdx = allRepositoryAnswerIds.findIndex(id => id === repositoryAnswerId);
  
  // 先尝试同一行前一个得分点
  if (colIdx > 0) {
    return { studentId, repositoryAnswerId: allRepositoryAnswerIds[colIdx - 1] };
  }
  // 否则跳到上一行最后一个得分点
  if (rowIdx > 0) {
    return { studentId: rows[rowIdx - 1].studentId, repositoryAnswerId: allRepositoryAnswerIds[allRepositoryAnswerIds.length - 1] };
  }
  // 已经是第一个单元格，循环到最后一个
  if (rows.length > 0 && allRepositoryAnswerIds.length > 0) {
    return { studentId: rows[rows.length - 1].studentId, repositoryAnswerId: allRepositoryAnswerIds[allRepositoryAnswerIds.length - 1] };
  }
  return null;
}

// 获取所有可见的 repositoryAnswerId
function getAllRepositoryAnswerIds(): number[] {
  const ids: number[] = [];
  questionStructure.value.forEach((typeGroup: any) => {
    if (!props.visibleQuestionTypes.includes(typeGroup.questionType)) return;
    (typeGroup.questions || []).forEach((q: any) => {
      (q.subItems || []).forEach((sub: any) => {
        ids.push(sub.repositoryAnswerId);
      });
    });
  });
  return ids;
}

const emit = defineEmits<{
  'update:visible': [visible: boolean]
  'save-changes': [data: StudentGradeData[]]
  'cancel-changes': []
  'submit-grades': [data: StudentGradeData[]]
  'refresh': []
  'update:visibleQuestionTypes': [types: string[]]
}>()

// ==================== 响应式数据 ====================

/** 加载状态 */
const loading = ref(false)

/** 成绩列表数据 */
const gradeList = ref<any[]>([])

/** 成绩统计信息 */
const gradeStats = ref<any>({
  averageScore: 0,
  maxScore: 0,
  minScore: 0,
  submittedCount: 0,
  pendingCount: 0
})



/** 搜索关键词 */
const searchKeyword = ref('')

/** 成绩筛选条件 */
const gradeFilter = ref('all')

// 弹窗显示状态
const directDialogVisible = ref(false)
const detailedDialogVisible = ref(false)
const directEditVisible = ref(false)
const detailedEditVisible = ref(false)
const importDialogVisible = ref(false)
const detailedImportDialogVisible = ref(false)
const columnSettingsPopupVisible = ref(false)

/** 可见的题目类型列表 */
const visibleQuestionTypes = ref<string[]>([
  '单选题',
  '多选题',
  '填空题',
  '简答题',
  '论述题',
  '编程题'
])

/** 原始成绩数据 */
const originalGradeData = ref<StudentGradeData[]>([])

/** 编辑中的成绩数据 */
const editingGradeData = ref<StudentGradeData[]>([])

/** 原始详细成绩数据 */
const originalDetailedGradeData = ref<any[]>([])

/** 编辑中的详细成绩数据 */
const editingDetailedGradeData = ref<any[]>([])


/** 数据变更状态 */
const hasChanges = ref(false)

/** 详细录入数据变更状态 */
const hasDetailedChanges = ref(false)

/** 直接录入分页配置 */
const directPagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showJumper: true,
  showSizeChanger: true,
  pageSizeOptions: [10, 20, 50, 100]
})

/** 详细录入分页配置 */
const detailedPagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showJumper: true,
  showSizeChanger: true,
  pageSizeOptions: [10, 20, 50, 100]
})

/** 正在编辑的直接录入成绩数据 */
const editingDirectGrade = ref<any>(null)

/** 正在编辑的详细录入成绩数据 */
const editingDetailedGrade = ref<any>(null)

// ==================== 数据接口定义 ====================

/** 课程目标成绩接口 */
interface CourseTargetScore {
  courseTargetNo: number
  courseTargetName: string
  score: number
  fullScore: number
  scoreRate: number
  poId: number
}

/** 学生成绩数据接口 */
interface StudentGradeData {
  studentId: number
  studentNumber: string
  studentName: string
  classId: number
  className: string
  totalScore: number
  fullScore: number
  scoreRate: number
  scoreGrade: string
  courseTargetScores: CourseTargetScore[]
  detailScores: any[]
  entryStatus: string
  entryTime: string
  lastModifyTime: string
  entryUserId: number
  entryUserName: string
}

// ==================== 静态数据 ====================

/** 课程目标数据 */
const courseObjectives = ref([
  { id: 1, name: '课程目标1', description: '掌握基本概念' },
  { id: 2, name: '课程目标2', description: '理解核心原理' },
  { id: 3, name: '课程目标3', description: '应用实践能力' }
])

/** 题目结构数据 */
const questionStructure = ref([
  {
    id: 'q1',
    questionNumber: '1',
    questionType: '单选题',
    subItems: [
      { id: 'q1_1', subNumber: '1.1', maxScore: 4 },
      { id: 'q1_2', subNumber: '1.2', maxScore: 4 },
      { id: 'q1_3', subNumber: '1.3', maxScore: 4 },
      { id: 'q1_4', subNumber: '1.4', maxScore: 4 },
      { id: 'q1_5', subNumber: '1.5', maxScore: 4 }
    ]
  },
  {
    id: 'q2',
    questionNumber: '2',
    questionType: '多选题',
    subItems: [
      { id: 'q2_1', subNumber: '2.1', maxScore: 5 },
      { id: 'q2_2', subNumber: '2.2', maxScore: 5 },
      { id: 'q2_3', subNumber: '2.3', maxScore: 5 }
    ]
  },
  {
    id: 'q3',
    questionNumber: '3',
    questionType: '填空题',
    subItems: [
      { id: 'q3_1', subNumber: '3.1', maxScore: 3 },
      { id: 'q3_2', subNumber: '3.2', maxScore: 3 },
      { id: 'q3_3', subNumber: '3.3', maxScore: 3 },
      { id: 'q3_4', subNumber: '3.4', maxScore: 3 }
    ]
  },
  {
    id: 'q4',
    questionNumber: '4',
    questionType: '简答题',
    subItems: [
      { id: 'q4_1', subNumber: '4.1', maxScore: 10 },
      { id: 'q4_2', subNumber: '4.2', maxScore: 10 }
    ]
  },
  {
    id: 'q5',
    questionNumber: '5',
    questionType: '论述题',
    subItems: [
      { id: 'q5_1', subNumber: '5.1', maxScore: 20 }
    ]
  },
  {
    id: 'q6',
    questionNumber: '6',
    questionType: '编程题',
    subItems: [
      { id: 'q6_1', subNumber: '6.1', maxScore: 15 }
    ]
  }
])

/** 直接录入成绩数据 */
const directGradeList = ref<StudentGradeData[]>([
  {
    studentId: 15,
    studentNumber: "20230001",
    studentName: "测试学生1",
    classId: 1,
    className: "RB软工数241",
    totalScore: 6,
    fullScore: 0,
    scoreRate: 0,
    scoreGrade: "不及格",
    courseTargetScores: [
      { courseTargetNo: 1, courseTargetName: "课程目标1", score: 1, fullScore: 100, scoreRate: 1.0000, poId: 387 },
      { courseTargetNo: 2, courseTargetName: "课程目标2", score: 2, fullScore: 100, scoreRate: 2.0000, poId: 388 },
      { courseTargetNo: 3, courseTargetName: "课程目标3", score: 3, fullScore: 100, scoreRate: 3.0000, poId: 391 }
    ],
    detailScores: [],
    entryStatus: "PARTIALLY_ENTERED",
    entryTime: "2025-07-21 09:38:01",
    lastModifyTime: "2025-07-21 09:38:01",
    entryUserId: 96,
    entryUserName: "用户96"
  },
  {
    studentId: 16,
    studentNumber: "20230002",
    studentName: "测试学生2",
    classId: 1,
    className: "RB软工数241",
    totalScore: 85,
    fullScore: 100,
    scoreRate: 85,
    scoreGrade: "良好",
    courseTargetScores: [
      { courseTargetNo: 1, courseTargetName: "课程目标1", score: 28, fullScore: 100, scoreRate: 28.0000, poId: 387 },
      { courseTargetNo: 2, courseTargetName: "课程目标2", score: 29, fullScore: 100, scoreRate: 29.0000, poId: 388 },
      { courseTargetNo: 3, courseTargetName: "课程目标3", score: 28, fullScore: 100, scoreRate: 28.0000, poId: 391 }
    ],
    detailScores: [],
    entryStatus: "ENTERED",
    entryTime: "2025-07-21 09:38:01",
    lastModifyTime: "2025-07-21 09:38:01",
    entryUserId: 96,
    entryUserName: "用户96"
  },
  // 新增多条数据，含0分、未录入、满分等
  {
    studentId: 17,
    studentNumber: "20230003",
    studentName: "测试学生3",
    classId: 1,
    className: "RB软工数241",
    totalScore: 0,
    fullScore: 100,
    scoreRate: 0,
    scoreGrade: "不及格",
    courseTargetScores: [
      { courseTargetNo: 1, courseTargetName: "课程目标1", score: 0, fullScore: 100, scoreRate: 0, poId: 387 },
      { courseTargetNo: 2, courseTargetName: "课程目标2", score: 0, fullScore: 100, scoreRate: 0, poId: 388 },
      { courseTargetNo: 3, courseTargetName: "课程目标3", score: 0, fullScore: 100, scoreRate: 0, poId: 391 }
    ],
    detailScores: [],
    entryStatus: "ENTERED",
    entryTime: "2025-07-21 09:38:01",
    lastModifyTime: "2025-07-21 09:38:01",
    entryUserId: 96,
    entryUserName: "用户96"
  },
  {
    studentId: 18,
    studentNumber: "20230004",
    studentName: "测试学生4",
    classId: 1,
    className: "RB软工数241",
    totalScore: 100,
    fullScore: 100,
    scoreRate: 100,
    scoreGrade: "满分",
    courseTargetScores: [
      { courseTargetNo: 1, courseTargetName: "课程目标1", score: 100, fullScore: 100, scoreRate: 1, poId: 387 },
      { courseTargetNo: 2, courseTargetName: "课程目标2", score: 100, fullScore: 100, scoreRate: 1, poId: 388 },
      { courseTargetNo: 3, courseTargetName: "课程目标3", score: 100, fullScore: 100, scoreRate: 1, poId: 391 }
    ],
    detailScores: [],
    entryStatus: "ENTERED",
    entryTime: "2025-07-21 09:38:01",
    lastModifyTime: "2025-07-21 09:38:01",
    entryUserId: 96,
    entryUserName: "用户96"
  },
  {
    studentId: 19,
    studentNumber: "20230005",
    studentName: "测试学生5",
    classId: 1,
    className: "RB软工数241",
    totalScore: 60,
    fullScore: 100,
    scoreRate: 60,
    scoreGrade: "及格",
    courseTargetScores: [
      { courseTargetNo: 1, courseTargetName: "课程目标1", score: 20, fullScore: 100, scoreRate: 0.2, poId: 387 },
      { courseTargetNo: 2, courseTargetName: "课程目标2", score: 20, fullScore: 100, scoreRate: 0.2, poId: 388 },
      { courseTargetNo: 3, courseTargetName: "课程目标3", score: 20, fullScore: 100, scoreRate: 0.2, poId: 391 }
    ],
    detailScores: [],
    entryStatus: "ENTERED",
    entryTime: "2025-07-21 09:38:01",
    lastModifyTime: "2025-07-21 09:38:01",
    entryUserId: 96,
    entryUserName: "用户96"
  },
  {
    studentId: 20,
    studentNumber: "20230006",
    studentName: "测试学生6",
    classId: 1,
    className: "RB软工数241",
    totalScore: null,
    fullScore: 100,
    scoreRate: null,
    scoreGrade: "未录入",
    courseTargetScores: [
      { courseTargetNo: 1, courseTargetName: "课程目标1", score: null, fullScore: 100, scoreRate: null, poId: 387 },
      { courseTargetNo: 2, courseTargetName: "课程目标2", score: null, fullScore: 100, scoreRate: null, poId: 388 },
      { courseTargetNo: 3, courseTargetName: "课程目标3", score: null, fullScore: 100, scoreRate: null, poId: 391 }
    ],
    detailScores: [],
    entryStatus: "NOT_ENTERED",
    entryTime: null,
    lastModifyTime: null,
    entryUserId: null,
    entryUserName: null
  }
])

/** 详细录入成绩数据 */
const detailedGradeList = ref([
  {
    studentId: '2021001',
    studentName: '张三',
    questions: {
      q1_1: 4, q1_2: 3, q1_3: 4, q1_4: 4, q1_5: 3,
      q2_1: 5, q2_2: 4, q2_3: 5,
      q3_1: 3, q3_2: 3, q3_3: 2, q3_4: 3,
      q4_1: 8, q4_2: 9,
      q5_1: 18,
      q6_1: 13
    },
    totalScore: 91,
    status: 'submitted',
    lastUpdate: '2024-01-15 10:30:00'
  },
  {
    studentId: '2021002',
    studentName: '李四',
    questions: {
      q1_1: 3, q1_2: 4, q1_3: 3, q1_4: 3, q1_5: 4,
      q2_1: 4, q2_2: 5, q2_3: 4,
      q3_1: 2, q3_2: 3, q3_3: 3, q3_4: 2,
      q4_1: 7, q4_2: 8,
      q5_1: 16,
      q6_1: 12
    },
    totalScore: 83,
    status: 'submitted',
    lastUpdate: '2024-01-15 11:20:00'
  },
  {
    studentId: '2021003',
    studentName: '王五',
    questions: {
      q1_1: 4, q1_2: 4, q1_3: 4, q1_4: 4, q1_5: 4,
      q2_1: 5, q2_2: 5, q2_3: 5,
      q3_1: 3, q3_2: 3, q3_3: 3, q3_4: 3,
      q4_1: 9, q4_2: 10,
      q5_1: 19,
      q6_1: 14
    },
    totalScore: 99,
    status: 'submitted',
    lastUpdate: '2024-01-15 14:15:00'
  },
  {
    studentId: '2021004',
    studentName: '赵六',
    questions: {
      q1_1: 3, q1_2: 3, q1_3: 2, q1_4: null, q1_5: 3,
      q2_1: 4, q2_2: 3, q2_3: null,
      q3_1: 2, q3_2: 2, q3_3: null, q3_4: 2,
      q4_1: 6, q4_2: null,
      q5_1: 15,
      q6_1: 10
    },
    totalScore: 55,
    status: 'pending',
    lastUpdate: null
  },
  {
    studentId: '2021005',
    studentName: '钱七',
    questions: {
      q1_1: null, q1_2: null, q1_3: null, q1_4: null, q1_5: null,
      q2_1: null, q2_2: null, q2_3: null,
      q3_1: null, q3_2: null, q3_3: null, q3_4: null,
      q4_1: null, q4_2: null,
      q5_1: null,
      q6_1: null
    },
    totalScore: null,
    status: 'pending',
    lastUpdate: null
  }
])

// ==================== 计算属性 ====================

/** 获取所有题目类型 */
const allQuestionTypes = computed(() => props.visibleQuestionTypes)

/** 直接录入弹窗标题 */
const directDialogTitle = computed(() => {
  return `成绩管理 - ${props.assessmentContent?.title || props.assessmentContent?.assessmentName || ''}`
})

/** 详细录入弹窗标题 */
const detailedDialogTitle = computed(() => {
  return `成绩管理 - ${props.assessmentContent?.title || props.assessmentContent?.assessmentName || ''}`
})

/** 直接录入成绩统计 */
const directGradeStats = computed(() => {
  const submittedGrades = directGradeList.value.filter(item => 
    item.entryStatus === 'ENTERED' && item.totalScore !== null
  )
  const totalScores = submittedGrades.map((item: StudentGradeData) => item.totalScore).filter((score: number | null) => score !== null) as number[]
  
  return {
    averageScore: totalScores.length > 0 ? (totalScores.reduce((a, b) => a + b, 0) / totalScores.length).toFixed(1) : '-',
    maxScore: totalScores.length > 0 ? Math.max(...totalScores).toFixed(1) : '-',
    minScore: totalScores.length > 0 ? Math.min(...totalScores).toFixed(1) : '-',
    submittedCount: submittedGrades.length,
    pendingCount: directGradeList.value.length - submittedGrades.length
  }
})

/** 详细录入成绩统计 */
const detailedGradeStats = computed(() => {
  const submitted = editingDetailedGradeData.value.filter((item: any) => item.entryStatus === 'ENTERED' && item.totalScore !== null)
  const scores = submitted.map((item: any) => item.totalScore).filter((score: number | null) => score !== null) as number[]
  
  return {
    averageScore: scores.length > 0 ? (scores.reduce((a, b) => a + b, 0) / scores.length).toFixed(1) : '-',
    maxScore: scores.length > 0 ? Math.max(...scores).toFixed(1) : '-',
    minScore: scores.length > 0 ? Math.min(...scores).toFixed(1) : '-',
    submittedCount: submitted.length,
    pendingCount: editingDetailedGradeData.value.length - submitted.length
  }
})

/** 过滤后的直接录入成绩列表 */
const filteredDirectGradeList = computed(() => {
  let filtered = [...editingGradeData.value]
  
  // 搜索过滤
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(item => 
      item.studentNumber.toLowerCase().includes(keyword) ||
      item.studentName.toLowerCase().includes(keyword)
    )
  }
  
  // 更新分页总数
  directPagination.total = filtered.length
  
  // 分页处理
  const start = (directPagination.current - 1) * directPagination.pageSize
  const end = start + directPagination.pageSize
  
  return filtered.slice(start, end)
})

/** 过滤后的详细成绩列表 */
const filteredDetailedGradeList = computed(() => {
  let filtered = [...editingDetailedGradeData.value]
  
  // 搜索过滤
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter((item: any) => 
      item.studentNumber.toLowerCase().includes(keyword) ||
      item.studentName.toLowerCase().includes(keyword)
    )
  }
  
  // 状态过滤
  if (gradeFilter.value !== 'all') {
    filtered = filtered.filter(item => item.status === gradeFilter.value)
  }
  
  // 更新分页总数
  detailedPagination.total = filtered.length
  
  // 分页处理
  const start = (detailedPagination.current - 1) * detailedPagination.pageSize
  const end = start + detailedPagination.pageSize
  
  return filtered.slice(start, end)
})

/** 直接录入表格列定义 */
const directTableColumns = computed(() => {
  const columns: any[] = [
    {
      colKey: 'studentNumber',
      title: '学号',
      width: 120,
      align: 'center' as const
    },
    {
      colKey: 'studentName',
      title: '姓名',
      width: 100,
      align: 'center' as const
    }
  ];
  // 添加课程目标列
  courseObjectives.value.forEach(objective => {
    columns.push({
      colKey: `courseTargetScores.${objective.id}`,
      title: objective.name,
      width: 120,
      align: 'center' as const
      // 不要写 cell 字段
    });
  });
  columns.push({
    colKey: 'totalScore',
    title: '总分',
    width: 100,
    align: 'center' as const
    // 不要写 cell 字段
  });
  return columns;
});

/** 详细录入表格列定义 */
const detailedTableColumns = computed(() => {
  const columns: any[] = [
    { colKey: 'studentNumber', title: '学号', width: 120, align: 'center', fixed: 'left' },
    { colKey: 'studentName', title: '姓名', width: 100, align: 'center', fixed: 'left' }
  ];
  questionStructure.value.forEach((typeGroup: any) => {
    if (!props.visibleQuestionTypes.includes(typeGroup.questionType)) return;
    const typeTitle = props.allQuestionTypeNames[typeGroup.questionType] || typeGroup.questionType;
    const typeChildren: any[] = [];
    (typeGroup.questions || []).forEach((q: any) => {
      const qChildren = (q.subItems || []).map((sub: any) => ({
        colKey: `score.${sub.repositoryAnswerId}`,
        title: `${q.questionNumber}.${sub.subNumber}`,
        width: 100,
        align: 'center'
      }));
      typeChildren.push({
        colKey: `q_${q.questionNumber}`,
        title: q.questionNumber,
        align: 'center',
        children: qChildren
      });
    });
    columns.push({
      colKey: `type_${typeGroup.questionType}`,
      title: typeTitle,
      align: 'center',
      children: typeChildren
    });
  });
  columns.push({ colKey: 'totalScore', title: '总分', width: 100, align: 'center', fixed: 'right' });
  return columns;
});

// ==================== 导入配置 ====================

/** 直接录入导入配置 */
const importConfig: ImportConfig = {
  title: '导入成绩',
  tips: '请按照模板格式填写学生成绩信息，支持批量导入成绩',
  templateFileName: '成绩导入模板.xlsx',
  templateData: [
    ['学号', '姓名', '课程目标1', '课程目标2', '课程目标3', '备注'],
    ['2021001', '张三', '85', '88', '82', ''],
    ['2021002', '李四', '78', '85', '80', ''],
    ['2021003', '王五', '90', '87', '85', '']
  ],
  acceptTypes: ['.xlsx', '.xls', '.csv'],
  maxFileSize: 10
}

/** 直接录入导入回调函数 */
const importCallbacks: ImportCallbacks = {
  onImport: async (file: File) => {
    // 模拟导入API调用
    return new Promise((resolve) => {
      setTimeout(() => {
        const random = Math.random()
        if (random > 0.2) {
          resolve({
            success: true,
            successMessage: `成功导入 ${Math.floor(random * 30 + 20)} 名学生成绩`,
            successCount: Math.floor(random * 30 + 20),
            failCount: 0
          })
        } else {
          resolve({
            success: false,
            successCount: Math.floor(random * 10),
            failCount: Math.floor(random * 5 + 3),
            errorMessages: [
              '第3行：学号不能为空',
              '第5行：课程目标1分数超出范围',
              '第8行：学号不存在'
            ]
          })
        }
      }, 2000)
    })
  },
  onSuccess: () => {
    loadGradeData()
    MessagePlugin.success('成绩导入成功')
  },
  onError: (error: Error) => {
    console.error('导入失败:', error)
    MessagePlugin.error('成绩导入失败')
  },
  onComplete: () => {
    importDialogVisible.value = false
  }
}

/** 生成详细导入模板 */
function generateDetailedImportTemplate() {
  // 表头行
  const headers = ['学号', '姓名']
  
  // 添加题目列 - 按题型分组
  const questionTypeGroups = new Map<string, any[]>()
  
  questionStructure.value.forEach(question => {
    if (!questionTypeGroups.has(question.questionType)) {
      questionTypeGroups.set(question.questionType, [])
    }
    questionTypeGroups.get(question.questionType)!.push(question)
  })
  
  // 按题型顺序添加列
  questionTypeGroups.forEach((questions, questionType) => {
    // 只为可见的题型添加列
    if (visibleQuestionTypes.value.includes(questionType)) {
      questions.forEach(question => {
        question.subItems.forEach((subItem: any) => {
          headers.push(`${question.questionNumber}.${subItem.subNumber.split('.')[1]}(${subItem.maxScore}分)`)
        })
      })
    }
  })
  
  headers.push('备注')
  
  // 示例数据行
  const exampleRow1 = ['2021001', '张三']
  const exampleRow2 = ['2021002', '李四']
  
  // 添加示例分数 - 按题型分组
  questionTypeGroups.forEach((questions, questionType) => {
    // 只为可见的题型添加示例分数
    if (visibleQuestionTypes.value.includes(questionType)) {
      questions.forEach(question => {
        question.subItems.forEach((subItem: any) => {
          // 随机生成示例分数，但不超过最大分数
          const maxScore = subItem.maxScore
          exampleRow1.push(String(Math.floor(Math.random() * maxScore * 0.8 + maxScore * 0.2)))
          exampleRow2.push(String(Math.floor(Math.random() * maxScore * 0.8 + maxScore * 0.2)))
        })
      })
    }
  })
  
  exampleRow1.push('')
  exampleRow2.push('')
  
  return [headers, exampleRow1, exampleRow2]
}

/** 详细录入导入配置 */
const detailedImportConfig: ImportConfig = {
  title: '导入详细成绩',
  tips: '请按照模板格式填写学生详细成绩信息，支持批量导入成绩',
  templateFileName: '详细成绩导入模板.xlsx',
  templateData: generateDetailedImportTemplate(),
  acceptTypes: ['.xlsx', '.xls', '.csv'],
  maxFileSize: 10
}

/** 详细录入导入回调函数 */
const detailedImportCallbacks: ImportCallbacks = {
  onImport: async (file: File) => {
    // 模拟导入API调用
    return new Promise((resolve) => {
      setTimeout(() => {
        const random = Math.random()
        if (random > 0.2) {
          resolve({
            success: true,
            successMessage: `成功导入 ${Math.floor(random * 30 + 20)} 名学生详细成绩`,
            successCount: Math.floor(random * 30 + 20),
            failCount: 0
          })
        } else {
          resolve({
            success: false,
            successCount: Math.floor(random * 10),
            failCount: Math.floor(random * 5 + 3),
            errorMessages: [
              '第3行：学号不能为空',
              '第5行：题目1.2分数超出范围',
              '第8行：学号不存在'
            ]
          })
        }
      }, 2000)
    })
  },
  onSuccess: () => {
    loadGradeData()
    MessagePlugin.success('详细成绩导入成功')
  },
  onError: (error: Error) => {
    console.error('导入失败:', error)
    MessagePlugin.error('详细成绩导入失败')
  },
  onComplete: () => {
    detailedImportDialogVisible.value = false
  }
}

// ==================== 方法 ====================

/** 加载成绩数据 */
const loadGradeData = async () => {
  loading.value = true
  try {
    // 如果有 score-params，则调用 API 获取数据
    if (props.scoreParams && props.scoreParams.assessmentId && props.scoreParams.taskId) {
      const response = await getAssessmentTaskScores(props.scoreParams.assessmentId, props.scoreParams.taskId)
      
      if (response.code === 200 && response.data) {
        // 更新成绩列表数据
        gradeList.value = response.data.gradeList || []
        gradeStats.value = response.data.gradeStats || {
          averageScore: 0,
          maxScore: 0,
          minScore: 0,
          submittedCount: 0,
          pendingCount: 0
        }
        
        // 更新题目结构数据
        questionStructure.value = response.data.questionStructure || []
        
        // 更新直接录入数据
        originalGradeData.value = JSON.parse(JSON.stringify(gradeList.value))
        editingGradeData.value = JSON.parse(JSON.stringify(gradeList.value))
        
        // 更新详细录入数据
        originalDetailedGradeData.value = JSON.parse(JSON.stringify(gradeList.value))
        editingDetailedGradeData.value = JSON.parse(JSON.stringify(gradeList.value))
        
        MessagePlugin.success('成绩数据加载成功')
      } else {
        console.error('获取成绩数据失败:', response.message)
        MessagePlugin.error('获取成绩数据失败')
      }
    } else {
      // 如果没有 score-params，使用 props 中的数据
      // 初始化直接录入原始数据和编辑数据
      originalGradeData.value = JSON.parse(JSON.stringify(directGradeList.value))
      editingGradeData.value = JSON.parse(JSON.stringify(directGradeList.value))
      
          // 初始化详细录入原始数据和编辑数据
    originalDetailedGradeData.value = JSON.parse(JSON.stringify(gradeList.value))
    editingDetailedGradeData.value = JSON.parse(JSON.stringify(gradeList.value))
      
      MessagePlugin.success('成绩数据加载成功')
    }
  } catch (error) {
    console.error('加载成绩数据失败:', error)
    MessagePlugin.error('加载成绩数据失败')
  } finally {
    loading.value = false
  }
}

/** 处理单元格点击 */
const handleCellClick = (studentId: number, targetNo: number) => {
  if (props.scoreStatus !== 1) return;
  editingCell.value = { studentId, targetNo };
  isInputReallyFocused.value = true;
  addGlobalKeydownListener();
  nextTick(() => {
    // 解绑上一个 input 的事件
    if (lastInput) {
      lastInput = null;
    }
    // 直接 focus input
    if (inputNumberRef.value && inputNumberRef.value.$el) {
      const input = inputNumberRef.value.$el.querySelector('input');
      if (input) {
        input.focus();
        lastInput = input;
      }
    }
  });
};

/** 处理详细录入单元格点击 */
const handleDetailedCellClick = (studentId: number, repositoryAnswerId: number) => {
  if (props.scoreStatus !== 1) return;
  editingDetailedCell.value = { studentId, repositoryAnswerId };
  isDetailedInputReallyFocused.value = true;
  addDetailedGlobalKeydownListener();
  nextTick(() => {
    // 解绑上一个 input 的事件
    if (lastDetailedInput) {
      lastDetailedInput = null;
    }
    // 直接 focus input
    if (detailedInputNumberRef.value && detailedInputNumberRef.value.$el) {
      const input = detailedInputNumberRef.value.$el.querySelector('input');
      if (input) {
        input.focus();
        lastDetailedInput = input;
      }
    }
  });
};

const exitEditMode = () => {
  editingCell.value = null;
  isInputReallyFocused.value = false;
  removeGlobalKeydownListener();
};

const exitDetailedEditMode = () => {
  editingDetailedCell.value = null;
  isDetailedInputReallyFocused.value = false;
  removeDetailedGlobalKeydownListener();
};

/** 弹窗级监听 - 事件委托+属性判断（升级为 composedPath 方案） */
const handleDialogMouseDown = (event: MouseEvent) => {
  if (!editingCell.value) {
    exitEditMode();
    return;
  }
  const path = event.composedPath() as HTMLElement[];
  const match = path.find(
    el =>
      el instanceof HTMLElement &&
      el.classList?.contains('inline-edit-cell') &&
      String(el.dataset.studentId) === String(editingCell.value!.studentId) &&
      String(el.dataset.targetNo) === String(editingCell.value!.targetNo)
  );
  if (match) {
    // 点击在当前编辑单元格内
    return;
  }
  exitEditMode();
};

/** 详细录入弹窗级监听 */
const handleDetailedDialogMouseDown = (event: MouseEvent) => {
  if (!editingDetailedCell.value) {
    exitDetailedEditMode();
    return;
  }
  const path = event.composedPath() as HTMLElement[];
  const match = path.find(
    el =>
      el instanceof HTMLElement &&
      el.classList?.contains('detailed-inline-edit-cell') &&
      String(el.dataset.studentId) === String(editingDetailedCell.value!.studentId) &&
      String(el.dataset.repositoryAnswerId) === String(editingDetailedCell.value!.repositoryAnswerId)
  );
  if (match) {
    // 点击在当前编辑单元格内
    return;
  }
  exitDetailedEditMode();
};

/** 处理单元格失焦 */
const handleCellBlur = () => {
  setTimeout(() => {
    // 如果还在编辑模式，且逻辑上应保持 focus
    if (editingCell.value && isInputReallyFocused.value && inputNumberRef.value && inputNumberRef.value.$el) {
      const input = inputNumberRef.value.$el.querySelector('input');
      const active = document.activeElement;
      // 只有当 active 不在 t-input-number 组件内时才 focus
      if (
        input &&
        (!active || !inputNumberRef.value.$el.contains(active))
      ) {
        input.focus();
      }
    }
    // 否则（已退出编辑模式），不做任何事
  }, 10);
};

/** 处理详细录入单元格失焦 */
const handleDetailedCellBlur = () => {
  setTimeout(() => {
    // 如果还在编辑模式，且逻辑上应保持 focus
    if (editingDetailedCell.value && isDetailedInputReallyFocused.value && detailedInputNumberRef.value && detailedInputNumberRef.value.$el) {
      const input = detailedInputNumberRef.value.$el.querySelector('input');
      const active = document.activeElement;
      // 只有当 active 不在 t-input-number 组件内时才 focus
      if (
        input &&
        (!active || !detailedInputNumberRef.value.$el.contains(active))
      ) {
        input.focus();
      }
    }
    // 否则（已退出编辑模式），不做任何事
  }, 10);
};

/** 更新课程目标成绩 */
const updateCourseTargetScore = (row: StudentGradeData, targetNo: number, value: number) => {
  const targetScore = row.courseTargetScores.find((ts: CourseTargetScore) => ts.courseTargetNo === targetNo)
  if (targetScore) {
    targetScore.score = value
    targetScore.scoreRate = value / targetScore.fullScore
    // 重新计算总分
    const totalScore = row.courseTargetScores.reduce((sum: number, ts: CourseTargetScore) => sum + ts.score, 0)
    row.totalScore = totalScore
    row.scoreRate = totalScore / row.fullScore
    hasChanges.value = true
  }
}

/** 更新详细录入成绩 */
const updateDetailedScore = (row: any, repositoryAnswerId: number, value: number) => {
  const scoreObj = row.detailScores?.find((ds: any) => ds.repositoryAnswerId === repositoryAnswerId)
  if (scoreObj) {
    scoreObj.score = value
    scoreObj.scoreRate = value / scoreObj.questionScore
    // 重新计算总分
    const totalScore = row.detailScores.reduce((sum: number, ds: any) => sum + (ds.score || 0), 0)
    row.totalScore = totalScore
    row.scoreRate = totalScore / row.fullScore
    hasDetailedChanges.value = true
  }
}

/** 获取课程目标成绩 */
const getCourseTargetScore = (row: StudentGradeData, targetNo: number) => {
  return row.courseTargetScores.find((ts: CourseTargetScore) => ts.courseTargetNo === targetNo)?.score || 0
}

/** 保存修改 */
const handleSaveChanges = () => {
  try {
    // 更新原始数据
    originalGradeData.value = JSON.parse(JSON.stringify(editingGradeData.value))
    hasChanges.value = false
    MessagePlugin.success('修改已保存')
    emit('save-changes', editingGradeData.value)
  } catch (error) {
    MessagePlugin.error('保存失败')
  }
}

/** 保存详细录入修改 */
const handleSaveDetailedChanges = () => {
  try {
    // 更新原始数据
    originalDetailedGradeData.value = JSON.parse(JSON.stringify(editingDetailedGradeData.value))
    hasDetailedChanges.value = false
    MessagePlugin.success('详细修改已保存')
    emit('save-changes', editingDetailedGradeData.value)
  } catch (error) {
    MessagePlugin.error('保存失败')
  }
}

/** 取消修改 */
const handleCancelChanges = () => {
  try {
    // 恢复编辑数据为原始数据
    editingGradeData.value = JSON.parse(JSON.stringify(originalGradeData.value))
    hasChanges.value = false
    editingCell.value = null
    MessagePlugin.success('修改已取消')
    emit('cancel-changes')
  } catch (error) {
    MessagePlugin.error('取消失败')
  }
}

/** 取消详细录入修改 */
const handleCancelDetailedChanges = () => {
  try {
    // 恢复编辑数据为原始数据
    editingDetailedGradeData.value = JSON.parse(JSON.stringify(originalDetailedGradeData.value))
    hasDetailedChanges.value = false
    editingDetailedCell.value = null
    MessagePlugin.success('详细修改已取消')
    emit('cancel-changes')
  } catch (error) {
    MessagePlugin.error('取消失败')
  }
}

/** 提交成绩 */
const handleSubmitGrades = () => {
  try {
    MessagePlugin.success('成绩提交成功')
    emit('submit-grades', editingGradeData.value)
  } catch (error) {
    MessagePlugin.error('提交失败')
  }
}

/** 提交详细成绩 */
const handleSubmitDetailedGrades = () => {
  try {
    MessagePlugin.success('详细成绩提交成功')
    emit('submit-grades', editingDetailedGradeData.value)
  } catch (error) {
    MessagePlugin.error('提交失败')
  }
}

/** 检查成绩是否有变更 */
const hasScoreChanged = (studentId: number, targetNo: number) => {
  const original = originalGradeData.value.find(og => og.studentId === studentId)
  const editing = editingGradeData.value.find(eg => eg.studentId === studentId)
  
  if (!original || !editing) return false
  
  const originalScore = original.courseTargetScores.find(ts => ts.courseTargetNo === targetNo)?.score
  const editingScore = editing.courseTargetScores.find(ts => ts.courseTargetNo === targetNo)?.score
  
  return originalScore !== editingScore
}

/** 检查详细录入成绩是否有变更 */
const hasDetailedScoreChanged = (studentId: number, repositoryAnswerId: number) => {
  const original = originalDetailedGradeData.value.find(og => og.studentId === studentId)
  const editing = editingDetailedGradeData.value.find(eg => eg.studentId === studentId)
  
  if (!original || !editing) return false
  
  const originalScore = original.detailScores?.find((ds: any) => ds.repositoryAnswerId === repositoryAnswerId)?.score
  const editingScore = editing.detailScores?.find((ds: any) => ds.repositoryAnswerId === repositoryAnswerId)?.score
  
  return originalScore !== editingScore
}

/** 检查总分是否有变更 */
const hasTotalScoreChanged = (studentId: number) => {
  const original = originalGradeData.value.find(og => og.studentId === studentId)
  const editing = editingGradeData.value.find(eg => eg.studentId === studentId)
  
  if (!original || !editing) return false
  
  return original.totalScore !== editing.totalScore
}

/** 检查详细录入总分是否有变更 */
const hasDetailedTotalScoreChanged = (studentId: number) => {
  const original = originalDetailedGradeData.value.find(og => og.studentId === studentId)
  const editing = editingDetailedGradeData.value.find(eg => eg.studentId === studentId)
  
  if (!original || !editing) return false
  
  return original.totalScore !== editing.totalScore
}

/** 获取原始成绩 */
const getOriginalScore = (studentId: number, targetNo: number) => {
  const original = originalGradeData.value.find(og => og.studentId === studentId)
  return original?.courseTargetScores.find(ts => ts.courseTargetNo === targetNo)?.score || '-'
}

/** 获取原始详细成绩 */
const getOriginalDetailedScore = (studentId: number, repositoryAnswerId: number) => {
  const original = originalDetailedGradeData.value.find(og => og.studentId === studentId)
  return original?.detailScores?.find((ds: any) => ds.repositoryAnswerId === repositoryAnswerId)?.score || '-'
}

/** 获取原始总分 */
const getOriginalTotalScore = (studentId: number) => {
  const original = originalGradeData.value.find(og => og.studentId === studentId)
  return original?.totalScore || '-'
}

/** 获取原始详细录入总分 */
const getOriginalDetailedTotalScore = (studentId: number) => {
  const original = originalDetailedGradeData.value.find(og => og.studentId === studentId)
  return original?.totalScore || '-'
}

/** 计算变更的单元格数量 */
const changedCellsCount = computed(() => {
  let count = 0
  
  editingGradeData.value.forEach(editingRow => {
    const originalRow = originalGradeData.value.find(og => og.studentId === editingRow.studentId)
    if (!originalRow) return
    
    // 检查课程目标成绩变更
    editingRow.courseTargetScores.forEach(editingScore => {
      const originalScore = originalRow.courseTargetScores.find(ts => ts.courseTargetNo === editingScore.courseTargetNo)
      if (originalScore && editingScore.score !== originalScore.score) {
        count++
      }
    })
  })
  
  return count
})

/** 计算详细录入变更的单元格数量 */
const changedDetailedCellsCount = computed(() => {
  let count = 0
  
  editingDetailedGradeData.value.forEach(editingRow => {
    const originalRow = originalDetailedGradeData.value.find(og => og.studentId === editingRow.studentId)
    if (!originalRow) return
    
    // 检查详细成绩变更
    editingRow.detailScores?.forEach((editingScore: any) => {
      const originalScore = originalRow.detailScores?.find((ds: any) => ds.repositoryAnswerId === editingScore.repositoryAnswerId)
      if (originalScore && editingScore.score !== originalScore.score) {
        count++
      }
    })
  })
  
  return count
})

/** 获取状态说明 */
const statusDescription = computed(() => {
  switch (props.scoreStatus) {
    case 0:
      return '（未开始状态：成绩录入尚未开始）'
    case 1:
      return '（进行中状态：可以编辑成绩）'
    case 2:
      return '（已提交状态：成绩已提交，不可编辑）'
    default:
      return ''
  }
})

/** 计算直接录入总分 */
const calculateDirectTotal = (gradeData: any) => {
  if (!gradeData || !gradeData.objectives) return '0'
  
  const scores = Object.values(gradeData.objectives).filter(score => score !== null) as number[]
  if (scores.length === 0) return '0'
  
  return (scores.reduce((a, b) => a + b, 0) / scores.length).toFixed(1)
}

/** 计算详细录入总分 */
const calculateDetailedTotal = (gradeData: any) => {
  if (!gradeData || !gradeData.questions) return 0
  
  const scores = Object.values(gradeData.questions).filter(score => score !== null) as number[]
  return scores.reduce((a, b) => a + b, 0)
}

/** 关闭弹窗 */
const handleClose = () => {
  directDialogVisible.value = false
  detailedDialogVisible.value = false
  emit('update:visible', false)
}

/** 刷新数据 */
const handleRefresh = () => {
  loadGradeData()
}

/** 导出直接录入成绩 */
const handleExportGrades = () => {
  MessagePlugin.info('导出直接录入成绩')
}

/** 导出详细成绩 */
const handleExportDetailedGrades = () => {
  try {
    MessagePlugin.info('正在导出详细成绩...')
    
    // 准备导出数据
    const exportData: string[][] = []
    
    // 添加表头行
    const headers = ['学号', '姓名']
    
    // 按题型分组添加列标题
    const questionTypeGroups = new Map<string, any[]>()
    
    questionStructure.value.forEach(question => {
      if (!questionTypeGroups.has(question.questionType)) {
        questionTypeGroups.set(question.questionType, [])
      }
      questionTypeGroups.get(question.questionType)!.push(question)
    })
    
    // 只导出可见题型的数据
    questionTypeGroups.forEach((questions, questionType) => {
      if (visibleQuestionTypes.value.includes(questionType)) {
        questions.forEach(question => {
          question.subItems.forEach((subItem: any) => {
            headers.push(`${question.questionNumber}.${subItem.subNumber.split('.')[1]}(${subItem.maxScore}分)`)
          })
        })
      }
    })
    
    headers.push('总分')
    headers.push('状态')
    
    exportData.push(headers)
    
    // 添加学生数据行
    detailedGradeList.value.forEach(student => {
      const row = [student.studentId, student.studentName]
      
      // 添加成绩数据
      questionTypeGroups.forEach((questions, questionType) => {
        if (visibleQuestionTypes.value.includes(questionType)) {
          questions.forEach(question => {
            question.subItems.forEach((subItem: any) => {
              const questions = student.questions as Record<string, number | null>
              const score = questions[subItem.id]
              row.push(score !== null && score !== undefined ? String(score) : '')
            })
          })
        }
      })
      
      // 添加总分和状态
      row.push(student.totalScore !== null ? String(student.totalScore) : '')
      row.push(student.status === 'submitted' ? '已录入' : '待录入')
      
      exportData.push(row)
    })
    
    // 使用xlsx库导出数据
    const ws = XLSX.utils.aoa_to_sheet(exportData)
    const wb = XLSX.utils.book_new()
    XLSX.utils.book_append_sheet(wb, ws, '详细成绩')
    
    // 生成文件名
    const fileName = `${props.assessmentContent?.title || '详细成绩'}_${props.classInfo?.className || ''}_${new Date().toISOString().slice(0, 10)}.xlsx`
    
    // 导出文件
    XLSX.writeFile(wb, fileName)
    
    MessagePlugin.success('详细成绩导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    MessagePlugin.error('详细成绩导出失败')
  }
}

/** 保存直接录入成绩 */
const handleSaveDirectGrade = async () => {
  try {
    // 重新计算总分
    if (editingDirectGrade.value) {
      const scores = Object.values(editingDirectGrade.value.objectives).filter(score => score !== null && score !== undefined) as number[]
      if (scores.length > 0) {
        editingDirectGrade.value.totalScore = parseFloat(scores.reduce((a, b) => a + b, 0).toFixed(1))
        editingDirectGrade.value.status = 'submitted'
      }
      
      // 更新原数据
      const index = directGradeList.value.findIndex(item => item.studentId === editingDirectGrade.value!.studentId)
      if (index !== -1) {
        directGradeList.value[index] = { ...editingDirectGrade.value }
      }
    }
    
    directEditVisible.value = false
    editingDirectGrade.value = null
    MessagePlugin.success('成绩保存成功')
  } catch (error) {
    MessagePlugin.error('保存成绩失败')
  }
}

/** 保存详细录入成绩 */
const handleSaveDetailedGrade = async () => {
  try {
    // 重新计算总分
    if (editingDetailedGrade.value) {
      const scores = Object.values(editingDetailedGrade.value.questions).filter(score => score !== null && score !== undefined) as number[]
      if (scores.length > 0) {
        editingDetailedGrade.value.totalScore = parseFloat(scores.reduce((a, b) => a + b, 0).toFixed(1))
        editingDetailedGrade.value.status = 'submitted'
      }
      
      // 更新原数据
      const index = detailedGradeList.value.findIndex(item => item.studentId === editingDetailedGrade.value!.studentId)
      if (index !== -1) {
        detailedGradeList.value[index] = { ...editingDetailedGrade.value }
      }
    }
    
    detailedEditVisible.value = false
    editingDetailedGrade.value = null
    MessagePlugin.success('成绩保存成功')
  } catch (error) {
    MessagePlugin.error('保存成绩失败')
  }
}

/** 关闭直接录入编辑弹窗 */
const handleCloseDirectEdit = () => {
  directEditVisible.value = false
  editingDirectGrade.value = null
}

/** 关闭详细录入编辑弹窗 */
const handleCloseDetailedEdit = () => {
  detailedEditVisible.value = false
  editingDetailedGrade.value = null
}

/** 直接录入分页处理 */
const handleDirectPageChange = (pageInfo: any) => {
  directPagination.current = pageInfo.current
  directPagination.pageSize = pageInfo.pageSize
}

/** 详细录入分页处理 */
const handleDetailedPageChange = (pageInfo: any) => {
  detailedPagination.current = pageInfo.current
  detailedPagination.pageSize = pageInfo.pageSize
}

// 旧的编辑方法已删除，使用新的单元格编辑方法

/** 开始详细录入行内编辑 */
const handleStartDetailedEdit = (row: any) => {
  // 暂时使用弹窗编辑
  editingDetailedGrade.value = { ...row }
  detailedEditVisible.value = true
}

/** 导入直接录入成绩 */
const handleImportGrades = () => {
  importDialogVisible.value = true
}

/** 导入详细录入成绩 */
const handleImportDetailedGrades = () => {
  // 更新模板数据，确保使用最新的题目结构
  detailedImportConfig.templateData = generateDetailedImportTemplate()
  detailedImportDialogVisible.value = true
}

/** 关闭列设置弹窗 */
const handleCloseColumnSettings = () => {
  columnSettingsPopupVisible.value = false
}

/** 处理题目类型可见性变化 */
const handleQuestionTypeVisibilityChange = (type: string, checked: boolean) => {
  let newArr = [...props.visibleQuestionTypes]
  if (checked && !newArr.includes(type)) {
    newArr.push(type)
  } else if (!checked) {
    newArr = newArr.filter(t => t !== type)
  }
  emit('update:visibleQuestionTypes', newArr)
}

/** 保存列设置 */
const handleSaveColumnSettings = () => {
  // 如果所有类型都被取消，至少保留一个
  if (visibleQuestionTypes.value.length === 0 && props.allQuestionTypes.length > 0) {
    visibleQuestionTypes.value = [props.allQuestionTypes[0]]
    MessagePlugin.warning('至少需要保留一种题目类型')
  }
  
  columnSettingsPopupVisible.value = false
}

// 监听props变化，控制弹窗显示
watch([
  () => props.visible,
  () => props.assessmentContent,
  () => props.scoreParams
], ([newVisible, newAssessmentContent, newScoreParams], [oldVisible, oldAssessmentContent, oldScoreParams]) => {
  if (newVisible && props.assessmentContent) {
    // 先关闭所有弹窗
    directDialogVisible.value = false
    detailedDialogVisible.value = false
    // 再根据考核内容的录入模式决定显示哪个弹窗
    if (props.assessmentContent.inputMode === 'direct' || props.assessmentContent.scoreType === 0) {
      directDialogVisible.value = true
    } else {
      detailedDialogVisible.value = true
    }
    loadGradeData()
  } else {
    directDialogVisible.value = false
    detailedDialogVisible.value = false
  }
})

watch(() => props.visibleQuestionTypes, (val) => {
  // 仅保留响应式监听，无 console.log
})

// 暴露给父组件的方法
defineExpose({
  loadGradeData
})

// ========== 列设置下拉 ==========
function handleColumnSettingsPopupClose() {
  columnSettingsPopupVisible.value = false
  handleSaveColumnSettings()
}

// 新增方法
const getCourseTargetScoreDisplay = (row: StudentGradeData, targetNo: number) => {
  const score = row.courseTargetScores.find((ts: CourseTargetScore) => ts.courseTargetNo === targetNo)?.score
  if (score === 0) return 0
  if (score === null || score === undefined) return '-'
  return score
}

// 新增方法
const getTotalScoreDisplay = (score: number | null | undefined) => {
  if (score === 0) return 0
  if (score === null || score === undefined) return '-'
  return score
}

// 新增方法
const getTotalScoreClass = (score: number | null | undefined) => {
  if (score === null || score === undefined) return ''
  if (score >= 90) return 'score-brand' // 主色
  if (score >= 80) return 'score-info'  // 信息色
  if (score >= 60) return 'score-success' // 成功色
  return 'score-error' // 错误色
}

// 生成详细录入所有分数点的 slot 配置
const detailedScoreSlots = computed(() => {
  const slots: Array<{ repositoryAnswerId: number, typeGroup: any, q: any, sub: any }> = [];
  questionStructure.value.forEach((typeGroup: any) => {
    if (!props.visibleQuestionTypes.includes(typeGroup.questionType)) return;
    (typeGroup.questions || []).forEach((q: any) => {
      (q.subItems || []).forEach((sub: any) => {
        slots.push({ repositoryAnswerId: sub.repositoryAnswerId, typeGroup, q, sub });
      });
    });
  });
  return slots;
});
</script>

<style lang="less" scoped>
// :deep(.grade-dialog-minwidth .t-dialog) {
//   min-width: 980px !important;
// }
.score-brand {
  background: var(--td-brand-color-light) !important;
  color: var(--td-brand-color) !important;
}
.score-info {
  background: var(--td-info-color-light) !important;
  color: var(--td-info-color) !important;
}
.score-success {
  background: var(--td-success-color-light) !important;
  color: var(--td-success-color) !important;
}
.score-error {
  background: var(--td-error-color-light) !important;
  color: var(--td-error-color) !important;
}
// 隐藏 t-input-number 内部 input 的 spin button
:deep(.t-input-number input[type='number']) {
  -moz-appearance: textfield;
}
:deep(.t-input-number input[type='number']::-webkit-outer-spin-button),
:deep(.t-input-number input[type='number']::-webkit-inner-spin-button) {
  -webkit-appearance: none;
  margin: 0;
}
// 优化总分变更高亮优先级
:deep(.total-score.score-changed) {
  background: var(--td-warning-color-light) !important;
  color: var(--td-warning-color) !important;
  z-index: 1;
  position: relative;
}
.status-hint {
  margin-left: 16px;
  color: var(--td-text-color-secondary);
  font-size: 14px;
  @media (max-width: 960px) {
    display: none;
  }
}
.search-toolbar {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
  .search-section {
    flex: 1 1 auto;
    min-width: 180px;
    display: flex;
    align-items: center;
  }
  .toolbar-actions {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: flex-end;
    gap: 8px;
    min-width: 240px;
  }
}
</style> 