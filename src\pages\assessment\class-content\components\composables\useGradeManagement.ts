/**
 * 成绩管理组合式函数
 * 包含成绩管理的所有业务逻辑，支持直接录入和详细录入两种模式
 */

import { ref, computed, watch, onMounted, reactive } from 'vue'
import { MessagePlugin } from 'tdesign-vue-next'
import FullScreenDialog from '@/components/FullScreenDialog/index.vue'
import ImportDialog from '@/components/ImportDialog/index.vue'
import type { ImportConfig, ImportCallbacks } from '@/components/ImportDialog/types'
import type { AssessmentContent } from '../../types'
import type { WorklistItem } from '@/api/base/classes'
import * as XLSX from 'xlsx'
import {
  Button as TButton,
  Icon as TIcon,
  Tag as TTag,
  Dialog as TDialog,
  Input as TInput,
  InputNumber as TInputNumber,
  Select as TSelect,
  Option as TOption,
  Table as TTable,
  Space as TSpace,
  Checkbox as TCheckbox
} from 'tdesign-vue-next'

// 类型定义
interface CourseObjective {
  id: string
  name: string
  description: string
}

interface SubItem {
  id: string
  subNumber: string
  maxScore: number
}

interface Question {
  id: string
  questionNumber: string
  questionType: string
  subItems: SubItem[]
}

interface DirectGrade {
  studentId: string
  studentName: string
  objectives: Record<string, number | null>
  totalScore: number | null
  status: 'submitted' | 'pending'
  lastUpdate: string | null
}

interface DetailedGrade {
  studentId: string
  studentName: string
  questions: Record<string, number | null>
  totalScore: number | null
  status: 'submitted' | 'pending'
  lastUpdate: string | null
}

interface GradeStats {
  averageScore: string
  maxScore: string
  minScore: string
  submittedCount: number
  pendingCount: number
}

interface PaginationConfig {
  current: number
  pageSize: number
  total: number
  showJumper: boolean
  showSizeChanger: boolean
  pageSizeOptions: number[]
}

/**
 * 成绩管理组合式函数
 * @returns 返回所有响应式数据、计算属性和方法
 */
export function useGradeManagement() {
  // ==================== 响应式数据 ====================
  
  /** 加载状态 */
  const loading = ref(false)
  
  /** 搜索关键词 */
  const searchKeyword = ref('')
  
  /** 成绩筛选条件 */
  const gradeFilter = ref('all')
  
  // 弹窗显示状态
  const directDialogVisible = ref(false)
  const detailedDialogVisible = ref(false)
  const directEditVisible = ref(false)
  const detailedEditVisible = ref(false)
  const importDialogVisible = ref(false)
  const detailedImportDialogVisible = ref(false)
  const columnSettingsVisible = ref(false)
  
  /** 可见的题目类型列表 */
  const visibleQuestionTypes = ref<string[]>([
    '单选题',
    '多选题',
    '填空题',
    '简答题',
    '论述题',
    '编程题'
  ])
  
  /** 行内编辑状态映射 */
  const editingRows = ref<Record<string, boolean>>({})
  
  /** 原始行数据备份，用于取消编辑 */
  const originalRowData = ref<Record<string, any>>({})
  
  /** 直接录入分页配置 */
  const directPagination = reactive<PaginationConfig>({
    current: 1,
    pageSize: 10,
    total: 0,
    showJumper: true,
    showSizeChanger: true,
    pageSizeOptions: [10, 20, 50, 100]
  })
  
  /** 详细录入分页配置 */
  const detailedPagination = reactive<PaginationConfig>({
    current: 1,
    pageSize: 10,
    total: 0,
    showJumper: true,
    showSizeChanger: true,
    pageSizeOptions: [10, 20, 50, 100]
  })
  
  /** 正在编辑的直接录入成绩数据 */
  const editingDirectGrade = ref<DirectGrade | null>(null)
  
  /** 正在编辑的详细录入成绩数据 */
  const editingDetailedGrade = ref<DetailedGrade | null>(null)
  
  // ==================== 静态数据 ====================
  
  /** 课程目标数据 */
  const courseObjectives = ref<CourseObjective[]>([
    { id: 'obj1', name: '课程目标1', description: '掌握基本概念' },
    { id: 'obj2', name: '课程目标2', description: '理解核心原理' },
    { id: 'obj3', name: '课程目标3', description: '应用实践能力' }
  ])
  
  /** 题目结构数据 */
  const questionStructure = ref<Question[]>([
    {
      id: 'q1',
      questionNumber: '1',
      questionType: '单选题',
      subItems: [
        { id: 'q1_1', subNumber: '1.1', maxScore: 4 },
        { id: 'q1_2', subNumber: '1.2', maxScore: 4 },
        { id: 'q1_3', subNumber: '1.3', maxScore: 4 },
        { id: 'q1_4', subNumber: '1.4', maxScore: 4 },
        { id: 'q1_5', subNumber: '1.5', maxScore: 4 }
      ]
    },
    {
      id: 'q2',
      questionNumber: '2',
      questionType: '多选题',
      subItems: [
        { id: 'q2_1', subNumber: '2.1', maxScore: 5 },
        { id: 'q2_2', subNumber: '2.2', maxScore: 5 },
        { id: 'q2_3', subNumber: '2.3', maxScore: 5 }
      ]
    },
    {
      id: 'q3',
      questionNumber: '3',
      questionType: '填空题',
      subItems: [
        { id: 'q3_1', subNumber: '3.1', maxScore: 3 },
        { id: 'q3_2', subNumber: '3.2', maxScore: 3 },
        { id: 'q3_3', subNumber: '3.3', maxScore: 3 },
        { id: 'q3_4', subNumber: '3.4', maxScore: 3 }
      ]
    },
    {
      id: 'q4',
      questionNumber: '4',
      questionType: '简答题',
      subItems: [
        { id: 'q4_1', subNumber: '4.1', maxScore: 10 },
        { id: 'q4_2', subNumber: '4.2', maxScore: 10 }
      ]
    },
    {
      id: 'q5',
      questionNumber: '5',
      questionType: '论述题',
      subItems: [
        { id: 'q5_1', subNumber: '5.1', maxScore: 20 }
      ]
    },
    {
      id: 'q6',
      questionNumber: '6',
      questionType: '编程题',
      subItems: [
        { id: 'q6_1', subNumber: '6.1', maxScore: 15 }
      ]
    }
  ])
  
  /** 直接录入成绩数据 */
  const directGradeList = ref<DirectGrade[]>([
    {
      studentId: '2021001',
      studentName: '张三',
      objectives: { obj1: 85, obj2: 88, obj3: 82 },
      totalScore: 255,
      status: 'submitted',
      lastUpdate: '2024-01-15 10:30:00'
    },
    {
      studentId: '2021002',
      studentName: '李四',
      objectives: { obj1: 78, obj2: 85, obj3: 80 },
      totalScore: 243,
      status: 'submitted',
      lastUpdate: '2024-01-15 11:20:00'
    },
    {
      studentId: '2021003',
      studentName: '王五',
      objectives: { obj1: 90, obj2: 87, obj3: 85 },
      totalScore: 262,
      status: 'submitted',
      lastUpdate: '2024-01-15 14:15:00'
    },
    {
      studentId: '2021004',
      studentName: '赵六',
      objectives: { obj1: 82, obj2: null, obj3: 78 },
      totalScore: 160,
      status: 'pending',
      lastUpdate: null
    },
    {
      studentId: '2021005',
      studentName: '钱七',
      objectives: { obj1: null, obj2: null, obj3: null },
      totalScore: null,
      status: 'pending',
      lastUpdate: null
    }
  ])
  
  /** 详细录入成绩数据 */
  const detailedGradeList = ref<DetailedGrade[]>([
    {
      studentId: '2021001',
      studentName: '张三',
      questions: {
        q1_1: 4, q1_2: 3, q1_3: 4, q1_4: 4, q1_5: 3,
        q2_1: 5, q2_2: 4, q2_3: 5,
        q3_1: 3, q3_2: 3, q3_3: 2, q3_4: 3,
        q4_1: 8, q4_2: 9,
        q5_1: 18,
        q6_1: 13
      },
      totalScore: 91,
      status: 'submitted',
      lastUpdate: '2024-01-15 10:30:00'
    },
    {
      studentId: '2021002',
      studentName: '李四',
      questions: {
        q1_1: 3, q1_2: 4, q1_3: 3, q1_4: 3, q1_5: 4,
        q2_1: 4, q2_2: 5, q2_3: 4,
        q3_1: 2, q3_2: 3, q3_3: 3, q3_4: 2,
        q4_1: 7, q4_2: 8,
        q5_1: 16,
        q6_1: 12
      },
      totalScore: 83,
      status: 'submitted',
      lastUpdate: '2024-01-15 11:20:00'
    },
    {
      studentId: '2021003',
      studentName: '王五',
      questions: {
        q1_1: 4, q1_2: 4, q1_3: 4, q1_4: 4, q1_5: 4,
        q2_1: 5, q2_2: 5, q2_3: 5,
        q3_1: 3, q3_2: 3, q3_3: 3, q3_4: 3,
        q4_1: 9, q4_2: 10,
        q5_1: 19,
        q6_1: 14
      },
      totalScore: 99,
      status: 'submitted',
      lastUpdate: '2024-01-15 14:15:00'
    },
    {
      studentId: '2021004',
      studentName: '赵六',
      questions: {
        q1_1: 3, q1_2: 3, q1_3: 2, q1_4: null, q1_5: 3,
        q2_1: 4, q2_2: 3, q2_3: null,
        q3_1: 2, q3_2: 2, q3_3: null, q3_4: 2,
        q4_1: 6, q4_2: null,
        q5_1: 15,
        q6_1: 10
      },
      totalScore: 55,
      status: 'pending',
      lastUpdate: null
    },
    {
      studentId: '2021005',
      studentName: '钱七',
      questions: {
        q1_1: null, q1_2: null, q1_3: null, q1_4: null, q1_5: null,
        q2_1: null, q2_2: null, q2_3: null,
        q3_1: null, q3_2: null, q3_3: null, q3_4: null,
        q4_1: null, q4_2: null,
        q5_1: null,
        q6_1: null
      },
      totalScore: null,
      status: 'pending',
      lastUpdate: null
    }
  ])
  
  // ==================== 计算属性 ====================
  
  /** 获取所有题目类型 */
  const allQuestionTypes = computed(() => {
    const types = new Set<string>()
    questionStructure.value.forEach(question => {
      types.add(question.questionType)
    })
    return Array.from(types)
  })
  
  /** 直接录入弹窗标题 */
  const directDialogTitle = computed(() => {
    return `成绩管理 - 直接录入模式`
  })
  
  /** 详细录入弹窗标题 */
  const detailedDialogTitle = computed(() => {
    return `成绩管理 - 详细录入模式`
  })
  
  /** 直接录入成绩统计 */
  const directGradeStats = computed<GradeStats>(() => {
    const submittedGrades = directGradeList.value.filter(item => 
      item.status === 'submitted' && item.totalScore !== null
    )
    const totalScores = submittedGrades.map(item => item.totalScore).filter(score => score !== null) as number[]
    
    return {
      averageScore: totalScores.length > 0 ? (totalScores.reduce((a, b) => a + b, 0) / totalScores.length).toFixed(1) : '-',
      maxScore: totalScores.length > 0 ? Math.max(...totalScores).toFixed(1) : '-',
      minScore: totalScores.length > 0 ? Math.min(...totalScores).toFixed(1) : '-',
      submittedCount: submittedGrades.length,
      pendingCount: directGradeList.value.length - submittedGrades.length
    }
  })
  
  /** 详细录入成绩统计 */
  const detailedGradeStats = computed<GradeStats>(() => {
    const submitted = detailedGradeList.value.filter(item => item.status === 'submitted')
    const scores = submitted.map(item => item.totalScore).filter(score => score !== null) as number[]
    
    return {
      averageScore: scores.length > 0 ? (scores.reduce((a, b) => a + b, 0) / scores.length).toFixed(1) : '-',
      maxScore: scores.length > 0 ? Math.max(...scores).toFixed(1) : '-',
      minScore: scores.length > 0 ? Math.min(...scores).toFixed(1) : '-',
      submittedCount: submitted.length,
      pendingCount: detailedGradeList.value.length - submitted.length
    }
  })
  
  /** 过滤后的直接录入成绩列表 */
  const filteredDirectGradeList = computed(() => {
    let filtered = [...directGradeList.value]
    
    // 搜索过滤
    if (searchKeyword.value) {
      const keyword = searchKeyword.value.toLowerCase()
      filtered = filtered.filter(item => 
        item.studentId.toLowerCase().includes(keyword) ||
        item.studentName.toLowerCase().includes(keyword)
      )
    }
    
    // 更新分页总数
    directPagination.total = filtered.length
    
    // 分页处理
    const start = (directPagination.current - 1) * directPagination.pageSize
    const end = start + directPagination.pageSize
    
    return filtered.slice(start, end)
  })
  
  /** 过滤后的详细成绩列表 */
  const filteredDetailedGradeList = computed(() => {
    let filtered = [...detailedGradeList.value]
    
    // 搜索过滤
    if (searchKeyword.value) {
      const keyword = searchKeyword.value.toLowerCase()
      filtered = filtered.filter(item => 
        item.studentId.toLowerCase().includes(keyword) ||
        item.studentName.toLowerCase().includes(keyword)
      )
    }
    
    // 状态过滤
    if (gradeFilter.value !== 'all') {
      filtered = filtered.filter(item => item.status === gradeFilter.value)
    }
    
    // 更新分页总数
    detailedPagination.total = filtered.length
    
    // 分页处理
    const start = (detailedPagination.current - 1) * detailedPagination.pageSize
    const end = start + detailedPagination.pageSize
    
    return filtered.slice(start, end)
  })
  
  /** 直接录入表格列定义 */
  const directTableColumns = computed(() => {
    const columns: any[] = [
      {
        colKey: 'studentId',
        title: '学号',
        width: 120,
        align: 'center' as const
      },
      {
        colKey: 'studentName',
        title: '姓名',
        width: 100,
        align: 'center' as const
      }
    ]
    
    // 添加课程目标列
    courseObjectives.value.forEach(objective => {
      columns.push({
        colKey: `objectives.${objective.id}`,
        title: objective.name,
        width: 120,
        align: 'center' as const
      })
    })
    
    columns.push(
      {
        colKey: 'totalScore',
        title: '总分',
        width: 100,
        align: 'center' as const
      },
      {
        colKey: 'actions',
        title: '操作',
        width: 120,
        align: 'center' as const
      }
    )
    
    return columns
  })
  
  /** 详细录入表格列定义 */
  const detailedTableColumns = computed(() => {
    const columns: any[] = [
      {
        colKey: 'studentId',
        title: '学号',
        width: 120,
        align: 'center' as const,
        fixed: 'left' as const
      },
      {
        colKey: 'studentName',
        title: '姓名',
        width: 100,
        align: 'center' as const,
        fixed: 'left' as const
      }
    ]
    
    // 按题目类型分组添加列
    const questionTypeGroups = new Map<string, Question[]>()
    
    questionStructure.value.forEach(question => {
      if (!questionTypeGroups.has(question.questionType)) {
        questionTypeGroups.set(question.questionType, [])
      }
      questionTypeGroups.get(question.questionType)!.push(question)
    })
    
    // 为每个题目类型添加列（仅添加visibleQuestionTypes中的类型）
    questionTypeGroups.forEach((questions, questionType) => {
      // 如果题型不在可见列表中，跳过
      if (!visibleQuestionTypes.value.includes(questionType)) {
        return
      }
      
      // 创建题型分组列
      const typeColumns: any[] = []
      
      // 为每个题目添加子列
      questions.forEach(question => {
        question.subItems.forEach((subItem: SubItem) => {
          typeColumns.push({
            colKey: `questions.${subItem.id}`,
            title: `${subItem.subNumber.split('.')[1]}`,
            width: 140,
            align: 'center' as const,
            cell: (h: any, { row }: { row: any }) => {
              if (editingRows.value[row.studentId]) {
                return h(TInputNumber, {
                  modelValue: row.questions[subItem.id],
                  'onUpdate:modelValue': (value: number) => {
                    row.questions[subItem.id] = value
                  },
                  min: 0,
                  max: subItem.maxScore,
                  decimalPlaces: 1,
                  placeholder: `/${subItem.maxScore}`,
                  style: { width: '120px' },
                  onBlur: () => handleSaveDetailedRowEdit(row),
                  onEnter: () => handleSaveDetailedRowEdit(row)
                })
              } else {
                return h('span', {
                  class: 'score-display',
                  style: { cursor: 'pointer', padding: '4px 8px', borderRadius: '4px' },
                  onClick: () => handleStartDetailedEdit(row)
                }, row.questions[subItem.id] || '-')
              }
            }
          })
        })
      })
      
      // 添加题型分组列
      columns.push({
        colKey: `type_${questionType}`,
        title: questionType,
        align: 'center' as const,
        children: typeColumns
      })
    })
    
    columns.push(
      {
        colKey: 'totalScore',
        title: '总分',
        width: 80,
        align: 'center' as const,
        fixed: 'right' as const
      },
      {
        colKey: 'actions',
        title: '操作',
        width: 120,
        align: 'center' as const,
        fixed: 'right' as const
      }
    )
    
    return columns
  })
  
  // ==================== 导入配置 ====================
  
  /** 直接录入导入配置 */
  const importConfig: ImportConfig = {
    title: '导入成绩',
    tips: '请按照模板格式填写学生成绩信息，支持批量导入成绩',
    templateFileName: '成绩导入模板.xlsx',
    templateData: [
      ['学号', '姓名', '课程目标1', '课程目标2', '课程目标3', '备注'],
      ['2021001', '张三', '85', '88', '82', ''],
      ['2021002', '李四', '78', '85', '80', ''],
      ['2021003', '王五', '90', '87', '85', '']
    ],
    acceptTypes: ['.xlsx', '.xls', '.csv'],
    maxFileSize: 10
  }
  
  /** 直接录入导入回调函数 */
  const importCallbacks: ImportCallbacks = {
    onImport: async (file: File) => {
      // 模拟导入API调用
      return new Promise((resolve) => {
        setTimeout(() => {
          const random = Math.random()
          if (random > 0.2) {
            resolve({
              success: true,
              successMessage: `成功导入 ${Math.floor(random * 30 + 20)} 名学生成绩`,
              successCount: Math.floor(random * 30 + 20),
              failCount: 0
            })
          } else {
            resolve({
              success: false,
              successCount: Math.floor(random * 10),
              failCount: Math.floor(random * 5 + 3),
              errorMessages: [
                '第3行：学号不能为空',
                '第5行：课程目标1分数超出范围',
                '第8行：学号不存在'
              ]
            })
          }
        }, 2000)
      })
    },
    onSuccess: () => {
      loadGradeData()
      MessagePlugin.success('成绩导入成功')
    },
    onError: (error: Error) => {
      console.error('导入失败:', error)
      MessagePlugin.error('成绩导入失败')
    },
    onComplete: () => {
      importDialogVisible.value = false
    }
  }
  
  /** 生成详细导入模板 */
  function generateDetailedImportTemplate() {
    // 表头行
    const headers = ['学号', '姓名']
    
    // 添加题目列 - 按题型分组
    const questionTypeGroups = new Map<string, Question[]>()
    
    questionStructure.value.forEach(question => {
      if (!questionTypeGroups.has(question.questionType)) {
        questionTypeGroups.set(question.questionType, [])
      }
      questionTypeGroups.get(question.questionType)!.push(question)
    })
    
    // 按题型顺序添加列
    questionTypeGroups.forEach((questions, questionType) => {
      // 只为可见的题型添加列
      if (visibleQuestionTypes.value.includes(questionType)) {
        questions.forEach(question => {
          question.subItems.forEach((subItem: SubItem) => {
            headers.push(`${question.questionNumber}.${subItem.subNumber.split('.')[1]}(${subItem.maxScore}分)`)
          })
        })
      }
    })
    
    headers.push('备注')
    
    // 示例数据行
    const exampleRow1 = ['2021001', '张三']
    const exampleRow2 = ['2021002', '李四']
    
    // 添加示例分数 - 按题型分组
    questionTypeGroups.forEach((questions, questionType) => {
      // 只为可见的题型添加示例分数
      if (visibleQuestionTypes.value.includes(questionType)) {
        questions.forEach(question => {
          question.subItems.forEach((subItem: SubItem) => {
            // 随机生成示例分数，但不超过最大分数
            const maxScore = subItem.maxScore
            exampleRow1.push(String(Math.floor(Math.random() * maxScore * 0.8 + maxScore * 0.2)))
            exampleRow2.push(String(Math.floor(Math.random() * maxScore * 0.8 + maxScore * 0.2)))
          })
        })
      }
    })
    
    exampleRow1.push('')
    exampleRow2.push('')
    
    return [headers, exampleRow1, exampleRow2]
  }
  
  /** 详细录入导入配置 */
  const detailedImportConfig: ImportConfig = {
    title: '导入详细成绩',
    tips: '请按照模板格式填写学生详细成绩信息，支持批量导入成绩',
    templateFileName: '详细成绩导入模板.xlsx',
    templateData: generateDetailedImportTemplate(),
    acceptTypes: ['.xlsx', '.xls', '.csv'],
    maxFileSize: 10
  }
  
  /** 详细录入导入回调函数 */
  const detailedImportCallbacks: ImportCallbacks = {
    onImport: async (file: File) => {
      // 模拟导入API调用
      return new Promise((resolve) => {
        setTimeout(() => {
          const random = Math.random()
          if (random > 0.2) {
            resolve({
              success: true,
              successMessage: `成功导入 ${Math.floor(random * 30 + 20)} 名学生详细成绩`,
              successCount: Math.floor(random * 30 + 20),
              failCount: 0
            })
          } else {
            resolve({
              success: false,
              successCount: Math.floor(random * 10),
              failCount: Math.floor(random * 5 + 3),
              errorMessages: [
                '第3行：学号不能为空',
                '第5行：题目1.2分数超出范围',
                '第8行：学号不存在'
              ]
            })
          }
        }, 2000)
      })
    },
    onSuccess: () => {
      loadGradeData()
      MessagePlugin.success('详细成绩导入成功')
    },
    onError: (error: Error) => {
      console.error('导入失败:', error)
      MessagePlugin.error('详细成绩导入失败')
    },
    onComplete: () => {
      detailedImportDialogVisible.value = false
    }
  }
  
  // ==================== 方法 ====================
  
  /** 加载成绩数据 */
  const loadGradeData = async () => {
    loading.value = true
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500))
      MessagePlugin.success('成绩数据加载成功')
    } catch (error) {
      MessagePlugin.error('加载成绩数据失败')
    } finally {
      loading.value = false
    }
  }
  
  /** 计算直接录入总分 */
  const calculateDirectTotal = (gradeData: any) => {
    if (!gradeData || !gradeData.objectives) return '0'
    
    const scores = Object.values(gradeData.objectives).filter(score => score !== null) as number[]
    if (scores.length === 0) return '0'
    
    return (scores.reduce((a, b) => a + b, 0) / scores.length).toFixed(1)
  }
  
  /** 计算详细录入总分 */
  const calculateDetailedTotal = (gradeData: any) => {
    if (!gradeData || !gradeData.questions) return 0
    
    const scores = Object.values(gradeData.questions).filter(score => score !== null) as number[]
    return scores.reduce((a, b) => a + b, 0)
  }
  
  /** 关闭弹窗 */
  const handleClose = () => {
    directDialogVisible.value = false
    detailedDialogVisible.value = false
  }
  
  /** 刷新数据 */
  const handleRefresh = () => {
    loadGradeData()
  }
  
  /** 导出直接录入成绩 */
  const handleExportGrades = () => {
    MessagePlugin.info('导出直接录入成绩')
  }
  
  /** 导出详细成绩 */
  const handleExportDetailedGrades = () => {
    try {
      MessagePlugin.info('正在导出详细成绩...')
      
      // 准备导出数据
      const exportData: string[][] = []
      
      // 添加表头行
      const headers = ['学号', '姓名']
      
      // 按题型分组添加列标题
      const questionTypeGroups = new Map<string, Question[]>()
      
      questionStructure.value.forEach(question => {
        if (!questionTypeGroups.has(question.questionType)) {
          questionTypeGroups.set(question.questionType, [])
        }
        questionTypeGroups.get(question.questionType)!.push(question)
      })
      
      // 只导出可见题型的数据
      questionTypeGroups.forEach((questions, questionType) => {
        if (visibleQuestionTypes.value.includes(questionType)) {
          questions.forEach(question => {
            question.subItems.forEach((subItem: SubItem) => {
              headers.push(`${question.questionNumber}.${subItem.subNumber.split('.')[1]}(${subItem.maxScore}分)`)
            })
          })
        }
      })
      
      headers.push('总分')
      headers.push('状态')
      
      exportData.push(headers)
      
      // 添加学生数据行
      detailedGradeList.value.forEach(student => {
        const row = [student.studentId, student.studentName]
        
        // 添加成绩数据
        questionTypeGroups.forEach((questions, questionType) => {
          if (visibleQuestionTypes.value.includes(questionType)) {
            questions.forEach(question => {
              question.subItems.forEach((subItem: SubItem) => {
                const questions = student.questions as Record<string, number | null>
                const score = questions[subItem.id]
                row.push(score !== null && score !== undefined ? String(score) : '')
              })
            })
          }
        })
        
        // 添加总分和状态
        row.push(student.totalScore !== null ? String(student.totalScore) : '')
        row.push(student.status === 'submitted' ? '已录入' : '待录入')
        
        exportData.push(row)
      })
      
      // 使用xlsx库导出数据
      const ws = XLSX.utils.aoa_to_sheet(exportData)
      const wb = XLSX.utils.book_new()
      XLSX.utils.book_append_sheet(wb, ws, '详细成绩')
      
      // 生成文件名
      const fileName = `详细成绩_${new Date().toISOString().slice(0, 10)}.xlsx`
      
      // 导出文件
      XLSX.writeFile(wb, fileName)
      
      MessagePlugin.success('详细成绩导出成功')
    } catch (error) {
      console.error('导出失败:', error)
      MessagePlugin.error('详细成绩导出失败')
    }
  }
  
  /** 保存直接录入成绩 */
  const handleSaveDirectGrade = async () => {
    try {
      // 重新计算总分
      if (editingDirectGrade.value) {
        const scores = Object.values(editingDirectGrade.value.objectives).filter(score => score !== null && score !== undefined) as number[]
        if (scores.length > 0) {
          editingDirectGrade.value.totalScore = parseFloat(scores.reduce((a, b) => a + b, 0).toFixed(1))
          editingDirectGrade.value.status = 'submitted'
        }
        
        // 更新原数据
        const index = directGradeList.value.findIndex(item => item.studentId === editingDirectGrade.value!.studentId)
        if (index !== -1) {
          directGradeList.value[index] = { ...editingDirectGrade.value }
        }
      }
      
      directEditVisible.value = false
      editingDirectGrade.value = null
      MessagePlugin.success('成绩保存成功')
    } catch (error) {
      MessagePlugin.error('保存成绩失败')
    }
  }
  
  /** 保存详细录入成绩 */
  const handleSaveDetailedGrade = async () => {
    try {
      // 重新计算总分
      if (editingDetailedGrade.value) {
        const scores = Object.values(editingDetailedGrade.value.questions).filter(score => score !== null && score !== undefined) as number[]
        if (scores.length > 0) {
          editingDetailedGrade.value.totalScore = parseFloat(scores.reduce((a, b) => a + b, 0).toFixed(1))
          editingDetailedGrade.value.status = 'submitted'
        }
        
        // 更新原数据
        const index = detailedGradeList.value.findIndex(item => item.studentId === editingDetailedGrade.value!.studentId)
        if (index !== -1) {
          detailedGradeList.value[index] = { ...editingDetailedGrade.value }
        }
      }
      
      detailedEditVisible.value = false
      editingDetailedGrade.value = null
      MessagePlugin.success('成绩保存成功')
    } catch (error) {
      MessagePlugin.error('保存成绩失败')
    }
  }
  
  /** 关闭直接录入编辑弹窗 */
  const handleCloseDirectEdit = () => {
    directEditVisible.value = false
    editingDirectGrade.value = null
  }
  
  /** 关闭详细录入编辑弹窗 */
  const handleCloseDetailedEdit = () => {
    detailedEditVisible.value = false
    editingDetailedGrade.value = null
  }
  
  /** 直接录入分页处理 */
  const handleDirectPageChange = (pageInfo: any) => {
    directPagination.current = pageInfo.current
    directPagination.pageSize = pageInfo.pageSize
  }
  
  /** 详细录入分页处理 */
  const handleDetailedPageChange = (pageInfo: any) => {
    detailedPagination.current = pageInfo.current
    detailedPagination.pageSize = pageInfo.pageSize
  }
  
  /** 开始行内编辑 */
  const handleStartEdit = (row: any) => {
    // 保存原始数据用于取消编辑
    originalRowData.value[row.studentId] = { ...row }
    editingRows.value[row.studentId] = true
  }
  
  /** 保存行内编辑 */
  const handleSaveRowEdit = async (row: any) => {
    try {
      // 重新计算总分 - 改为求和
      const scores = Object.values(row.objectives).filter(score => score !== null && score !== undefined) as number[]
      if (scores.length > 0) {
        row.totalScore = parseFloat(scores.reduce((a, b) => a + b, 0).toFixed(1))
        row.status = 'submitted'
      }
      
      // 模拟保存API
      await new Promise(resolve => setTimeout(resolve, 300))
      
      editingRows.value[row.studentId] = false
      delete originalRowData.value[row.studentId]
      
      MessagePlugin.success('成绩保存成功')
    } catch (error) {
      MessagePlugin.error('保存成绩失败')
    }
  }
  
  /** 取消行内编辑 */
  const handleCancelEdit = (row: any) => {
    // 恢复原始数据
    const original = originalRowData.value[row.studentId]
    if (original) {
      Object.assign(row, original)
    }
    
    editingRows.value[row.studentId] = false
    delete originalRowData.value[row.studentId]
  }
  
  /** 开始详细录入行内编辑 */
  const handleStartDetailedEdit = (row: any) => {
    // 保存原始数据用于取消编辑
    originalRowData.value[row.studentId] = { ...row }
    editingRows.value[row.studentId] = true
  }
  
  /** 保存详细录入行内编辑 */
  const handleSaveDetailedRowEdit = async (row: any) => {
    try {
      // 重新计算总分 - 改为求和
      const scores = Object.values(row.questions).filter(score => score !== null && score !== undefined) as number[]
      if (scores.length > 0) {
        row.totalScore = parseFloat(scores.reduce((a, b) => a + b, 0).toFixed(1))
        row.status = 'submitted'
      }
      
      // 模拟保存API
      await new Promise(resolve => setTimeout(resolve, 300))
      
      editingRows.value[row.studentId] = false
      delete originalRowData.value[row.studentId]
      
      MessagePlugin.success('成绩保存成功')
    } catch (error) {
      MessagePlugin.error('保存成绩失败')
    }
  }
  
  /** 取消详细录入行内编辑 */
  const handleCancelDetailedEdit = (row: any) => {
    // 恢复原始数据
    const original = originalRowData.value[row.studentId]
    if (original) {
      Object.assign(row, original)
    }
    
    editingRows.value[row.studentId] = false
    delete originalRowData.value[row.studentId]
  }
  
  /** 导入直接录入成绩 */
  const handleImportGrades = () => {
    importDialogVisible.value = true
  }
  
  /** 导入详细录入成绩 */
  const handleImportDetailedGrades = () => {
    // 更新模板数据，确保使用最新的题目结构
    detailedImportConfig.templateData = generateDetailedImportTemplate()
    detailedImportDialogVisible.value = true
  }
  
  /** 关闭列设置弹窗 */
  const handleCloseColumnSettings = () => {
    columnSettingsVisible.value = false
  }
  
  /** 处理题目类型可见性变化 */
  const handleQuestionTypeVisibilityChange = (type: string, checked: boolean) => {
    if (checked && !visibleQuestionTypes.value.includes(type)) {
      visibleQuestionTypes.value.push(type)
    } else if (!checked) {
      visibleQuestionTypes.value = visibleQuestionTypes.value.filter(t => t !== type)
    }
  }
  
  /** 保存列设置 */
  const handleSaveColumnSettings = () => {
    // 如果所有类型都被取消，至少保留一个
    if (visibleQuestionTypes.value.length === 0 && allQuestionTypes.value.length > 0) {
      visibleQuestionTypes.value = [allQuestionTypes.value[0]]
      MessagePlugin.warning('至少需要保留一种题目类型')
    }
    
    columnSettingsVisible.value = false
  }
  
  // ==================== 返回所有数据和方法 ====================
  
  return {
    // 响应式数据
    loading,
    searchKeyword,
    gradeFilter,
    directDialogVisible,
    detailedDialogVisible,
    directEditVisible,
    detailedEditVisible,
    importDialogVisible,
    detailedImportDialogVisible,
    columnSettingsVisible,
    visibleQuestionTypes,
    editingRows,
    originalRowData,
    directPagination,
    detailedPagination,
    editingDirectGrade,
    editingDetailedGrade,
    
    // 计算属性
    directDialogTitle,
    detailedDialogTitle,
    courseObjectives,
    questionStructure,
    directGradeList,
    detailedGradeList,
    directGradeStats,
    detailedGradeStats,
    filteredDirectGradeList,
    directTableColumns,
    filteredDetailedGradeList,
    detailedTableColumns,
    allQuestionTypes,
    importConfig,
    importCallbacks,
    detailedImportConfig,
    detailedImportCallbacks,
    
    // 方法
    handleClose,
    handleRefresh,
    handleExportGrades,
    handleExportDetailedGrades,
    handleSaveDirectGrade,
    handleSaveDetailedGrade,
    handleCloseDirectEdit,
    handleCloseDetailedEdit,
    handleDirectPageChange,
    handleDetailedPageChange,
    handleStartEdit,
    handleSaveRowEdit,
    handleCancelEdit,
    handleStartDetailedEdit,
    handleSaveDetailedRowEdit,
    handleCancelDetailedEdit,
    handleImportGrades,
    handleImportDetailedGrades,
    handleCloseColumnSettings,
    handleQuestionTypeVisibilityChange,
    handleSaveColumnSettings,
    calculateDirectTotal,
    calculateDetailedTotal,
    loadGradeData
  }
} 