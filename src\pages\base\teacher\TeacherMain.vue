<template>
  <div class="teacher-dashboard">
    <!-- 课程历史记录 -->
    <t-card :bordered="false" class="timeline-card" hover-shadow>
      <template #actions>
        <t-input v-model="searchKeyword" placeholder="搜索课程" style="width: 250px" clearable @enter="searchCourseTimeline">
          <template #suffix-icon>
            <t-icon name="search" @click="searchCourseTimeline" style="cursor: pointer;" />
          </template>
        </t-input>
      </template>
      <CourseTimeline ref="courseTimelineRef" />
    </t-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import {
  Button as TButton,
  Card as TCard,
  Row as TRow,
  Col as TCol,
  Space as TSpace,
  Icon as TIcon,
  Tag as TTag,
  Progress as TProgress,
  List as TList,
  ListItem as TListItem,
  Checkbox as TCheckbox,
  RadioGroup as TRadioGroup,
  RadioButton as TRadioButton,
  Input as TInput,
  MessagePlugin
} from 'tdesign-vue-next'
// import CourseTimeline from './components/CourseTimeline.vue'

const router = useRouter()
const courseTimelineRef = ref(null)
const searchKeyword = ref('')

// 统计数据
const statistics = ref({
  courseCount: 15,
  newCourses: 2,
  studentCount: 358,
  activeStudents: 225,
  pendingTasks: 8,
  avgAchievement: 85,
  achievementTrend: 3.2
})

// 本学期课程列表
const currentCourses = ref([
  {
    id: '1',
    name: '数据结构',
    status: '进行中',
    time: '周一 1-2节',
    class: '2022计科1班',
    studentCount: 45,
    reach: 78,
    image: '/image/courses/course1.jpg'
  },
  {
    id: '2',
    name: '计算机网络',
    status: '进行中',
    time: '周二 3-4节',
    class: '2022网络1班',
    studentCount: 38,
    reach: 82,
    image: '/image/courses/course2.jpg'
  },
  {
    id: '3',
    name: '操作系统',
    status: '进行中',
    time: '周四 5-6节',
    class: '2022软工1班',
    studentCount: 42,
    reach: 85,
    image: '/image/courses/course3.jpg'
  },
  {
    id: '4',
    name: '编译原理',
    status: '进行中',
    time: '周五 7-8节',
    class: '2022计科2班',
    studentCount: 40,
    reach: 76,
    image: '/image/courses/course4.jpg'
  }
])

// 待办事项相关
const todoFilter = ref('all')
const todoList = ref([
  {
    title: '操作系统实验报告批改',
    description: '需要批改45份实验报告',
    completed: false,
    priority: 'high',
    date: '2024-04-28'
  },
  {
    title: '编译原理期中考试',
    description: '准备期中考试试卷',
    completed: false,
    priority: 'medium',
    date: '2024-05-02'
  },
  {
    title: '教研室会议',
    description: '讨论新学期课程安排',
    completed: true,
    priority: 'medium',
    date: '2024-04-25'
  },
  {
    title: '计算机网络项目指导',
    description: '指导学生完成期末项目',
    completed: false,
    priority: 'low',
    date: '2024-05-10'
  }
])

const filteredTodoList = computed(() => {
  if (todoFilter.value === 'all') {
    return todoList.value
  } else if (todoFilter.value === 'pending') {
    return todoList.value.filter(item => !item.completed)
  } else {
    return todoList.value.filter(item => item.completed)
  }
})

const newTodo = ref({
  title: '',
  description: '新的待办事项',
  completed: false,
  priority: 'medium',
  date: new Date().toISOString().split('T')[0]
})

// 进度条颜色
const getProgressColor = (value: number) => {
  if (value >= 85) return '#00a870' // 优秀
  if (value >= 70) return '#0052d9' // 良好
  if (value >= 60) return '#ff9d00' // 及格
  return '#e34d59' // 不及格
}

// 添加新待办事项
const addNewTodo = () => {
  if (!newTodo.value.title) return

  todoList.value.unshift({
    ...newTodo.value
  })

  newTodo.value.title = ''
  MessagePlugin.success('添加待办事项成功')
}

// 切换待办事项状态
const toggleTodoStatus = (index: number) => {
  const item = todoList.value[index]
  if (item.completed) {
    MessagePlugin.success(`已完成: ${item.title}`)
  }
}

// 点击课程卡片，进入新的课程详情页
const goToCourse = (courseId: string) => {
  router.push(`/teachers/course/${courseId}/homework`);
}

const goToGrading = () => {
  // 批阅列表需要id参数，先跳转到作业任务列表页
  router.push('/teachersHomeWork/task')
}

const showAllCourses = () => {
  // 实现查看全部课程的逻辑
  router.push('/teachers/courses')
}

// 搜索课程历程
const searchCourseTimeline = () => {
  if (courseTimelineRef.value) {
    courseTimelineRef.value.searchCourse(searchKeyword.value)
  }
}

onMounted(() => {
  // 从API获取数据
  // fetchTeacherStatistics()
  // fetchCurrentCourses()
  // fetchTodoList()
})
</script>

<style scoped lang="less">
.teacher-dashboard {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: calc(100vh - 64px);
}

.welcome-card {
  margin-bottom: 24px;

  .welcome-content {
    text-align: center;
    padding: 40px 0;

    h1 {
      font-size: 24px;
      margin-bottom: 16px;
      color: #0052d9;
    }

    p {
      font-size: 16px;
      color: #666;
    }
  }
}

.section-title {
  font-size: 20px;
  font-weight: 500;
  margin: 32px 0 16px 0;
  color: #333;
}

.course-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

.course-card {
  border-radius: 8px;
  overflow: hidden;
  background: white;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
  cursor: pointer;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
  }

  .course-header {
    height: 140px;
    background-size: cover;
    background-position: center;
    position: relative;

    .course-status {
      position: absolute;
      top: 12px;
      right: 12px;
      background: rgba(0, 0, 0, 0.6);
      color: white;
      padding: 2px 12px;
      border-radius: 12px;
      font-size: 13px;
    }
  }

  .course-content {
    padding: 16px;

    h3 {
      margin: 0 0 12px 0;
      font-size: 18px;
    }

    .course-info {
      display: flex;
      gap: 8px;
      margin-bottom: 16px;
    }

    .course-stats {
      display: flex;
      justify-content: space-between;
      margin-bottom: 8px;

      .stats-item {
        display: flex;
        align-items: center;
        gap: 6px;
        color: #666;
      }
    }
  }
}

.timeline-card {
  margin-bottom: 24px;
}
</style>
