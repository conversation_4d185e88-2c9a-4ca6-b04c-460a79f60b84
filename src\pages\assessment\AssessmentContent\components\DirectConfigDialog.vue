<template>
  <t-dialog
    v-model:visible="data.visible"
    :header="configTitle"
    placement="center"
    width="90vw"
    :confirm-btn="{ content: '保存配置', loading: data.submitting, disabled: !isConfigValid }"
    @confirm="handleSave"
    @cancel="handleCancel"
    @close="handleClose"
  >
    <div class="direct-config-container">
      <div class="config-header">
        <div class="description-card">
          <div class="description-content">
            <p class="config-description">{{ data.content?.description }}</p>
          </div>
        </div>
      </div>

      <div class="mode-description">
        <t-icon name="info-circle" />
        <span>直接录入模式：按课程目标设置分值，系统自动计算占比</span>
      </div>
      
      <div class="course-objectives-table">
        <t-table
          :data="directModeData"
          :columns="directModeConfigColumns"
          :foot-data="directModeFootData"
          :row-class-name="getDirectModeRowClassName"
          row-key="objectiveId"
          bordered
        >
          <template #objectiveName="{ row }">
            <div class="objective-name-cell">
              {{ row.objectiveName }}
            </div>
          </template>
          
          <template #score="{ row, rowIndex }">
            <div class="score-cell">
              <t-input-number
                v-model="directModeData[rowIndex].score"
                :min="0"
                :max="1000"
                :step="1"
                placeholder="分值"
                @change="calculateDirectModePercentage"
              />
            </div>
          </template>
          
          <template #percentage="{ row }">
            <div class="percentage-cell">
              <span 
                :class="{ 
                  'percentage-value': row.percentage > 0,
                  'invalid-percentage': totalDirectScore > 0 && row.percentage === 0 
                }"
              >
                {{ row.percentage }}%
              </span>
            </div>
          </template>

          <template #description="{ row }">
            <div class="description-cell">
                {{ row.description }}
            </div>
          </template>
        </t-table>
      </div>

      <!-- 验证提示 -->
      <div v-if="totalDirectPercentage !== 100" class="validation-message">
        <t-icon name="error-circle" />
        <span>占比总和必须为100%才能保存配置</span>
      </div>
    </div>
  </t-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { MessagePlugin } from 'tdesign-vue-next'
import type { PrimaryTableCol } from 'tdesign-vue-next'
import { TableProps } from 'tdesign-vue-next'
import { storage } from '@/utils/storage'

// Props 定义
interface DirectConfigData {
  visible: boolean
  content: {
    id: string
    name: string
    description: string
    inputMode: string
  } | null
  courseObjectives: Array<{
    id: string
    objectiveCode: string
    objectiveName: string
    description: string
    weight: number
  }>
  submitting?: boolean
}

const props = defineProps<{ data: DirectConfigData }>()
const data = props.data

// 响应式数据
const directModeData = ref<any[]>([])
const isLoadingCache = ref(false)

// 配置弹窗表格列配置
const directModeConfigColumns: TableProps['columns'] = [
  { 
    colKey: 'objectiveName', 
    title: '课程目标', 
    minWidth: 200, 
    align: 'left'
  },
  { 
    colKey: 'score', 
    title: '分值', 
    width: 180, 
    align: 'center'
  },
  { 
    colKey: 'percentage', 
    title: '占比(%)', 
    width: 100, 
    align: 'center'
  },
  { 
    colKey: 'description', 
    title: '说明', 
    minWidth: 300, 
    align: 'left'
  }
]

// 计算属性
const configTitle = computed(() => {
  if (!data.content) return '直接录入配置'
  return `直接录入配置 - ${data.content.name}`
})

const totalDirectScore = computed(() => {
  return directModeData.value.reduce((total, item) => total + (item.score || 0), 0)
})

const totalDirectPercentage = computed(() => {
  const total = totalDirectScore.value
  if (total === 0) return 0
  return Math.round(directModeData.value.reduce((sum, item) => sum + (item.percentage || 0), 0))
})

const isConfigValid = computed(() => {
  return totalDirectPercentage.value === 100
})

// 直接录入模式表尾数据
const directModeFootData = computed(() => {
  return [{
    objectiveName: '合计',
    score: `${totalDirectScore.value}分`,
    percentage: `${totalDirectPercentage.value}%`,
    description: totalDirectPercentage.value === 100 ? '配置有效' : '占比总和必须为100%'
  }]
})

// 方法定义
const calculateDirectModePercentage = () => {
  const totalScore = directModeData.value.reduce((sum, item) => sum + (item.score || 0), 0)
  
  if (totalScore > 0) {
    directModeData.value.forEach(item => {
      item.percentage = Math.round((item.score / totalScore) * 100)
    })
  } else {
    directModeData.value.forEach(item => {
      item.percentage = 0
    })
  }
  
  // 自动保存到缓存
  saveToCache()
}

const getDirectModeRowClassName = ({ row }: { row: any }) => {
  return row.objectiveId === 'total' ? 'total-row' : ''
}

// 缓存相关函数
const getCacheKey = (contentId: string) => {
  return `assessment_content_direct_config_${contentId}`
}

const saveToCache = () => {
  if (data.content?.id && !isLoadingCache.value) {
    const cacheKey = getCacheKey(data.content.id)
    const cacheData = {
      inputMode: 'direct',
      directModeData: directModeData.value,
      timestamp: Date.now()
    }
    storage.set(cacheKey, cacheData)
  }
}

const loadFromCache = () => {
  if (data.content?.id) {
    const cacheKey = getCacheKey(data.content.id)
    const cacheData = storage.get(cacheKey)
    
    if (cacheData && cacheData.directModeData) {
      isLoadingCache.value = true
      directModeData.value = cacheData.directModeData
      nextTick(() => {
        calculateDirectModePercentage()
        isLoadingCache.value = false
      })
      return true
    }
  }
  return false
}

// 初始化数据
const initializeData = () => {
  if (data.courseObjectives && data.courseObjectives.length > 0) {
    // 初始化课程目标配置数据
    directModeData.value = data.courseObjectives.map(objective => ({
      objectiveId: objective.id,
      objectiveName: objective.objectiveName,
      score: 0,
      percentage: 0,
      description: objective.description
    }))
  }
}

// 事件处理
const handleSave = async () => {
  if (!isConfigValid.value) {
    MessagePlugin.warning('配置无效，占比总和必须为100%')
    return
  }
  
  try {
    // 这里可以调用API保存配置
    await new Promise(resolve => setTimeout(resolve, 1000))
    MessagePlugin.success('直接录入配置保存成功')
    data.visible = false
  } catch (error) {
    console.error('配置保存失败:', error)
    MessagePlugin.error('配置保存失败，请重试')
  }
}

const handleCancel = () => {
  data.visible = false
}

const handleClose = () => {
  data.visible = false
}

// 监听器
watch(() => data.visible, (newVal) => {
  if (newVal) {
    // 尝试从缓存加载数据
    const hasCache = loadFromCache()
    
    // 如果没有缓存数据，初始化默认数据
    if (!hasCache && directModeData.value.length === 0) {
      initializeData()
    }
    
    // 确保数据被正确计算
    nextTick(() => {
      calculateDirectModePercentage()
    })
  }
})

// 监听数据变化，自动保存到缓存
watch(directModeData, () => {
  if (!isLoadingCache.value) {
    saveToCache()
    nextTick(() => {
      calculateDirectModePercentage()
    })
  }
}, { deep: true })
</script>

<style lang="less" scoped>
// 直接录入配置弹窗样式
.direct-config-container {
  padding: 16px 0;
  
  .config-header {
    margin-bottom: 24px;

    .description-card {
      display: flex;
      align-items: flex-start;
      gap: 12px;
      padding: 16px;
      background: linear-gradient(135deg, var(--td-brand-color-1) 0%, var(--td-brand-color-2) 100%);
      border: 1px solid var(--td-brand-color-3);
      border-radius: 8px;
      border-left: 4px solid var(--td-brand-color);

      .description-content {
        flex: 1;

        .config-description {
          margin: 0;
          color: var(--td-text-color-primary);
          font-size: 14px;
          line-height: 1.6;
        }
      }
    }
  }

  .mode-description {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 16px;
    padding: 12px;
    background: var(--td-bg-color-page);
    border-radius: 6px;
    font-size: 14px;
    color: var(--td-text-color-secondary);

    .t-icon {
      color: var(--td-brand-color);
    }
  }

  // 表格表头样式 - 所有表头居中对齐
  :deep(.t-table__header) {
    th {
      text-align: center !important;
    }
  }

  // 表格表尾样式
  :deep(.t-table__footer) {
    .direct-mode-footer {
      background: var(--td-bg-color-page) !important;
      border-top: 2px solid var(--td-brand-color) !important;

      td {
        background: var(--td-bg-color-page) !important;
        font-weight: 600 !important;
        text-align: center !important;
        color: var(--td-text-color-primary) !important;
        padding: 12px 16px !important;

        // 占比列的状态颜色
        &:nth-child(3) {
          &.percentage-valid {
            color: var(--td-success-color) !important;
            background: var(--td-success-color-1) !important;
          }
          
          &.percentage-invalid {
            color: var(--td-error-color) !important;
            background: var(--td-error-color-1) !important;
          }
        }

        // 说明列的状态颜色
        &:nth-child(4) {
          &.description-valid {
            color: var(--td-success-color) !important;
            background: var(--td-success-color-1) !important;
          }
          
          &.description-invalid {
            color: var(--td-error-color) !important;
            background: var(--td-error-color-1) !important;
          }
        }
      }
    }
  }

  .validation-message {
    display: flex;
    align-items: center;
    gap: 6px;
    margin-top: 12px;
    padding: 12px 16px;
    color: var(--td-error-color);
    font-size: 13px;
    background: var(--td-error-color-1);
    border: 1px solid var(--td-error-color-3);
    border-radius: 6px;

    .t-icon {
      font-size: 14px;
    }
  }

  .invalid-percentage {
    color: var(--td-warning-color);
  }

  // 课程目标表格样式
  .course-objectives-table {
    margin-bottom: 24px;
    
    :deep(.t-table) {
      .t-table__body {
        tr {
          &:hover {
            background-color: var(--td-bg-color-container-hover);
          }
          
          &.total-row {
            background-color: var(--td-bg-color-page);
            font-weight: 600;
            
            td {
              border-top: 2px solid var(--td-brand-color);
              color: var(--td-text-color-primary);
            }
          }
        }
        
        td {
          padding: 12px 16px;
          vertical-align: middle;
          
          &.objective-name-cell {
            text-align: left;
            font-weight: 500;
            color: var(--td-text-color-primary);
          }
          
          &.score-cell {
            text-align: center;
            
            .t-input-number {
              width: 120px;
            }
          }
          
          &.percentage-cell {
            text-align: center;
            font-weight: 500;
            
            .percentage-value {
              color: var(--td-brand-color);
              font-weight: 600;
            }
            
            .invalid-percentage {
              color: var(--td-error-color);
            }
          }
          
          &.description-cell {
            text-align: left;
            
            .t-textarea {
              width: 100%;
            }
          }
        }
      }
    }
  }

  // 添加课程目标按钮样式
  .add-objective-section {
    margin-bottom: 24px;
    
    .add-objective-button {
      width: 100%;
      height: 48px;
      border: 2px dashed var(--td-border-level-2-color);
      border-radius: 8px;
      background: var(--td-bg-color-page);
      color: var(--td-text-color-secondary);
      font-size: 14px;
      transition: all 0.3s ease;
      
      &:hover {
        border-color: var(--td-brand-color);
        color: var(--td-brand-color);
        background: var(--td-brand-color-1);
      }
      
      .t-icon {
        margin-right: 8px;
        font-size: 16px;
      }
    }
  }

  // 弹窗底部按钮样式
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding-top: 24px;
    border-top: 1px solid var(--td-border-level-1-color);
    
    .t-button {
      min-width: 80px;
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .direct-config-container {
      padding: 12px 0;
      
      .config-header {
        margin-bottom: 16px;
        
        .description-card {
          padding: 12px;
        }
      }
      
      .course-objectives-table {
        :deep(.t-table) {
          .t-table__body {
            td {
              padding: 8px 12px;
              
              &.score-cell {
                .t-input-number {
                  width: 100px;
                }
              }
            }
          }
        }
      }
    }
  }
}

// 弹窗内容样式
:deep(.t-dialog__body) {
  padding: 24px;
  max-height: 70vh;
  overflow-y: auto;
}

:deep(.t-dialog__header) {
  padding: 20px 24px 0;
  border-bottom: 1px solid var(--td-border-level-1-color);
  margin-bottom: 0;
}

:deep(.t-dialog__footer) {
  padding: 0 24px 20px;
  border-top: 1px solid var(--td-border-level-1-color);
  margin-top: 0;
}
</style> 