import Mock from 'mockjs';
import { MockMethod } from 'vite-plugin-mock';
import { collegeMockData } from '../src/api/mock/systemMockData';

// 导出assessment mock数据，以便其他地方使用
export * from './assessment';

export default [
  {
    url: '/api/get-purchase-list',
    method: 'get',
    response: () => ({
      code: 0,
      data: {
        ...Mock.mock({
          'list|1-100': [
            {
              index: /S20201228115950[0-9][0-9][0-9]/,
              pdName: 'Macbook',
              pdNum: 'p_tmp_60a637cd0d',
              'purchaseNum|1-100': 100,
              adminName: '财务部111',
              updateTime: '2020-05-20@date("HH:mm:ss")',
              pdType: '电子产品',
            },
            {
              index: /S20201228115950[0-9][0-9][0-9]/,
              pdName: 'Macbook',
              pdNum: 'p_tmp_60a637cd0d',
              'purchaseNum|1-100': 100,
              adminName: '财务部',
              updateTime: '2020-05-20@date("HH:mm:ss")',
            },
          ],
        }),
      },
    }),
  },
  {
    url: '/api/get-list',
    method: 'get',
    response: () => ({
      code: 0,
      data: {
        ...Mock.mock({
          'list|1-100': [
            {
              'index|+1': 1,
              'status|1': '@natural(0, 4)',
              no: 'BH00@natural(01, 100)',
              name: '@city()办公用品采购项目',
              'paymentType|1': '@natural(0, 1)',
              'contractType|1': '@natural(0, 2)',
              updateTime: '2020-05-30 @date("HH:mm:ss")',
              amount: '@natural(10, 500),000,000',
              adminName: '@cname()',
            },
          ],
        }),
      },
    }),
  },
  {
    url: '/api/detail-basic',
    method: 'get',
    response: () => ({
      code: 0,
      data: {
        ...Mock.mock({
          name: 'td_20023747',
          loginType: 'Web',
          currentRole: 'Admin',
          rightsList: '通用权限',
          userStatus: '启用',
          language: '简体中文',
          timeZone: '(GMT+08:00)中国时区—北京（Asia/Beijing）',
        }),
      },
    }),
  },
  {
    url: '/api/get-card-list',
    method: 'get',
    response: () => ({
      code: 0,
      data: {
        ...Mock.mock({
          'list|48-50': [
            {
              'index|+1': 1,
              isSetup: '@boolean',
              'type|1': '@natural(1, 5)',
              'banner|1': [
                'https://tdesign.gtimg.com/starter/cloud-db.jpg',
                'https://tdesign.gtimg.com/starter/cloud-server.jpg',
                'https://tdesign.gtimg.com/starter/ssl.jpg',
                'https://tdesign.gtimg.com/starter/t-sec.jpg',
                'https://tdesign.gtimg.com/starter/face-recognition.jpg',
              ],
              'name|1': ['人脸识别', 'SSL证书', 'CVM', '云数据库', 'T-Sec 云防火墙'],
              'description|1': [
                '基于腾讯优图强大的面部分析技术，提供包括人脸检测与分析、五官定位、人脸搜索、人脸比对、人脸',
                '云硬盘为您提供用于CVM的持久性数据块级存储服务。云硬盘中的数据自动地可用区内以多副本冗',
                'SSL证书又叫服务器证书，腾讯云为您提供证书的一站式服务，包括免费、付费证书的申请、管理及部',
                '腾讯安全云防火墙产品，是腾讯云安全团队结合云原生的优势，自主研发的SaaS化防火墙产品，无需客无需客无需客无需客无需客无需客无需客',
                '云数据库MySQL为用户提供安全可靠，性能卓越、易于维护的企业级云数据库服务。',
              ],
            },
          ],
        }),
      },
    }),
  },
  {
    url: '/api/get-project-list',
    method: 'get',
    response: () => ({
      code: 0,
      data: {
        ...Mock.mock({
          'list|1-50': [
            {
              'index|+1': 1,
              adminPhone: '+86 13587609955',
              updateTime: '2020-05-30 @date("HH:mm:ss")',
              'adminName|1': ['顾娟	', '常刚', '郑洋'],
              'name|1': [
                '沧州市办公用品采购项目',
                '红河哈尼族彝族自治州办公用品采购项目	',
                '铜川市办公用品采购项目',
                '陇南市办公用品采购项目	',
                '六安市办公用品采购项目	 ',
              ],
            },
          ],
        }),
      },
    }),
  },
  {
    url: '/api/post',
    method: 'post',
    timeout: 2000,
    response: {
      code: 0,
      data: {
        name: 'vben',
      },
    },
  },
  {
    url: '/api/get-menu-list-i18n',
    method: 'get',
    timeout: 2000,
    response: {
      code: 0,
      data: {
        ...Mock.mock({
          list: [
            {
              path: '/list',
              name: 'list',
              component: 'LAYOUT',
              redirect: '/list/base',
              meta: {
                title: {
                  zh_CN: '列表页',
                  en_US: 'List',
                },
                icon: 'view-list',
              },
              children: [
                {
                  path: 'base',
                  name: 'ListBase',
                  component: '/list/base/index',
                  meta: {
                    title: {
                      zh_CN: '基础列表页',
                      en_US: 'Base List',
                    },
                  },
                },
                {
                  path: 'card',
                  name: 'ListCard',
                  component: '/list/card/index',
                  meta: {
                    title: {
                      zh_CN: '卡片列表页',
                      en_US: 'Card List',
                    },
                  },
                },
                {
                  path: 'filter',
                  name: 'ListFilter',
                  component: '/list/filter/index',
                  meta: {
                    title: {
                      zh_CN: '筛选列表页',
                      en_US: 'Filter List',
                    },
                  },
                },
                {
                  path: 'tree',
                  name: 'ListTree',
                  component: '/list/tree/index',
                  meta: {
                    title: {
                      zh_CN: '树状筛选列表页',
                      en_US: 'Tree List',
                    },
                  },
                },
              ],
            },
            {
              path: '/form',
              name: 'form',
              component: 'LAYOUT',
              redirect: '/form/base',
              meta: {
                title: {
                  zh_CN: '表单页',
                  en_US: 'Form',
                },
                icon: 'edit-1',
              },
              children: [
                {
                  path: 'base',
                  name: 'FormBase',
                  component: '/form/base/index',
                  meta: {
                    title: {
                      zh_CN: '基础表单页',
                      en_US: 'Base Form',
                    },
                  },
                },
                {
                  path: 'step',
                  name: 'FormStep',
                  component: '/form/step/index',
                  meta: {
                    title: {
                      zh_CN: '分步表单页',
                      en_US: 'Step Form',
                    },
                  },
                },
              ],
            },
            {
              path: '/detail',
              name: 'detail',
              component: 'LAYOUT',
              redirect: '/detail/base',
              meta: {
                title: {
                  zh_CN: '详情页',
                  en_US: 'Detail',
                },
                icon: 'layers',
              },
              children: [
                {
                  path: 'base',
                  name: 'DetailBase',
                  component: '/detail/base/index',
                  meta: {
                    title: {
                      zh_CN: '基础详情页',
                      en_US: 'Base Detail',
                    },
                  },
                },
                {
                  path: 'advanced',
                  name: 'DetailAdvanced',
                  component: '/detail/advanced/index',
                  meta: {
                    title: {
                      zh_CN: '多卡片详情页',
                      en_US: 'Card Detail',
                    },
                  },
                },
                {
                  path: 'deploy',
                  name: 'DetailDeploy',
                  component: '/detail/deploy/index',
                  meta: {
                    title: {
                      zh_CN: '数据详情页',
                      en_US: 'Data Detail',
                    },
                  },
                },
                {
                  path: 'secondary',
                  name: 'DetailSecondary',
                  component: '/detail/secondary/index',
                  meta: {
                    title: {
                      zh_CN: '二级详情页',
                      en_US: 'Secondary Detail',
                    },
                  },
                },
              ],
            },
            {
              path: '/frame',
              name: 'Frame',
              component: 'Layout',
              redirect: '/frame/doc',
              meta: {
                icon: 'internet',
                title: {
                  zh_CN: '外部页面',
                  en_US: 'External',
                },
              },
              children: [
                {
                  path: 'doc',
                  name: 'Doc',
                  component: 'IFrame',
                  meta: {
                    frameSrc: 'https://tdesign.tencent.com/starter/docs/vue-next/get-started',
                    title: {
                      zh_CN: '使用文档（内嵌）',
                      en_US: 'Documentation(IFrame)',
                    },
                  },
                },
                {
                  path: 'TDesign',
                  name: 'TDesign',
                  component: 'IFrame',
                  meta: {
                    frameSrc: 'https://tdesign.tencent.com/vue-next/getting-started',
                    title: {
                      zh_CN: 'TDesign 文档（内嵌）',
                      en_US: 'TDesign (IFrame)',
                    },
                  },
                },
                {
                  path: 'TDesign2',
                  name: 'TDesign2',
                  component: 'IFrame',
                  meta: {
                    frameSrc: 'https://tdesign.tencent.com/vue-next/getting-started',
                    frameBlank: true,
                    title: {
                      zh_CN: 'TDesign 文档（外链',
                      en_US: 'TDesign Doc(Link)',
                    },
                  },
                },
              ],
            },
          ],
        }),
      },
    },
  },
  {
    url: '/api/assignment-submissions',
    method: 'get',
    response: (req: { query: { assignmentId?: string; type?: string } }) => {
      const { assignmentId, type } = req.query;

      // 使用静态学生数据
      const allStudents = Array.from({ length: 30 }, (_, i) => ({
        id: `submit-${i + 1}`,
        studentId: `2022${String(200 + i).padStart(3, '0')}`,
        studentName: `学生${i + 1}`,
        className: 'R8软工1班' + (i % 4 + 1) + '班',
        avatar: `/image/avatar/student-${(i % 5) + 1}.jpg`,
        submissionTime: Mock.mock('@date("yyyy-MM-dd HH:mm:ss")'),
        status: i % 2 === 0 ? 'graded' : 'submitted', // 偶数学生已批阅，奇数学生未批阅
        score: i % 2 === 0 ? Mock.Random.integer(60, 95) : null,
        assignmentId,
        assignmentType: type,
      }));

      // 只返回未批阅（status = 'submitted'）的学生数据
      const submissions = allStudents.filter(student => student.status === 'submitted');

      // 计算统计信息 - 基于所有学生的数据
      const total = allStudents.length;
      const submitted = allStudents.filter(s => s.status !== 'not_submitted').length;
      const graded = allStudents.filter(s => s.status === 'graded').length;
      const scores = allStudents.filter(s => s.status === 'graded').map(s => s.score);
      const avgScore = scores.length > 0 ? scores.reduce((a, b) => a + b, 0) / scores.length : 0;
      const passRate = scores.length > 0 ? (scores.filter(s => s >= 60).length / scores.length) * 100 : 0;

      return {
        code: 0,
        data: {
          submissions,
          stats: {
            total,
            submitted,
            graded,
            avgScore,
            passRate: Math.round(passRate),
          }
        }
      };
    }
  },
  {
    url: '/api/submission-detail',
    method: 'get',
    response: (req: { query: { assignmentId?: string; studentId?: string; type?: string } }) => {
      const { assignmentId, studentId, type } = req.query;

      // 获取真实的考试/作业标题
      let title = '';

      if (type === 'exam') {
        const exams = [
          { id: '1', title: '数据结构期中考试' },
          { id: '2', title: '算法设计与分析阶段测试' },
          { id: '3', title: '面向对象程序设计期末考试' },
          { id: '4', title: '计算机网络阶段测试' },
          { id: '5', title: '操作系统原理随堂测试' },
          { id: '6', title: '数据库概论期中考试' }
        ];

        const exam = exams.find(e => e.id === assignmentId);
        title = exam ? exam.title : `考试 #${assignmentId}`;
      } else {
        const homeworks = [
          { id: '1', title: '第一次编程作业' },
          { id: '2', title: '数据库设计作业' },
          { id: '3', title: 'Web开发实践' },
          { id: '4', title: '算法分析作业' },
          { id: '5', title: 'UI设计作业' }
        ];

        const homework = homeworks.find(h => h.id === assignmentId);
        title = homework ? homework.title : `作业 #${assignmentId}`;
      }

      // 生成题目 - 确保总分为100分
      const generateQuestions = () => {
        const questions = [];

        // 单选题 - 5*4=20分
        for (let i = 0; i < 4; i++) {
          questions.push({
            id: `q-mc-${i + 1}`,
            questionNumber: i + 1, // 添加题号
            type: 'single',
            content: `这是第${i + 1}道单选题，下列选项中正确的是？`,
            score: 5,
            options: [
              { value: 'A', content: '选项A', isCorrect: i % 4 === 0 },
              { value: 'B', content: '选项B', isCorrect: i % 4 === 1 },
              { value: 'C', content: '选项C', isCorrect: i % 4 === 2 },
              { value: 'D', content: '选项D', isCorrect: i % 4 === 3 },
            ],
            studentAnswer: String.fromCharCode(65 + (i % 4)),
            correctAnswer: String.fromCharCode(65 + (i % 4)),
            givenScore: 5,
            needManualGrading: false,
            graded: true
          });
        }

        // 多选题 - 2*10=20分
        for (let i = 0; i < 2; i++) {
          const correctAnswers = ['A', 'C'];
          questions.push({
            id: `q-ms-${i + 1}`,
            questionNumber: 5 + i, // 连续题号
            type: 'multiple',
            content: `这是第${i + 1}道多选题，下列选项中正确的有？`,
            score: 10,
            options: [
              { value: 'A', content: '选项A', isCorrect: true },
              { value: 'B', content: '选项B', isCorrect: false },
              { value: 'C', content: '选项C', isCorrect: true },
              { value: 'D', content: '选项D', isCorrect: false },
            ],
            studentAnswer: ['A', 'C'],
            correctAnswer: ['A', 'C'],
            givenScore: 10,
            needManualGrading: false,
            graded: true
          });
        }

        // 填空题 - 2*10=20分
        for (let i = 0; i < 2; i++) {
          questions.push({
            id: `q-fb-${i + 1}`,
            questionNumber: 7 + i, // 连续题号
            type: 'fill_blank',
            content: `这是第${i + 1}道填空题，中国的首都是_____，最大的城市是_____。`,
            score: 10,
            blanks: [
              { correctAnswer: '北京', studentAnswer: '北京', isCorrect: true },
              { correctAnswer: '上海', studentAnswer: '上海', isCorrect: true }
            ],
            studentAnswer: ['北京', '上海'],
            correctAnswer: ['北京', '上海'],
            givenScore: 10,
            needManualGrading: false,
            graded: true
          });
        }

        // 简答题 - 4*10=40分
        for (let i = 0; i < 4; i++) {
          const isEvenStudent = parseInt(studentId || '0') % 2 === 0;
          questions.push({
            id: `q-essay-${i + 1}`,
            questionNumber: 9 + i, // 连续题号
            type: 'essay',
            content: `这是第${i + 1}道简答题，请简述Java的垃圾回收机制。`,
            score: 10,
            referenceAnswer: 'Java垃圾回收是一种自动内存管理机制，通过标记-清除、复制、标记-整理等算法实现...',
            studentAnswer: '垃圾回收是Java的一个重要特性，它可以自动回收不再使用的对象占用的内存，通过标记和清除阶段完成...',
            // 偶数学号学生已批阅，奇数学号学生未批阅
            givenScore: isEvenStudent ? 8 : null,
            needManualGrading: true,
            graded: isEvenStudent,
            teacherComment: isEvenStudent ? '回答基本正确，但不够详细' : ''
          });
        }

        return questions;
      };

      // 根据学生ID确定是否已批阅
      const isEvenStudent = parseInt(studentId || '0') % 2 === 0;
      const isGraded = isEvenStudent; // 偶数学号已批阅，奇数学号未批阅

      // 获取所有学生ID
      const studentIds = Array.from({ length: 30 }, (_, i) => i.toString());

      // 只返回未批阅的学生ID（奇数学号）
      const ungraded_studentIds = studentIds.filter(id => parseInt(id) % 2 !== 0);

      return {
        code: 0,
        data: {
          assignmentInfo: {
            id: assignmentId,
            title: title,
            type: type,
            totalScore: 100,
            passScore: 60
          },
          studentInfo: {
            id: studentId,
            name: `学生${parseInt(studentId || '0') % 100}`,
            studentId: `2022${String(200 + parseInt(studentId || '0') % 100).padStart(3, '0')}`,
            className: 'R8软工1班',
            avatar: `/image/avatar/student.jpg`// 学生头像
          },
          submissionInfo: {
            id: `submit-${studentId}`,
            submissionTime: '2023-11-15 14:30:22',
            status: isGraded ? 'graded' : 'submitted',
            score: isGraded ? 85 : null
          },
          questions: generateQuestions(),
          // 如果是查看批阅页面，只返回未批阅的学生ID，否则返回所有学生
          studentIds: ungraded_studentIds
        }
      };
    }
  },
  {
    url: '/api/save-grading',
    method: 'post',
    response: () => {
      return {
        code: 0,
        data: {
          success: true,
          message: '批阅已保存'
        }
      };
    }
  },
  {
    url: '/api/assignment-info',
    method: 'get',
    response: (req: { query: { id?: string; type?: string } }) => {
      const { id, type } = req.query;

      // 模拟从数据库中根据ID获取作业/考试信息
      let title = '';

      // 假设这些是你的考试或作业数据
      if (type === 'exam') {
        const exams = [
          { id: '1', title: '数据结构期中考试' },
          { id: '2', title: '算法设计与分析阶段测试' },
          { id: '3', title: '面向对象程序设计期末考试' },
          { id: '4', title: '计算机网络阶段测试' },
          { id: '5', title: '操作系统原理随堂测试' },
          { id: '6', title: '数据库概论期中考试' }
        ];

        // 根据ID查找对应的考试
        const exam = exams.find(e => e.id === id);
        if (exam) {
          title = exam.title;
        } else {
          title = `考试 #${id}`;
        }
      } else {
        const homeworks = [
          { id: '1', title: '第一次编程作业' },
          { id: '2', title: '数据库设计作业' },
          { id: '3', title: 'Web开发实践' },
          { id: '4', title: '算法分析作业' },
          { id: '5', title: 'UI设计作业' }
        ];

        // 根据ID查找对应的作业
        const homework = homeworks.find(h => h.id === id);
        if (homework) {
          title = homework.title;
        } else {
          title = `作业 #${id}`;
        }
      }

      return {
        code: 0,
        data: {
          id,
          title,
          type,
          totalScore: 100,
          passScore: 60,
          startTime: '2023-11-01 10:00:00',
          endTime: '2023-11-15 23:59:59'
        }
      };
    }
  },
  // 学院列表接口 - POST方法
  {
    url: '/base/academy/list',
    method: 'post',
    response: (req: { body: any }) => {
      const { current = 1, pageSize = 10, academyName, academyPresidentId, status } = req.body;
      
      console.log('Mock接收到学院列表POST请求参数:', req.body);
      
      // 过滤数据
      let filteredData = [...collegeMockData];
      
      // 按学院名称筛选
      if (academyName) {
        filteredData = filteredData.filter(item => 
          item.name.includes(academyName.toString())
        );
      }
      
      // 按院长筛选
      if (academyPresidentId) {
        filteredData = filteredData.filter(item => 
          item.dean === academyPresidentId.toString()
        );
      }
      
      // 按状态筛选
      if (status !== undefined && status !== '') {
        const statusText = status === 0 ? '启用' : '停用';
        filteredData = filteredData.filter(item => 
          item.status === statusText
        );
      }
      
      // 计算分页
      const pageNum = Number(current);
      const pageSizeNum = Number(pageSize);
      const total = filteredData.length;
      const start = (pageNum - 1) * pageSizeNum;
      const end = start + pageSizeNum;
      const list = filteredData.slice(start, end);
      
      console.log('Mock返回学院列表POST响应:', { list, total });
      
      return {
        code: 200,
        message: '获取成功',
        data: {
          list,
          total
        }
      };
    }
  }
] as MockMethod[];
