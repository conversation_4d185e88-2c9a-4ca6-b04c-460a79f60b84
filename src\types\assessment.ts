// 考核内容发布页面数据结构定义
// 重新规整后的数据结构，便于前后端联调

// 1. 课程基本信息数据结构
export interface CourseBasicInfo {
  courseId: string
  courseName: string
  courseCode: string
  courseCredit: number
  semester: string // 学期
  academicYear: string // 学年
  teacherId: string // 授课教师ID
  teacherName: string // 授课教师姓名
  classId: string // 班级ID
  className: string // 班级名称
  studentCount: number // 学生人数
  createTime: string
  updateTime: string
}

// 兼容性类型别名
export interface CourseInfo {
  courseId: string
  courseName: string
  courseCode: string
  semester: string
  credits: number
  teacherName: string
}

// 2. 考核环节数据结构
export interface AssessmentSection {
  id: string
  courseId: string // 所属课程ID
  name: string // 考核环节名称
  type: string // 考核类型（作业、测验、期末考试等）
  weight: number // 权重占比
  description?: string // 描述
  status: 'draft' | 'published' | 'ended' // 状态
  inputMode: 'direct' | 'detailed' // 录入模式
  sortOrder: number // 排序
  createTime: string
  updateTime: string
  creatorId: string
  creatorName: string
  objectiveDistribution?: {
    objectiveId: string
    name: string
    percentage: number
  }[]
}

// 3. 课程目标数据结构
export interface CourseObjective {
  id: string
  courseId: string // 所属课程ID
  objectiveCode?: string // 目标编号（如：课程目标1）
  objectiveName?: string // 目标名称
  name?: string // 兼容性字段
  description: string // 详细描述
  weight: number // 权重占比
  graduationRequirement?: string // 对应毕业要求
  knowledgePoints?: string[] // 关联知识点
  sortOrder: number // 排序
  status?: 'active' | 'inactive' // 状态
  createTime?: string
  updateTime?: string
}

// 4. 考核内容数据结构
export interface AssessmentContent {
  id: string
  sectionId: string // 所属考核环节ID
  courseId: string // 所属课程ID
  name: string // 考核内容名称
  description: string // 考核描述
  source?: 'unified' | 'teacher' // 来源：统一发放或教师自建
  sourceInfo?: {
    type: string // 来源类型描述
    creatorId?: string // 创建者ID
    creatorName?: string // 创建者姓名
  }
  inputMode: 'direct' | 'detailed' // 录入方式
  weight?: number // 权重
  examTime?: {
    startTime: string // 考核开始时间
    endTime: string // 考核结束时间
    duration: number // 考核时长（分钟）
  }
  assessmentTime?: string // 考核时间
  status: 'draft' | 'published' | 'ended' // 状态
  publishTime?: string // 发布时间
  totalScore?: number // 总分
  passScore?: number // 及格分数
  configStatus?: 'not_configured' | 'configured' | 'partially_configured' // 配置状态
  sortOrder?: number // 排序
  createTime: string
  updateTime: string
  creatorId: string
  creatorName: string
  objectiveDistribution?: {
    objectiveId: string
    name: string
    score: number
  }[]
}

// 5. 考核内容配置数据结构

// 5.1 直接录入配置
export interface DirectInputConfig {
  id: string
  contentId: string // 考核内容ID
  courseId: string // 课程ID
  objectives: DirectObjectiveConfig[] // 课程目标配置
  totalScore: number // 总分
  configStatus: 'draft' | 'completed' // 配置状态
  createTime: string
  updateTime: string
}

export interface DirectObjectiveConfig {
  objectiveId: string // 课程目标ID
  objectiveCode: string // 目标编号
  objectiveName: string // 目标名称
  score: number // 分值
  percentage: number // 占比
  description: string // 说明
}

// 5.2 详细录入配置
export interface DetailedInputConfig {
  id: string
  contentId: string // 考核内容ID
  courseId: string // 课程ID
  questions: QuestionConfig[] // 题目配置
  questionTypes: QuestionTypeConfig[] // 题目类型配置（包含自定义类型）
  objectiveSummary: ObjectiveSummaryConfig[] // 课程目标汇总
  totalScore: number // 总分
  configStatus: 'draft' | 'completed' // 配置状态
  createTime: string
  updateTime: string
}

export interface QuestionConfig {
  id: string
  questionNumber: number // 题目编号
  questionType: string // 题目类型
  questionTypeLabel: string // 题目类型显示名称
  questionContent: string // 题目内容
  subItems: SubItemConfig[] // 子项配置
  totalScore: number // 题目总分
  sortOrder: number // 排序
}

export interface SubItemConfig {
  id: string
  questionId: string // 所属题目ID
  subNumber: string // 子项编号（如：1.1, 1.2）
  content: string // 子项内容（答案）
  score: number // 分值
  objectiveId: string // 对应课程目标ID
  objectiveCode: string // 课程目标编号
  sortOrder: number // 排序
}

export interface QuestionTypeConfig {
  id: string
  typeCode: string // 类型代码
  typeName: string // 类型名称
  isCustom: boolean // 是否自定义类型
  isActive: boolean // 是否启用
  createTime: string
}

export interface ObjectiveSummaryConfig {
  objectiveId: string // 课程目标ID
  objectiveCode: string // 目标编号
  objectiveName: string // 目标名称
  questionTypes: Record<string, {
    count: number // 子项数量
    score: number // 总分
    subNumbers: string[] // 子项编号列表
  }>
  totalScore: number // 总分
  percentage: number // 占比
}

// API 接口定义

// 获取课程基本信息
export interface GetCourseBasicInfoRequest {
  courseId: string
}

export interface GetCourseBasicInfoResponse {
  code: number
  message: string
  data: CourseBasicInfo
}

// 获取考核环节列表
export interface GetAssessmentSectionsRequest {
  courseId: string
  status?: 'draft' | 'published' | 'ended'
  pageNum?: number
  pageSize?: number
}

export interface GetAssessmentSectionsResponse {
  code: number
  message: string
  data: {
    list: AssessmentSection[]
    total: number
    pageNum: number
    pageSize: number
  }
}

// 获取课程目标列表
export interface GetCourseObjectivesRequest {
  courseId: string
  status?: 'active' | 'inactive'
}

export interface GetCourseObjectivesResponse {
  code: number
  message: string
  data: CourseObjective[]
}

// 获取考核内容列表
export interface GetAssessmentContentsRequest {
  courseId: string
  sectionId?: string
  status?: 'draft' | 'published' | 'ended'
  pageNum?: number
  pageSize?: number
}

export interface GetAssessmentContentsResponse {
  code: number
  message: string
  data: {
    list: AssessmentContent[]
    total: number
    pageNum: number
    pageSize: number
  }
}

// 获取考核内容配置
export interface GetAssessmentConfigRequest {
  contentId: string
  inputMode: 'direct' | 'detailed'
}

export interface GetDirectConfigResponse {
  code: number
  message: string
  data: DirectInputConfig
}

export interface GetDetailedConfigResponse {
  code: number
  message: string
  data: DetailedInputConfig
}

// 保存考核内容配置
export interface SaveDirectConfigRequest {
  contentId: string
  config: Omit<DirectInputConfig, 'id' | 'createTime' | 'updateTime'>
}

export interface SaveDetailedConfigRequest {
  contentId: string
  config: Omit<DetailedInputConfig, 'id' | 'createTime' | 'updateTime'>
}

export interface SaveConfigResponse {
  code: number
  message: string
  data: {
    configId: string
  }
}

// 班级相关数据结构
export interface ClassInfo {
  classId: string
  className: string
  majorId?: string
  majorName?: string
  major?: string // 兼容性字段
  studentCount: number
  scheduleInfo?: {
    classTime: string // 上课时间
    classroom: string // 教室
    weekday: string // 星期
    timeSlot: string // 时间段
  }
  classTime?: string // 兼容性字段
  teacherInfo?: {
    teacherId: string
    teacherName: string
    role: string // 角色：主讲教师、助教等
  }
  teacherName?: string // 兼容性字段
  publishStatus?: 'not_published' | 'published' | 'ended' // 发布状态
  isPublished?: boolean // 兼容性字段
  publishTime?: string // 发布时间
  entranceYear?: string // 入学年份
  classStatus?: 'active' | 'graduated' // 班级状态
  createTime?: string
  updateTime?: string
}

// 发布相关数据结构
export interface PublishDialogData {
  contentId: string
  contentName: string
  contentDescription: string
  inputMode: 'direct' | 'detailed'
  assessmentTime?: string
}

// 缺失的类型定义
export interface QuestionDetail {
  questionId: string
  contentId: string
  title: string
  description: string
  type: string
  totalScore: number
  objectiveScores: {
    objectiveId: string
    objectiveName: string
    score: number
  }[]
}

export interface ObjectiveOverview {
  objectiveId: string
  objectiveName: string
  totalScore: number
  percentage: number
}

export interface DirectModeData {
  objectiveId: string
  objectiveName: string
  description: string
  score: number
  percentage: number
}

export interface ScoringCriteria {
  title: string
  description: string
}

// 发布请求和响应
export interface PublishContentRequest {
  contentId: string
  classIds: string[]
  publishTime?: string
  notifyStudents?: boolean
}

export interface PublishContentResponse {
  code: number
  message: string
  data: {
    publishId: string
    successCount: number
    failedCount: number
    failedClasses?: string[]
  }
}

// 取消发布请求
export interface UnpublishContentRequest {
  contentId: string
  classIds: string[]
}

export interface UnpublishContentResponse {
  code: number
  message: string
  data: {
    successCount: number
    failedCount: number
    failedClasses?: string[]
  }
}

// 获取班级列表请求
export interface GetClassListRequest {
  courseId: string
  teacherId?: string
  keyword?: string
  publishStatus?: 'not_published' | 'published' | 'ended'
  pageNum?: number
  pageSize?: number
}

export interface GetClassListResponse {
  code: number
  message: string
  data: {
    list: ClassInfo[]
    total: number
    pageNum: number
    pageSize: number
  }
} 