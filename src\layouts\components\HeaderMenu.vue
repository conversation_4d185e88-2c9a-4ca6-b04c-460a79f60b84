<template>
<t-popup
  ref="userDrop"
  :overlay-inner-style="{ width: '128px' }"
  placement="bottom-right"
  trigger="click"
  :close-after-inner-click="true"
>
  <template #content>
    <t-dropdown-menu>
      <div class="user-dropdown">
        <!-- 显示用户角色信息 -->
        <div class="user-role">
          角色: {{ userRole }}
        </div>
        <t-dropdown-item v-for="item in userDropdownList" :key="item.value" @click="useDropDown(item)">
          {{ item.label }}
        </t-dropdown-item>
      </div>
    </t-dropdown-menu>
  </template>
</t-popup>
</template>

<script lang="ts">
import { computed, defineComponent } from 'vue';

import { useUserStore } from '@/store';

export default defineComponent({
  setup() {
    const userStore = useUserStore();
    
    // 获取当前用户角色信息
    const userRole = computed(() => {
      const roleCode = userStore.userInfo?.roleCode || -1;
      const roleMap: Record<string, string> = {
        '0': '学校管理员',
        '1': '专业负责人',
        '2': '课题负责人',
        '3': '教师',
        '4': '学生',
        '-1': '未知角色'
      };
      return `${roleMap[String(roleCode)] || '未知'} (${roleCode})`;
    });
    
    return {
      userRole,
    };
  },
});
</script>

<style lang="less" scoped>
.user-dropdown {
  .user-role {
    padding: 8px 16px;
    color: var(--td-text-color-primary);
    font-size: 14px;
    border-bottom: 1px solid var(--td-component-stroke);
    margin-bottom: 8px;
  }
}
</style> 