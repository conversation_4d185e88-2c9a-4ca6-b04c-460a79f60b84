# 班级考核内容管理

## 概述
班级考核内容管理模块提供了完整的班级考核管理功能，包括班级列表展示、考核内容管理、成绩录入等核心功能。

## 功能特性

### 1. 班级列表管理
- 班级卡片展示，包含班级基本信息
- 支持点击跳转到班级考核管理页面
- 响应式设计，适配不同屏幕尺寸

### 2. 班级考核管理
- 班级基本信息展示
- 考核统计卡片（学生人数、考核项目、待录入、完成率）
- 考核内容列表，支持筛选和搜索
- 班级切换功能

### 3. 成绩管理功能（已完善）

#### 直接录入模式
- ✅ **美观的统计卡片**：展示平均分、最高分、最低分、已录入、待录入等统计信息
- ✅ **行内编辑功能**：点击分数直接编辑，输入框尺寸优化，支持保存和取消操作
- ✅ **分页支持**：表格分页，支持调整每页显示数量
- ✅ **导入成绩功能**：支持Excel/CSV格式的成绩导入
- ✅ **搜索功能**：支持按学号姓名搜索
- ✅ **总分计算**：各课程目标成绩的和（非平均值）

#### 详细录入模式
- ✅ **全屏弹窗**：提供更大的操作空间
- ✅ **美观的统计卡片**：与直接录入模式一致的统计卡片展示
- ✅ **题目结构展示**：按题目类型分组显示，支持表格列合并
- ✅ **行内编辑功能**：点击分数直接编辑题目小项，无需弹窗
- ✅ **搜索筛选**：支持搜索学号姓名，按状态筛选
- ✅ **分页支持**：表格分页，提升大数据量下的性能
- ✅ **导入成绩功能**：支持详细成绩批量导入
- ✅ **自动计算总分**：自动计算总分为所有小项之和
- ✅ **复杂表头**：按题目类型分组，支持多级表头展示（如单选题1-5，填空题9.1-9.3等）
- ✅ **列设置功能**：可选择显示/隐藏特定题型，优化表格展示
- ✅ **导入导出优化**：支持按题目结构自动生成导入模板，导出完整成绩数据

## 文件结构

```
src/pages/assessment/class-content/
├── index.vue                    # 班级列表页面
├── management.vue               # 班级考核管理页面
├── components/
│   ├── index.ts                # 组件导出
│   ├── ClassSelector.vue       # 班级选择器
│   ├── GradeManagementDialog.vue # 成绩管理弹窗（已完善）
│   └── ClassCardWithSelection.vue # 带选择功能的班级卡片
├── composables/
│   └── useAssessment.ts        # 考核数据管理
├── types/
│   └── index.ts               # 类型定义
├── styles/
│   └── common.less            # 通用样式
└── README.md                  # 说明文档
```

## 路由配置

```typescript
// 班级考核管理
{
  path: '/teacher/course/assessment/class-content/:courseId',
  name: 'TeacherCourseAssessmentClassContent',
  component: () => import('@/pages/assessment/class-content/index.vue')
}

// 班级考核内容管理
{
  path: '/teacher/course/class/content/details/:courseId/:classId',
  name: 'TeacherCourseAssessmentClassContentDetails',
  component: () => import('@/pages/assessment/class-content/management.vue')
}
```

## 接口定义

### 考核内容接口
```typescript
interface AssessmentContent {
  id: string
  sectionName: string
  title: string
  inputMode: 'direct' | 'detailed'
  inputCount: number
  totalCount: number
  completionRate: number
  lastUpdate: string
}
```

### 学生成绩接口
```typescript
interface StudentGrade {
  studentId: string
  studentName: string
  objectives: Record<string, number>
  totalScore?: number
  status: 'pending' | 'submitted'
  lastUpdate?: string
}
```

## 使用示例

### 1. 基本使用
```vue
<template>
  <GradeManagementDialog
    v-model:visible="gradeDialogVisible"
    :assessment-content="assessmentContent"
    :class-info="classInfo"
    @success="handleSuccess"
  />
</template>
```

### 2. 测试页面
访问 `/test-grade-management` 可以测试所有成绩管理功能。

## 最新改进（v2.0）

### 直接录入模式改进
1. **统计卡片重设计**：使用渐变色图标和更美观的卡片布局
2. **行内编辑功能**：点击分数直接编辑，无需弹窗
3. **分页支持**：表格分页，提升大数据量下的性能
4. **导入功能升级**：
   - 录入成绩按钮改为导入成绩
   - 使用统一的文件上传组件
   - 支持下载成绩模板
   - 模板自动包含课程目标列
5. **批量编辑**：支持批量设置课程目标分数

### 技术特性
- 响应式设计，适配移动端
- TypeScript 类型安全
- 组件化设计，易于维护
- 统一的样式规范
- 完整的错误处理

## 注意事项

1. **数据格式**：确保传入的数据格式符合接口定义
2. **权限控制**：根据用户角色控制功能访问权限
3. **性能优化**：大数据量时建议使用分页和虚拟滚动
4. **浏览器兼容**：支持现代浏览器，IE11+

## 扩展功能

### 计划中的功能
- [ ] 成绩导出为PDF格式
- [ ] 成绩统计图表展示
- [ ] 成绩历史记录查看
- [ ] 批量操作日志记录
- [ ] 成绩异常检测和提醒

### 可扩展接口
- 自定义成绩计算规则
- 自定义导入导出格式
- 自定义统计指标
- 自定义权限控制

## 更新日志

### v2.3 (2024-01-22)
- ✅ 完善详细录入模式的表头展示，实现按题型分组的复杂表头
- ✅ 新增列设置功能，支持选择显示/隐藏特定题型
- ✅ 优化导入导出功能，导入模板自动根据题目结构生成
- ✅ 改进表格布局，固定左侧学生信息和右侧总分/操作列
- ✅ 优化工具栏布局，将导入导出按钮移至搜索栏
- ✅ 更新模拟数据，增加更多题型和子项

### v2.2 (2024-01-20)
- ✅ 完善详细录入模式的成绩管理功能
- ✅ 新增统计卡片展示，与直接录入模式保持一致
- ✅ 新增行内编辑功能，支持直接编辑题目小项分数
- ✅ 新增分页支持，提升大数据量表格性能
- ✅ 新增导入成绩功能，支持详细成绩批量导入
- ✅ 优化搜索工具栏，统一界面风格
- ✅ 更新模拟数据，确保总分计算正确

### v2.1 (2024-01-20)
- ✅ 优化行内编辑输入框尺寸，从100px增加到120px
- ✅ 移除批量编辑功能，简化用户界面
- ✅ 移除下载模板按钮，统一使用导入弹窗中的模板功能
- ✅ 修正总分计算逻辑，从平均值改为求和
- ✅ 更新模拟数据，确保总分计算一致性
- ✅ 简化搜索工具栏，移除不必要的操作按钮

### v2.0 (2024-01-20)
- ✅ 完善直接录入模式的成绩管理功能
- ✅ 新增统计卡片展示
- ✅ 新增行内编辑功能
- ✅ 新增分页支持
- ✅ 新增导入成绩和下载模板功能
- ✅ 新增批量编辑功能
- ✅ 优化UI设计和用户体验

### v1.0 (2024-01-15)
- ✅ 基础班级考核管理功能
- ✅ 成绩管理弹窗（直接录入和详细录入）
- ✅ 班级选择器组件
- ✅ 响应式设计 