import { netConfig } from '@/config/net.config'
import { settingConfig } from '@/config/setting.config'
const config: { [key: string]: any } = {
  ...netConfig,
  ...settingConfig,
}

export default config

export const {
  contentType,
  successCode,
  statusName,
  messageName,
  title,
  baseURL,
  timeout,
  debounce,
  codeVerificationArray,
  titleSeparator,
  titleReverse,
  abbreviation,
  isHashRouterMode,
  routesWhiteList,
  loginRouter,
  loadingText,
  tokenName,
  tokenTableName,
  storage,
  i18n,
  messageDuration,
  errorLog,
  loginInterception,
  authentication,
  rolesControl,
  publicPath,
  recordRoute,
} = config
