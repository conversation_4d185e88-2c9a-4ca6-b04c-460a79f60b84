<template>
    <div class="exam-dashboard">
        <!-- 顶部操作栏 -->
        <div class="action-bar">
            <t-button theme="primary" @click="createNewExam">
                <template #icon>
                    <t-icon name="add" />
                </template>
                新建考试
            </t-button>
            <t-button variant="outline" @click="openQuestionBank">考试库</t-button>
            <t-button variant="outline" @click="importExam">
                <template #icon>
                    <t-icon name="upload" />
                </template>
                导入考试
            </t-button>
        </div>
        <!-- 筛选区域 -->
        <div class="filter-section">
            <t-select v-model="selectedClass" placeholder="全部班级" :options="classOptions" />

            <div class="status-filter">
                <t-radio-group v-model="status" variant="primary-filled">
                    <t-radio-button value="all">全部</t-radio-button>
                    <t-radio-button value="not-started">未开始</t-radio-button>
                    <t-radio-button value="in-progress">进行中</t-radio-button>
                    <t-radio-button value="finished">已结束</t-radio-button>
                </t-radio-group>
            </div>

            <t-input v-model="searchKeyword" placeholder="搜索" class="search-input">
                <template #prefix-icon>
                    <t-icon name="search" />
                </template>
            </t-input>
        </div>

        <!-- 考试列表 -->
        <div class="exam-list">
            <div v-for="exam in filteredExamList" :key="exam.id" class="exam-item">
                <div class="exam-info">
                    <h3>{{ exam.title }}</h3>
                    <div class="meta-info">
                        <span class="class-info">{{ exam.classes }}</span>
                        <span class="time-info">
                            <t-icon name="time" />
                            考试时间: {{ exam.startTime }} 至 {{ exam.endTime }}
                        </span>
                    </div>
                </div>

                <div class="exam-stats">
                    <div class="stat">
                        <span class="number">{{ exam.submissionCount }}</span>
                        <span class="label">待批</span>
                    </div>
                    <div class="stat">
                        <span class="number">{{ exam.viewCount }}</span>
                        <span class="label">已交</span>
                    </div>
                    <div class="stat">
                        <span class="number">{{ exam.commentCount }}</span>
                        <span class="label">未交</span>
                    </div>
                </div>

                <div class="actions">
                    <t-button theme="primary" variant="text" @click="goToGrading(exam)">批阅</t-button>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import {ref, computed, onMounted} from 'vue'
import {
    Button as TButton,
    Select as TSelect,
    RadioGroup as TRadioGroup,
    RadioButton as TRadioButton,
    Input as TInput,
    Icon as TIcon,
    MessagePlugin
} from 'tdesign-vue-next'
import { useRouter,useRoute} from 'vue-router'
import{ TeacherExam ,getExamInfo} from "@/api/base/teacher/teacherHome";

const router = useRouter()
const route = useRoute()

const selectedClass = ref('')
const status = ref('all')
const searchKeyword = ref('')

const classOptions = [
    { label: '全部班级', value: 'all' },
    { label: 'RB软工树241', value: '241' },
    { label: 'RB软工树242', value: '242' },
    { label: 'RB软工树243', value: '243' },
    { label: 'RB软工树244', value: '244' },
]

// 更详细的考试数据
const examList = ref<TeacherExam[]>([])
const fetchExamList = async ()=>{
    try {
        const course_id = route.params.courseId;
        const class_id=route.params.classId;
        const exams = await getExamInfo(Number(course_id),Number(class_id))
        examList.value = exams
    } catch (error) {
        console.error('Error fetching exam list:', error);
        MessagePlugin.warning('获取考试列表失败');
    }
}
//获取试卷数据
onMounted(()=>{
  fetchExamList();
})

// 添加过滤逻辑
const filteredExamList = computed(() => {
    return examList.value.filter(item => {
        // 班级过滤
        if (selectedClass.value !== 'all' && !item.classId.includes(selectedClass.value)) {
            return false
        }

        // 状态过滤
        if (status.value !== 'all' && item.status !== status.value) {
            return false
        }

        // 关键词搜索
        if (searchKeyword.value && !item.title.toLowerCase().includes(searchKeyword.value.toLowerCase())) {
            return false
        }

        return true
    })
})

const createNewExam = () => {
    // 跳转到新建考试页面
    router.push('/teachers/newtask?type=exam') // 可以使用查询参数区分考试与作业
}

const importExam = () => {
    router.push('/teachers/import-task?type=exam')
}

// 打开题库
const openQuestionBank = () => {
    router.push('/teachers/question-bank?mode=exam')
}

// 添加跳转函数
const goToGrading = (exam) => {
    router.push(`/teachers/grading-list/${exam.id}?type=exam`)
}
</script>

<style scoped>
.search-icon {
    width: 16px;
    height: 16px;
}

.exam-dashboard {
    padding: 20px;
    background: #f5f5f5;
}

.action-bar {
    display: flex;
    gap: 12px;
    margin-bottom: 20px;
}

.filter-section {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 16px;
}

.status-filter {
    flex-grow: 1;
}

:deep(.t-radio-group) {
    display: flex;
}

:deep(.t-radio-button) {
    flex: 1;
    max-width: 120px;
    justify-content: center;
}

.search-input {
    width: 260px;
}

@media (max-width: 768px) {
    .filter-section {
        flex-direction: column;
        align-items: stretch;
    }

    :deep(.t-radio-button) {
        max-width: none;
    }

    .search-input {
        width: 100%;
    }
}

.exam-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.exam-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.exam-info {
    flex-grow: 1;
}

.exam-info h3 {
    margin: 0 0 8px 0;
    font-size: 16px;
    color: #333;
}

.meta-info {
    display: flex;
    gap: 24px;
    color: #666;
    font-size: 14px;
}

.class-info {
    color: #0052d9;
}

.time-info {
    display: flex;
    align-items: center;
    gap: 4px;
}

.exam-stats {
    display: flex;
    gap: 32px;
    margin-right: 32px;
}

.stat {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.stat .number {
    font-size: 20px;
    font-weight: bold;
    color: #333;
}

.stat .label {
    font-size: 12px;
    color: #666;
}

.actions {
    display: flex;
    gap: 8px;
}
</style>
