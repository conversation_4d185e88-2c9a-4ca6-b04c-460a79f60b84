export default {
  outputOverview: {
    title: 'In/Out Overview',
    subtitle: '(pieces)',
    export: 'Export data',
    month: {
      input: 'Total in store this month',
      output: 'Total out store this month',
    },
    since: 'Since last week',
  },
  rankList: {
    title: 'Sales order ranking',
    week: 'This week',
    month: 'Latest 3 months',
    info: 'Detail',
  },
  topPanel: {
    card1: 'Total Revenue',
    card2: 'Total Refund',
    card3: 'Active User(s)',
    card4: 'Generate Order(s)',
    cardTips: 'since last week',
    analysis: {
      title: 'Analysis Data',
      unit: 'ten thousand yuan',
      series1: 'this month',
      series2: 'last month',
      channels: 'Sales Channels',
      channel1: 'online',
      channel2: 'shop',
      channelTips: ' sales ratio',
    },
  },
  saleColumns: {
    index: 'Ranking',
    productName: 'Customer',
    growUp: 'Grow up',
    count: 'Count',
    operation: 'Operation',
  },
  buyColumns: {
    index: 'Ranking',
    productName: 'Supplier',
    growUp: 'Grow up',
    count: 'Count',
    operation: 'Operation',
  },
  chart: {
    week1: 'MON',
    week2: 'TUE',
    week3: 'WED',
    week4: 'THU',
    week5: 'FRI',
    week6: 'SAT',
    week7: 'SUN',
    max: 'Max',
    min: 'Min',
    thisMonth: 'this month',
    lastMonth: 'last month',
  },
};
