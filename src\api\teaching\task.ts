import { request } from '@/utils/request';

const BASE_URL = '/teaching/task';

// 班级信息
export interface TaskClassVO {
  classId: number;
  className: string;
  studentNumber: number;
}

// 教学任务教师信息
export interface TaskTeacherVO {
  teacherId: number;
  teacherName: string;
  role: number; // 1:主讲教师 2:辅导教师 3:助教
  roleName: string;
}

// 教学任务详情 VO
export interface TaskWorkDetailVO {
  academicYear: string; // 学年，如：2024-2025
  semester: string; // 学期，如：春季学期/秋季学期
  taskId: number;
  taskName: string;
  taskNumber: number;
  teachWeek: number;
  weekHours: number;
  classes: TaskClassVO[];
  teachers: TaskTeacherVO[];
  studentCount: number;
  classCount: number;
}

// 分页信息
export interface Page<T> {
  records: T[];
  total: number;
  size: number;
  current: number;
  pages: number;
}

// 教学任务数据类型（保持向后兼容）
export interface TaskWorkVO {
  id: number;
  courseId: number;
  taskNumber: number;
  taskName: string;
  taskYear: number;
  taskTerm: number;
  teachWeek: number;
  weekHours: number;
  totalHours: number;
  courseLeaderId: number;
  status: number;
  classIds: number[];
  teachers: TaskTeacherVO[];
  totalStudentCount: number;
  // 扩展字段用于显示
  courseName?: string;
  classCount?: number;
  semester?: string;
}

// 教学任务统计信息 VO
export interface TaskWorkStatisticsVO {
  taskYear: number; // 学年，如：2024
    taskTerm: number; // 学期，如：1（春季学期）/2（秋季学期）
  academicYear: string; // 学年，如：2024-2025
  semester: string; // 学期，如：春季学期/秋季学期
  courseId: number; // 课程ID
  courseName: string; // 课程名称
  taskDetails: Page<TaskWorkDetailVO>; // 该学期的教学任务分页列表
  totalClassCount: number; // 该学期班级总数量
  totalStudentCount: number; // 该学期学生总数
  totalTaskCount: number; // 该学期任务总数
  totalTeacherCount?: number; // 该学期授课教师总数（可选）
}

// 统计汇总信息
export interface TaskStatisticsSummary {
  totalTasks: number;
  taskGrowth: number;
  totalTeachers: number;
  teacherGrowth: number;
  totalStudents: number;
  studentGrowth: number;
  activeClasses: number;
  classGrowth: number;
}

// 获取学期教学任务静态数据（后端已处理合并）
export function getSemesterTasks(planId: string, termType: number, params?: any): Promise<any> {
  return request({
    url: `${BASE_URL}/generate-task-list/${planId}/${termType}`,
    method: 'get',
    params
  });
}

/**
 * 根据课程ID获取按学期分组的教学任务统计信息
 * @param courseId 课程ID
 * @returns 按学期分组的统计信息列表
 */
export function getTaskWorkStatistics(courseId: number) {
  return request({
    url: `/teaching/task/statistics/course/${courseId}`,
    method: 'GET',
  }).catch(error => {
    console.warn('教学任务统计接口调用失败，使用模拟数据:', error);
    return Promise.resolve({} as Page<TaskWorkStatisticsVO>);
  });
}

/**
 * 根据课程ID获取教学任务列表（分页）
 * @param courseId 课程ID
 * @param params 查询参数
 * @returns 教学任务列表
 */
export function getTaskWorkList(courseId: number, params?: {
  taskYear?: number;
  taskTerm?: number;
  current?: number;
  pageSize?: number;
}){
 
  // 构建请求数据，使用POST方法和data参数（符合项目规范）
  const requestData = {
    courseId: courseId,
    current: params?.current || 1,
    pageSize: params?.pageSize || 10,
    taskYear: params?.taskYear,
    taskTerm: params?.taskTerm
  };
  
  return request({
    url: `/teaching/task/page/${courseId}`,
    method: 'POST',
    data: requestData
  }).catch(error => {
    console.warn('教学任务列表接口调用失败，使用模拟数据:', error);
  });
}

/**
 * 根据课程ID获取教学任务列表（不分页）
 * @param courseId 课程ID
 * @param params 查询参数
 * @returns 教学任务列表
 */
export function getTaskWorkListByTaskYear(courseId: number, params?: {
  taskYear?: number;
  taskTerm?: number;
}){
 
  // 构建请求数据，使用POST方法和data参数（符合项目规范）
  const requestData = {
    courseId: courseId,
    taskYear: params?.taskYear,
    taskTerm: params?.taskTerm
  };
  
  return request({
    url: `/teaching/task/academicYear/${courseId}`,
    method: 'POST',
    data: requestData
  }).catch(error => {
    console.warn('教学任务列表接口调用失败，使用模拟数据:', error);
  });
}

/**
 * 根据课程id获得教学任务列表，不需要分页，不需要taskYear和taskTerm
 * @param courseId 课程ID
 * @returns 教学任务列表
 */
export function getTaskWorkListByCourseId(courseId: number) {
  return request({
    url: `${BASE_URL}/course/${courseId}`,
    method: 'POST'
  }).catch(error => {
    console.warn('获取课程的所有教学任务列表接口调用失败:', error);
  });
}

/**
 * 根据assessmentID获取教学任务列表（不分页）
 * @param assessmentId 评估ID
 * @param params 查询参数
 * @returns 教学任务列表
 */
export function getTaskWorkListByAssessmentId(assessmentId: number){

  return request({
    url: `/teaching/task/academicYear/${assessmentId}`,
    method: 'POST'
  }).catch(error => {
    console.warn('获取一个考核发布的所有教学任务列表接口调用失败:', error);
  });
}

/**
 * 获取教学任务详情
 * @param taskId 教学任务ID
 * @returns 教学任务详情
 */
export function getTaskWorkDetail(taskId: number): Promise<TaskWorkVO> {
  return request({
    url: `/teaching/task/${taskId}`,
    method: 'GET',
  }).catch(error => {
    console.warn('教学任务详情接口调用失败，使用模拟数据:', error);
    return {} as Promise<TaskWorkVO>;;
  });
}

/**
 * 获取教学任务的教师列表
 * @param taskId 教学任务ID
 * @returns 教师列表
 */
export function getTaskTeachers(taskId: number): Promise<TaskTeacherVO[]> {
  return request({
    url: `/teaching/task/${taskId}/teachers`,
    method: 'GET',
  }).catch(error => {
    console.warn('教学任务教师列表接口调用失败，使用模拟数据:', error);
    const mockTeachers: TaskTeacherVO[] = [
      { teacherId: 1, teacherName: '张教授', role: 1, roleName: '主讲教师' },
      { teacherId: 2, teacherName: '李老师', role: 3, roleName: '助教' }
    ];
    return Promise.resolve(mockTeachers);
  });
}


