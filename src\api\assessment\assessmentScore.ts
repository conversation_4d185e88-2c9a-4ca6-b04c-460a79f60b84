import { request } from '@/utils/request';
// 如果 ApiResponse 类型在某处定义，请正确导入它，例如：
// import type { ApiResponse } from '@/types/api';
// 如果没有定义，请添加如下类型定义：
type ApiResponse<T> = {
  code: number;
  message: string;
  data: T;
};

const BASE_URL = '/teaching/assessment/score'
/**
 * 查询考核关联的教学任务详情
 * 根据考核id（assessmentId）查询已发布的所有教学任务的详情，供成绩管理调用
 * 教学任务名称、教学任务对应的班级信息，包括班级名称、班级人数，成绩提交状态、已录入人数、未录入人数
 */
export const getAssessmentRelatedTasks = (assessmentId: number): Promise<ApiResponse<any>> => {
  return request<ApiResponse<any>>({
    url: `${BASE_URL}/related-tasks/${assessmentId}`,
    method: 'GET'
  });
}; 

///{assessmentId}/task/{taskId}/scores
/**
     * 根据考核id（assessmentId）与教学任务id（taskId）查询学生成绩详情
     * 学生名单，学生对应课程目标的成绩列表
     * 查询学生成绩详情
     */
export const getAssessmentTaskScores = (assessmentId: number, taskId: number): Promise<ApiResponse<any>> => {
  return request<ApiResponse<any>>({
    url: `${BASE_URL}/${assessmentId}/task/${taskId}/scores`,
    method: 'GET'
  });
};

/**
 * 需要根据课程Id获得该课程的所有的目标成绩
 * @param courseId 
 * @returns 
 */
export const getTargetScoresByCourseId = (courseId: number): Promise<ApiResponse<any>> => {
  return request<ApiResponse<any>>({
    url: `${BASE_URL}/target-scores/course/${courseId}`,
    method: 'GET'
  });
};

//export/target/template
/**
 * 导出课程目标成绩模板
 */
export const exportTargetScoreTemplate = (assessmentId: number, taskId: number): Promise<ApiResponse<any>> => {
  return request<ApiResponse<any>>({
    url: `${BASE_URL}/export/target/template/${assessmentId}/task/${taskId}`,
    method: 'GET',
    responseType: 'blob' // 处理文件下载
  });
};

//import/target
/**
 * 导入课程目标成绩
 */
export const importTargetScore = (assessmentId: number, taskId: number, file: File): Promise<ApiResponse<any>> => {
  const formData = new FormData();
  formData.append('file', file);
  return request<ApiResponse<any>>({
    url: `${BASE_URL}/import/target/excel/${assessmentId}/${taskId}`,
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data' // 确保正确处理文件上传
    }
  });
};

/**
 * 批量保存学生课程目标成绩
 * @param batchSaveDTO 批量保存请求数据
 * @returns 保存结果
 */
export const batchSaveTargetScores = (batchSaveDTO: any): Promise<ApiResponse<any>> => {
  return request<ApiResponse<any>>({
    url: `${BASE_URL}/batch-save-target-scores`,
    method: 'POST',
    data: batchSaveDTO
  });
};
