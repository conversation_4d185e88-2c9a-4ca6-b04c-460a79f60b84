import request from "@/utils/request";
import {CoursePaperListResult, CourseQuestionListResult} from "@/api/model/course/questionBankModel";


//获取试卷列表数据
export async function getPaperList(): Promise<CoursePaperListResult> {
  try {
    const res = await request({
      url: 'api/course/paper/list',
      method: 'get'
    });
    return res;
  } catch (error) {
    console.error('获取对应试卷列表数据失败:', error);
    throw error;
  }
}

//获取题库题目数据
export async function getQuestionList(): Promise<CourseQuestionListResult> {
  try {
    const res = await request({
      url: 'api/course/question/list',
      method: 'get'
    });
    return res;
  } catch (error) {
    console.error('获取对应题目数据失败:', error);
    throw error;
  }
}


//获取课程目标数据
export async function getCourseObjectivesList(): Promise<CourseQuestionListResult> {
  try {
    const res = await request({
      url: 'api/course/CourseObjectives/list',
      method: 'get'
    });
    return res;
  } catch (error) {
    console.error('获取对应课程目标数据失败:', error);
    throw error;
  }
}


