<template>
  <div class="course-objectives-section">
    <div v-if="objectives && objectives.length > 0" class="objectives-cards">
      <div
        v-for="(objective, index) in objectives"
        :key="index"
        class="objective-card"
      >
        <div class="card-header">
          <div class="objective-number">{{ objective.number || index + 1 }}</div>
          <div class="objective-label">
            {{ objective.objectiveName || `课程目标${objective.number || index + 1}` }}
          </div>
          <t-tag theme="success" variant="light" size="small" class="status-tag">
            预期达成度: {{ objective.expectedScore || '-' }}
          </t-tag>
        </div>
        <div class="objective-content">
          {{ objective.description || '暂无描述' }}
        </div>
      </div>
    </div>
    <div v-else class="empty-objectives">
      <t-empty description="暂无课程目标">
        <template #image>
          <t-icon name="info-circle" size="48px" />
        </template>
      </t-empty>
    </div>
  </div>
</template>

<script setup lang="ts">
import { CourseObjectiveVO } from '@/api/training/course';

// Props 定义
interface Props {
  objectives: CourseObjectiveVO[];
}

const props = defineProps<Props>();

// 添加一个watch来监控属性变化
import { watch, onMounted } from 'vue';

onMounted(() => {
  console.log('CourseObjectives组件已挂载，接收到的课程目标数据:', props.objectives);
});

watch(() => props.objectives, (newVal) => {
  console.log('CourseObjectives组件接收到新的课程目标数据:', newVal);
}, { deep: true });
</script>

<style lang="less" scoped>
.course-objectives-section {
  /* 移除了外边距、内边距、背景和边框，因为这些现在在父容器中设置 */
  
  /* 移除了标题样式，因为现在标题在父组件中 */

  .objectives-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 16px;

    .objective-card {
      background: #fff;
      border: 1px solid var(--td-border-level-1-color);
      border-radius: 6px;
      padding: 16px;
      transition: all 0.2s ease;

      &:hover {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        border-color: var(--td-brand-color);
      }

      .card-header {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 12px;

        .objective-number {
          width: 24px;
          height: 24px;
          background: var(--td-brand-color);
          color: white;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 12px;
          font-weight: 600;
          flex-shrink: 0;
        }

        .objective-label {
          font-weight: 500;
          color: var(--td-text-color-primary);
          flex: 1;
        }

        .status-tag {
          flex-shrink: 0;
        }
      }

      .objective-content {
        color: var(--td-text-color-secondary);
        line-height: 1.5;
        font-size: 14px;
      }
    }
  }

  .empty-objectives {
    padding: 40px 20px;
    text-align: center;
    color: var(--td-text-color-placeholder);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .course-objectives-section {
    padding: 16px;

    .objectives-cards {
      grid-template-columns: 1fr;
      gap: 12px;

      .objective-card {
        padding: 12px;

        .card-header {
          .objective-label {
            font-size: 14px;
          }
        }

        .objective-content {
          font-size: 13px;
        }
      }
    }
  }
}
</style>
