<template>
  <div class="professional-dashboard">
        <!-- 专业选择弹窗 -->
    <MajorSelector
      v-model:visible="showMajorSelector"
      :current-major-id="currentMajor.id"
      :major-list="majorList"
      @select="handleMajorSelect"
    />

    <!-- 背景装饰 -->
    <div class="background-decoration"></div>

    <!-- 头部信息 -->
    <div class="dashboard-header">
      <div class="header-content">
        <div class="breadcrumb-section">
          <t-breadcrumb>
            <t-breadcrumb-item>教师工作台</t-breadcrumb-item>
            <t-breadcrumb-item>仪表盘</t-breadcrumb-item>
          </t-breadcrumb>
        </div>
        <div class="major-info" v-if="currentMajor.name">
          <div class="title-container">
            <h1 class="dashboard-title">{{ currentMajor.name }}</h1>
            <t-tag v-if="currentMajor.type" theme="success" size="small" class="major-tag">
              {{ currentMajor.type }}
            </t-tag>
          </div>
          <div class="title-actions">
            <t-button theme="primary" variant="outline" size="small" @click="handleReselectMajor">
              <template #icon>
                <t-icon name="swap" />
              </template>
              重新选择专业
            </t-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="dashboard-content" v-if="currentMajor.name">
      <!-- 总体统计模块 -->
      <div class="statistics-section">
        <div class="section-header">
          <div class="header-left">
            <h2 class="section-title">
              <t-icon name="chart-line" class="title-icon" />
              总体统计
            </h2>
            <p class="section-subtitle">专业教学数据概览</p>
          </div>
        </div>

        <div class="stats-grid">
          <div class="stat-card" v-for="(stat, index) in statsData" :key="stat.key" :style="{ '--delay': `${index * 0.1}s` }">
            <div class="stat-card-inner">
              <div class="stat-icon-wrapper" :class="stat.iconClass">
                <t-icon :name="stat.icon" size="28px" />
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ stat.value }}</div>
                <div class="stat-label">{{ stat.label }}</div>
                <div class="stat-trend" :class="stat.trendClass" v-if="stat.trend">
                  <t-icon :name="stat.trendIcon" size="12px" />
                  <span>{{ stat.trend }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 当前学期课程模块 -->
      <div class="current-courses-section">
        <div class="section-header">
          <div class="header-left">
            <h2 class="section-title">
              <t-icon name="book" class="title-icon" />
              当前学期课程
            </h2>
          </div>
          <div class="header-right">
            <t-select
              v-model="currentSemester"
              placeholder="选择学期"
              style="width: 200px;"
              :options="semesterOptions"
              @change="handleSemesterChange"
            />
          </div>
        </div>

        <div class="courses-grid">
          <div class="course-card" v-for="(course, index) in currentCourses" :key="course.id" :style="{ '--delay': `${(index + 5) * 0.1}s` }">
            <div class="course-card-inner">
              <!-- 课程状态标签 -->
              <div class="course-status" :class="getCourseStatusClass(course)">
                {{ getCourseStatusText(course) }}
              </div>

              <!-- 课程基本信息 -->
              <div class="course-header">
                <h3 class="course-name">{{ course.name }}</h3>
                <div class="course-code">{{ course.code }}</div>
              </div>

              <!-- 授课统计信息 - 横向小型卡片 -->
              <div class="course-classes">
                <div class="course-statistics">
                  <div class="stat-card-mini">
                    <div class="stat-icon-mini">
                      <t-icon name="layers" size="16px" />
                    </div>
                    <div class="stat-content-mini">
                      <div class="stat-number-mini">{{ course.classCount }}</div>
                      <div class="stat-label-mini">授课班级</div>
                    </div>
                  </div>
                  <div class="stat-card-mini">
                    <div class="stat-icon-mini">
                      <t-icon name="user" size="16px" />
                    </div>
                    <div class="stat-content-mini">
                      <div class="stat-number-mini">{{ course.studentCount }}</div>
                      <div class="stat-label-mini">学生总数</div>
                    </div>
                  </div>
                  <div class="stat-card-mini">
                    <div class="stat-icon-mini">
                      <t-icon name="team" size="16px" />
                    </div>
                    <div class="stat-content-mini">
                      <div class="stat-number-mini">{{ course.teacherCount || 0 }}</div>
                      <div class="stat-label-mini">教师团队</div>
                    </div>
                  </div>
                </div>
                <div class="class-details">
                  <!-- <t-tooltip
                    :content="formatClassList(course.classDetails).allClassesText"
                    placement="top"
                    theme="light"
                    :show-arrow="true"
                    :disabled="!formatClassList(course.classDetails).hasMore"
                    :delay="300"
                    :destroy-on-close="false"
                    overlay-class-name="professional-dashboard-tooltip"
                  >
                    <span class="details-label">班级详情：</span>
                  </t-tooltip> -->
                  <t-tooltip
                    :content="formatClassList(course.classDetails).allClassesText"
                    placement="top"
                    theme="light"
                    :show-arrow="true"
                    :disabled="!formatClassList(course.classDetails).hasMore"
                    :delay="300"
                    :destroy-on-close="false"
                    overlay-class-name="professional-dashboard-tooltip"
                  >
                    <div class="class-list">
                      <span
                        v-for="classInfo in formatClassList(course.classDetails).displayClasses"
                        :key="classInfo.name"
                        class="class-item"
                      >
                        {{ classInfo.name }} {{ classInfo.count }}人
                      </span>
                      <span
                        v-if="formatClassList(course.classDetails).hasMore"
                        class="class-item class-more"
                      >
                        ...等{{ formatClassList(course.classDetails).remainingCount }}个班级
                      </span>
                    </div>
                  </t-tooltip>
                </div>
              </div>

              <!-- 操作按钮 -->
              <div class="course-actions">
                <t-button
                  theme="primary"
                  variant="base"
                  size="small"
                  class="action-btn"
                  @click="() => handleCourseDetail(course)"
                >
                  <template #icon>
                    <t-icon name="view-module" size="16px" />
                  </template>
                  课程详情
                </t-button>
                <t-button theme="success" variant="base" size="small" class="action-btn">
                  <template #icon>
                    <t-icon name="chart-bar" size="16px" />
                  </template>
                  课程分析
                </t-button>
                <t-button theme="warning" variant="base" size="small" class="action-btn">
                  <template #icon>
                    <t-icon name="setting" size="16px" />
                  </template>
                  内容管理
                </t-button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 课程授课统计模块 -->
      <div class="teaching-stats-section">
        <div class="section-header">
          <div class="header-left">
            <h2 class="section-title">
              <t-icon name="chart-bar" class="title-icon" />
              课程授课统计
            </h2>
            <p class="section-subtitle">历史课程教学统计分析</p>
          </div>
          <div class="header-right">
            <t-input
              v-model="searchKeyword"
              placeholder="搜索课程（课程号/课程名称）"
              style="width: 280px;"
              clearable
              @input="handleSearch"
            >
              <template #prefix-icon>
                <t-icon name="search" />
              </template>
            </t-input>
          </div>
        </div>

        <div class="teaching-grid">
          <div class="teaching-card" v-for="(course, index) in filteredTeachingStats" :key="course.id" :style="{ '--delay': `${(index + 8) * 0.1}s` }">
            <div class="teaching-card-inner">
              <!-- 学分标签 -->
              <div class="course-credits">
                {{ course.credits }}学分
              </div>

              <!-- 课程基本信息 -->
              <div class="teaching-header">
                <h3 class="teaching-name">{{ course.name }}</h3>
                <div class="course-code">{{ course.code }}</div>
              </div>

              <!-- 历史统计信息 - 横向小型卡片 -->
              <div class="history-statistics">
                <div class="stat-card-mini">
                  <div class="stat-icon-mini">
                    <t-icon name="calendar" size="16px" />
                  </div>
                  <div class="stat-content-mini">
                    <div class="stat-number-mini">{{ course.semesterCount }}</div>
                    <div class="stat-label-mini">学期统计</div>
                  </div>
                </div>
                <div class="stat-card-mini">
                  <div class="stat-icon-mini">
                    <t-icon name="user" size="16px" />
                  </div>
                  <div class="stat-content-mini">
                    <div class="stat-number-mini">{{ course.totalStudents }}</div>
                    <div class="stat-label-mini">学生统计</div>
                  </div>
                </div>
                <div class="stat-card-mini">
                  <div class="stat-icon-mini">
                    <t-icon name="layers" size="16px" />
                  </div>
                  <div class="stat-content-mini">
                    <div class="stat-number-mini">{{ course.classCount }}</div>
                    <div class="stat-label-mini">班级统计</div>
                  </div>
                </div>
              </div>

              <!-- 操作按钮 -->
              <div class="teaching-actions">
                <t-button theme="success" variant="base" size="small" class="action-btn">
                  <template #icon>
                    <t-icon name="chart-bar" size="16px" />
                  </template>
                  课程分析
                </t-button>
                <t-button theme="warning" variant="base" size="small" class="action-btn">
                  <template #icon>
                    <t-icon name="setting" size="16px" />
                  </template>
                  内容管理
                </t-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
  Tag as TTag,
  Icon as TIcon,
  Breadcrumb as TBreadcrumb,
  BreadcrumbItem as TBreadcrumbItem,
  Button as TButton,
  Select as TSelect,
  Option as TOption,
  Input as TInput,
  Tooltip as TTooltip
} from 'tdesign-vue-next'
import MajorSelector from './components/MajorSelector.vue'
import { getTeacherSemesterCourses, getTeachingHistoryCourses } from '@/api/training/course'
import SemesterUtils from '@/utils/semesterUtils'
import { storage } from '@/utils/storage';
import { CourseCacheInfo } from '@/types/course'
// 新增：引入获取专业列表的接口
import { getMajorSelectorByTeacher } from '@/api/base/major'
import { getDictLabelByTypeTitle } from '@/utils/dictUtil'

// 类型定义
interface CurrentMajor {
  id?: number | string;
  name?: string;
  type?: string;
}

interface StatItem {
  key: string;
  label: string;
  value: string;
  icon: string;
  iconClass: string;
  trend?: string;
  trendIcon?: string;
  trendClass?: string;
}

interface CourseInfo {
  id: string | number;
  taskId: string | number;
  name: string;
  code: string;
  classCount: number;
  studentCount: number;
  teacherCount: number;
  classDetails: Array<{name: string; count: number}>;
  isCurrentSemester: boolean;
}

interface TeachingCourse {
  id: string;
  name: string;
  code: string;
  credits: number;
  semesterCount: number;
  totalStudents: number;
  avgScore: number;
  classCount: number; // Added for history stats
}

// 路由相关
const route = useRoute()
const router = useRouter()

// 专业选择弹窗状态
const showMajorSelector = ref(false)

// 当前专业信息
const currentMajor = ref<CurrentMajor>({})

// 新增：专业列表
const majorList = ref<MajorSelectorVO[]>([])

// 新增：加载专业列表方法
const loadMajorList = async () => {
  try {
    const { data } = await getMajorSelectorByTeacher()
    console.log('data', data)
    majorList.value = data || []
  } catch (e) {
    majorList.value = []
  }
}

// 当前学期
const currentSemester = ref('')

// 学期选项
const semesterOptions = ref<Array<{label: string; value: string}>>([])

// 搜索关键词
const searchKeyword = ref('')

// 过滤后的课程统计数据
const filteredTeachingStats = computed(() => {
  if (!searchKeyword.value) {
    return teachingStats.value
  }

  const keyword = searchKeyword.value.toLowerCase()
  return teachingStats.value.filter(course =>
    course.name.toLowerCase().includes(keyword) ||
    course.code.toLowerCase().includes(keyword)
  )
})

// 总体统计数据
const statsData = ref<StatItem[]>([
  {
    key: 'total-courses',
    label: '授课总门数',
    value: '24',
    icon: 'book',
    iconClass: 'stat-icon-primary'
  },
  {
    key: 'total-students',
    label: '授课总学生数',
    value: '1,286',
    icon: 'user-group',
    iconClass: 'stat-icon-success'
  },
  {
    key: 'current-courses',
    label: '当前课程门数',
    value: '8',
    icon: 'calendar',
    iconClass: 'stat-icon-warning',
  },
  {
    key: 'current-classes',
    label: '当前班级数',
    value: '15',
    icon: 'layers',
    iconClass: 'stat-icon-error',
  },
  {
    key: 'current-students',
    label: '当前学生人数',
    value: '486',
    icon: 'user',
    iconClass: 'stat-icon-info',
  }
])

// 当前学期课程
const currentCourses = ref<CourseInfo[]>([])

// 课程授课统计
const teachingStats = ref<TeachingCourse[]>([])

// 设置当前专业并可选跳转
const setCurrentMajor = async (major: any, needRoute = false) => {
  // 获取专业类型中文名
  const typeLabel = await getDictLabelByTypeTitle('专业类型', major.discipline || major.type || '')
  currentMajor.value = {
    id: major.majorId || major.id,
    name: major.majorName || major.name,
    type: typeLabel || major.discipline || major.type || ''
  }
  if (needRoute) {
    await router.replace({
      name: 'DashboardTeacher',
      params: { majorId: currentMajor.value.id },
      query: {
        majorName: currentMajor.value.name,
        type: currentMajor.value.type
      }
    })
  }
  await initializeData()
}

// 专业选择处理
const handleMajorSelect = async (major: MajorSelectorVO) => {
  await setCurrentMajor(major, true)
  showMajorSelector.value = false
  console.log('选择专业:', major)
}

// 获取当前学期
const getCurrentSemester = () => {
  return SemesterUtils.getCurrentSemesterShortName()
}

// 生成学期选项列表
const generateSemesterOptions = () => {
  // 从2010年开始到当前年份，按最新到最旧排序
  const options = SemesterUtils.getSemesterOptions()
  
  // 确保没有重复的选项
  const uniqueOptions = options.filter((option, index, self) => 
    index === self.findIndex(o => o.value === option.value)
  )
  
  return uniqueOptions
}

// 学期变更处理
const handleSemesterChange = (value: any) => {
  console.log('学期变更:', value)
  // 学期变更后刷新当前学期课程数据
  loadCurrentSemesterCourses()
}

// 搜索处理
const handleSearch = (value: string) => {
  console.log('搜索关键词:', value)
  // 搜索逻辑已通过computed实现
}

// 加载当前学期课程数据
const loadCurrentSemesterCourses = async () => {
  try {
    // 使用学期工具类解析学期信息
    const semesterInfo = SemesterUtils.parseSemester(currentSemester.value)
    if (!semesterInfo) {
      console.error('学期格式无效:', currentSemester.value)
      return
    }
    
    const response = await getTeacherSemesterCourses(semesterInfo.year, semesterInfo.semester)
    currentCourses.value = response.data.map((course: any) => ({
        id: course.courseId,
        taskId: course.taskId | 0, // 添加taskId字段
        name: course.courseName,
        code: course.courseCode,
        classCount: course.classCount || 0,
        studentCount: course.totalStudents || 0,
        teacherCount: course.teacherCount || 0,
        classDetails: course.classes?.map((cls: any) => ({
          name: cls.className,
          count: cls.studentNumber
        })) || [],
        isCurrentSemester: course.isCurrentSemester || false
      }))
  } catch (error) {
    console.error('加载当前学期课程数据失败:', error)
  }
}

// 加载课程授课历史统计数据
const loadTeachingHistoryCourses = async () => {
  try {
    const response = await getTeachingHistoryCourses()
    teachingStats.value = response.data.map((course: any) => ({
        id: course.courseId.toString(),
        name: course.courseName,
        code: course.courseCode,
        credits: course.courseCredit || 0,
        semesterCount: course.semesterCount || 0,
        totalStudents: course.totalStudents || 0,
        avgScore: 85, // 暂时使用固定值，后续可从后端获取
        classCount: course.classCount || 0 // Added for history stats
      }))
  } catch (error) {
    console.error('加载课程授课历史统计数据失败:', error)
  }
}

// 初始化页面数据
const initializeData = async () => {
  // 生成学期选项列表
  semesterOptions.value = generateSemesterOptions()

  // 设置当前学期
  currentSemester.value = getCurrentSemester()

  console.log('当前专业:', currentMajor.value)
  console.log('当前学期:', currentSemester.value)
  console.log('学期选项:', semesterOptions.value)

  // 加载数据
  await loadCurrentSemesterCourses()
  await loadTeachingHistoryCourses()
}

// 格式化班级列表显示
const formatClassList = (classDetails: Array<{name: string; count: number}>) => {
  const maxDisplay = 2 // 最多显示2个班级

  if (classDetails.length <= 3) {
    // 班级数量少于等于3个时，显示所有班级
    return {
      displayClasses: classDetails,
      hasMore: false,
      remainingCount: 0,
      allClassesText: formatTooltipContent(classDetails)
    }
  } else {
    // 班级数量多于3个时，显示前2个 + 省略
    return {
      displayClasses: classDetails.slice(0, maxDisplay),
      hasMore: true,
      remainingCount: classDetails.length - maxDisplay,
      allClassesText: formatTooltipContent(classDetails)
    }
  }
}

// 格式化Tooltip内容
const formatTooltipContent = (classDetails: Array<{name: string; count: number}>) => {
  const totalStudents = classDetails.reduce((sum, c) => sum + c.count, 0)
  const classListText = classDetails.map(c => `${c.name} ${c.count}人`).join('\n')

  return `共${classDetails.length}个班级，${totalStudents}名学生\n\n${classListText}`
}

// 重新选择专业
const handleReselectMajor = () => {
  showMajorSelector.value = true
}

// 获取课程状态样式类
const getCourseStatusClass = (course: CourseInfo) => {
  // 解析当前选择的学期
  const semesterInfo = SemesterUtils.parseSemester(currentSemester.value)
  if (!semesterInfo) {
    return 'status-completed'
  }
  
  const status = SemesterUtils.getSemesterStatus(semesterInfo.year, semesterInfo.semester)
  
  switch (status) {
    case SemesterUtils.SEMESTER_STATUS.PREPARING:
      return 'status-preparing'
    case SemesterUtils.SEMESTER_STATUS.ONGOING:
      return 'status-active'
    case SemesterUtils.SEMESTER_STATUS.COMPLETED:
      return 'status-completed'
    default:
      return 'status-completed'
  }
}

// 获取课程状态显示文本
const getCourseStatusText = (course: CourseInfo) => {
  // 解析当前选择的学期
  const semesterInfo = SemesterUtils.parseSemester(currentSemester.value)
  if (!semesterInfo) {
    return '已结课'
  }
  
  const status = SemesterUtils.getSemesterStatus(semesterInfo.year, semesterInfo.semester)
  return SemesterUtils.getSemesterStatusDisplayName(status)
}

// 获取当前学期信息（学年和学期）
const parseCurrentSemester = () => {
  // 假设 currentSemester.value 形如 '2024-2025-1' 或 '2025春季学期'，需根据实际格式解析
  // 这里假设 SemesterUtils.parseSemester 返回 { year: '2025', semester: '1' }
  const info = SemesterUtils.parseSemester(currentSemester.value);
  return info || { year: '', semester: '' };
};

// 跳转到课程详情
const handleCourseDetail = (course: CourseInfo) => {
  // 解析当前学期信息
  const semesterInfo = parseCurrentSemester();
  // 构建缓存对象
  const cacheInfo: CourseCacheInfo = {
    courseId: String(course.id),
    courseName: course.name,
    courseCode: String(course.code),
    academicYear: Number(semesterInfo.year),
    semester: Number(semesterInfo.semester)
  };
  // 缓存到 storage
  storage.set('lastCourseDetail', cacheInfo);

  // 正常跳转
  router.push({
    name: 'TeacherCourseOverview',
    params: { taskId: course.taskId, courseId: course.id },
    query: { from: route.fullPath }
  });
};

onMounted(async () => {
  // 先加载专业列表
  await loadMajorList()

  // 1. URL有专业信息
  const majorId = route.params.majorId as string
  const majorName = route.query.majorName as string
  const type = route.query.type as string

  if (majorId && majorName && type) {
    await setCurrentMajor({ id: majorId, name: majorName, type }, false)
    return
  }

  // 2. 只有一个专业，自动选择
  if (majorList.value.length === 1) {
    await setCurrentMajor(majorList.value[0], true)
    return
  }

  // 3. 其它情况弹窗
  showMajorSelector.value = true
})
</script>

<style lang="less" scoped>
.professional-dashboard {
  min-height: 100%;
  background: linear-gradient(135deg, var(--td-brand-color) 0%, var(--td-brand-color-8) 100%);
  position: relative;
  overflow: hidden;

  // 移除所有装饰效果，使用纯净的背景
  .background-decoration {
    display: none;
  }


  .dashboard-header {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding: 24px;
    position: relative;
    z-index: 10;

    .header-content {
      margin: 0 auto;
      width: 100%;

      .breadcrumb-section {
        margin-bottom: 16px;

        :deep(.t-breadcrumb) {
          .t-breadcrumb__item {
            color: var(--td-text-color-secondary);

            &:last-child {
              color: var(--td-text-color-anti);
              font-weight: 500;
            }
          }

          .t-breadcrumb__separator {
            color: var(--td-text-color-placeholder);
          }
        }
      }

      .major-info {
        display: flex;
        justify-content: space-between;
        align-items: flex-end;

        .title-container {
          position: relative;
          display: inline-block;
        }

        .dashboard-title {
          font-size: 32px;
          font-weight: 700;
          color: var(--td-text-color-anti);
          margin: 0;
          text-shadow: 0 2px 10px var(--td-shadow-1);
          padding-right: 60px; // 为右上角标签留出空间
        }

        .major-tag {
          position: absolute;
          top: -4px;
          right: 0;
          backdrop-filter: blur(10px);
          font-size: 10px;

          :deep(.t-tag__text) {
            color: var(--td-brand-color);
            font-weight: 600;
            font-size: 10px;
          }
        }

        .title-actions {
          flex-shrink: 0;

          :deep(.t-button) {
            background: rgba(255, 255, 255, 0.1);
            border-color: rgba(255, 255, 255, 0.3);
            color: var(--td-text-color-anti);

            &:hover {
              background: rgba(255, 255, 255, 0.2);
              border-color: rgba(255, 255, 255, 0.5);
            }
          }
        }
      }
    }
  }

  .dashboard-content {
    margin: 0 auto;
    width: 100%;
    padding: 32px 24px;
    position: relative;
    z-index: 5;

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-end;
      margin-bottom: 32px;

      .header-left {
        flex: 1;

        .section-title {
          font-size: 24px;
          font-weight: 700;
          color: var(--td-text-color-anti);
          margin-bottom: 8px;
          display: flex;
          align-items: center;
          gap: 12px;

          .title-icon {
            color: var(--td-text-color-anti);
            filter: drop-shadow(0 2px 4px var(--td-shadow-1));
          }
        }

        .section-subtitle {
          font-size: 16px;
          color: rgba(255, 255, 255, 0.85);
          margin: 0;
        }
      }

      .header-right {
        flex-shrink: 0;

        :deep(.t-select) {
          .t-input {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);

            .t-input__inner {
              color: var(--td-text-color-anti);

              &::placeholder {
                color: var(--td-text-color-placeholder);
              }
            }

            .t-input__suffix {
              color: var(--td-text-color-secondary);
            }
          }

          &:hover .t-input {
            border-color: rgba(255, 255, 255, 0.5);
          }
        }

        :deep(.t-input) {
          background: rgba(255, 255, 255, 0.1);
          border: 1px solid rgba(255, 255, 255, 0.3);

          .t-input__inner {
            color: var(--td-text-color-anti);

            &::placeholder {
              color: var(--td-text-color-placeholder);
            }
          }

          .t-input__prefix,
          .t-input__suffix {
            color: var(--td-text-color-secondary);
          }

          &:hover {
            border-color: rgba(255, 255, 255, 0.5);
          }
        }
      }
    }
  }

  // 统计卡片样式
  .statistics-section {
    margin-bottom: 48px;
  }

  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 24px;

    .stat-card {
      opacity: 0;
      transform: translateY(30px);
      animation: cardSlideUp 0.8s ease-out forwards;
      animation-delay: var(--delay, 0s);

      .stat-card-inner {
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 16px;
        padding: 24px;
        display: flex;
        align-items: center;
        gap: 16px;
        transition: all 0.3s ease;

        &:hover {
          background: rgba(255, 255, 255, 0.2);
          border-color: rgba(255, 255, 255, 0.3);
          transform: translateY(-2px);
          box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }
      }

      .stat-icon-wrapper {
        width: 60px;
        height: 60px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;

        &.stat-icon-primary {
          background: linear-gradient(135deg, var(--td-brand-color-3), var(--td-brand-color-5));
        }

        &.stat-icon-success {
          background: linear-gradient(135deg, var(--td-success-color-3), var(--td-success-color-5));
        }

        &.stat-icon-warning {
          background: linear-gradient(135deg, var(--td-warning-color-3), var(--td-warning-color-5));
        }

        &.stat-icon-error {
          background: linear-gradient(135deg, var(--td-error-color-3), var(--td-error-color-5));
        }

        &.stat-icon-info {
          background: linear-gradient(135deg, var(--td-gray-color-3), var(--td-gray-color-5));
        }

        :deep(.t-icon) {
          color: white;
        }
      }

      .stat-content {
        flex: 1;

        .stat-number {
          font-size: 28px;
          font-weight: 700;
          color: var(--td-text-color-anti);
          line-height: 1;
          margin-bottom: 4px;
        }

        .stat-label {
          font-size: 14px;
          color: rgba(255, 255, 255, 0.8);
        }

        .stat-trend {
          display: flex;
          align-items: center;
          gap: 4px;
          font-size: 12px;
          margin-top: 4px;

          &.positive {
            color: var(--td-success-color);
          }

          &.negative {
            color: var(--td-error-color);
          }
        }
      }
    }
  }

  // 课程卡片样式
  .current-courses-section,
  .teaching-stats-section {
    margin-bottom: 48px;
  }

  .courses-grid,
  .teaching-grid {
    display: grid;
    gap: 24px;

    // 响应式网格布局：大屏3个 -> 中屏2个 -> 小屏1个
    grid-template-columns: repeat(3, 1fr);

    // 大屏幕 (1200px+): 3列
    @media (min-width: 1200px) {
      grid-template-columns: repeat(3, 1fr);
    }

    // 中屏幕 (768px-1199px): 2列
    @media (max-width: 1199px) and (min-width: 768px) {
      grid-template-columns: repeat(2, 1fr);
      gap: 20px;
    }

    // 小屏幕 (767px以下): 1列
    @media (max-width: 767px) {
      grid-template-columns: 1fr;
      gap: 16px;
    }

    .course-card,
    .teaching-card {
      opacity: 0;
      transform: translateY(30px);
      animation: cardSlideUp 0.8s ease-out forwards;
      animation-delay: var(--delay, 0s);

      .course-card-inner,
      .teaching-card-inner {
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 16px;
        padding: 24px;
        height: 100%;
        transition: all 0.3s ease;
        position: relative;

        &:hover {
          background: rgba(255, 255, 255, 0.2);
          border-color: rgba(255, 255, 255, 0.3);
          transform: translateY(-2px);
          box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }
      }

      .course-status {
        position: absolute;
        top: 16px;
        right: 16px;
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 500;

        &.status-active {
          background: var(--td-success-color);
          color: white;
        }

        &.status-completed {
          background: var(--td-gray-color-6);
          color: white;
        }

        &.status-preparing {
          background: var(--td-warning-color);
          color: white;
        }
      }

      .course-credits {
        position: absolute;
        top: 16px;
        right: 16px;
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 500;
        background: var(--td-brand-color);
        color: white;
      }

      .course-header,
      .teaching-header {
        margin-bottom: 20px;
        padding-right: 80px;

        .course-name,
        .teaching-name {
          font-size: 18px;
          font-weight: 600;
          color: var(--td-text-color-anti);
          margin-bottom: 4px;
          line-height: 1.4;
        }

        .course-code {
          font-size: 13px;
          color: rgba(255, 255, 255, 0.7);
          font-family: 'Monaco', 'Consolas', monospace;
        }
      }

      .course-classes {
        margin-bottom: 20px;

        .course-statistics,
        .history-statistics {
          display: flex;
          gap: 12px;
          margin-bottom: 16px;
          flex-wrap: wrap;

          // 小屏幕下统计卡片的响应式优化
          @media (max-width: 768px) {
            gap: 8px;
          }
        }

        .stat-card-mini {
          display: flex;
          align-items: center;
          gap: 10px;
          background: rgba(255, 255, 255, 0.12);
          border: 1px solid rgba(255, 255, 255, 0.2);
          border-radius: 12px;
          padding: 12px 16px;
          backdrop-filter: blur(8px);
          transition: all 0.3s ease;
          flex: 1;
          min-width: 0;

          &:hover {
            background: rgba(255, 255, 255, 0.18);
            border-color: rgba(255, 255, 255, 0.35);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
          }

          .stat-icon-mini {
            color: rgba(255, 255, 255, 0.8);
            flex-shrink: 0;
          }

          .stat-content-mini {
            flex: 1;
            min-width: 0;

            .stat-number-mini {
              font-size: 18px;
              font-weight: 700;
              color: var(--td-text-color-anti);
              line-height: 1.2;
              margin-bottom: 2px;
            }

            .stat-label-mini {
              font-size: 11px;
              color: rgba(255, 255, 255, 0.7);
              line-height: 1;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }
        }

        .class-details {
          .details-label {
            font-size: 13px;
            color: rgba(255, 255, 255, 0.7);
            margin-right: 8px;
          }

          .class-list {
            display: inline-flex;
            flex-wrap: wrap;
            gap: 8px;

            .class-item {
              background: rgba(255, 255, 255, 0.1);
              border: 1px solid rgba(255, 255, 255, 0.2);
              padding: 2px 8px;
              border-radius: 12px;
              font-size: 12px;
              color: rgba(255, 255, 255, 0.9);

              &.class-more {
                color: rgba(255, 255, 255, 0.6);
                font-style: italic;
              }
            }
          }
        }
      }

      // 历史课程统计样式
      .history-statistics {
        display: flex;
        gap: 12px;
        margin-bottom: 16px;
        flex-wrap: wrap;

        .stat-card-mini {
          display: flex;
          align-items: center;
          gap: 10px;
          background: rgba(255, 255, 255, 0.12);
          border: 1px solid rgba(255, 255, 255, 0.2);
          border-radius: 12px;
          padding: 12px 16px;
          backdrop-filter: blur(8px);
          transition: all 0.3s ease;
          flex: 1;
          min-width: 0;

          &:hover {
            background: rgba(255, 255, 255, 0.18);
            border-color: rgba(255, 255, 255, 0.35);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
          }

          .stat-icon-mini {
            color: rgba(255, 255, 255, 0.8);
            flex-shrink: 0;
          }

          .stat-content-mini {
            flex: 1;
            min-width: 0;

            .stat-number-mini {
              font-size: 18px;
              font-weight: 700;
              color: var(--td-text-color-anti);
              line-height: 1.2;
              margin-bottom: 2px;
            }

            .stat-label-mini {
              font-size: 11px;
              color: rgba(255, 255, 255, 0.7);
              line-height: 1;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }
        }
      }

      .course-actions,
      .teaching-actions {
        display: flex;
        gap: 8px;
        flex-wrap: wrap;
        width: 100%;

        .action-btn {
          border-radius: 8px !important;
          font-weight: 500 !important;
          backdrop-filter: blur(8px) !important;
          transition: all 0.3s ease !important;
          flex: 1 !important;

          &:hover {
            transform: translateY(-2px) !important;
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15) !important;
          }
        }

        :deep(.t-button) {
          background: rgba(255, 255, 255, 0.15) !important;
          border: 1px solid rgba(255, 255, 255, 0.25) !important;
          color: rgba(255, 255, 255, 0.95) !important;

          &:hover {
            background: rgba(255, 255, 255, 0.25) !important;
            border-color: rgba(255, 255, 255, 0.4) !important;
            color: white !important;
          }

          // 不同主题色的微调
          &.t-button--theme-primary {
            background: rgba(67, 97, 238, 0.25) !important;
            border-color: rgba(67, 97, 238, 0.4) !important;

            &:hover {
              background: rgba(67, 97, 238, 0.35) !important;
              border-color: rgba(67, 97, 238, 0.6) !important;
            }
          }

          &.t-button--theme-success {
            background: rgba(0, 168, 112, 0.25) !important;
            border-color: rgba(0, 168, 112, 0.4) !important;

            &:hover {
              background: rgba(0, 168, 112, 0.35) !important;
              border-color: rgba(0, 168, 112, 0.6) !important;
            }
          }

          &.t-button--theme-warning {
            background: rgba(255, 146, 51, 0.25) !important;
            border-color: rgba(255, 146, 51, 0.4) !important;

            &:hover {
              background: rgba(255, 146, 51, 0.35) !important;
              border-color: rgba(255, 146, 51, 0.6) !important;
            }
          }
        }
      }
    }
  }

  // 响应式设计 - 其他非网格元素的响应式调整
  @media (max-width: 768px) {
    .dashboard-header .header-content .major-info {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
    }

    .dashboard-content {
      padding: 24px 16px;
    }

    .section-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
    }

    .stats-grid {
      grid-template-columns: 1fr;
      gap: 16px;
    }

    // 小屏幕下优化统计卡片
    .stat-card-mini {
      padding: 10px 12px !important;
      gap: 8px !important;

      .stat-content-mini {
        .stat-number-mini {
          font-size: 16px !important;
        }

        .stat-label-mini {
          font-size: 10px !important;
        }
      }
    }

    // 小屏幕下优化按钮
    .course-actions,
    .teaching-actions {
      gap: 6px !important;

      .action-btn {
        font-size: 12px !important;

        :deep(.t-icon) {
          font-size: 14px !important;
        }
      }
    }
  }
}

// 动画定义
@keyframes cardSlideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 移除装饰动画，使用纯净背景
// @keyframes subtleFlow {
//   0%, 100% {
//     transform: translateY(0px) translateX(0px) scale(1);
//     opacity: 0.6;
//   }
//   33% {
//     transform: translateY(-5px) translateX(3px) scale(1.008);
//     opacity: 0.7;
//   }
//   66% {
//     transform: translateY(3px) translateX(-2px) scale(0.995);
//     opacity: 0.65;
//   }
// }


</style>

<!-- 教师工作台Tooltip样式 - 现代化风格优化 -->
<style lang="less">
// 教师工作台页面的Tooltip现代化风格 - 与页面卡片保持一致的设计语言
.professional-dashboard-tooltip {
  // Tooltip容器优化 - 主题色渐变设计
  .t-popup__content {
    // 优化主题色渐变背景 - 使用中等深度的颜色
    background:
      linear-gradient(135deg,
        var(--td-brand-color-3) 0%,
        var(--td-brand-color-4) 50%,
        var(--td-brand-color-5) 100%
      ) !important;
    backdrop-filter: blur(12px) saturate(1.2) !important;

    // 简化边框系统
    border: none !important;
    border-radius: 12px !important;

    // 简化阴影系统 - 无边框
    box-shadow:
      0 12px 32px rgba(0, 0, 0, 0.2),
      0 6px 16px rgba(0, 0, 0, 0.12),
      0 2px 8px rgba(0, 0, 0, 0.08) !important;

    // 统一白色字体
    color: rgba(255, 255, 255, 1) !important;
    font-size: 13px !important;
    font-weight: 500 !important;
    max-width: 300px !important;
    white-space: pre-line !important;
    word-break: keep-all !important;
    line-height: 1.6 !important;
    padding: 16px 20px !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;

    // 现代化动画
    transform-origin: center bottom !important;
    animation: modernTooltipAppear 0.4s cubic-bezier(0.34, 1.56, 0.64, 1) !important;

    // 移除顶部光效，避免过亮
    // &::before {
    //   content: '';
    //   position: absolute;
    //   top: 0;
    //   left: 0;
    //   right: 0;
    //   height: 1px;
    //   background: linear-gradient(90deg,
    //     transparent 0%,
    //     rgba(255, 255, 255, 0.6) 50%,
    //     transparent 100%);
    //   border-radius: 12px 12px 0 0;
    // }
  }

  // 简化箭头设计 - 主题色风格，优化位置
  .t-popup__arrow {
    // 调整箭头位置，使其更贴近边框
    transform: translateY(13px) !important;

    &::before {
      border-top-color: var(--td-brand-color-4) !important;
      filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
    }

    &::after {
      border-top-color: var(--td-brand-color-5) !important;
      filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
    }
  }
}

// 现代化Tooltip出现动画
@keyframes modernTooltipAppear {
  0% {
    opacity: 0;
    transform: translateY(8px) scale(0.9);
    filter: blur(4px);
  }
  60% {
    opacity: 0.8;
    transform: translateY(-2px) scale(1.02);
    filter: blur(1px);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
    filter: blur(0);
  }
}
</style>
