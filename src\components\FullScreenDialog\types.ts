export interface FullScreenDialogProps {
  visible: boolean
  header?: string
  confirmBtn?: {
    content?: string
    loading?: boolean
    disabled?: boolean
    theme?: string
  }
  cancelBtn?: {
    content?: string
    theme?: string
  }
  // 动画配置
  animation?: {
    type?: 'fade' | 'slide' | 'scale' | 'zoom' | 'bounce' | 'flip' | 'none'
    duration?: number
    easing?: string
    delay?: number
  }
  // 背景遮罩配置
  overlay?: {
    blur?: boolean
    opacity?: number
    color?: string
  }
}

export interface FullScreenDialogEmits {
  (e: 'update:visible', value: boolean): void
  (e: 'confirm'): void
  (e: 'cancel'): void
  (e: 'close'): void
  (e: 'animation-start'): void
  (e: 'animation-end'): void
}

export interface AnimationConfig {
  type: 'fade' | 'slide' | 'scale' | 'zoom' | 'bounce' | 'flip' | 'none'
  duration: number
  easing: string
  delay: number
}

export interface OverlayConfig {
  blur: boolean
  opacity: number
  color: string
} 