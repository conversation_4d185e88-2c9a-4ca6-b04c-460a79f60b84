/* 成绩管理弹窗样式 */
.grade-management-dialog,
.detailed-grade-management {
  /* 考核内容信息区域 */
  .assessment-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 20px;
    background: var(--td-bg-color-container);
    border-radius: 6px;
    border: 1px solid var(--td-border-level-1-color);

    .info-content {
      h3 {
        margin: 0 0 12px 0;
        font-size: 18px;
        font-weight: 600;
        color: var(--td-text-color-primary);
      }

      .info-meta {
        display: flex;
        gap: 24px;

        .meta-item {
          display: flex;
          align-items: center;

          .label {
            color: var(--td-text-color-secondary);
            margin-right: 8px;
          }

          .value {
            color: var(--td-text-color-primary);
            font-weight: 500;
          }
        }
      }
    }

    .info-actions {
      display: flex;
      gap: 12px;
    }
  }

  /* 统计卡片样式 */
  .stats-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-bottom: 24px;

    .stat-card {
      display: flex;
      align-items: center;
      padding: 20px;
      background: var(--td-bg-color-container);
      border-radius: 8px;
      border: 1px solid var(--td-border-level-1-color);
      transition: all 0.3s ease;

      &:hover {
        box-shadow: var(--td-shadow-2);
        transform: translateY(-2px);
      }

      .stat-icon {
        width: 48px;
        height: 48px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 16px;

        .t-icon {
          font-size: 24px;
          color: white;
        }

        /* 不同统计类型的图标颜色 */
        &.average {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        &.highest {
          background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        &.lowest {
          background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        &.submitted {
          background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }

        &.pending {
          background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }
      }

      .stat-content {
        .stat-value {
          font-size: 24px;
          font-weight: 600;
          color: var(--td-text-color-primary);
          line-height: 1;
          margin-bottom: 4px;
        }

        .stat-label {
          font-size: 14px;
          color: var(--td-text-color-secondary);
        }
      }
    }
  }

  /* 搜索工具栏样式 */
  .search-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding: 16px 20px;
    background: var(--td-bg-color-container);
    border-radius: 6px;
    border: 1px solid var(--td-border-level-1-color);

    .search-section {
      display: flex;
      align-items: center;
      gap: 12px;

      .status-hint {
        font-size: 14px;
        color: var(--td-text-color-secondary);
        white-space: nowrap;
      }
    }

    .filter-section {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .action-buttons {
      display: flex;
      align-items: center;
      gap: 12px;
    }
  }

  /* 成绩表格样式 */
  .grade-table,
  .detailed-grade-table {
    background: var(--td-bg-color-container);
    border-radius: 6px;
    padding: 20px;
    border: 1px solid var(--td-border-level-1-color);

    .total-score {
      font-weight: 600;
      
      &.high-score {
        color: var(--td-success-color);
      }
    }

    /* 行内编辑样式 */
    .inline-edit-cell {
      .score-display {
        cursor: pointer;
        padding: 4px 8px;
        border-radius: 4px;
        transition: all 0.2s ease;

        &:hover {
          background: var(--td-bg-color-page);
          color: var(--td-brand-color);
        }
      }
    }

    /* 详细录入表格特殊样式 */
    :deep(.t-table__content) {
      .t-table__body {
        .score-display:hover {
          background: var(--td-bg-color-page);
          color: var(--td-brand-color);
        }
      }
    }
  }

  /* 表格控制区域样式 */
  .table-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding: 16px 20px;
    background: var(--td-bg-color-container);
    border-radius: 6px;
    border: 1px solid var(--td-border-level-1-color);

    .search-section,
    .filter-section {
      display: flex;
      align-items: center;
      gap: 12px;
    }
  }
}

/* 编辑弹窗样式 */
.direct-grade-edit,
.detailed-grade-edit {
  .student-info {
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 1px solid var(--td-border-level-1-color);

    h4 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: var(--td-text-color-primary);
    }
  }

  .grade-form {
    .form-section {
      margin-bottom: 24px;

      h5 {
        margin: 0 0 16px 0;
        font-size: 14px;
        font-weight: 600;
        color: var(--td-text-color-primary);
      }

      /* 课程目标成绩样式 */
      .objective-grades {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 16px;

        .objective-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 12px;
          background: var(--td-bg-color-page);
          border-radius: 6px;
          border: 1px solid var(--td-border-level-1-color);

          .objective-label {
            font-size: 14px;
            color: var(--td-text-color-primary);
            font-weight: 500;
          }
        }
      }

      /* 题目成绩样式 */
      .question-grades {
        .question-group {
          margin-bottom: 20px;
          padding: 16px;
          background: var(--td-bg-color-page);
          border-radius: 6px;
          border: 1px solid var(--td-border-level-1-color);

          .question-header {
            display: flex;
            align-items: center;
            margin-bottom: 12px;

            .question-number {
              font-weight: 600;
              color: var(--td-brand-color);
              margin-right: 8px;
            }

            .question-type {
              font-size: 12px;
              color: var(--td-text-color-secondary);
              background: var(--td-bg-color-container);
              padding: 2px 8px;
              border-radius: 4px;
            }
          }

          .sub-items {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 12px;

            .sub-item {
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 8px 12px;
              background: var(--td-bg-color-container);
              border-radius: 4px;
              border: 1px solid var(--td-border-level-2-color);

              .sub-label {
                font-size: 13px;
                color: var(--td-text-color-primary);
                font-weight: 500;
              }
            }
          }
        }
      }
    }

    /* 总分显示样式 */
    .total-score-section {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px;
      background: var(--td-bg-color-page);
      border-radius: 6px;
      border: 1px solid var(--td-border-level-1-color);

      .total-label {
        font-size: 16px;
        font-weight: 600;
        color: var(--td-text-color-primary);
      }

      .total-value {
        font-size: 18px;
        font-weight: 700;
        color: var(--td-brand-color);
      }
    }
  }
}

/* 列设置弹窗样式 */
.column-settings {
  padding: 16px 0;
  
  .settings-description {
    margin: 0 0 16px;
    color: var(--td-text-color-secondary);
    font-size: 14px;
  }
  
  .checkbox-group {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    
    .t-checkbox {
      margin-right: 0;
      min-width: 100px;
    }
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .grade-management-dialog,
  .detailed-grade-management {
    .assessment-info {
      flex-direction: column;
      gap: 16px;

      .info-meta {
        flex-direction: column;
        gap: 8px;
      }

      .info-actions {
        flex-wrap: wrap;
      }
    }

    .stats-cards {
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 12px;

      .stat-card {
        padding: 16px;

        .stat-icon {
          width: 40px;
          height: 40px;
          margin-right: 12px;

          .t-icon {
            font-size: 20px;
          }
        }

        .stat-content .stat-value {
          font-size: 20px;
        }
      }
    }

    .search-toolbar {
      flex-direction: column;
      gap: 12px;
      align-items: stretch;

      .filter-section {
        justify-content: center;
      }
    }

    .table-controls {
      flex-direction: column;
      gap: 12px;
    }
  }

  .direct-grade-edit,
  .detailed-grade-edit {
    .grade-form .form-section {
      .objective-grades {
        grid-template-columns: 1fr;
      }

      .question-grades .question-group .sub-items {
        grid-template-columns: 1fr;
      }
    }
  }
}

/* 中等屏幕适配 */
@media (max-width: 1024px) and (min-width: 769px) {
  .grade-management-dialog,
  .detailed-grade-management {
    .stats-cards {
      grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    }

    .search-toolbar {
      .filter-section {
        flex-wrap: wrap;
      }
    }
  }
}

/* 大屏幕优化 */
@media (min-width: 1200px) {
  .grade-management-dialog,
  .detailed-grade-management {
    .stats-cards {
      grid-template-columns: repeat(5, 1fr);
    }

    .grade-table,
    .detailed-grade-table {
      :deep(.t-table) {
        .t-table__header {
          th {
            background: var(--td-bg-color-page);
            font-weight: 600;
          }
        }
      }
    }
  }
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
  .grade-management-dialog,
  .detailed-grade-management {
    .stat-card {
      .stat-icon {
        &.average {
          background: linear-gradient(135deg, #4c63d2 0%, #5a4b8a 100%);
        }

        &.highest {
          background: linear-gradient(135deg, #d17af0 0%, #d54a5f 100%);
        }

        &.lowest {
          background: linear-gradient(135deg, #3d9bfe 0%, #00d2fe 100%);
        }

        &.submitted {
          background: linear-gradient(135deg, #3ad66b 0%, #2ed9c7 100%);
        }

        &.pending {
          background: linear-gradient(135deg, #da5a8a 0%, #dee140 100%);
        }
      }
    }
  }
}

/* 动画效果 */
.stat-card {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 加载状态样式 */
.grade-table,
.detailed-grade-table {
  &.loading {
    position: relative;
    min-height: 200px;

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(255, 255, 255, 0.8);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 10;
    }
  }
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: var(--td-text-color-secondary);

  .empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
  }

  .empty-text {
    font-size: 14px;
    margin-bottom: 16px;
  }

  .empty-action {
    .t-button {
      margin: 0 8px;
    }
  }
}

/* 工具提示样式 */
.tooltip-content {
  max-width: 300px;
  font-size: 12px;
  line-height: 1.4;
}

/* 状态标签样式 */
.status-tag {
  &.submitted {
    background: var(--td-success-color-light);
    color: var(--td-success-color);
    border: 1px solid var(--td-success-color);
  }

  &.pending {
    background: var(--td-warning-color-light);
    color: var(--td-warning-color);
    border: 1px solid var(--td-warning-color);
  }
}

/* 分数显示样式 */
.score-display {
  &.high-score {
    color: var(--td-success-color);
    font-weight: 600;
  }

  &.medium-score {
    color: var(--td-warning-color);
    font-weight: 500;
  }

  &.low-score {
    color: var(--td-error-color);
    font-weight: 500;
  }

  &.score-changed {
    background-color: var(--td-warning-color-light) !important;
    color: var(--td-warning-color) !important;
    font-weight: 600;
    transition: all 0.2s ease;
  }
}

/* 总分变更样式 */
.total-score {
  &.score-changed {
    background-color: var(--td-warning-color-light) !important;
    color: var(--td-warning-color) !important;
    font-weight: 600;
    transition: all 0.2s ease;
    padding: 4px 8px;
    border-radius: 4px;
  }
} 
