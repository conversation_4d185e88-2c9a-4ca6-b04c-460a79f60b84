<template>
    <div class="questionnaire-container">
        <h1>问卷调查</h1>
        <div class="category-container">
            <t-card
                    v-if="categorizedQuestions.single.length > 0"
                    class="category-card"
            >
                <template #header>
                    <div class="category-title">单选题</div>
                </template>
                <div
                        v-for="question in categorizedQuestions.single"
                        :key="question.id"
                        class="question-item"
                >
                    <t-card
                            v-if="question.type === 1"
                            class="question-card"
                            :id="'question-card-' + question.id"
                    >
                        <div class="question-title">
                            {{ question.title }}
                            <span v-if="question.required" class="required-star">*</span>
                        </div>
                        <div class="options-group">
                            <label
                                    v-for="(option, index) in question.options"
                                    :key="index"
                                    class="option-item"
                            >
                                <input
                                        type="radio"
                                        :name="`question-${question.id}`"
                                        :value="option"
                                        v-model="answers[question.id]"
                                />
                                <span class="option-text">{{ option }}</span>
                            </label>
                        </div>
                    </t-card>
                </div>
            </t-card>

            <t-card
                    v-if="categorizedQuestions.multiple.length > 0"
                    class="category-card"
            >
                <template #header>
                    <div class="category-title">多选题</div>
                </template>
                <div
                        v-for="question in categorizedQuestions.multiple"
                        :key="question.id"
                        class="question-item"
                >
                    <t-card
                            v-if="question.type === 2"
                            class="question-card"
                            :id="'question-card-' + question.id"
                    >
                        <div class="question-title">
                            {{ question.title }}
                            <span v-if="question.required" class="required-star">*</span>
                        </div>
                        <div class="options-group">
                            <label
                                    v-for="(option, index) in question.options"
                                    :key="index"
                                    class="option-item"
                            >
                                <input
                                        type="checkbox"
                                        :value="option"
                                        @change="updateCheckbox(question.id, option, ($event.target as HTMLInputElement).checked)"
                                />
                                <span class="option-text">{{ option }}</span>
                            </label>
                        </div>
                    </t-card>
                </div>
            </t-card>

            <t-card
                    v-if="categorizedQuestions.text.length > 0"
                    class="category-card"
            >
                <template #header>
                    <div class="category-title">简答题</div>
                </template>
                <div
                        v-for="question in categorizedQuestions.text"
                        :key="question.id"
                        class="question-item"
                >
                    <t-card
                            v-if="question.type === 3"
                            class="question-card"
                            :id="'question-card-' + question.id"
                    >
                        <div class="question-title">
                            {{ question.title }}
                            <span v-if="question.required" class="required-star">*</span>
                        </div>
                        <textarea
                                v-model="answers[question.id]"
                                class="text-answer"
                                :placeholder="question.required ? '请输入内容（必填）' : '请输入内容（选填）'"
                        ></textarea>
                    </t-card>
                </div>
            </t-card>
        </div>
        <div class="footer-container">
            <button
                    class="submit-btn"
                    :disabled="isSubmitting"
                    @click="submitHandler"
            >
                {{ isSubmitting ? '提交中...' : '提交问卷' }}
            </button>
        </div>
    </div>
</template>

<script setup lang="ts">
import {ref, reactive, onMounted, watch} from 'vue'
import {StudentQuestionInfo, StudentRepliedInfo, StudentRepliedListResult} from "@/api/model/student/studentQuestionl";
import {getQuestionnaireList, putQuestionnaireList} from "@/api/base/student/studentQuestion";
import { computed } from 'vue'
import {useRoute, useRouter} from "vue-router";

const router = useRouter();
const route = useRoute();
const pid = route.query.pid as string;

const questions=ref<StudentQuestionInfo[]>([]);

const categorizedQuestions = computed(() => {
    return {
        single: questions.value.filter(q => q.type === 1),
        multiple: questions.value.filter(q => q.type === 2),
        text: questions.value.filter(q => q.type === 3)
    }
})

const answers = reactive<{
    [key: number]: string | string[]
}>({})

const isSubmitting = ref(false)

// 定义LocalStorage的键名
const STORAGE_KEY = `questionnaire_answers_${pid}`;

// 加载保存的答案
const loadSavedAnswers = () => {
    const savedAnswers = localStorage.getItem(STORAGE_KEY);
    if (savedAnswers) {
        try {
            const parsedAnswers = JSON.parse(savedAnswers);
            // 确保只加载当前问卷的问题答案
            questions.value.forEach(question => {
                if (parsedAnswers[question.id] !== undefined) {
                    answers[question.id] = parsedAnswers[question.id];
                }
            });
        } catch (e) {
            console.error('解析保存的答案失败:', e);
        }
    }
};

// 保存答案到LocalStorage
const saveAnswers = () => {
    // 创建一个只包含当前问卷问题的答案对象
    const answersToSave = {};
    questions.value.forEach(question => {
        if (answers[question.id] !== undefined) {
            answersToSave[question.id] = answers[question.id];
        }
    });

    try {
        localStorage.setItem(STORAGE_KEY, JSON.stringify(answersToSave));
    } catch (e) {
        console.error('保存答案到LocalStorage失败:', e);
    }
};

watch(answers, (newAnswers) => {
    saveAnswers();
}, { deep: true });

const submitHandler = async () => {
    let missingFields: string[] = [];
    let firstMissingQuestion: StudentQuestionInfo | null = null;

    questions.value.some(question => {
        if (question.required) {
            const answer = answers[question.id];
            if (!answer || (Array.isArray(answer) && answer.length === 0)) {
                missingFields.push(question.title);
                if (!firstMissingQuestion) {
                    firstMissingQuestion = question;
                }
                return true;
            }
        }
        return false;
    });

    if (missingFields.length > 0) {
        if (firstMissingQuestion) {
            const element = document.getElementById(
                `question-card-${firstMissingQuestion.id}`
            );
            element?.scrollIntoView({behavior: 'smooth'});
        }

        alert(`请填写以下必填项：\n${missingFields.join('\n')}`);
        return;
    }

    const repliedList: StudentRepliedInfo[] = questions.value.map(question => {
        const answer = answers[question.id];

        const repliedArray = Array.isArray(answer)
            ? answer
            : answer !== undefined ? [answer] : [];

        return {
            id: question.id,
            type: question.type,
            replied: repliedArray
        };
    });

    const payload: StudentRepliedListResult = {
        list: repliedList
    };

    try {
        isSubmitting.value = true;
        console.log('正在提交...', payload);
        await putQuestionnaireList(payload);
        console.log('提交成功，准备跳转');
        // 提交成功后清除保存的答案
        localStorage.removeItem(STORAGE_KEY);
        router.push({ path: '/studentsQuestion/success' });
    } catch (error) {
        console.error('提交失败:', error);
        alert('提交失败，请重试');
        isSubmitting.value = false;
    }
}

const updateCheckbox = (questionId: number, value: string, checked: boolean) => {
    const current = (answers[questionId] as string[]) || []
    if (checked) {
        current.push(value)
    } else {
        const index = current.indexOf(value)
        if (index > -1) {
            current.splice(index, 1)
        }
    }
    answers[questionId] = current
}

const fetchData = async () => {
    try {
        const { list } = await getQuestionnaireList(pid)
        questions.value=list
        loadSavedAnswers();
    } catch (e) {
        console.error('数据获取失败:', e);
    }
};

onMounted(() => {
    fetchData();
});
</script>


<style scoped lang="less">

.questionnaire-container {
  min-height: 100vh;
  padding: 30px 20px 80px;
  background: #f5f7fa;
  position: relative;
}

h1 {
  text-align: center;
  color: #2c3e50;
  margin-bottom: 30px;
  font-size: 2.2em;
  font-weight: 600;
  letter-spacing: 1px;
  position: relative;
  padding-bottom: 15px;

  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: #409eff;
    border-radius: 2px;
  }
}

.category-container {
  display: grid;
  grid-template-columns: 1fr;
  gap: 20px;
}

.category-card {
  border-radius: 12px !important;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 1px solid rgba(64, 158, 255, 0.1);

  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(64,158,255,0.12);
  }

  :deep(.t-card__header) {
    background: linear-gradient(135deg, #409eff, #66b1ff);
    padding: 18px 25px;
  }

  .category-title {
    color: white;
    font-size: 1.2em;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;

    &::before {
      content: '• ';
      margin-right: 8px;
      color: rgba(255,255,255,0.8);
    }
  }
}

.question-card {
  margin: 15px 0;
  border: 1px solid #eef0f3;
  border-radius: 10px !important;
  transition: all 0.2s ease;
  scroll-margin-top: 20px;

  &:hover {
    border-color: #cce0ff;
  }
}

.option-item {
  padding: 12px 15px;
  border-radius: 8px;
  transition: all 0.2s ease;

  input[type="radio"]:checked + .option-text,
  input[type="checkbox"]:checked + .option-text {
    color: #409eff;
    font-weight: 500;
  }
}

.question-title {
  font-size: 1.1em;
  font-weight: 600;
  margin-bottom: 15px;
  color: #2c3e50;
  padding-left: 10px;
  border-left: 4px solid #409eff;
}

.required-star {
  color: #ff4d4d;
  margin-left: 4px;
  font-weight: bold;
}

.options-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
}

.option-item {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 12px;
  border-radius: 6px;
  transition: all 0.2s;

  &:hover {
    background: #e9ecef;
    transform: translateX(4px);
  }

  input[type="radio"],
  input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: #409eff;
  }
}

.option-text {
  font-size: 14px;
  color: #495057;
  font-weight: 500;
}

.text-answer {
  width: 100%;
  min-height: 120px;
  padding: 15px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  resize: vertical;
  font-size: 14px;
  transition: border-color 0.3s;

  &:focus {
    outline: none;
    border-color: #409eff;
    box-shadow: 0 0 0 3px rgba(64,158,255,0.1);
  }
}

.submit-btn {
  width: 240px;
  padding: 16px;
  font-size: 16px;
  border-radius: 12px;
  background: linear-gradient(135deg, #409eff, #66b1ff);
  box-shadow: 0 4px 15px rgba(64,158,255,0.25);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 200%;
    height: 100%;
    background: linear-gradient(
            90deg,
            rgba(255,255,255,0) 25%,
            rgba(255,255,255,0.2) 50%,
            rgba(255,255,255,0) 75%
    );
    transition: left 0.8s;
  }

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 25px rgba(64,158,255,0.35);

    &::before {
      left: 100%;
    }
  }

  &:active {
    transform: translateY(1px);
  }

  &:disabled {
    background: linear-gradient(135deg, #a0cfff, #c5d8ff);
    box-shadow: none;
    cursor: not-allowed;
  }
}

.footer-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20px;
  background: linear-gradient(180deg, rgba(245,247,250,0) 0%, #f5f7fa 30%);
  display: flex;
  justify-content: center;
  z-index: 10;
}

@media (max-width: 768px) {
  .questionnaire-container {
    padding: 20px 15px 70px;
  }

  h1 {
    font-size: 1.8em;
    margin-bottom: 25px;
  }

  .submit-btn {
    width: 100%;
    max-width: 280px;
    padding: 14px;
  }
}
</style>
