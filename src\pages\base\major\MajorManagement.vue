<template>
  <div class="major-management-container">
    <t-card>
      <template #title>
        <div class="page-header">
          <t-button theme="default" variant="text" @click="backToCollegeList">
            <template #icon><t-icon name="chevron-left" /></template>
            返回学院列表
          </t-button>
          <span class="divider">|</span>
          <span>{{ collegeName }} - 专业管理</span>
        </div>
      </template>

      <!-- 搜索区域 -->
      <div class="search-area">
        <t-form ref="searchForm" :data="searchFormData" layout="inline">
          <t-form-item label="专业名称" class="form-item-small">
            <t-select
              v-model="searchFormData.name"
              placeholder="请输入或选择专业名称"
              clearable
              filterable
              allow-input
              :options="majorNameOptions"
            />
          </t-form-item>
          <t-form-item label="专业代码" class="form-item-small">
            <t-select
              v-model="searchFormData.code"
              placeholder="请输入或选择专业代码"
              clearable
              filterable
              allow-input
              :options="majorCodeOptions"
            />
          </t-form-item>
          <t-form-item label="状态" class="form-item-small">
            <t-select
              v-model="searchFormData.status"
              placeholder="请选择状态"
              clearable
              :options="statusOptions"
            />
          </t-form-item>
          <t-form-item>
            <t-space>
              <t-button theme="primary" @click="handleSearch">搜索</t-button>
              <t-button theme="default" @click="resetSearch">重置</t-button>
            </t-space>
          </t-form-item>
        </t-form>
      </div>

      <template #actions>
        <t-space>
          <t-button theme="primary" @click="handleAdd">新增专业</t-button>
          <t-button theme="default" @click="handleImport">导入</t-button>
          <t-button theme="default" @click="handleExport">导出</t-button>
        </t-space>
      </template>

      <t-loading :loading="loading">
        <t-table
          :data="data"
          :columns="columns"
          row-key="id"
          hover
          stripe
          table-layout="fixed"
          :horizontal-scroll-bar="true"
          :max-height="'calc(100vh - 200px)'"
          :pagination="pagination"
        >
          <template #serial-number="{ rowIndex }">
            {{ (pagination.current - 1) * pagination.pageSize + rowIndex + 1 }}
          </template>
          <template #status="{ row }">
            <t-tag
              :theme="row.status === '启用' ? 'success' : 'danger'"
              variant="light"
            >
              {{ row.status }}
            </t-tag>
          </template>
          <template #operation="slotProps">
            <div class="operation-container">
              <t-space size="small">
                <t-button size="small" variant="outline" theme="primary" @click="handleViewDetail(slotProps.row)">
                  <template #icon><t-icon name="browse" /></template>
                  查看
                </t-button>
                <t-button size="small" variant="outline" theme="primary" @click="handleEdit(slotProps.row)">
                  <template #icon><t-icon name="edit" /></template>
                  编辑
                </t-button>
                <t-button
                  size="small"
                  variant="outline"
                  :theme="slotProps.row.status === '启用' ? 'warning' : 'success'"
                  @click="handleToggleStatus(slotProps.row)"
                  style="min-width: 72px;"
                >
                  <template #icon>
                    <t-icon :name="slotProps.row.status === '启用' ? 'close-circle' : 'check-circle'" />
                  </template>
                  {{ slotProps.row.status === '启用' ? '停用' : '启用' }}
                </t-button>
                <t-button size="small" variant="outline" theme="danger" @click="handleDelete(slotProps.row)">
                  <template #icon><t-icon name="delete" /></template>
                  删除
                </t-button>
              </t-space>
            </div>
          </template>
        </t-table>
      </t-loading>
    </t-card>

    <!-- 新增/编辑对话框 -->
    <t-dialog
      v-model:visible="dialogVisible"
      :header="dialogTitle"
      :width="500"
      :footer="false"
      @close="dialogVisible = false"
    >
      <t-form
        ref="formRef"
        :data="formData"
        :rules="rules"
        label-width="100px"
        @submit="handleSubmit"
      >
        <t-form-item label="专业名称" name="name">
          <t-input v-model="formData.name" placeholder="请输入专业名称"></t-input>
        </t-form-item>

        <t-form-item label="专业代码" name="code">
          <t-input v-model="formData.code" placeholder="请输入专业代码"></t-input>
        </t-form-item>

        <t-form-item label="专业类型" name="majorType">
          <!-- 强制显示下拉列表 - 测试版本 -->
          <t-select
            v-model="formData.majorType"
            placeholder="请选择专业类型（下拉列表）"
            clearable
            :options="[
              { label: '工学', value: '1' },
              { label: '理学', value: '2' },
              { label: '文学', value: '3' },
              { label: '管理学', value: '4' },
              { label: '经济学', value: '5' }
            ]"
            style="border: 2px solid red;"
          ></t-select>
          <!-- 调试信息 -->
          <div style="font-size: 12px; color: #666; margin-top: 4px; background: yellow; padding: 4px;">
            🔧 调试: 选项数量 {{ majorTypeOptions.length }}, 当前值: {{ formData.majorType }}
            <t-button size="small" theme="primary" @click="debugMajorType" style="margin-left: 8px;">
              调试
            </t-button>
          </div>
        </t-form-item>

        <t-form-item label="专业负责人" name="directorId">
          <t-select
            v-model="formData.directorId"
            placeholder="请选择专业负责人"
            :options="leaderOptions"
          ></t-select>
        </t-form-item>

        <t-form-item label="状态" name="status">
          <t-radio-group v-model="formData.status">
            <t-radio value="启用">启用</t-radio>
            <t-radio value="停用">停用</t-radio>
          </t-radio-group>
        </t-form-item>

        <t-form-item>
          <t-space>
            <t-button theme="primary" type="submit">确认</t-button>
            <t-button theme="default" variant="base" @click="dialogVisible = false">取消</t-button>
          </t-space>
        </t-form-item>
      </t-form>
    </t-dialog>

    <!-- 导入对话框 -->
    <t-dialog
      v-model:visible="importDialogVisible"
      header="导入专业信息"
      :width="500"
      :footer="false"
      @close="importDialogVisible = false"
    >
      <t-space direction="vertical" style="width: 100%" size="large">
        <t-alert theme="info" message="请先下载导入模板，按照模板格式填写数据后再上传。" />

        <t-button theme="default" @click="downloadTemplate">下载导入模板</t-button>

        <t-upload
          ref="uploadRef"
          theme="file"
          accept=".xlsx,.xls,.csv"
          :files="importFile ? [importFile] : []"
          @change="onImportFileChange"
          :autoUpload="false"
          :showUploadProgress="false"
          :multiple="false"
        >
          <t-button>
            <template #icon><t-icon name="upload" /></template>
            选择文件
          </t-button>
          <template #tips>
            <span>支持 .xlsx、.xls 和 .csv 格式的文件</span>
          </template>
        </t-upload>

        <div v-if="uploading">
          <t-progress :percentage="uploadPercent" :label="false" />
        </div>

        <t-space>
          <t-button theme="primary" @click="confirmImport" :loading="uploading">确认导入</t-button>
          <t-button theme="default" variant="base" @click="importDialogVisible = false" :disabled="uploading">取消</t-button>
        </t-space>
      </t-space>
    </t-dialog>

    <!-- 详情对话框 -->
    <t-dialog
      v-model:visible="detailDialogVisible"
      header="专业详情"
      :width="600"
      :footer="false"
      @close="detailDialogVisible = false"
    >
      <t-loading :loading="detailLoading">
        <div class="detail-container" v-if="detailData">
          <t-descriptions layout="vertical" size="large" bordered>
            <t-descriptions-item label="专业ID">{{ detailData.id }}</t-descriptions-item>
            <t-descriptions-item label="专业名称">{{ detailData.name }}</t-descriptions-item>
            <t-descriptions-item label="专业代码">{{ detailData.code }}</t-descriptions-item>
            <t-descriptions-item label="所属学院">{{ detailData.collegeName }}</t-descriptions-item>
            <t-descriptions-item label="专业负责人">{{ detailData.director }}</t-descriptions-item>
            <t-descriptions-item label="状态">
              <t-tag :theme="detailData.status === '启用' ? 'success' : 'danger'" variant="light">
                {{ detailData.status }}
              </t-tag>
            </t-descriptions-item>
            <t-descriptions-item label="课程数量">{{ detailData.courses || 0 }}</t-descriptions-item>
            <t-descriptions-item label="班级数量">{{ detailData.classes || 0 }}</t-descriptions-item>
            <t-descriptions-item label="学生数量">{{ detailData.students || 0 }}</t-descriptions-item>
            <t-descriptions-item label="创建人">{{ detailData.creator }}</t-descriptions-item>
            <t-descriptions-item label="创建时间">{{ formatDate(detailData.createTime, 'YYYY-MM-DD HH:mm:ss') }}</t-descriptions-item>
            <t-descriptions-item label="更新人">{{ detailData.updater }}</t-descriptions-item>
            <t-descriptions-item label="更新时间">{{ formatDate(detailData.updateTime, 'YYYY-MM-DD HH:mm:ss') }}</t-descriptions-item>
          </t-descriptions>
        </div>
        <t-empty v-else description="暂无详情数据" />
      </t-loading>
      <template #footer>
        <t-space>
          <t-button theme="default" @click="detailDialogVisible = false">关闭</t-button>
        </t-space>
      </template>
    </t-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch, nextTick } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { MessagePlugin, DialogPlugin } from 'tdesign-vue-next';
import { getDictOptionsByTypeTitle } from '@/utils/dictUtil';

import { 
  getMajorList, 
  MajorItem, 
  addMajor, 
  updateMajor, 
  deleteMajor, 
  importMajors, 
  exportMajors, 
  downloadImportTemplate, 
  getMajorDetail 
} from '@/api/system/major';

import { getAcademyDetail } from '@/api/base/academy';
import { formatDate } from '@/utils/date';

const router = useRouter();
const route = useRoute();

// 学院信息
const collegeId = ref(route.params.id);
const collegeName = ref(route.query.name || '');

// 表格数据
const data = ref<MajorItem[]>([]);
const loading = ref(false);

// 搜索条件
const searchFormData = reactive({
  name: '',
  code: '',
  status: ''
});

// 搜索选项
const majorNameOptions = ref<{ label: string; value: string }[]>([]);
const majorCodeOptions = ref<{ label: string; value: string }[]>([]);
const statusOptions = ref([
  { label: '启用', value: '启用' },
  { label: '停用', value: '停用' }
]);

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  pageSizeOptions: [10, 20, 50],
  showJumper: true,
  showPageSize: true,
  onChange: (pageInfo: any) => {
    pagination.current = pageInfo.current;
    pagination.pageSize = pageInfo.pageSize;
    fetchMajorList();
  },
});

// 表格列配置
const columns = [
  { colKey: 'serial-number', title: '序号', width: 80 },
  { colKey: 'id', title: '专业ID', width: 100 },
  { colKey: 'name', title: '专业名称', width: 140 },
  { colKey: 'code', title: '专业代码', width: 120 },
  { colKey: 'director', title: '专业负责人', width: 120 },
  { colKey: 'courses', title: '课程数', width: 80 },
  { colKey: 'classes', title: '班级数', width: 80 },
  { colKey: 'students', title: '学生数', width: 80 },
  { colKey: 'status', title: '状态', width: 100 },
  { 
    colKey: 'createTime', 
    title: '创建时间', 
    width: 140,
    cell: (h: any, { row }: { row: MajorItem }) => formatDate(row.createTime, 'YYYY-MM-DD HH:mm')
  },
  { colKey: 'operation', title: '操作', width: 260, align: 'center' as const },
];

// 专业负责人选项
const leaderOptions = ref<{ label: string; value: string }[]>([]);

// 表单数据
const formData = reactive({
  id: '',
  name: '',
  code: '',
  directorId: '',
  status: '启用',
  collegeId: collegeId.value,
  majorType: '' // 专业类型
});

// 专业类型选项
const majorTypeOptions = ref<{ label: string; value: string | number }[]>([]);

// 表单校验规则
const rules = {
  name: [
    { required: true, message: '请输入专业名称', trigger: 'blur' as const },
    { max: 50, message: '专业名称不能超过50个字符', trigger: 'blur' as const }
  ],
  code: [
    { required: true, message: '请输入专业代码', trigger: 'blur' as const },
    { max: 20, message: '专业代码不能超过20个字符', trigger: 'blur' as const }
  ],
  majorType: [
    { required: true, message: '请选择专业类型', trigger: 'change' as const }
  ],
  directorId: [
    { required: true, message: '请选择专业负责人', trigger: 'change' as const }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' as const }
  ],
};

// 表单引用
const formRef = ref(null);

// 对话框控制
const dialogVisible = ref(false);
const dialogTitle = ref('新增专业');

// 导入对话框控制
const importDialogVisible = ref(false);
const importFile = ref(null);
const uploading = ref(false);
const uploadPercent = ref(0);
const uploadRef = ref(null);

// 详情对话框控制
const detailDialogVisible = ref(false);
const detailLoading = ref(false);
const detailData = ref<MajorItem | null>(null);

// 获取专业列表
const fetchMajorList = async () => {
  loading.value = true;
  try {
    const params = {
      current: pagination.current,
      pageSize: pagination.pageSize,
      collegeId: collegeId.value,
      name: searchFormData.name,
      code: searchFormData.code,
      status: searchFormData.status
    };
    
    const result = await getMajorList(params);
    if (result && result.code === 200 && result.data) {
      data.value = result.data.list;
      pagination.total = result.data.total;
      
      // 更新搜索选项
      updateSearchOptions();
    } else {
      MessagePlugin.error(result?.message || '获取专业列表失败');
    }
  } catch (error: any) {
    console.error('获取专业列表错误:', error);
    MessagePlugin.error(error.message || '获取专业列表失败');
  } finally {
    loading.value = false;
  }
};

// 更新搜索选项
const updateSearchOptions = () => {
  // 提取所有专业名称作为选项
  majorNameOptions.value = [...new Set(data.value.map(item => item.name))]
    .filter(Boolean)
    .map(name => ({ label: name, value: name }));
    
  // 提取所有专业代码作为选项
  majorCodeOptions.value = [...new Set(data.value.map(item => item.code))]
    .filter(Boolean)
    .map(code => ({ label: code, value: code }));
};

// 返回学院列表
const backToCollegeList = () => {
  router.push('/system-college');
};

// 处理搜索
const handleSearch = () => {
  pagination.current = 1; // 重置到第一页
  fetchMajorList();
};

// 重置搜索
const resetSearch = () => {
  searchFormData.name = '';
  searchFormData.code = '';
  searchFormData.status = '';
  pagination.current = 1;
  fetchMajorList();
};

// 打开新增对话框
const handleAdd = async () => {
  dialogTitle.value = '新增专业';

  // 确保专业类型数据已加载
  console.log('打开新增对话框，检查专业类型数据...');
  console.log('当前 majorTypeOptions:', majorTypeOptions.value);

  if (majorTypeOptions.value.length === 0) {
    console.log('专业类型数据为空，重新加载...');
    try {
      majorTypeOptions.value = await getDictOptionsByTypeTitle('专业类型');
      if (majorTypeOptions.value.length === 0) {
        // 使用默认数据
        majorTypeOptions.value = [
          { label: '工学', value: '1' },
          { label: '理学', value: '2' },
          { label: '文学', value: '3' },
          { label: '管理学', value: '4' },
          { label: '经济学', value: '5' }
        ];
      }
    } catch (error) {
      console.error('重新加载专业类型数据失败:', error);
      majorTypeOptions.value = [
        { label: '工学', value: '1' },
        { label: '理学', value: '2' },
        { label: '文学', value: '3' },
        { label: '管理学', value: '4' },
        { label: '经济学', value: '5' }
      ];
    }
  }

  dialogVisible.value = true;

  // 初始化表单数据
  formData.id = '';
  formData.name = '';
  formData.code = '';
  formData.directorId = '';
  formData.status = '启用';
  formData.collegeId = collegeId.value as string;
  formData.majorType = ''; // 重置专业类型

  console.log('对话框打开后，majorTypeOptions:', majorTypeOptions.value);
};

// 打开编辑对话框
const handleEdit = (row: MajorItem) => {
  dialogTitle.value = '编辑专业';
  dialogVisible.value = true;

  // 设置表单数据
  formData.id = row.id as string;
  formData.name = row.name;
  formData.code = row.code;
  formData.directorId = row.directorId as string;
  formData.status = row.status as string;
  formData.collegeId = row.collegeId as string;
  formData.majorType = (row as any).majorType || ''; // 设置专业类型，如果没有则为空
};

// 切换专业状态
const handleToggleStatus = (row: MajorItem) => {
  const newStatus = row.status === '启用' ? '停用' : '启用';
  const confirmDialog = DialogPlugin.confirm({
    header: `${newStatus}确认`,
    body: `确定要${newStatus}专业"${row.name}"吗？`,
    confirmBtn: `确认${newStatus}`,
    cancelBtn: '取消',
    onConfirm: async () => {
      try {
        const res = await updateMajor(row.id, { ...row, status: newStatus });
        if (res.code === 200) {
          MessagePlugin.success(`${newStatus}成功`);
          fetchMajorList();
        } else {
          MessagePlugin.error(res.message || `${newStatus}失败`);
        }
      } catch (error) {
        console.error(`${newStatus}失败:`, error);
        MessagePlugin.error(error.message || `${newStatus}失败`);
      } finally {
        confirmDialog.hide();
      }
    }
  });
};

// 删除专业
const handleDelete = (row: MajorItem) => {
  const confirmDialog = DialogPlugin.confirm({
    header: '删除确认',
    body: `确定要删除专业"${row.name}"吗？`,
    confirmBtn: '确认删除',
    cancelBtn: '取消',
    onConfirm: async () => {
      try {
        const res = await deleteMajor(row.id);
        if (res.code === 200) {
          MessagePlugin.success(res.message || '删除成功');
          fetchMajorList();
        } else {
          MessagePlugin.error(res.message || '删除失败');
        }
      } catch (error) {
        console.error('删除专业失败:', error);
        MessagePlugin.error(error.message || '删除专业失败');
      } finally {
        confirmDialog.hide();
      }
    }
  });
};

// 提交表单
const handleSubmit = async (context: any) => {
  if (context.validateResult === true) {
    // 显示确认弹窗
    const operation = !formData.id ? '新增' : '更新';
    const confirmDialog = DialogPlugin.confirm({
      header: `${operation}确认`,
      body: `确定要${operation}专业"${formData.name}"吗？`,
      confirmBtn: `确认${operation}`,
      cancelBtn: '取消',
      onConfirm: async () => {
        try {
          const params = {
            name: formData.name,
            code: formData.code,
            directorId: formData.directorId,
            collegeId: formData.collegeId,
            status: formData.status,
            majorType: formData.majorType // 添加专业类型字段
          };

          console.log('表单数据:', params, '学院ID:', collegeId.value);

          let res;
          if (!formData.id) {
            // 确保新增专业时设置了学院ID
            if (!params.collegeId && collegeId.value) {
              params.collegeId = collegeId.value;
            }
            
            // 新增专业
            console.log('准备添加专业:', params);
            res = await addMajor(params);
          } else {
            // 更新专业
            res = await updateMajor(formData.id, params);
          }

          if (res && res.code === 200) {
            MessagePlugin.success(res.message || (formData.id ? '更新成功' : '添加成功'));
            dialogVisible.value = false;
            fetchMajorList();
          } else {
            MessagePlugin.error(res.message || (formData.id ? '更新失败' : '添加失败'));
          }
        } catch (error: any) {
          console.error('保存专业失败:', error);
          MessagePlugin.error(error.message || '保存失败');
        } finally {
          confirmDialog.hide();
        }
      }
    });
  } else {
    MessagePlugin.warning(context.firstError);
  }
};

// 导入相关功能
const handleImport = () => {
  importDialogVisible.value = true;
};

// 下载导入模板
const downloadTemplate = async () => {
  try {
    const blob = await downloadImportTemplate();
    
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = '专业导入模板.xlsx';
    link.click();
    
    URL.revokeObjectURL(link.href);
    MessagePlugin.success('模板下载成功');
  } catch (error) {
    console.error('下载模板失败:', error);
    MessagePlugin.error('下载模板失败');
  }
};

// 导入文件改变
const onImportFileChange = (file: any) => {
  importFile.value = file;
};

// 执行导入
const confirmImport = async () => {
  if (!importFile.value) {
    MessagePlugin.warning('请选择要导入的文件');
    return;
  }

  uploading.value = true;
  uploadPercent.value = 0;

  try {
    // 模拟上传进度
    const progressInterval = setInterval(() => {
      if (uploadPercent.value < 90) {
        uploadPercent.value += 10;
      }
    }, 200);

    const formData = new FormData();
    formData.append('file', importFile.value.raw);
    
    try {
      const res = await importMajors(formData, collegeId.value);

      clearInterval(progressInterval);
      uploadPercent.value = 100;

      if (res.code === 200) {
        importDialogVisible.value = false;
        MessagePlugin.success(`导入成功，成功导入${res.data.success}条记录，失败${res.data.fail}条`);

        // 重新加载数据
        fetchMajorList();
      } else {
        MessagePlugin.error(res.message || '导入失败');
      }
    } catch (error) {
      console.error('导入请求失败:', error);
      MessagePlugin.error(error.message || '导入失败，请检查文件格式');
      clearInterval(progressInterval);
    }
  } finally {
    // 重置上传状态
    uploading.value = false;
    uploadPercent.value = 0;
    importFile.value = null;
    if (uploadRef.value) {
      uploadRef.value.uploadValue = null;
    }
  }
};

// 导出专业
const handleExport = async () => {
  try {
    const params = {
      collegeId: collegeId.value,
      name: searchFormData.name || undefined,
      code: searchFormData.code || undefined,
      status: searchFormData.status || undefined,
      fileType: 'xlsx' // 默认导出为Excel格式
    };

    const res = await exportMajors(params);

    if (res instanceof Blob) {
      // 创建下载链接
      const link = document.createElement('a');
      const url = URL.createObjectURL(res);
      link.href = url;
      link.download = `专业列表_${new Date().toISOString().split('T')[0]}.xlsx`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      MessagePlugin.success('导出成功');
    } else {
      MessagePlugin.error('导出失败，返回数据格式错误');
    }
  } catch (error) {
    console.error('导出失败:', error);
    MessagePlugin.error(error.message || '导出失败');
  }
};

// 查看专业详情
const handleViewDetail = async (row: MajorItem) => {
  detailLoading.value = true;
  detailDialogVisible.value = true;
  
  try {
    const res = await getMajorDetail(row.id);
    if (res && res.code === 200 && res.data) {
      detailData.value = res.data;
    } else {
      MessagePlugin.error(res?.message || '获取专业详情失败');
    }
  } catch (error: any) {
    console.error('获取专业详情失败:', error);
    MessagePlugin.error(error.message || '获取专业详情失败');
  } finally {
    detailLoading.value = false;
  }
};

// 初始化
onMounted(async () => {
  // 加载专业负责人选项（实际项目中应该从API获取）
  leaderOptions.value = [
    { label: '张教授', value: '1' },
    { label: '李教授', value: '2' },
    { label: '王教授', value: '3' }
  ];

  // 加载专业类型字典数据
  try {

    majorTypeOptions.value = await getDictOptionsByTypeTitle('专业类型');


    // 如果没有数据，添加一些测试数据
    if (majorTypeOptions.value.length === 0) {
      console.warn('字典中没有专业类型数据，使用默认数据');
      majorTypeOptions.value = [
        { label: '工学', value: '1' },
        { label: '理学', value: '2' },
        { label: '文学', value: '3' },
        { label: '管理学', value: '4' },
        { label: '经济学', value: '5' }
      ];
    }
  } catch (error) {
    console.error('加载专业类型字典数据失败:', error);
    MessagePlugin.error('加载专业类型数据失败，使用默认数据');

    // 加载失败时使用默认数据
    majorTypeOptions.value = [
      { label: '工学', value: '1' },
      { label: '理学', value: '2' },
      { label: '文学', value: '3' },
      { label: '管理学', value: '4' },
      { label: '经济学', value: '5' }
    ];
  }

  fetchMajorList();
});

// 调试函数
const debugMajorType = () => {
  console.log('=== 专业类型调试信息 ===');
  console.log('majorTypeOptions.value:', majorTypeOptions.value);
  console.log('majorTypeOptions.value.length:', majorTypeOptions.value.length);
  console.log('formData.majorType:', formData.majorType);
  console.log('dialogVisible.value:', dialogVisible.value);

  // 强制重新加载字典数据
  console.log('重新加载字典数据...');
  getDictOptionsByTypeTitle('专业类型').then(data => {
    console.log('重新加载的字典数据:', data);
    majorTypeOptions.value = data;
  }).catch(error => {
    console.error('重新加载失败:', error);
  });
};

// 监听专业类型选项变化
watch(majorTypeOptions, (newOptions) => {
  console.log('majorTypeOptions 发生变化:', newOptions);
}, { deep: true });

// 监听对话框显示状态
watch(dialogVisible, (newVisible) => {
  console.log('对话框显示状态变化:', newVisible);
  if (newVisible) {
    console.log('对话框打开时的 majorTypeOptions:', majorTypeOptions.value);
  }
});
</script>

<style lang="less" scoped>
.major-management-container {
  padding: 20px;
}

.page-header {
  display: flex;
  align-items: center;
  
  .divider {
    margin: 0 12px;
    color: rgba(0, 0, 0, 0.3);
  }
}

.search-area {
  margin-bottom: 20px;
  
  .form-item-small {
    width: 180px;
  }
}

.operation-container {
  display: flex;
  justify-content: center;
  
  // Vben风格的按钮样式
  :deep(.t-button) {
    margin: 0 2px;
    border-radius: 4px;
    padding: 0 6px;
    height: 28px;
    font-size: 12px;
    font-weight: 500;
    
    // 确保图标和文字间距合适
    .t-icon {
      margin-right: 2px;
      font-size: 14px;
    }
    
    // 提高按钮颜色的饱和度
    &.t-button--theme-primary {
      background-color: rgba(33, 150, 243, 0.1);
      border-color: #1890ff;
      color: #1890ff;
      
      &:hover {
        background-color: rgba(33, 150, 243, 0.2);
        border-color: #40a9ff;
        color: #40a9ff;
      }
    }
    
    &.t-button--theme-danger {
      background-color: rgba(244, 67, 54, 0.1);
      border-color: #f5222d;
      color: #f5222d;
      
      &:hover {
        background-color: rgba(244, 67, 54, 0.2);
        border-color: #ff4d4f;
        color: #ff4d4f;
      }
    }
    
    &.t-button--theme-warning {
      background-color: rgba(255, 152, 0, 0.1);
      border-color: #fa8c16;
      color: #fa8c16;
      
      &:hover {
        background-color: rgba(255, 152, 0, 0.2);
        border-color: #ffa940;
        color: #ffa940;
      }
    }
    
    &.t-button--theme-success {
      background-color: rgba(76, 175, 80, 0.1);
      border-color: #52c41a;
      color: #52c41a;
      
      &:hover {
        background-color: rgba(76, 175, 80, 0.2);
        border-color: #73d13d;
        color: #73d13d;
      }
    }
  }
  
  // 调整t-space组件内部间距
  :deep(.t-space) {
    gap: 4px !important;
  }
}
</style>
