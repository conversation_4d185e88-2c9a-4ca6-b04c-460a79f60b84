<template>
  <div 
    class="class-card-simple"
    :class="{ 
      'current': isCurrent,
      'clickable': clickable
    }"
    @click="handleClick"
  >
    <!-- 当前班级角标 -->
    <div v-if="isCurrent" class="current-class-corner">
      <div class="corner-content">
        <t-icon name="check-circle-filled" size="12px" />
        <span>当前</span>
      </div>
    </div>

    <div class="card-header">
      <div class="class-name">
        {{ classData.className }}
        <t-tag 
          v-if="isCurrent && showCurrentTag"
          theme="primary" 
          variant="light" 
          size="small" 
          class="current-tag"
        >
          当前班级
        </t-tag>
      </div>
      <div class="student-count">
        <t-icon name="user" />
        {{ classData.studentCount }}人
      </div>
    </div>
    
    <div class="card-content">
      <div class="teacher-info">
        <div class="teacher-name">{{ classData.teacherName }}</div>
        <div class="teacher-title">{{ classData.teacherTitle }}</div>
      </div>
      
      <div class="schedule-info" v-if="classData.scheduleInfo && classData.scheduleInfo.length > 0">
        <div class="schedule-item" v-for="(schedule, index) in classData.scheduleInfo" :key="index">
          <t-icon name="time" />
          <span>{{ schedule.time }}</span>
        </div>
      </div>
      
      <div class="class-stats">
        <div class="stat-item">
          <span class="stat-label">总学时</span>
          <span class="stat-value">{{ classData.totalHours }}h</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">周学时</span>
          <span class="stat-value">{{ classData.weekHours }}h</span>
        </div>
      </div>
    </div>
    
    <div class="card-footer" v-if="showFooter">
      <t-button 
        theme="primary" 
        size="small" 
        :variant="isCurrent ? 'base' : 'outline'"
        @click.stop="handleButtonClick"
        class="action-btn"
      >
        <t-icon :name="isCurrent ? 'check-circle' : 'arrow-right'" />
        {{ isCurrent ? '当前班级' : (actionText || '进入管理') }}
      </t-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { WorklistItem } from '@/api/base/classes'

interface Props {
  classData: WorklistItem
  currentClassId?: string | number
  clickable?: boolean
  showCurrentTag?: boolean
  showFooter?: boolean
  actionText?: string
}

const props = withDefaults(defineProps<Props>(), {
  currentClassId: '',
  clickable: true,
  showCurrentTag: false,
  showFooter: true,
  actionText: ''
})

const emit = defineEmits<{
  'click': [classData: WorklistItem]
  'action': [classData: WorklistItem]
}>()

const isCurrent = computed(() => {
  return String(props.classData.classId) === String(props.currentClassId)
})

const handleClick = () => {
  if (props.clickable) {
    emit('click', props.classData)
  }
}

const handleButtonClick = () => {
  emit('action', props.classData)
}
</script>

<style lang="less" scoped>
.class-card-simple {
  background: var(--td-bg-color-container);
  border: 1px solid var(--td-border-level-1-color);
  border-radius: 8px;
  padding: 16px;
  position: relative;
  transition: all 0.3s ease;

  &.clickable {
    cursor: pointer;

    &:hover {
      border-color: var(--td-brand-color);
      box-shadow: 0 4px 12px rgba(0, 82, 217, 0.1);
      transform: translateY(-2px);
    }
  }

  &.current {
    border: 2px solid var(--td-warning-color);
    box-shadow: var(--td-shadow-2), 0 0 0 4px var(--td-warning-color-1);
    
    .class-name {
      color: var(--td-warning-color);
      font-weight: 600;
    }
  }

  .current-class-corner {
    position: absolute;
    top: -5px;
    left: -5px;
    width: 40px;
    height: 40px;
    overflow: hidden;
    z-index: 4;
    border-top-left-radius: 8px;

    .corner-content {
      position: absolute;
      top: 8px;
      left: -14px;
      width: 60px;
      height: 18px;
      background: linear-gradient(135deg, var(--td-warning-color), var(--td-warning-color-8));
      color: var(--td-text-color-anti);
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 2px;
      font-size: 9px;
      font-weight: 600;
      transform: rotate(-45deg);
      box-shadow: 0 2px 8px rgba(255, 153, 0, 0.3);

      span {
        font-size: 8px;
        letter-spacing: 0.5px;
      }
    }
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 12px;

    .class-name {
      font-size: 16px;
      font-weight: 600;
      color: var(--td-text-color-primary);
      display: flex;
      align-items: center;
      gap: 8px;
      flex-wrap: wrap;
      flex: 1;
      margin-right: 8px;
    }

    .student-count {
      display: flex;
      align-items: center;
      gap: 4px;
      color: var(--td-text-color-secondary);
      font-size: 14px;
      white-space: nowrap;
    }
  }

  .card-content {
    margin-bottom: 16px;

    .teacher-info {
      margin-bottom: 12px;

      .teacher-name {
        font-size: 14px;
        font-weight: 500;
        color: var(--td-text-color-primary);
        margin-bottom: 2px;
      }

      .teacher-title {
        font-size: 12px;
        color: var(--td-text-color-secondary);
      }
    }

    .schedule-info {
      margin-bottom: 12px;

      .schedule-item {
        display: flex;
        align-items: center;
        gap: 4px;
        font-size: 12px;
        color: var(--td-text-color-secondary);
        margin-bottom: 4px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    .class-stats {
      display: flex;
      gap: 16px;

      .stat-item {
        display: flex;
        flex-direction: column;
        align-items: center;

        .stat-label {
          font-size: 12px;
          color: var(--td-text-color-secondary);
          margin-bottom: 2px;
        }

        .stat-value {
          font-size: 14px;
          font-weight: 500;
          color: var(--td-text-color-primary);
        }
      }
    }
  }

  .card-footer {
    .action-btn {
      width: 100%;
      transition: all 0.3s ease;

      &:hover {
        transform: translateX(2px);
      }
    }
  }
}
</style> 