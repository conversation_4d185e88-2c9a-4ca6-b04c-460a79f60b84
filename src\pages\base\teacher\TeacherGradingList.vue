<template>
    <div class="grading-container">
        <!-- 顶部导航 -->
        <div class="page-header">
            <t-button theme="default" @click="goBack">
                <template #icon><t-icon name="arrow-left" /></template>
                返回{{ isExam ? '考试' : '作业' }}管理
            </t-button>
            <h2>{{ assignmentInfo.title }} - 批阅</h2>
        </div>

        <!-- 统计信息 -->
        <t-card class="statistics-card">
            <div class="statistics-grid">
                <div class="statistic-item">
                    <div class="statistic-value">{{ submissionStats.total }}</div>
                    <div class="statistic-label">总人数</div>
                </div>
                <div class="statistic-item">
                    <div class="statistic-value">{{ submissionStats.submitted }}</div>
                    <div class="statistic-label">已提交</div>
                </div>
                <div class="statistic-item">
                    <div class="statistic-value">{{ submissionStats.graded }}</div>
                    <div class="statistic-label">已批阅</div>
                </div>
                <div class="statistic-item">
                    <div class="statistic-value">{{ submissionStats.avgScore.toFixed(1) }}</div>
                    <div class="statistic-label">平均分</div>
                </div>
                <div class="statistic-item">
                    <div class="statistic-value">{{ submissionStats.passRate }}%</div>
                    <div class="statistic-label">及格率</div>
                </div>
            </div>
        </t-card>

        <!-- 过滤器 -->
        <div class="filter-section">
            <t-select v-model="selectedClass" placeholder="全部班级" :options="classOptions" />
            <t-select v-model="submissionFilter" :options="submissionOptions" />
            <t-input v-model="searchKeyword" placeholder="搜索学生" class="search-input">
                <template #prefix-icon><t-icon name="search" /></template>
            </t-input>
            <t-button theme="primary" variant="outline" @click="batchGrading">批量自动批阅</t-button>
        </div>

        <!-- 学生提交列表 -->
        <t-table :data="filteredSubmissions" :columns="columns" :row-key="rowKey" bordered stripe hover
            :loading="loading" :pagination="pagination" @page-change="onPageChange">
            <template #grading-status="{ row }">
                <t-tag :theme="getStatusTheme(row.status)" :variant="row.status === 'graded' ? 'light' : 'dark'">
                    {{ getStatusText(row.status) }}
                </t-tag>
            </template>

            <template #score="{ row }">
                <div v-if="row.status === 'graded'" :class="getScoreClass(row.score)">
                    {{ row.score }}
                </div>
                <span v-else>-</span>
            </template>

            <template #operation="{ row }">
                <t-space>
                    <t-button theme="primary" size="small" :disabled="row.status === 'not_submitted'"
                        @click="gradeSubmission(row)">
                        {{ row.status === 'graded' ? '查看' : '批阅' }}
                    </t-button>
                </t-space>
            </template>
        </t-table>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
    Button as TButton,
    Card as TCard,
    Table as TTable,
    Select as TSelect,
    Input as TInput,
    Tag as TTag,
    Space as TSpace,
    Icon as TIcon,
    MessagePlugin
} from 'tdesign-vue-next'

const route = useRoute()
const router = useRouter()

// 判断是作业还是考试
const isExam = computed(() => route.query.type === 'exam')
const isExamMode = computed(() => route.query.examMode === 'true')
const assignmentId = ref(route.params.id as string)

// 作业/考试信息
const assignmentInfo = ref({
    id: '',
    title: '加载中...',
    type: isExam.value ? 'exam' : 'homework',
    totalScore: 100,
    passScore: 60
})

// 提交状态
const submissionStats = ref({
    total: 0,
    submitted: 0,
    graded: 0,
    avgScore: 0,
    passRate: 0
})

// 表格相关
const loading = ref(false)
const rowKey = 'id'
const selectedClass = ref('all')
const submissionFilter = ref('all')
const searchKeyword = ref('')
const submissions = ref([])

// 班级选项
const classOptions = [
    { label: '全部班级', value: 'all' },
    { label: 'R8软工1班241', value: '241' },
    { label: 'R8软工1班242', value: '242' },
    { label: 'R8软工1班243', value: '243' },
    { label: 'R8软工1班244', value: '244' },
]

// 提交状态过滤器选项
const submissionOptions = [
    { label: '全部状态', value: 'all' },
    { label: '未提交', value: 'not_submitted' },
    { label: '未批阅', value: 'submitted' },
    { label: '已批阅', value: 'graded' },
]

// 表格列定义
const columns = [
    { colKey: 'studentId', title: '学号', width: 120 },
    { colKey: 'name', title: '姓名', width: 100 },
    { colKey: 'className', title: '班级', width: 140 },
    { colKey: 'submissionTime', title: '提交时间', width: 160 },
    { colKey: 'status', title: '状态', width: 100, cell: 'grading-status' },
    { colKey: 'autoGraded', title: '自动批阅', width: 100 },
    { colKey: 'manualGraded', title: '手动批阅', width: 100 },
    { colKey: 'score', title: '总分', width: 80, cell: 'score' },
    { colKey: 'operation', title: '操作', width: 100, fixed: 'right', cell: 'operation' },
]

// 分页设置
const pagination = ref({
    current: 1,
    pageSize: 10,
    total: 0,
    showJumper: true,
    pageSizeOptions: [10, 20, 50],
})

// 返回上一页
const goBack = () => {
    const courseId = route.params.courseId || 'all';
    
    if (isExamMode.value) {
        // 返回试卷管理
        router.push(`/teachers/course/${courseId}/exam`);
    } else if (isExam.value) {
        // 返回试卷管理（旧版本兼容）
        router.push(`/teachers/course/${courseId}/exam`);
    } else {
        // 返回作业管理
        router.push(`/teachers/course/${courseId}/homework`);
    }
}

// 过滤后的提交列表
const filteredSubmissions = computed(() => {
    return submissions.value.filter(item => {
        // 班级过滤
        if (selectedClass.value !== 'all' && item.classId !== selectedClass.value) {
            return false
        }

        // 状态过滤
        if (submissionFilter.value !== 'all' && item.status !== submissionFilter.value) {
            return false
        }

        // 关键词搜索
        if (searchKeyword.value &&
            !item.name.toLowerCase().includes(searchKeyword.value.toLowerCase()) &&
            !item.studentId.includes(searchKeyword.value)) {
            return false
        }

        return true
    })
})

// 批量自动批阅
const batchGrading = () => {
    loading.value = true

    // 模拟批阅过程
    setTimeout(() => {
        let newGraded = 0

        submissions.value.forEach(submission => {
            if (submission.status === 'submitted') {
                submission.status = 'graded'
                submission.autoGraded = '已完成'
                submission.manualGraded = submission.needManual ? '待批阅' : '无需批阅'

                // 如果不需要手动批阅，自动计算总分
                if (!submission.needManual) {
                    submission.score = Math.floor(Math.random() * 41) + 60 // 模拟60-100分
                }

                newGraded++
            }
        })

        // 更新统计信息
        submissionStats.value.graded += newGraded
        updateStatistics()

        loading.value = false
        MessagePlugin.success(`批量批阅完成，共批阅${newGraded}份作业`)
    }, 1500)
}

// 更新统计信息
const updateStatistics = () => {
    const graded = submissions.value.filter(s => s.status === 'graded')
    const totalScore = graded.reduce((sum, s) => sum + (s.score || 0), 0)
    const passCount = graded.filter(s => s.score >= assignmentInfo.value.passScore).length

    submissionStats.value.avgScore = graded.length ? totalScore / graded.length : 0
    submissionStats.value.passRate = graded.length ? Math.round((passCount / graded.length) * 100) : 0
}

// 获取状态显示文本
const getStatusText = (status) => {
    switch (status) {
        case 'not_submitted': return '未提交'
        case 'submitted': return '未批阅'
        case 'graded': return '已批阅'
        default: return status
    }
}

// 获取状态标签主题
const getStatusTheme = (status) => {
    switch (status) {
        case 'not_submitted': return 'danger'
        case 'submitted': return 'warning'
        case 'graded': return 'success'
        default: return 'default'
    }
}

// 获取分数样式
const getScoreClass = (score) => {
    if (score >= 90) return 'score-excellent'
    if (score >= assignmentInfo.value.passScore) return 'score-pass'
    return 'score-fail'
}

// 批阅单个提交
const gradeSubmission = (submission) => {
    const courseId = route.params.courseId || 'all';
    
    router.push({
        path: `/teachers/course/${courseId}/grade-submission/${assignmentId.value}`,
        query: {
            studentId: submission.studentId,
            type: isExam.value ? 'exam' : 'homework',
            examMode: isExamMode.value ? 'true' : undefined
        }
    });
}

// 分页处理
const onPageChange = (pageInfo) => {
    pagination.value.current = pageInfo.current
    pagination.value.pageSize = pageInfo.pageSize
}

// 获取作业/考试信息
const fetchAssignmentInfo = () => {
    loading.value = true

    // 从exam.vue或task.vue页面传过来的ID和类型
    const id = assignmentId.value
    const type = isExam.value ? 'exam' : 'homework'

    // 根据ID从服务器获取具体信息
    fetch(`/api/assignment-info?id=${id}&type=${type}`)
        .then(response => response.json())
        .then(data => {
            if (data.code === 0) {
                assignmentInfo.value = data.data
                fetchSubmissions()
            } else {
                loading.value = false
            }
        })
        .catch(error => {
            console.error('Error:', error)
            loading.value = false
        })
}

// 获取提交列表
const fetchSubmissions = () => {
    // 模拟API调用
    setTimeout(() => {
        // 生成40个学生的提交数据
        const generatedSubmissions = Array.from({ length: 40 }, (_, i) => {
            const id = `${i + 1}`
            const studentId = `2022${String(i + 1).padStart(6, '0')}`
            const name = `学生${i + 1}`
            const classId = ['241', '242', '243', '244'][i % 4]
            const className = `R8软工1班${classId}`

            // 随机状态
            const statusIndex = Math.floor(Math.random() * 10)
            let status, submissionTime, autoGraded, manualGraded, score

            if (statusIndex < 2) {
                // 20% 未提交
                status = 'not_submitted'
                submissionTime = '-'
                autoGraded = '-'
                manualGraded = '-'
                score = null
            } else if (statusIndex < 5) {
                // 30% 已提交未批阅
                status = 'submitted'

                // 随机提交时间
                const date = new Date()
                date.setDate(date.getDate() - Math.floor(Math.random() * 3))
                date.setHours(Math.floor(Math.random() * 24))
                date.setMinutes(Math.floor(Math.random() * 60))
                submissionTime = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`

                autoGraded = '未批阅'
                manualGraded = '未批阅'
                score = null
            } else {
                // 50% 已批阅
                status = 'graded'

                // 随机提交时间（较早）
                const date = new Date()
                date.setDate(date.getDate() - Math.floor(Math.random() * 5) - 2)
                date.setHours(Math.floor(Math.random() * 24))
                date.setMinutes(Math.floor(Math.random() * 60))
                submissionTime = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`

                autoGraded = '已完成'
                manualGraded = Math.random() > 0.7 ? '已完成' : '无需批阅'
                score = Math.floor(Math.random() * 41) + 60 // 60-100分
            }

            // 是否需要手动批阅（有简答题等）
            const needManual = Math.random() > 0.3 // 70%需要手动批阅

            return {
                id,
                studentId,
                name,
                classId,
                className,
                status,
                submissionTime,
                autoGraded,
                manualGraded,
                score,
                needManual
            }
        })

        submissions.value = generatedSubmissions

        // 更新统计数据
        submissionStats.value = {
            total: generatedSubmissions.length,
            submitted: generatedSubmissions.filter(s => s.status !== 'not_submitted').length,
            graded: generatedSubmissions.filter(s => s.status === 'graded').length,
            avgScore: 0,
            passRate: 0
        }

        updateStatistics()
        pagination.value.total = generatedSubmissions.length

        loading.value = false
    }, 800)
}

// 页面加载时获取数据
onMounted(() => {
    fetchAssignmentInfo()
})
</script>

<style scoped lang="less">
.grading-container {
    padding: 20px;
}

.page-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;

    h2 {
        margin: 0 0 0 16px;
    }
}

.statistics-card {
    margin-bottom: 20px;
}

.statistics-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 40px;
}

.statistic-item {
    text-align: center;
    min-width: 100px;
}

.statistic-value {
    font-size: 28px;
    font-weight: 600;
    color: #0052d9;
    margin-bottom: 4px;
}

.statistic-label {
    font-size: 14px;
    color: #666;
}

.filter-section {
    display: flex;
    gap: 16px;
    margin-bottom: 20px;
    flex-wrap: wrap;
    align-items: center;
}

.search-input {
    width: 240px;
}

:deep(.t-select) {
    min-width: 150px;
}

/* 分数样式 */
.score-excellent {
    color: #00a870;
    font-weight: 500;
}

.score-pass {
    color: #0052d9;
}

.score-fail {
    color: #e34d59;
    font-weight: 500;
}

@media (max-width: 768px) {
    .filter-section {
        flex-direction: column;
        align-items: flex-start;
    }

    .search-input,
    :deep(.t-select) {
        width: 100%;
    }
}
</style>