<template>
  <div class="course-teaching-outline">

    <!-- 操作按钮区域 -->
    <div class="course-header">
      <div class="header-info">
        <h3 class="section-title">
          <t-icon name="edit-1" class="title-icon" />
          教学大纲管理
        </h3>
        <p class="section-desc">管理课程的教学内容与学时分配</p>
      </div>
      <div class="header-actions">
        <t-space>
          <t-button 
            v-if="!isEditing" 
            theme="primary" 
            @click="toggleEdit"
          >
            <template #icon><t-icon name="edit" /></template>
            编辑
          </t-button>
          <template v-else>
            <t-button 
              theme="primary" 
              :loading="saving"
              @click="handleSave"
            >
              <template #icon><t-icon name="check" /></template>
              保存
            </t-button>
            <t-button 
              theme="default" 
              @click="handleCancel"
            >
              <template #icon><t-icon name="close" /></template>
              取消
            </t-button>
          </template>
        </t-space>
      </div>
    </div>

    <!-- 教学内容及学时分配 -->
    <div class="teaching-content-section" style="margin-top: 20px;">
      <!-- <div class="section-header">
        <div class="section-title">
          <t-icon name="book" class="title-icon" />
          教学内容及学时分配
        </div>
      </div> -->

      <div class="teaching-table">
        <!-- 理论教学内容分配卡片 -->
        <TheoryTeachingCard
          :data="teachingContents"
          :is-editing="isEditing"
          :course-objectives="props.courseObjectives"
          @update="updateTeachingContents"
        />
      </div>

      <!-- 新增功能模块 -->
      <div class="additional-modules">
        <!-- 实践教学内容分配卡片 -->
        <PracticalTeachingCard
          :data="practicalTeachingData"
          :is-editing="isEditing"
          @update="updatePracticalTeaching"
        />

        <!-- 课程思政教学设计卡片 -->
        <IdeologicalEducationCard
          :data="ideologicalEducationData"
          :teaching-contents="teachingContents"
          :is-editing="isEditing"
          @update="updateIdeologicalEducation"
        />

        <!-- 总学时统计卡片 -->
        <!-- <TotalHoursSummaryCard
          :teaching-contents="teachingContents"
          :practical-teaching="practicalTeachingData"
          :ideological-education="ideologicalEducationData"
        /> -->
      </div>

      <!-- 学时分配总表（表7） -->
      <HoursDistributionTable
        :teaching-contents="teachingContents"
        :practical-teaching="practicalTeachingData"
        :ideological-education="ideologicalEducationData"
      />
    </div>

  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, reactive, watch } from 'vue'
import { MessagePlugin } from 'tdesign-vue-next'
import PracticalTeachingCard from './PracticalTeachingCard.vue'
import TheoryTeachingCard from './TheoryTeachingCard.vue'
import IdeologicalEducationCard from './IdeologicalEducationCard.vue'
import TotalHoursSummaryCard from './TotalHoursSummaryCard.vue'
import HoursDistributionTable from './HoursDistributionTable.vue'
import { CourseObjectiveVO } from '@/api/training/course'

// 教学内容接口
interface TeachingContent {
  id: string
  title: string
  content: string
  requirements: string
  method: string
  format: string
  targets: number[]
  hours: number
  assignment: string
}

// Props
interface Props {
  courseId?: string | number
  initialData?: Record<string, any>
  courseObjectives?: CourseObjectiveVO[]
}

const props = withDefaults(defineProps<Props>(), {
  initialData: () => ({}),
  courseObjectives: () => []
})

// Emits
const emit = defineEmits<{
  save: [data: Record<string, any>]
  reset: []
}>()

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const isEditing = ref(false)

// 教学内容相关数据
const teachingContents = ref(props.initialData.teachingContents || [])

// 课程目标数据
const courseObjectives = ref([
  '掌握Java语言的基本语法和面向对象编程思想',
  '能够使用Java开发简单的应用程序',
  '理解Java程序的运行机制和开发环境',
  '具备解决实际编程问题的能力'
])

// 实践教学内容数据
const practicalTeachingData = ref<any[]>([])

// 课程思政教学设计数据
const ideologicalEducationData = ref<any[]>([])

// 计算属性
const totalHours = computed(() => {
  return teachingContents.value.reduce((sum: number, item: any) => sum + (item.hours || 0), 0)
})

const theoryHours = computed(() => {
  // 这里可以根据教学形式来区分理论和实践学时
  return Math.floor(totalHours.value * 0.7) // 假设70%为理论学时
})

const practiceHours = computed(() => {
  return totalHours.value - theoryHours.value
})

// 方法
const toggleEdit = () => {
  isEditing.value = true
}

const handleSave = async () => {
  try {
    saving.value = true
    
    // 构建保存数据
    const saveData = {
      teachingContents: teachingContents.value
    }
    
    emit('save', saveData)
    isEditing.value = false
    MessagePlugin.success('教学内容保存成功')
  } catch (error) {
    console.error('保存失败:', error)
    MessagePlugin.error('保存失败')
  } finally {
    saving.value = false
  }
}

const handleCancel = () => {
  isEditing.value = false
  // 重置数据
  teachingContents.value = props.initialData.teachingContents || []
  emit('reset')
  MessagePlugin.info('已取消编辑')
}

// 更新理论教学内容
const updateTeachingContents = (data: TeachingContent[]) => {
  if (Array.isArray(data)) {
    teachingContents.value = data
  } else {
    console.warn('理论教学数据格式错误:', data)
    teachingContents.value = []
  }
}

// 更新实践教学内容
const updatePracticalTeaching = (data: any[]) => {
  if (Array.isArray(data)) {
    practicalTeachingData.value = data
  } else {
    console.warn('实践教学数据格式错误:', data)
    practicalTeachingData.value = []
  }
}

// 更新课程思政教学设计
const updateIdeologicalEducation = (data: any[]) => {
  if (Array.isArray(data)) {
    ideologicalEducationData.value = data
  } else {
    console.warn('课程思政数据格式错误:', data)
    ideologicalEducationData.value = []
  }
}

// 初始化数据
const initializeData = () => {
  try {
    if (props.initialData && props.initialData.teachingContents && Array.isArray(props.initialData.teachingContents)) {
      // 确保每个教学内容都有完整的属性
      teachingContents.value = props.initialData.teachingContents.map((item: any) => ({
        id: item.id || `content_${Date.now()}_${Math.random()}`,
        title: item.title || '',
        content: item.content || '',
        requirements: item.requirements || '',
        method: item.method || '',
        format: item.format || '',
        targets: Array.isArray(item.targets) ? item.targets : [],
        hours: typeof item.hours === 'number' ? item.hours : 0,
        assignment: item.assignment || ''
      }))
    } else {
      // 初始状态为空，不设置默认数据
      teachingContents.value = []
    }

    console.log('教学内容初始化完成，数据条数:', teachingContents.value.length)
  } catch (error) {
    console.error('初始化教学内容数据时出错:', error)
    // 出错时设置空数组，避免渲染错误
    teachingContents.value = []
  }
}

// 监听 props 变化
watch(() => props.initialData, (newData) => {
  if (newData && Object.keys(newData).length > 0) {
    teachingContents.value = newData.teachingContents || []
  }
}, { deep: true })

onMounted(() => {
  initializeData()
})
</script>

<style lang="less" scoped>
.course-teaching-outline {
  background: #fff;

  .course-objectives-section {
    margin-bottom: 32px;
    padding: 20px;
    background: var(--td-bg-color-container);
    border-radius: 6px;
    border: 1px solid var(--td-border-level-1-color);

    .section-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 16px;
      font-weight: 600;
      color: var(--td-text-color-primary);
      margin-bottom: 20px;

      .title-icon {
        color: var(--td-brand-color);
        font-size: 18px;
      }
    }

    .objectives-cards {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 16px;

      .objective-card {
        background: #fff;
        border: 1px solid var(--td-border-level-1-color);
        border-radius: 6px;
        padding: 16px;
        transition: all 0.2s ease;

        &:hover {
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          border-color: var(--td-brand-color);
        }

        .card-header {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 12px;

          .objective-number {
            width: 24px;
            height: 24px;
            background: var(--td-brand-color);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 600;
            flex-shrink: 0;
          }

          .objective-label {
            font-weight: 500;
            color: var(--td-text-color-primary);
            flex: 1;
          }

          .status-tag {
            flex-shrink: 0;
          }
        }

        .objective-content {
          color: var(--td-text-color-secondary);
          line-height: 1.5;
          font-size: 14px;
        }
      }
    }
  }

  .course-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 24px 24px 16px;
    border-bottom: 1px solid var(--td-border-level-1-color);

    .header-info {
      .section-title {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 18px;
        font-weight: 600;
        margin: 0 0 8px 0;
        color: var(--td-text-color-primary);

        .title-icon {
          color: var(--td-brand-color);
          font-size: 20px;
        }
      }

      .section-desc {
        margin: 0;
        color: var(--td-text-color-secondary);
        font-size: 14px;
      }
    }

    .header-actions {
      flex-shrink: 0;
    }
  }

  .teaching-content-section {
    padding: 0 24px 24px;
    
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;

      .section-title {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 18px;
        font-weight: 600;
        color: var(--td-text-color-primary);

        .title-icon {
          color: var(--td-brand-color);
          font-size: 20px;
        }
      }
      .header-info {
      font-style: italic;
      color: var(--td-text-color-placeholder);
      font-size: 14px;
      margin-right: 8px;
    }

      .edit-actions {
        display: flex;
        gap: 8px;

        .edit-button {
          margin-right: 8px;
        }

        .save-button,
        .cancel-button {
          min-width: 80px;
        }
      }
    }

    .teaching-table {
      margin-bottom: 24px;

      .empty-content {
        padding: 60px 20px;
        text-align: center;
        background: var(--td-bg-color-container);
        border-radius: 6px;
        border: 1px solid var(--td-border-level-1-color);
      }

      .content-display {
        .content-title {
          font-weight: 600;
          color: var(--td-text-color-primary);
          margin-bottom: 8px;
        }

        .content-details {
          color: var(--td-text-color-secondary);
          line-height: 1.5;
          white-space: pre-line;
        }
      }

      .content-edit {
        .title-input {
          margin-bottom: 8px;
        }

        .content-textarea {
          width: 100%;
        }
      }

      .content-error {
        color: var(--td-text-color-placeholder);
        font-style: italic;
        text-align: center;
        padding: 8px;
      }

      .no-targets {
        color: var(--td-text-color-placeholder);
        font-size: 12px;
      }
    }

    .hours-summary {
      .summary-content {
        display: flex;
        justify-content: space-around;
        align-items: center;
        padding: 16px;

        .summary-item {
          display: flex;
          align-items: center;
          gap: 8px;

          .label {
            font-weight: 500;
            color: var(--td-text-color-secondary);
          }

          .value {
            font-size: 18px;
            font-weight: 600;
            color: var(--td-brand-color);
          }
        }
      }
    }
  }

  .version-history {
    .current-version {
      margin-bottom: 20px;
      text-align: center;
    }

    .version-list {
      max-height: 400px;
      overflow-y: auto;
    }

    .version-item {
      border: 1px solid var(--td-border-level-1-color);
      border-radius: 6px;
      padding: 16px;
      margin-bottom: 12px;

      .version-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        .version-info {
          display: flex;
          align-items: center;
          gap: 12px;

          .version-number {
            font-weight: 600;
            color: var(--td-brand-color);
          }

          .version-time {
            color: var(--td-text-color-secondary);
            font-size: 12px;
          }

          .version-creator {
            color: var(--td-text-color-placeholder);
            font-size: 12px;
          }
        }
      }

      .version-comment {
        color: var(--td-text-color-secondary);
        margin-bottom: 8px;
      }

      .version-stats {
        color: var(--td-text-color-placeholder);
        font-size: 12px;
      }
    }

    .empty-versions {
      text-align: center;
      padding: 40px;
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    padding: 16px;

    .course-objectives-section {
      padding: 16px;

      .objectives-cards {
        grid-template-columns: 1fr;
        gap: 12px;

        .objective-card {
          padding: 12px;

          .card-header {
            .objective-label {
              font-size: 14px;
            }
          }

          .objective-content {
            font-size: 13px;
          }
        }
      }
    }

    .teaching-content-section {
      .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
      }

      .hours-summary .summary-content {
        flex-direction: column;
        gap: 16px;
      }
    }

    .version-history {
      .version-item .version-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
      }
    }
  }
}

:deep(.t-table) {
  .t-table__content {
    .t-table__body {
      .t-table__row {
        .t-table__cell {
          vertical-align: top;
          padding: 12px 8px;
        }
      }
    }
  }

  /* 新增功能模块样式 */
  .additional-modules {
    display: grid;
    grid-template-columns: 1fr;
    gap: 24px;
    margin-bottom: 32px;
  }

  @media (min-width: 1200px) {
    .additional-modules {
      grid-template-columns: 1fr 1fr;

      .total-hours-summary-card {
        grid-column: 1 / -1;
      }
    }
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .additional-modules {
      gap: 16px;
    }
  }
}
</style>
