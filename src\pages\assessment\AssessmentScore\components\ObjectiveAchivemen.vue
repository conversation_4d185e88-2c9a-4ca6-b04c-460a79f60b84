<template>
  <div class="objective-achievement">
    <t-card>
      <div class="chart-title">课程目标达成度详情（courseId: {{ courseId }}）</div>
      <div class="desc">此处可展示课程目标达成度分析内容、图表等。</div>
    </t-card>
  </div>
</template>

<script setup lang="ts">
const props = defineProps<{ courseId: number }>();
</script>

<style scoped>
.objective-achievement {
  width: 100%;
}
.chart-title {
  font-weight: 600;
  font-size: 16px;
  margin-bottom: 8px;
}
.desc {
  color: #666;
  margin-top: 12px;
}
</style>
