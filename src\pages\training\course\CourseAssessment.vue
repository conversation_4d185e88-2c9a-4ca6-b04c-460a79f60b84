<template>
  <div class="course-assessment-container">
    <!-- 头部信息 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-info">
          <h1 class="page-title">课程考核方式</h1>
          <p class="page-subtitle">配置课程考核方式与权重分配</p>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="page-content">
      <t-card>
        <template #title>
          <div class="card-header">
            <t-icon name="check-circle" class="title-icon" />
            <span>课程考核方式配置</span>
          </div>
        </template>
        
        <div class="assessment-config">
          <!-- 考核方式表格 -->
          <t-table
            :data="assessmentMethods"
            :columns="columns"
            :bordered="true"
            :hover="true"
            :loading="loading"
            row-key="id"
          >
            <template #weight="slotProps">
              <t-input-number
                v-model="slotProps.row.weight"
                theme="normal"
                size="small"
                :min="0"
                :max="100"
                :step="5"
                suffix="%"
              />
            </template>
            <template #operation="slotProps">
              <t-space size="small">
                <t-button theme="primary" variant="text" size="small">
                  <template #icon>
                    <t-icon name="edit" />
                  </template>
                  编辑
                </t-button>
                <t-button theme="danger" variant="text" size="small">
                  <template #icon>
                    <t-icon name="delete" />
                  </template>
                  删除
                </t-button>
              </t-space>
            </template>
          </t-table>
          
          <!-- 添加考核方式按钮 -->
          <div class="add-assessment-btn">
            <t-button theme="primary">
              <template #icon>
                <t-icon name="add" />
              </template>
              添加考核方式
            </t-button>
          </div>
        </div>
      </t-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useCourseId } from '@/hooks/useCourseId';

// 使用课程ID钩子函数
const { courseId, loading } = useCourseId({
  onCourseIdChange: (id) => {
    console.log('课程ID变更:', id);
    loadAssessmentData(id);
  }
});

// 表格列配置
const columns = [
  { colKey: 'index', title: '序号', width: 80 },
  { colKey: 'name', title: '考核方式名称', width: 200 },
  { colKey: 'description', title: '考核描述', width: 300 },
  { colKey: 'weight', title: '权重', width: 150 },
  { colKey: 'operation', title: '操作', width: 150 }
];

// 考核方式数据
const assessmentMethods = ref([
  { id: 1, index: 1, name: '期末考试', description: '闭卷笔试，考察课程理论知识', weight: 50 },
  { id: 2, index: 2, name: '课程作业', description: '平时作业完成情况', weight: 20 },
  { id: 3, index: 3, name: '实验报告', description: '实验课实验报告质量', weight: 20 },
  { id: 4, index: 4, name: '课堂表现', description: '课堂参与度和提问回答质量', weight: 10 }
]);

// 加载考核方式数据
const loadAssessmentData = async (id: string | undefined) => {
  if (!id) return;
  
  loading.value = true;
  try {
    console.log('加载课程考核方式数据，课程ID:', id);
    // TODO: 调用API加载数据
    // const response = await api.getCourseAssessments(id);
    // assessmentMethods.value = response.data;
    
    // 模拟加载延迟
    setTimeout(() => {
      loading.value = false;
    }, 500);
  } catch (error) {
    console.error('加载课程考核方式数据失败:', error);
    loading.value = false;
  }
};

onMounted(() => {
  console.log('课程考核方式页面加载，课程ID:', courseId.value);
  if (courseId.value) {
    loadAssessmentData(courseId.value);
  }
});
</script>

<style lang="less" scoped>
.course-assessment-container {
  padding: 20px;
  
  .page-header {
    margin-bottom: 24px;
    
    .header-content {
      .title-info {
        .page-title {
          font-size: 24px;
          font-weight: 600;
          margin: 0 0 8px 0;
        }
        
        .page-subtitle {
          font-size: 14px;
          color: var(--td-text-color-secondary);
          margin: 0;
        }
      }
    }
  }
  
  .page-content {
    .card-header {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .title-icon {
        color: var(--td-brand-color);
      }
    }
    
    .assessment-config {
      margin-top: 16px;
      
      .add-assessment-btn {
        margin-top: 16px;
        text-align: right;
      }
    }
  }
}
</style>
