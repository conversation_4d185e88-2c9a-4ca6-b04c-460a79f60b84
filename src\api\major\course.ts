import request from '@/utils/request';

/**
 * 分页查询课程列表
 * @param params 查询参数
 * @returns Promise<any> // Changed from PageResult<CourseItem> to any
 */
export function getCourseList(params: any): Promise<any> {
  return request.post('/tp/course/list', params);
}

export function getCourseListByLeaderAndPlan(params: any): Promise<any> {
  return request.get('/tp/course/findByLeaderAndPlan', params);
}

/**
 * 添加课程
 * @param data 课程数据
 * @returns Promise<any>
 */
export function addCourse(data: any): Promise<any> {
  return request.post('/tp/course/add', data);
}

/**
 * 更新课程
 * @param data 课程数据
 * @returns Promise<any>
 */
export function updateCourse(data: any): Promise<any> {
  return request.put('/tp/course/mod', data);
}

/**
 * 删除课程（软删除）
 * @param courseId 课程ID
 * @returns Promise<any>
 */
export function deleteCourse(courseId: number | string): Promise<any> {
  return request.delete(`/tp/course/del?courseId=${courseId}`);
}

/**
 * 获取课程详情
 * @param courseId 课程ID
 * @returns Promise<CourseItem> // getCourseDetail can still attempt to return CourseItem
 */
export function getCourseDetail(courseId: number | string): Promise<any> {
  return request.get(`/tp/course/${courseId}`);
}
