<template>
  <t-row :gutter="[24, 24]">
    <t-col :flex="3">
      <t-card class="user-intro" :bordered="false">
        <t-avatar size="80px" class="avatar">s</t-avatar>
        <div class="name">{{ studentInfo?.name || '未设置' }}</div>
        <div class="position">
          <div>
            Hi，<span class="regular">祝你有个美好的一天</span>
          </div>
        </div>
      </t-card>

      <!-- 基本信息卡片 -->
      <t-card class="info-card-essential" title="基本信息" :bordered="false">
        <div class="info-grid">
          <div class="info-item">
            <span class="info-label">姓名:</span>
            <span class="info-value">{{ studentInfo?.name || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">学号:</span>
            <span class="info-value">{{ studentInfo?.number || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">学校:</span>
            <span class="info-value">{{ studentInfo?.school || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">班级:</span>
            <span class="info-value">{{ studentInfo?.class || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">专业:</span>
            <span class="info-value">{{ studentInfo?.specialized || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">当前学期:</span>
            <span class="info-value">{{ studentInfo?.semester || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">联系电话:</span>
            <span class="info-value">{{ studentInfo?.phone  || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">宿舍:</span>
            <span class="info-value">{{ studentInfo?.dormitory  || '-' }}</span>
          </div>

        </div>
      </t-card>

      <!-- 教育经历卡片 -->
      <t-card class="info-card" title="教育经历" :bordered="false">
        <div class="info-grid">
          <div class="info-item">
            <span class="info-label">入学时间:</span>
            <span class="info-value">{{ studentInfo?.matriculation || '-' }}</span>
            <span class="info-label">毕业时间:</span>
            <span class="info-value">{{ studentInfo?.graduate || '-' }}</span>
          </div>
        </div>
      </t-card>

      <!-- 其他信息卡片 -->
      <t-card class="info-card" title="其他信息" :bordered="false">
        <div class="info-grid">
          <div class="info-item">
            <span class="info-label">任职情况:</span>
            <span class="info-value">{{ studentInfo?.posts || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">获奖情况:</span>
            <span class="info-value">{{ studentInfo?.awards || '-' }}</span>
          </div>
        </div>
      </t-card>
    </t-col>
  </t-row>
</template>

<script lang="ts">
export default {
  name: 'UserStudentIndex',
};
</script>

<script setup lang="ts">
import { LineChart } from 'echarts/charts';
import { GridComponent, LegendComponent, TooltipComponent } from 'echarts/components';
import * as echarts from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import {computed, nextTick, onMounted, onUnmounted, ref, watch} from 'vue';
import { useSettingStore } from '@/store';
import { changeChartsTheme } from '@/utils/color';
import {getInformation} from "@/api/base/student/studentInformation";
import {StudentInformationsInfo} from "@/api/model/student/studentInformation";

echarts.use([GridComponent, TooltipComponent, LineChart, CanvasRenderer, LegendComponent]);

let lineContainer: HTMLElement;
let lineChart: echarts.ECharts;
const store = useSettingStore();

const updateContainer = () => {
  lineChart?.resize({
    width: lineContainer.clientWidth,
    height: lineContainer.clientHeight,
  });
};

const studentInfo = ref<StudentInformationsInfo>({
  sid: 0,
  name: '',
  number: '',
  semester: '',
  phone: '',
  school: '',
  class: '',
  specialized: '',
  dormitory: '',
  awards: '',
  posts: '',
  political: '',
  matriculation: '',
  graduate: ''
});

// 获取学生信息
const loadStudentInfo = async () => {
  try {
    const res = await getInformation();
    studentInfo.value = res.list;
    console.log(studentInfo.value)
  } catch (e) {
    console.error('加载学生信息失败:', e);
  }
};

onMounted(() => {
  nextTick(() => {
    loadStudentInfo();
  });
  window.addEventListener('resize', updateContainer, false);
});

onUnmounted(() => {
  window.removeEventListener('resize', updateContainer);
});

watch(
    () => store.brandTheme,
    () => {
      changeChartsTheme([lineChart]);
    },
);
</script>

<style lang="less" scoped>

.info-card {
  margin-bottom: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

  :deep(.t-card__header) {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    padding-bottom: 16px;
    border-bottom: 1px solid #f0f0f0;
  }

  .info-grid {
    display: flex;
    flex-direction: column;
    gap: 16px;
    padding: 8px 0;

    .info-item {
      display: flex;
      align-items: flex-start;
      line-height: 1.6;
      padding: 8px 0;

      .info-label {
        color: #666;
        min-width: 100px;
        margin-right: 12px;
        font-size: 16px;
        font-weight: 500;
      }

      .info-value {
        color: #333;
        flex: 1;
        font-size: 16px;
        word-break: break-word;
      }

      .separator {
        margin: 0 12px;
        color: #999;
        font-size: 16px;
      }
    }
  }
}

.info-card-essential {
  margin-bottom: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

  :deep(.t-card__header) {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    padding-bottom: 16px;
    border-bottom: 1px solid #f0f0f0;
  }

  .info-grid {
    display: flex;
    flex-direction: row;
    gap: 16px;
    padding: 8px 0;
    flex-wrap: wrap;

    .info-item {
      display: flex;
      align-items: flex-start;
      line-height: 1.6;
      padding: 14px 60px;

      .info-label {
        color: #666;
        min-width: 100px;
        margin-right: 12px;
        font-size: 16px;
        font-weight: 500;
      }

      .info-value {
        color: #333;
        flex: 1;
        font-size: 16px;
        word-break: break-word;
      }

      .separator {
        margin: 0 12px;
        color: #999;
        font-size: 16px;
      }
    }
  }
}
</style>
