export interface StudentQuestionListResult {
  code:number
  message:string
  list: Array<StudentQuestionInfo>;
}
export interface StudentQuestionInfo {
  id: number;
  type: number;
  title: string;
  options: string[];
  required: number;
  maxFraction:number//题目本身的分值
}

export interface StudentRepliedListResult {
  code:number
  message:string
  list: Array<StudentRepliedInfo>;
}
export interface StudentRepliedInfo {
  id: number;//题目id
  type:number;//题目类型（单选，多选,文本）
  replied: string[];//学生回答
}

export interface StudentQuestionModifyResult {
  code: number;
  message: string;
  list: Array<StudentQuestionWithAnswer>;
}

export interface StudentQuestionWithAnswer extends StudentQuestionInfo {
  studentAnswer: string | string[];
}


export interface StudentQuestionAnswerResult {
  code: number;
  message: string;
  list: Array<StudentQuestionAnswer>;
}
export interface StudentQuestionAnswer extends StudentQuestionWithAnswer {
  answer: string | string[];
  fraction:number;//得分
}
