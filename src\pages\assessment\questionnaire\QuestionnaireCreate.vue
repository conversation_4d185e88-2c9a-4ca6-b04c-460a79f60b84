<template>
  <div class="questionnaire-create-container">
    <div class="header">
      <t-button variant="outline" @click="goBack">
        <template #icon><t-icon name="arrow-left" /></template>
        返回
      </t-button>
      <h2 class="title">问卷编辑</h2>
    </div>

    <div class="content-wrapper">
      <!-- 左侧问卷表单区域 (60%) -->
      <div class="form-section">
        <t-card title="问卷信息" :bordered="false">
          <t-form ref="formRef" :data="formData" :rules="rules" label-align="top">
            <t-form-item label="问卷名称" name="questionnaire_name" required>
              <t-input v-model="formData.questionnaire_name" placeholder="请输入问卷名称" />
            </t-form-item>

            <t-form-item label="问卷类型" name="questionnaire_type" required>
              <t-select v-model="formData.questionnaire_type" placeholder="请选择问卷类型">
                <t-option v-for="type in questionnaireTypes" :key="type.value" :value="type.value" :label="type.label" />
              </t-select>
            </t-form-item>

            <t-form-item label="已选题目" class="selected-questions">
              <t-table
                :data="selectedQuestions"
                :columns="selectedColumns"
                row-key="question_id"
                size="small"
                :pagination="{ defaultPageSize: 5 }"
              >
                <template #operation="{ row }">
                  <t-link theme="danger" @click="removeQuestion(row.question_id)">移除</t-link>
                </template>
                <template #score="{ row }">
                  <t-input-number v-model="row.score" :min="0" size="small" />
                </template>
              </t-table>
            </t-form-item>
          </t-form>
        </t-card>

        <div class="action-buttons">
          <t-button theme="primary" @click="submitForm">保存问卷</t-button>
          <t-button variant="outline" @click="goBack">取消</t-button>
        </div>
      </div>

      <!-- 右侧题目选择区域 (40%) -->
      <div class="question-section">
        <t-card title="题目库" :bordered="false">
          <div class="question-toolbar">
            <t-space>
              <t-input v-model="searchQuery" placeholder="搜索题目" clearable @clear="handleSearchClear">
                <template #prefix-icon>
                  <t-icon name="search" />
                </template>
              </t-input>

              <!-- 新增导入按钮 -->
              <t-button theme="default" @click="showImportDialog = true">
                <template #icon><t-icon name="upload" /></template>
                导入题集
              </t-button>

              <t-button theme="default" @click="toggleEditMode">
                <template #icon><t-icon :name="isEditMode ? 'check' : 'edit'" /></template>
                {{ isEditMode ? '退出编辑' : '编辑题目' }}
              </t-button>

              <t-button theme="primary" @click="showCreateQuestionDialog">
                <template #icon><t-icon name="add" /></template>
                新建题目
              </t-button>
            </t-space>
          </div>

          <!-- 添加导入对话框 -->
          <t-dialog
            v-model:visible="showImportDialog"
            header="导入题集"
            width="600px"
            :on-confirm="handleImport"
            :on-close="() => showImportDialog = false"
          >
            <div class="import-dialog-content">
              <t-upload
                ref="uploadRef"
                action="/api/questionBank/import"
                :auto-upload="false"
                :before-upload="beforeUpload"
                accept=".json,.xlsx,.csv"
                theme="file"
                :files="files"
                :on-change="handleFileChange"
              ></t-upload>
            </div>
          </t-dialog>

          <!-- <t-collapse-transition :visible="showFilter">
            <div class="filter-panel">
              <t-form layout="inline">
                <t-form-item label="题目类型">
                  <t-select v-model="filterParams.type" clearable placeholder="全部类型">
                    <t-option v-for="type in questionTypes" :key="type.value" :value="type.value" :label="type.label" />
                  </t-select>
                </t-form-item>

              </t-form>
            </div>
          </t-collapse-transition> -->

          <t-table
            :data="filteredQuestions"
            :columns="questionColumns"
            row-key="question_id"
            size="small"
            :pagination="{ defaultPageSize: 10 }"
            :loading="loading"
            @row-click="handleQuestionClick"
          >
            <template #operation="{ row }">
              <t-space v-if="!isEditMode">
                <t-button size="small" @click.stop="addQuestion(row)">添加</t-button>
              </t-space>
              <t-space v-else>
                <t-button size="small" variant="outline" @click.stop="showEditQuestionDialog(row)">编辑</t-button>
                <t-button size="small" theme="danger" variant="outline" @click.stop="handleDeleteQuestion(row)">删除</t-button>
              </t-space>
            </template>
          </t-table>
        </t-card>
      </div>
    </div>

    <!-- 新建题目对话框 -->
    <t-dialog
      v-model:visible="showQuestionDialog"
      header="新建题目"
      width="700px"
      :on-confirm="createQuestion"
      :on-close="closeQuestionDialog"
    >
      <t-form ref="questionFormRef" :data="newQuestion" label-align="top" :rules="questionRules">
        <t-form-item label="题目内容" name="question_content" required>
          <t-input v-model="newQuestion.question_content" placeholder="请输入题目内容" />
        </t-form-item>

        <t-form-item label="题目类型" name="question_type" required>
          <t-select v-model="newQuestion.question_type" placeholder="请选择题目类型">
            <t-option v-for="type in apiQuestionTypes" :key="type.value" :value="type.value" :label="type.label" />
          </t-select>
        </t-form-item>

        <t-form-item label="是否必填" name="is_required">
          <t-radio-group v-model="newQuestion.is_required">
            <t-radio :value="1">是</t-radio>
            <t-radio :value="0">否</t-radio>
          </t-radio-group>
        </t-form-item>

        <t-form-item label="选项设置" v-if="showOptionsField" name="options">
          <div v-for="(option, index) in questionOptions" :key="index" class="option-item">
            <t-input v-model="questionOptions[index]" :placeholder="`选项 ${index + 1}`" clearable>
              <template #suffix>
                <t-button variant="text" @click="removeOption(index)">
                  <t-icon name="close" />
                </t-button>
              </template>
            </t-input>
          </div>
          <t-button variant="dashed" @click="addOption">
            <t-icon name="add" />
            添加选项
          </t-button>
          <div class="option-format-tip">多个选项用英文分号(;)分隔</div>
        </t-form-item>
      </t-form>
    </t-dialog>
    <!-- 新增修改题目对话框 -->
    <t-dialog
      v-model:visible="showEditDialog"
      header="修改题目"
      width="700px"
      :on-confirm="updateQuestion"
      :on-close="closeEditDialog"
    >
      <t-form ref="editFormRef" :data="editQuestion" label-align="top" :rules="questionRules">
        <t-form-item label="题目内容" name="question_content" required>
          <t-input v-model="editQuestion.question_content" placeholder="请输入题目内容" />
        </t-form-item>

        <t-form-item label="题目类型" name="question_type" required>
          <t-select v-model="editQuestion.question_type" placeholder="请选择题目类型" :disabled="false">
            <t-option v-for="type in apiQuestionTypes" :key="type.value" :value="type.value" :label="type.label" />
          </t-select>
        </t-form-item>

        <t-form-item label="是否必填" name="is_required">
          <t-radio-group v-model="editQuestion.is_required">
            <t-radio :value="1">是</t-radio>
            <t-radio :value="0">否</t-radio>
          </t-radio-group>
        </t-form-item>

        <t-form-item label="选项设置" v-if="showEditOptionsField" name="options">
          <div v-for="(option, index) in editQuestionOptions" :key="index" class="option-item">
            <t-input v-model="editQuestionOptions[index]" :placeholder="`选项 ${index + 1}`" clearable>
              <template #suffix>
                <t-button variant="text" @click="removeEditOption(index)">
                  <t-icon name="close" />
                </t-button>
              </template>
            </t-input>
          </div>
          <t-button variant="dashed" @click="addEditOption">
            <t-icon name="add" />
            添加选项
          </t-button>
          <div class="option-format-tip">多个选项用英文分号(;)分隔</div>
        </t-form-item>
      </t-form>
    </t-dialog>
  </div>
</template>

<script setup lang="ts">
import {ref, reactive, computed, onMounted, h, watch} from 'vue';
import { useRouter } from 'vue-router';
import {MessagePlugin, UploadFile,type FormRules,type FormInstanceFunctions} from 'tdesign-vue-next';

const router = useRouter();


// 新增的导入相关状态和方法
const showImportDialog = ref(false);
const files = ref<UploadFile[]>([]);
const uploadRef = ref(null);
const importMode = ref('append');
const duplicateMode = ref('skip');
const uploadTips = '支持Excel文件，文件大小不超过10MB';
//支持JSON、Excel、CSV格式文件，文件大小不超过10MB

// 文件上传前校验
const beforeUpload = (file:File) => {
  const allowedTypes = [
    'application/json',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'text/csv'
  ];

  const isAllowed = allowedTypes.includes(file.type) ||
    ['.json', '.xlsx', '.csv'].some(ext => file.name.endsWith(ext));

  if (!isAllowed) {
    MessagePlugin.error('请上传Excel文件');
    //请上传JSON、Excel或CSV格式的文件
    return false;
  }

  if (file.size > 10 * 1024 * 1024) {
    MessagePlugin.error('文件大小不能超过10MB');
    return false;
  }

  return true;
};

// 文件变化处理
const handleFileChange = (fileList : UploadFile[]) => {
  files.value = fileList;
};


// 处理导入
const handleImport = async () => {
  if (files.value.length === 0) {
    MessagePlugin.error('请选择要导入的文件');
    return;
  }

  try {
    const file = files.value[0].raw;
    const formData = new FormData();
    formData.append('file', file);
    formData.append('importMode', 'append');
    formData.append('duplicateMode', 'skip');

    // 模拟API调用
    MessagePlugin.loading('正在导入题集...');
    // const res = await importQuestionSet(formData);

    // 模拟处理延迟
    await new Promise(resolve => setTimeout(resolve, 1500));

    // 模拟导入的数据
    const importedQuestions = [
      {
        question_id: Math.max(...allQuestions.value.map(q => q.question_id)) + 1,
        question_name: '相同的题目1',
        type: 'single_choice',
      },
      {
        question_id: Math.max(...allQuestions.value.map(q => q.question_id)) + 2,
        question_name: '相同的题目1', // 相同题目内容但不同类型
        type: 'multiple_choice',

      },
      {
        question_id: Math.max(...allQuestions.value.map(q => q.question_id)) + 3,
        question_name: '相同的题目2',
        type: 'single_choice', // 相同题目内容和类型

      }
    ];

    let duplicateCount = 0;
    const actuallyImportedQuestions = [];

    importedQuestions.forEach(question => {
      // 只有当题目内容和类型都相同时才认为是重复题目
      const isDuplicate = allQuestions.value.some(q =>
        q.question_name === question.question_name &&
        q.type === question.type
      );

      if (!isDuplicate) {
        allQuestions.value.push(question);
        actuallyImportedQuestions.push(question);
      } else {
        duplicateCount++;
      }
    });

    let message = `成功导入${actuallyImportedQuestions.length}个题目`;
    if (duplicateCount > 0) {
      message += `，已过滤重复的${duplicateCount}条题目`;
    }

    MessagePlugin.success(message);
    showImportDialog.value = false;
    files.value = [];
  } catch (error) {
    MessagePlugin.error('导入题集失败');
    console.error(error);
  }
};

// 问卷类型选项
const questionnaireTypes = [
  { value: 1, label: '学生问卷' },
  { value: 2, label: '教师问卷' },
  { value: 3, label: '毕业生问卷' },
];

// 题目类型选项
const questionTypes = [
  { value: 'single_choice', label: '单选题' },
  { value: 'multiple_choice', label: '多选题' },
  { value: 'text', label: '文本题' }
];

// 表单数据
const formData = reactive<Questionnaire>({
  questionnaire_name: '',
  questionnaire_type: null,
  // graduation_year: '',
});

// 表单验证规则
const rules: FormRules<Questionnaire> = {
  questionnaire_name: [{ required: true, message: '请输入问卷名称', type: 'error' }],
  questionnaire_type: [{ required: true, message: '请选择问卷类型', type: 'error' }],
};

// 已选题目相关
const selectedQuestions = ref([]);
const selectedColumns = [
  { colKey: 'question_name', title: '题目内容', ellipsis: true },
  {
    colKey: 'type',
    title: '类型',
    width: 100,
    cell: (h, { row }) => {
      const type = questionTypes.find(t => t.value === row.type);
      return type ? type.label : '未知';
    }
  },
  { colKey: 'score', title: '分值', width: 120, slots: { customRender: 'score' } },
  { colKey: 'operation', title: '操作', width: 80, slots: { customRender: 'operation' } },
];


// 题目库相关
const allQuestions = ref([]);
const loading = ref(false);
const searchQuery = ref('');
const showFilter = ref(false);
const filterParams = reactive({
  type: null,
  dateRange: [],
});




const questionColumns = [
  { colKey: 'question_name', title: '题目内容', ellipsis: true },
  {
    colKey: 'type',
    title: '类型',
    width: 100,
    cell: (h, { row }) => {
      const type = questionTypes.find(t => t.value === row.type);
      return type ? type.label : '未知';
    }
  },
  //{ colKey: 'create_time', title: '创建时间', width: 120 },
  { colKey: 'operation', title: '操作', width: 120, slots: { customRender: 'operation' } },
];

// 新建题目相关
const showQuestionDialog = ref(false);
// 新增的题目类型定义（与接口保持一致）
const apiQuestionTypes = [
  { value: 1, label: '单选题' },
  { value: 2, label: '多选题' },
  //{ value: 3, label: '填空题' },
  //{ value: 4, label: '评分题' },
  { value: 3, label: '问答题' },
];

// 训练目标选项（示例）
const trainingGoals = [
  { value: 1, label: '知识掌握' },
  { value: 2, label: '技能提升' },
  { value: 3, label: '态度培养' },
];

// 适用角色选项（示例）
const applicableRoles = [
  { value: 1, label: '学生' },
  { value: 2, label: '教师' },
  { value: 3, label: '管理员' },
];

// 新建题目表单验证规则
const questionRules = {
  question_content: [{ required: true, message: '请输入题目内容', type: 'error' }],
  question_type: [{ required: true, message: '请选择题目类型', type: 'error' }],
  options: [
    {
      validator: (val: string) => {
        if (!showOptionsField.value) return true;
        return val && val.split(';').filter(opt => opt.trim()).length >= 2;
      },
      message: '至少需要两个有效选项',
      type: 'error'
    }
  ]
};
const newQuestion = reactive<QuestionnaireFormData>({
  question_content: '',
  question_type: null,
  //training_goal_id: undefined,
  //applicable_role_id: undefined,
  is_required: 1,
  options: '',
  status: 1
});

// 用于选项编辑的临时数组
const questionOptions = ref<string[]>(['', '']);


// 计算是否显示选项字段
const showOptionsField = computed(() => {
  return newQuestion.question_type === 1 || newQuestion.question_type === 2;
});


// 观察题目类型变化
watch(() => newQuestion.question_type, (newVal) => {
  if (newVal === 1 || newVal === 2) {
    // 如果是选择题，初始化选项
    questionOptions.value = ['', ''];
    newQuestion.options = '';
  } else {
    // 非选择题，清空选项
    questionOptions.value = [];
    newQuestion.options = '';
  }
});

// 添加选项
const addOption = () => {
  questionOptions.value.push('');
};



// 移除选项
const removeOption = (index: number) => {
  if (questionOptions.value.length > 2) {
    questionOptions.value.splice(index, 1);
    updateOptionsString();
  } else {
    MessagePlugin.warning('至少需要两个选项');
  }
};

// 更新选项字符串
const updateOptionsString = () => {
  newQuestion.options = questionOptions.value
    .map(opt => opt.trim())
    .filter(opt => opt)
    .join(';');
};

// 监听选项变化
watch(questionOptions, () => {
  updateOptionsString();
}, { deep: true });

// 计算属性：过滤后的题目列表
const filteredQuestions = computed(() => {
  return allQuestions.value.filter(question => {
    // 搜索条件
    const matchesSearch = searchQuery.value === '' ||
      question.question_name.includes(searchQuery.value);

    // 类型条件
    const matchesType = filterParams.type === null ||
      question.type === filterParams.type;

    // 时间条件
    let matchesDate = true;
    if (filterParams.dateRange && filterParams.dateRange.length === 2) {
      const [start, end] = filterParams.dateRange;
      matchesDate = question.create_time >= start && question.create_time <= end;
    }

    return matchesSearch && matchesType && matchesDate;
  });
});

// 方法：加载题目列表
const loadQuestions = async () => {
  try {
    loading.value = true;
    // 这里调用API获取题目列表
    // const res = await getQuestionList();
    // allQuestions.value = res.data;

    // 模拟数据
    allQuestions.value = [
      {
        question_id: 1,
        question_name: '您对课程内容的满意度如何？',
        type: 'rating',
        //create_time: '2023-01-01',
      },
      {
        question_id: 2,
        question_name: '您最喜欢的课程是哪一门？',
        type: 'single_choice',
        //create_time: '2023-01-15',
      },
      {
        question_id: 3,
        question_name: '您认为哪些方面需要改进？（多选）',
        type: 'multiple_choice',
        //create_time: '2023-02-01',
      },
      {
        question_id: 4,
        question_name: '您对教师教学的其他建议',
        type: 'text',
        //create_time: '2023-02-15',
      },
    ];
  } catch (error) {
    MessagePlugin.error('加载题目失败');
    console.error(error);
  } finally {
    loading.value = false;
  }
};

// 方法：添加题目到问卷
const addQuestion = (question:Questionnaire_Question_Set) => {
  if (selectedQuestions.value.some(q => q.question_id === question.question_id)) {
    MessagePlugin.warning('该题目已添加');
    return;
  }

  selectedQuestions.value.push({
    ...question,
    score: 1, // 默认分值
  });
  MessagePlugin.success('添加成功');
};

// 方法：从问卷中移除题目
const removeQuestion = (questionId:number) => {
  const index = selectedQuestions.value.findIndex(q => q.question_id === questionId);
  if (index !== -1) {
    selectedQuestions.value.splice(index, 1);
    MessagePlugin.success('移除成功');
  }
};


const formRef = ref<FormInstanceFunctions>();
// 方法：提交问卷表单
const submitForm = async () => {
  const validateResult = await formRef.value.validate();
  if (validateResult === true) {
    try {
      if (selectedQuestions.value.length === 0) {
        MessagePlugin.warning('请至少添加一个题目');
        return;
      }

      // 构造提交数据
      const questionSet: Questionnaire_Question_Set[] = selectedQuestions.value.map(question => ({
        question_id: question.question_id,
        score: question.score,
      }));

      const submitData: CreateQuestionnaireRequest = {
        questionnaire: {
          questionnaire_name: formData.questionnaire_name,
          questionnaire_type: formData.questionnaire_type,
          graduation_year: formData.graduation_year,
        },
        questionSet: questionSet, // 添加 questionSet 属性
      };

      // 调用API创建问卷
      await addQuestionnaire(submitData);
      MessagePlugin.success('问卷创建成功');
      router.push('./questionnaire_management');
    } catch (error) {
      MessagePlugin.error('创建问卷失败');
      console.error(error);
    }
  }
};


// 方法：新建题目相关
const showCreateQuestionDialog = () => {
  showQuestionDialog.value = true;
};

// 修改后的关闭对话框方法
const closeQuestionDialog = () => {
  showQuestionDialog.value = false;
  // 重置表单数据
  Object.assign(newQuestion, {
    question_content: '',
    question_type: null,
   // training_goal_id: undefined,
   // applicable_role_id: undefined,
    is_required: 1,
    options: '',
    status: 1
  });
  questionOptions.value = ['', ''];
};



const questionFormRef = ref<FormInstanceFunctions>();
const createQuestion = async () => {
  const validateResult = await questionFormRef.value?.validate();
  if (validateResult === true) {
    try {
      // 准备提交数据
      const submitData = {
        question_id:1,
        question_content: newQuestion.question_content,
       // training_goal_id: newQuestion.training_goal_id,
        options: newQuestion.options,
        is_required: newQuestion.is_required,
       // applicable_role_id: newQuestion.applicable_role_id,
        create_time:"2020",
        update_time:'2020',
        status: newQuestion.status,
        create_by:1,
        update_by:2,
        question_type: newQuestion.question_type
      };
      console.log(submitData)
      // 调用API创建题目
      const res = await CreateQuestion(submitData);

      // 将新题目添加到题目库
      const newQuestionData = {
        question_id: res.data.question_id,
        question_name: newQuestion.question_content,
        type: getQuestionTypeName(newQuestion.question_type),
        create_time: new Date().toISOString().split('T')[0],
      };

      allQuestions.value.unshift(newQuestionData);
      MessagePlugin.success('题目创建成功');
      closeQuestionDialog();
    } catch (error) {
      MessagePlugin.error('创建题目失败');
      console.error(error);
    }
  }
};

// 辅助方法：获取题目类型名称
const getQuestionTypeName = (type: number) => {
  const found = apiQuestionTypes.find(t => t.value === type);
  return found ? found.label : '未知类型';
};


//编辑功能
const showEditDialog = ref(false);
const isEditMode = ref(false)
const editFormRef = ref<FormInstanceFunctions>();
const editQuestion = reactive<QuestionnaireFormData>({
  question_id: undefined,
  question_content: '',
  question_type: null,
 //training_goal_id: undefined,
  //applicable_role_id: undefined,
  //is_required: 1,
  update_by:1,
  update_time:"2020-02-20",
  options: '',
  status: 1
});
const editQuestionOptions = ref<string[]>([]);
const currentEditId = ref<number | null>(null);

// 计算是否显示编辑选项字段
const showEditOptionsField = computed(() => {
  return editQuestion.question_type === 1 || editQuestion.question_type === 2;
});

//进入编辑模式
const toggleEditMode = ()=>{
  isEditMode.value =! isEditMode.value
}

// 显示编辑题目对话框
const showEditQuestionDialog = (row: any) => {
  currentEditId.value = row.question_id;

  // 根据题目类型转换选项
  const options = row.type === 'single_choice' || row.type === 'multiple_choice'
    ? (row.options || '').split(';')
    : [];

  // 填充编辑表单数据
  Object.assign(editQuestion, {
    question_id: row.question_id,
    question_content: row.question_name,
    question_type: getQuestionTypeValue(row.type),
   // training_goal_id: row.training_goal_id || undefined,
    //applicable_role_id: row.applicable_role_id || undefined,
    //is_required: row.is_required || 1,
    update_time:"2020",
    update_by:1,
    options: row.options || '',
    status: row.status || 1
  });

  editQuestionOptions.value = options.filter((opt: string) => opt.trim());
  showEditDialog.value = true;
};

// 关闭编辑对话框
const closeEditDialog = () => {
  showEditDialog.value = false;
  currentEditId.value = null;
  Object.assign(editQuestion, {
    question_id: undefined,
    question_content: '',
    question_type: null,
    //training_goal_id: undefined,
    //applicable_role_id: undefined,
    is_required: 1,
    options: '',
    status: 1
  });
  editQuestionOptions.value = [];
};

// 添加编辑选项
const addEditOption = () => {
  editQuestionOptions.value.push('');
};

// 移除编辑选项
const removeEditOption = (index: number) => {
  if (editQuestionOptions.value.length > 2) {
    editQuestionOptions.value.splice(index, 1);
    updateEditOptionsString();
  } else {
    MessagePlugin.warning('至少需要两个选项');
  }
};

// 更新编辑选项字符串
const updateEditOptionsString = () => {
  editQuestion.options = editQuestionOptions.value
    .map(opt => opt.trim())
    .filter(opt => opt)
    .join(';');
};

// 监听编辑选项变化
watch(editQuestionOptions, () => {
  updateEditOptionsString();
}, { deep: true });

// 更新题目
const updateQuestion = async () => {
  const validateResult = await editFormRef.value?.validate();
  if (validateResult === true) {
    try {
      // 准备提交数据
      const submitData = {
        question_id: editQuestion.question_id,
        question_content: editQuestion.question_content,
        question_type: editQuestion.question_type,
        //training_goal_id: editQuestion.training_goal_id,
        //applicable_role_id: editQuestion.applicable_role_id,
        //is_required: editQuestion.is_required,
        update_by:1,
        update_time:"2022-02-20",
        options: editQuestion.options,
        status: editQuestion.status
      };

      // 调用API更新题目
      await ModQuestion(submitData);

      // 更新本地题目列表
      const index = allQuestions.value.findIndex(q => q.question_id === currentEditId.value);
      if (index !== -1) {
        allQuestions.value[index] = {
          ...allQuestions.value[index],
          question_name: editQuestion.question_content,
          options: editQuestion.options,
          //training_goal_id: editQuestion.training_goal_id,
          //applicable_role_id: editQuestion.applicable_role_id,
          is_required: editQuestion.is_required
        };
      }

      MessagePlugin.success('题目更新成功');
      closeEditDialog();
    } catch (error) {
      MessagePlugin.error('更新题目失败');
      console.error(error);
    }
  }
};


//删除
const handleDeleteQuestion = (row : number) => {

}

// 辅助方法：根据类型名称获取类型值
const getQuestionTypeValue = (typeName: string) => {
  const found = apiQuestionTypes.find(t => t.label === typeName);
  return found ? found.value : null;
};

// 方法：导航相关
const goBack = () => {
  router.push('../questionnaire_management');
};

const handleSearchClear = () => {
  searchQuery.value = '';
};

const handleQuestionClick = ({ row }) => {
  // 点击行时也可以添加题目
  addQuestion(row);
};

// 生命周期钩子
onMounted(() => {
  loadQuestions();
});
</script>

<style lang="less" scoped>

.import-dialog-content {
  padding: 16px 0;

  .import-options {
    margin-top: 16px;
    padding: 16px;
    background-color: var(--td-bg-color-secondarycontainer);
    border-radius: var(--td-radius-default);
  }
}
.questionnaire-create-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
  background-color: var(--td-bg-color-container);

  .header {
    display: flex;
    align-items: center;
    margin-bottom: 16px;

    .title {
      margin: 0 0 0 16px;
      font-size: 18px;
      font-weight: 500;
    }
  }

  .content-wrapper {
    display: flex;
    flex: 1;
    gap: 16px;
    height: calc(100% - 52px);

    .form-section {
      flex: 6;
      display: flex;
      flex-direction: column;
      gap: 16px;

      .selected-questions {
        :deep(.t-table) {
          margin-top: 12px;
        }
      }

      .action-buttons {
        display: flex;
        justify-content: flex-end;
        gap: 12px;
        margin-top: 16px;
      }
    }

    .question-section {
      flex: 4;
      display: flex;
      flex-direction: column;

      .question-toolbar {
        display: flex;
        justify-content: space-between;
        margin-bottom: 16px;
        gap: 12px;
      }

      .filter-panel {
        padding: 12px;
        margin-bottom: 16px;
        background-color: var(--td-bg-color-secondarycontainer);
        border-radius: var(--td-radius-default);
      }
    }
  }

  .option-item {
    display: flex;
    align-items: center;
    margin-bottom: 8px;

    :deep(.t-input) {
      flex: 1;
    }

    :deep(.t-button) {
      margin-left: 8px;
    }
  }
}
</style>
