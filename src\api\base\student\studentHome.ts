import request from '@/utils/request';
import {
    <PERSON><PERSON>ter<PERSON><PERSON>ult,
    StudentCourseListResult,
    StudentCourseTypeListResult, StudentHomeInfo,
    StudentHomeListResult, StudentInfo, StudentListResult
} from "@/api/model/student/studentHomeModel";


//毕业目标数据
export async function getList(): Promise<StudentHomeListResult> {
    try {
        const res = await request({
            url: '/api/student/HomeGraduate/list',
            method: 'GET'
        });
        if (res.code === 200) {
            return res.data;
        } else {
            throw new Error(res.message || '请求失败');
        }
    } catch (error) {
        console.error('获取毕业目标数据失败:', error);
        throw error;
    }
}


// 获取课程列表
export async function getCourseList(): Promise<StudentCourseListResult> {
    try {
        const res = await request({
            url: '/api/student/HomeCourse/list',
            method: 'GET'
        });
        if (res.code === 200) {
            return res.data;
        } else {
            throw new Error(res.message || '请求失败');
        }
    } catch (error) {
        console.error('获取获取课程列表失败:', error);
        throw error;
    }
}


// 获取课程类型
export async function getCourseTypeList(): Promise<StudentCourseTypeListResult> {
    try {
        const res = await request({
            url: '/api/student/HomeType/list',
            method: 'GET'
        });
        if (res.code === 200) {
            return res.data;
        } else {
            throw new Error(res.message || '请求失败');
        }
    } catch (error) {
        console.error('获取获取课程类型失败:', error);
        throw error;
    }
}


//获取学生信息（总学分，已修学分，必修课学分，当前学期，每学期课程数量）
export async function getStudent(): Promise<StudentListResult> {
  try {
    const res = await request({
        url: '/api/student/HomeInformation/list',
        method: 'GET'
    });
      if (res.code === 200) {
          return res.data;
      } else {
          throw new Error(res.message || '请求失败');
      }
  } catch (error) {
      console.error('获取总学分失败:', error);
      throw error;
  }
}


//获取学期名称
export async function getSemester(): Promise<SemesterResult> {
  try {
    const res = await request({
        url: '/api/student/semester/list',
        method: 'GET'
    });
      if (res.code === 200) {
          return res.data;
      } else {
          throw new Error(res.message || '请求失败');
      }
  } catch (error) {
      console.error('获取学期失败:', error);
      throw error;
  }
}
