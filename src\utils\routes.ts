import qs from 'qs'
import { hasPermission } from '@/utils/permission'
import { isExternal } from '@/utils/validate'
import {loginRouter, recordRoute} from '@/config'
import {ObeRoute, ObeRouteRecord} from "@/types/router";

/**
 * @description all模式渲染后端返回路由,支持包含views路径的所有页面
 * @param asyncRoutes
 * @returns {*}
 */
export function convertRouter(asyncRoutes: ObeRouteRecord[]) {
  const routeAllPathToCompMap = import.meta.glob(`../**/*.vue`)
  return asyncRoutes.map((route: any) => {
    if (route.component) {
      if (route.component === 'Layout') {
        route.component = () => import('@/layouts/index.vue')
      } else if (route.component === 'HeaderOnly') {
        route.component = () => import('@/layouts/header-only.vue')
      } else if (route.component === 'DynamicSidebar') {
        route.component = () => import('@/layouts/dynamic-sidebar.vue')
      } else {
        // 处理组件路径
        let componentPath = route.component

        // 移除开头的斜杠
        if (componentPath.startsWith('/')) {
          componentPath = componentPath.slice(1)
        }

        // 确保以.vue结尾
        if (!componentPath.endsWith('.vue')) {
          componentPath = `${componentPath}.vue`
        }

        // 尝试多种可能的路径
        const possiblePaths = [
          `../pages/${componentPath}`,
          `../views/${componentPath}`,
          `../${componentPath}`,
          `../src/pages/${componentPath}`,
          `../src/views/${componentPath}`
        ]

        // 特殊处理：如果是 training/course 下的组件，尝试 backup 目录
        if (componentPath.includes('training/course/')) {
          const backupPaths = [
            `../pages/${componentPath.replace('training/course/', 'training/course/backup/')}`,
            `../pages/${componentPath.replace('training/course/', 'training/course/backup/components/')}`,
          ]
          possiblePaths.push(...backupPaths)
        }

        const matchedPath = possiblePaths.find(p => routeAllPathToCompMap[p])

        if (matchedPath) {
          route.component = routeAllPathToCompMap[matchedPath]
        } else {
          console.error(`Component not found for path: ${route.component}`)
        }
      }
    }
    if (route.children && route.children.length) route.children = convertRouter(route.children)
    if (route.children && route.children.length === 0) delete route.children
    return route
  })
}

/**
 * @description 根据roles数组拦截路由
 * @param routes 路由
 * @param rolesControl 是否进行权限控制
 * @param baseUrl 基础路由
 * @returns {[]}
 */
export const filterRoutes = (routes: ObeRouteRecord[], rolesControl: boolean, baseUrl = '/') => {
  return routes
    .filter((route: ObeRouteRecord) => (rolesControl && route.meta && route.meta.guard ? hasPermission(route.meta.guard) : true))
    .map((route: ObeRouteRecord) => {
      route = { ...route }
      if (route.path !== '*' && !isExternal(route.path)) {
        if (baseUrl.slice(-1) === '/') route.path = baseUrl + (route.path[0] === '/' ? route.path.slice(1) : route.path)
        else route.path = baseUrl + (route.path[0] === '/' ? route.path : `/${route.path}`)
      }
      if (route.children && route.children.length > 0) {
        route.children = filterRoutes(route.children, rolesControl, route.path)
        if (route.children.length > 0) {
          route.childrenPathList = route.children.flatMap((item: ObeRouteRecord) => item.childrenPathList)
          if (!route.redirect) route.redirect = route.children[0].redirect ? route.children[0].redirect : route.children[0].path
        }
      } else route.childrenPathList = [route.path]
      return route
    })
}

/**
 * 根据path路径获取matched
 * @param routes 菜单routes
 * @param path 路径
 * @returns {*} matched
 */
export function handleMatched(
  routes: ObeRouteRecord[],
  path: string
): ObeRouteRecord[] {
  return routes
    .filter(
      (route: ObeRouteRecord) =>
        (route?.childrenPathList || []).indexOf(path) + 1
    )
    .flatMap((route: ObeRouteRecord) =>
      route.children ? [route, ...handleMatched(route.children, path)] : [route]
    )
}

/**
 * 生成单个多标签元素，可用于同步/异步添加多标签
 * @param tag route页信息
 */
export function handleTabs(tag: ObeRoute | ObeRouteRecord): any {
  let parentIcon = null
  if ('matched' in tag)
    for (let i = tag.matched.length - 2; i >= 0; i--)
      if (!parentIcon && tag.matched[i].meta.icon)
        parentIcon = tag.matched[i].meta.icon
  if (!parentIcon) parentIcon = 'menu-line'
  const path = handleActivePath(<ObeRoute>tag, true)
  if (tag.name && tag.meta.tabHidden !== true)
    return {
      path,
      query: 'query' in tag ? tag.query : {},
      params: 'params' in tag ? tag.params : {},
      name: tag.name as string,
      parentIcon,
      meta: { ...tag.meta },
    }
}

/**
 * 根据当前route获取激活菜单
 * @param route 当前路由
 * @param isTab 是否是标签
 * @returns {string|*}
 */
export function handleActivePath(route: ObeRoute, isTab = false) {
  const { meta, path } = route
  const rawPath = route.matched
    ? route.matched[route.matched.length - 1].path
    : path
  const fullPath =
    route.query && Object.keys(route.query).length > 0
      ? `${route.path}?${qs.stringify(route.query)}`
      : route.path
  if (isTab) return meta.dynamicNewTab ? fullPath : rawPath
  if (meta.activeMenu) return meta.activeMenu
  return fullPath
}

/**
 * 获取当前跳转登录页的Route
 * @param currentPath 当前页面地址
 */
export function toLoginRoute(currentPath: string) {
  if (recordRoute && currentPath !== '/')
    return {
      path: loginRouter,
      query: { redirect: currentPath },
      replace: true,
    }
  else return { path: loginRouter, replace: true }
}

/**
 * 获取路由中所有的Name
 * @param routes 路由数组
 * @returns {*} Name数组
 */
export function getNames(routes: ObeRouteRecord[]): string[] {
  return routes.flatMap((route: ObeRouteRecord) => {
    const names = []
    if (route.name) names.push(route.name)
    if (route.children) names.push(...getNames(route.children))
    return names
  })
}
