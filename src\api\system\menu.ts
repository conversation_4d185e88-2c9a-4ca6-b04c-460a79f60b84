import request from '@/utils/request';

/**
 * 获取菜单树
 */
export function getMenuTree() {
  return request({
    url: '/menu/list/tree',
    method: 'get'
  });
}

/**
 * 获取菜单列表
 * @param data 查询参数
 */
export function getMenuList(data: {
  title?: string;
  code?: string;
  type?: number;
  hidden?: boolean;
}) {
  return request({
    url: '/menu/list',
    method: 'post',
    data
  });
}

/**
 * 获取菜单树形选项
 */
export function getMenuTreeOption() {
  return request({
    url: '/menu/tree/option',
    method: 'get',
  });
}

/**
 * 新增菜单
 * @param data 菜单数据
 */
export function addMenu(data: any) {
  return request({
    url: '/menu',
    method: 'post',
    data
  });
}

/**
 * 更新菜单
 * @param data 菜单数据
 */
export function updateMenu(data: any) {
  return request({
    url: '/menu',
    method: 'put',
    data
  });
}

/**
 * 删除菜单
 * @param ids 菜单ID数组
 */
export function deleteMenu(ids: number[]) {
  return request({
    url: '/menu',
    method: 'delete',
    data: { ids }
  });
}

/**
 * 获取角色权限列表
 * @param roleId 角色ID
 */
export function getRoleMenuIds(roleId: number) {
  return request({
    url: `/menu/list/role/${roleId}`,
    method: 'get'
  });
}

/**
 * 更新菜单状态
 * @param id 菜单ID
 * @param status 状态
 */
export function updateMenuStatus(id: number, status: number) {
  return request({
    url: '/menu/status',
    method: 'put',
    data: { id, status }
  });
}

/**
 * 获取菜单路由列表（用于动态路由）
 */
export function getMenuRouteList() {
  return request({
    url: '/menu/route/list',
    method: 'get',
  });
}
