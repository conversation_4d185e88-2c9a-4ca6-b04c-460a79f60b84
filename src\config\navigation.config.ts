/**
 * 导航配置文件
 * 定义不同用户角色对应的默认首页路径
 */

// 用户角色枚举
export enum UserRole {
  SCHOOL_ADMIN = '0',    // 学校管理员
  MAJOR_LEADER = '1',    // 专业负责人  
  COURSE_LEADER = '2',   // 课程负责人
  TEACHER = '3',         // 教师
  STUDENT = '4',         // 学生
}

// 角色-首页映射配置
export const ROLE_HOME_MAPPING: Record<string, string> = {
  [UserRole.SCHOOL_ADMIN]: '/system/home',           // 学校管理员 → 系统管理首页
  [UserRole.MAJOR_LEADER]: '/dashboard/major',       // 专业负责人 → 教师工作台
  [UserRole.COURSE_LEADER]: '/dashboard/course-leader', // 课程负责人 → 课程负责人工作台
  [UserRole.TEACHER]: '/dashboard/major',            // 教师 → 教师工作台
  [UserRole.STUDENT]: '/student/home',               // 学生 → 学生首页
}

// 默认首页（当角色未匹配时使用）
export const DEFAULT_HOME_ROUTE = '/system/home'

// 角色描述映射（用于调试和日志）
export const ROLE_DESCRIPTIONS: Record<string, string> = {
  [UserRole.SCHOOL_ADMIN]: '学校管理员',
  [UserRole.MAJOR_LEADER]: '专业负责人',
  [UserRole.COURSE_LEADER]: '课程负责人', 
  [UserRole.TEACHER]: '教师',
  [UserRole.STUDENT]: '学生',
}

/**
 * 获取指定角色的首页路径
 */
export function getHomeRouteByRole(role: number | string): string {
  const roleKey = String(role)
  return ROLE_HOME_MAPPING[roleKey] || DEFAULT_HOME_ROUTE
}

/**
 * 获取角色描述
 */
export function getRoleDescription(role: number | string): string {
  const roleKey = String(role)
  return ROLE_DESCRIPTIONS[roleKey] || '未知角色'
} 