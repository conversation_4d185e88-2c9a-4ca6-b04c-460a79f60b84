import request, { uploadRequest } from '@/utils/request';

// 用户接口类型定义
export interface User {
  id: number;
  username: string;
  name: string;
  email: string;
  phone: string;
  status: number; // 0表示正常，99表示禁用
  roles: Role[];
  gender?: number;
  avatar?: string;
  birthday?: string;
  introduction?: string;
}

export interface Role {
  id: number;
  name: string;
  code: string;
}

export interface UserQuery {
  page: {
    current: number;
    size: number;
  };
  phone?: string;
  email?: string;
  username?: string;
  realName?: string;
  gender?: number;
  status?: number;
}

export interface UserForm {
  id?: number;
  username: string;
  name: string;
  password?: string;
  email: string;
  phone: string;
  status?: boolean;
  gender?: number;
  avatar?: string;
  birthday?: string;
  introduction?: string;
}

export interface UserRoleAssign {
  userId: number;
  roleIds: number[];
}

export interface BatchDelete {
  ids: number[];
}

// 获取用户列表
export function getUserList(data: UserQuery) {
  return request({
    url: '/user/list',
    method: 'post',
    data
  });
}

// 添加用户
export function addUser(data: UserForm) {
  return request({
    url: '/user',
    method: 'post',
    data
  });
}

// 更新用户
export function updateUser(data: UserForm) {
  return request({
    url: '/user',
    method: 'put',
    data
  });
}

// 删除用户
export function deleteUser(id: number) {
  return request({
    url: `/user/${id}`,
    method: 'delete'
  });
}

// 获取用户详情
export function getUserDetail(id: number) {
  return request({
    url: `/user/${id}`,
    method: 'get'
  });
}

// 分配用户角色
export function assignUserRoles(data: UserRoleAssign) {
  return request({
    url: '/user/roles',
    method: 'put',
    data
  });
}

// 重置用户密码
export function resetUserPassword(id: number) {
  return request({
    url: `/user/reset-password/${id}`,
    method: 'put'
  });
}

// 批量删除用户
export function batchDeleteUsers(data: BatchDelete) {
  return request({
    url: '/user/batch',
    method: 'delete',
    data
  });
}

// 切换用户状态（封禁/解封）
export function toggleUserStatus(id: number) {
  return request({
    url: `/user/block/${id}`,
    method: 'put'
  });
}

// 导入用户
export function importUsers(file: File, onProgress?: (progressEvent: any) => void) {
  const formData = new FormData();
  formData.append('file', file);
  return uploadRequest({
    url: '/user/import',
    method: 'post',
    data: formData
  }, onProgress);
} 