import { request } from '@/utils/request';

// 查询某毕业要求下的全部指标点
export function getEoList(planId: number) {
  return request({
    url: '/tp/eo/getList',
    method: 'get',
    params: { plan_id: planId }
  });
}

// 新增指标点
export function addEo(data: any) {
  return request({
    url: '/tp/eo/add',
    method: 'post',
    data
  });
}

// 修改指标点
export function updateEo(data: any) {
  return request({
    url: '/tp/eo/mod',
    method: 'put',
    data
  });
}

// 删除指标点
export function deleteEo(id: number) {
  return request({
    url: '/tp/eo/del',
    method: 'delete',
    params: { id }
  });
}
