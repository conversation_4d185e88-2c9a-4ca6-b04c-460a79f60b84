<template>
  <div class="scatter-chart-container">
    <div ref="chartRef" class="chart" :id="chartId" style="width: 100%; height: 500px;"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, onBeforeUnmount } from 'vue';
import * as echarts from 'echarts';

interface ScatterChartPoint {
  index: number;
  value: number;
  name: string;
  number?: string;
}

interface Props {
  chartId?: string;
  title?: string;
  points: ScatterChartPoint[];
  xLabel?: string;
  yLabel?: string;
}

const props = defineProps<Props>();
const chartRef = ref<HTMLDivElement | null>(null);
let chartInstance: echarts.ECharts | null = null;

const renderChart = () => {
  if (!chartRef.value) return;
  if (!props.points || props.points.length === 0) return;
  if (!chartInstance) {
    chartInstance = echarts.init(chartRef.value);
  }
  const scatterData = props.points.map(p => [p.index, p.value, p.name, p.number || '']);
  const option = {
    title: {
      text: props.title || '成绩散点图',
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      formatter: (params: any) => {
        return `序号: ${params.value[0]}<br/>成绩: ${params.value[1]}<br/>姓名: ${params.value[2]}${params.value[3] ? `<br/>学号: ${params.value[3]}` : ''}`;
      }
    },
    legend: { show: false },
    xAxis: {
      name: props.xLabel || '序号',
      type: 'value',
      scale: true
    },
    yAxis: {
      name: props.yLabel || '成绩',
      type: 'value',
      scale: true
    },
    series: [{
      type: 'scatter',
      name: '成绩分布',
      data: scatterData,
      symbolSize: 10,
      emphasis: {
        focus: 'series',
        label: {
          show: true,
          formatter: (params: any) => params.data[2],
          position: 'top'
        }
      }
    }],
    grid: {
      left: '10%',
      right: '10%',
      top: '20%',
      bottom: '15%'
    }
  };
  chartInstance.setOption(option);
  chartInstance.resize();
};

watch(() => props.points, () => {
  renderChart();
}, { deep: true });

onMounted(() => {
  renderChart();
  window.addEventListener('resize', handleResize);
});

onBeforeUnmount(() => {
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }
  window.removeEventListener('resize', handleResize);
});

function handleResize() {
  if (chartInstance) {
    chartInstance.resize();
  }
}
</script>

<style scoped>
.scatter-chart-container {
  width: 100%;
}
.chart {
  width: 100%;
  height: 500px;
}
</style>
