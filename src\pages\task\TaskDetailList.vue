<template>
  <div class="task-detail-container">
    <!-- 头部信息 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-info">
          <div class="title-row">
            <t-button theme="default" variant="text" @click="handleGoBack" class="back-button">
              <template #icon>
                <t-icon name="chevron-left" />
              </template>
              返回教学数据管理
            </t-button>
            <h1 class="page-title">教学任务详情</h1>
          </div>
          <!-- <p class="page-subtitle">查看指定学年学期的教学任务详细信息</p> -->
          <div class="page-info">
            <t-tag theme="primary" variant="light">{{ courseInfo.courseName }}</t-tag>
            <t-tag theme="success" variant="light">{{ pageParams.year }}学年</t-tag>
            <t-tag theme="warning" variant="light">{{ semesterName }}</t-tag>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="page-content">
      <!-- 统计概览 -->
      <div class="overview-section">
        <div class="overview-row">
          <div class="overview-card">
            <div class="card-header">
              <t-icon name="layers" class="card-icon tasks" />
              <span class="card-title">教学任务总数</span>
            </div>
            <div class="card-value">{{ overviewData.totalTasks }}</div>
            <div class="card-desc">本学期教学任务数量</div>
          </div>
          <div class="overview-card">
            <div class="card-header">
              <t-icon name="view-module" class="card-icon classes" />
              <span class="card-title">教学班级总数</span>
            </div>
            <div class="card-value">{{ overviewData.totalClasses }}</div>
            <div class="card-desc">涉及的教学班级数量</div>
          </div>
          <div class="overview-card">
            <div class="card-header">
              <t-icon name="user-avatar" class="card-icon students" />
              <span class="card-title">学生总数</span>
            </div>
            <div class="card-value">{{ overviewData.totalStudents }}</div>
            <div class="card-desc">参与学习的学生人数</div>
          </div>
          <div class="overview-card">
            <div class="card-header">
              <t-icon name="user-circle" class="card-icon teachers" />
              <span class="card-title">授课教师</span>
            </div>
            <div class="card-value">{{ overviewData.totalTeachers }}</div>
            <div class="card-desc">参与授课的教师人数</div>
          </div>
        </div>
      </div>

      <!-- 教学任务详情列表 -->
      <t-card>
        <template #title>
          <div class="flex items-center">
            <t-icon name="view-list" class="mr-2 text-blue-500" />
            <span>教学任务详情列表</span>
          </div>
        </template>

        <t-table
          :data="taskList"
          :columns="taskColumns"
          :bordered="true"
          :hover="true"
          :loading="loading"
          row-key="taskId"
        >
          <!-- 教学任务名称 -->
          <template #taskName="{ row }">
            <div class="task-name-cell">
              <span class="task-name">{{ row.taskName || '-' }}</span>
              <t-tag size="small" theme="default" variant="light" class="task-number">
                任务 {{ row.taskNumber }}
              </t-tag>
            </div>
          </template>

          <!-- 教学班级 -->
          <template #classes="{ row }">
            <div class="classes-cell">
              <div v-for="(classItem, index) in row.classes" :key="classItem.classId" class="class-item">
                <t-tag size="small" theme="primary" variant="light">
                  {{ classItem.className }}
                </t-tag>
                <span class="student-count">({{ classItem.studentNumber }}人)</span>
              </div>
              <div v-if="!row.classes || row.classes.length === 0" class="no-data">
                暂无班级信息
              </div>
            </div>
          </template>

          <!-- 任课教师 -->
          <template #teachers="{ row }">
            <div class="teachers-cell">
              <div v-for="(teacher, index) in row.teachers" :key="teacher.teacherId" class="teacher-item">
                <div class="teacher-info">
                  <span class="teacher-name">{{ teacher.teacherName }}</span>
                  <t-tag 
                    size="small" 
                    :theme="getTeacherRoleTheme(teacher.role)" 
                    variant="light"
                    class="teacher-role"
                  >
                    {{ teacher.roleName }}
                  </t-tag>
                </div>
                <div class="teacher-title" v-if="(teacher as ExtendedTaskTeacherVO).title">
                  {{ (teacher as ExtendedTaskTeacherVO).title }}
                </div>
              </div>
              <div v-if="!row.teachers || row.teachers.length === 0" class="no-data">
                暂无教师信息
              </div>
            </div>
          </template>

          <!-- 教学安排 -->
          <template #weekInfo="{ row }">
            <div class="week-info-cell">
              <div class="week-item">
                <span class="label">教学周数:</span>
                <span class="value">{{ row.teachWeek || 0 }}周</span>
              </div>
              <div class="week-item">
                <span class="label">周学时:</span>
                <span class="value">{{ row.weekHours || 0 }}学时</span>
              </div>
            </div>
          </template>

          <!-- 操作列 -->
          <template #operation="{ row }">
            <t-space size="small">
              <t-button theme="primary" variant="text" size="small" @click="handleViewDetail(row)">
                <template #icon><t-icon name="view" /></template>
                查看详情
              </t-button>
              <t-button theme="success" variant="text" size="small" @click="handleManageGrade(row)">
                <template #icon><t-icon name="chart-bubble" /></template>
                成绩管理
              </t-button>
            </t-space>
          </template>
        </t-table>

        <!-- 空状态 -->
        <div v-if="!loading && (!taskList || taskList.length === 0)" class="empty-state">
          <t-empty description="暂无教学任务数据">
            <template #image>
              <t-icon name="inbox" size="48px" />
            </template>
          </t-empty>
        </div>
      </t-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { MessagePlugin } from 'tdesign-vue-next';
import { 
  getTaskWorkListByTaskYear,
  type TaskWorkDetailVO,
  type TaskTeacherVO
} from '@/api/teaching/task';

// 扩展教师信息接口，包含职称
interface ExtendedTaskTeacherVO extends TaskTeacherVO {
  title?: string; // 教师职称
}

const route = useRoute();
const router = useRouter();

// 页面参数
const pageParams = ref({
  courseId: Number(route.query.courseId) || Number(route.params.courseId) || 1,
  year: Number(route.query.year) ,
  term: Number(route.query.term) 
});

// 课程信息
const courseInfo = ref({
  courseName: route.query.courseName || '数据结构与算法'
});

// 加载状态
const loading = ref(false);

// 学期名称映射
const semesterName = computed(() => {
  return pageParams.value.term === 1 ? '春季学期' : '秋季学期';
});

// 概览数据
const overviewData = ref({
  totalTasks: 0,
  totalClasses: 0,
  totalStudents: 0,
  totalTeachers: 0
});

// 教学任务列表
const taskList = ref<TaskWorkDetailVO[]>([]);

// 学年字符串转换为数字（如 "2023-2024" -> 2023）
const convertYearStringToNumber = (yearString: string): number => {
  if (!yearString) return new Date().getFullYear();
  const yearMatch = yearString.match(/^(\d{4})/);
  return yearMatch ? parseInt(yearMatch[1], 10) : new Date().getFullYear();
};

// 表格列配置
const taskColumns = [
  { colKey: 'taskName', title: '教学任务', width: 250 },
  { colKey: 'classes', title: '教学班级', width: 300 },
  { colKey: 'teachers', title: '任课教师', width: 250 },
  { colKey: 'weekInfo', title: '教学安排', width: 180 },
  { colKey: 'operation', title: '操作', width: 200, fixed: 'right' as const }
];

// 获取教师角色主题色
const getTeacherRoleTheme = (role: number): 'success' | 'danger' | 'default' | 'primary' | 'warning' => {
  switch (role) {
    case 1: return 'primary'; // 主讲教师
    case 2: return 'success'; // 辅导教师
    case 3: return 'warning'; // 助教
    default: return 'default';
  }
};

// 加载教学任务详情数据
const loadTaskDetails = async () => {
  try {
    loading.value = true;
    console.log('加载教学任务详情，参数:', pageParams.value);
    
    const response = await getTaskWorkListByTaskYear(pageParams.value.courseId, {
      taskYear: pageParams.value.year,
      taskTerm: pageParams.value.term
    });
    
    console.log('教学任务详情响应:', response);
    
    if (response && response.data) {
      const data = response.data;
      
      // 根据实际API返回结构处理数据
      if (Array.isArray(data)) {
        // 如果直接返回任务列表数组
        taskList.value = data.map(formatTaskData);
        calculateOverviewData(taskList.value);
      } else if (data.records && Array.isArray(data.records)) {
        // 如果返回分页对象
        taskList.value = data.records.map(formatTaskData);
        calculateOverviewData(taskList.value);
      } else if (data.taskDetails && Array.isArray(data.taskDetails)) {
        // 如果包含taskDetails字段
        taskList.value = data.taskDetails.map(formatTaskData);
        calculateOverviewData(taskList.value);
        
        // 更新课程名称（如果返回了的话）
        if (data.courseName) {
          courseInfo.value.courseName = data.courseName;
        }
      } else {
        console.warn('响应数据格式不符合预期:', data);
        generateMockData(); // 生成模拟数据用于展示
      }
    } else {
      console.warn('响应数据为空');
      generateMockData();
    }
    
  } catch (error) {
    console.error('加载教学任务详情失败:', error);
    MessagePlugin.error('加载教学任务详情失败');
    generateMockData(); // 出错时使用模拟数据
  } finally {
    loading.value = false;
  }
};

// 格式化任务数据
const formatTaskData = (task: any): TaskWorkDetailVO => {
  return {
    academicYear: task.academicYear || pageParams.value.year,
    semester: task.semester || semesterName.value,
    taskId: task.taskId || task.id,
    taskName: task.taskName || '未知任务',
    taskNumber: task.taskNumber || 0,
    teachWeek: task.teachWeek || 16,
    weekHours: task.weekHours || 4,
    classes: task.classes || [],
    teachers: task.teachers || [],
    studentCount: task.studentCount || (task.classes ? task.classes.reduce((sum: number, cls: any) => sum + (cls.studentNumber || 0), 0) : 0),
    classCount: task.classCount || (task.classes ? task.classes.length : 0)
  };
};

// 计算概览数据
const calculateOverviewData = (tasks: TaskWorkDetailVO[]) => {
  const totalTasks = tasks.length;
  const totalClasses = tasks.reduce((sum, task) => sum + (task.classes?.length || 0), 0);
  const totalStudents = tasks.reduce((sum, task) => sum + (task.studentCount || 0), 0);
  
  // 统计唯一教师数量
  const teacherSet = new Set<number>();
  tasks.forEach(task => {
    task.teachers?.forEach(teacher => {
      teacherSet.add(teacher.teacherId);
    });
  });
  
  overviewData.value = {
    totalTasks,
    totalClasses,
    totalStudents,
    totalTeachers: teacherSet.size
  };
};

// 生成模拟数据（用于演示和错误兜底）
const generateMockData = () => {
  const mockTasks: TaskWorkDetailVO[] = [
    {
      academicYear: pageParams.value.year,
      semester: semesterName.value,
      taskId: 1,
      taskName: '数据结构基础理论',
      taskNumber: 1,
      teachWeek: 16,
      weekHours: 4,
      classes: [
        { classId: 1, className: '计科2021-1班', studentNumber: 42 },
        { classId: 2, className: '计科2021-2班', studentNumber: 38 }
      ],
      teachers: [
        { teacherId: 1, teacherName: '张教授', role: 1, roleName: '主讲教师', title: '教授' } as ExtendedTaskTeacherVO,
        { teacherId: 2, teacherName: '李老师', role: 3, roleName: '助教', title: '讲师' } as ExtendedTaskTeacherVO
      ],
      studentCount: 80,
      classCount: 2
    },
    {
      academicYear: pageParams.value.year,
      semester: semesterName.value,
      taskId: 2,
      taskName: '算法设计与分析',
      taskNumber: 2,
      teachWeek: 16,
      weekHours: 3,
      classes: [
        { classId: 3, className: '软工2021-1班', studentNumber: 36 }
      ],
      teachers: [
        { teacherId: 3, teacherName: '王教授', role: 1, roleName: '主讲教师', title: '副教授' } as ExtendedTaskTeacherVO
      ],
      studentCount: 36,
      classCount: 1
    },
    {
      academicYear: pageParams.value.year,
      semester: semesterName.value,
      taskId: 3,
      taskName: '数据结构实验',
      taskNumber: 3,
      teachWeek: 16,
      weekHours: 2,
      classes: [
        { classId: 1, className: '计科2021-1班', studentNumber: 42 },
        { classId: 2, className: '计科2021-2班', studentNumber: 38 },
        { classId: 3, className: '软工2021-1班', studentNumber: 36 }
      ],
      teachers: [
        { teacherId: 4, teacherName: '赵老师', role: 1, roleName: '主讲教师', title: '实验师' } as ExtendedTaskTeacherVO,
        { teacherId: 5, teacherName: '刘老师', role: 2, roleName: '辅导教师', title: '助教' } as ExtendedTaskTeacherVO
      ],
      studentCount: 116,
      classCount: 3
    }
  ];
  
  taskList.value = mockTasks;
  calculateOverviewData(mockTasks);
  MessagePlugin.info('当前展示模拟数据');
};

// 返回上一页
const handleGoBack = () => {
  router.back();
};

// 查看任务详情
const handleViewDetail = (row: TaskWorkDetailVO) => {
  MessagePlugin.info(`查看任务详情: ${row.taskName}`);
  // 可以跳转到更详细的任务信息页面
};

// 成绩管理
const handleManageGrade = (row: TaskWorkDetailVO) => {
  MessagePlugin.info(`成绩管理: ${row.taskName}`);
  // 可以跳转到该任务的成绩管理页面
};

onMounted(() => {
  console.log('教学任务详情页面加载，参数:', pageParams.value);
  loadTaskDetails();
});
</script>

<style lang="less" scoped>
.task-detail-container {
  padding: 20px;
  
  .page-header {
    background: white;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    border: 1px solid rgba(0, 0, 0, 0.06);
    
    .header-content {
      .title-info {
        .title-row {
          display: flex;
          align-items: center;
          gap: 16px;
          margin-bottom: 8px;
          
          .back-button {
            font-size: 14px;
            color: var(--td-text-color-secondary);
            
            &:hover {
              color: var(--td-brand-color);
            }
          }
          
          .page-title {
            font-size: 24px;
            font-weight: 600;
            margin: 0;
            color: var(--td-text-color-primary);
          }
        }
        
        .page-subtitle {
          font-size: 14px;
          color: var(--td-text-color-secondary);
          margin: 0 0 16px 0;
        }
        
        .page-info {
          display: flex;
          gap: 8px;
          flex-wrap: wrap;
        }
      }
    }
  }
  
  .page-content {
    .overview-section {
      margin-bottom: 24px;
      .overview-row {
        display: flex;
        gap: 16px;
        flex-wrap: wrap;
      }
      .overview-card {
        flex: 1 1 0;
        min-width: 220px;
        background: white;
        border: 1px solid rgba(0, 0, 0, 0.08);
        border-radius: 12px;
        padding: 20px;
        transition: all 0.3s ease;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        
        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
          border-color: var(--td-brand-color-3);
        }
        
        .card-header {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 12px;
          
          .card-icon {
            font-size: 20px;
            &.tasks {
              color: var(--td-brand-color);
            }
            &.classes {
              color: var(--td-success-color);
            }
            &.students {
              color: var(--td-warning-color);
            }
            &.teachers {
              color: #722ed1;
            }
          }
          .card-title {
            font-size: 14px;
            font-weight: 500;
            color: var(--td-text-color-secondary);
          }
        }
        .card-value {
          font-size: 28px;
          font-weight: 700;
          color: var(--td-text-color-primary);
          margin-bottom: 4px;
          line-height: 1.2;
        }
        .card-desc {
          font-size: 12px;
          color: var(--td-text-color-placeholder);
        }
      }
    }
    
    // 表格单元格样式
    .task-name-cell {
      .task-name {
        font-weight: 500;
        color: var(--td-text-color-primary);
        display: block;
        margin-bottom: 4px;
      }
      
      .task-number {
        font-size: 12px;
      }
    }
    
    .classes-cell {
      .class-item {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 4px;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .student-count {
          font-size: 12px;
          color: var(--td-text-color-secondary);
        }
      }
      
      .no-data {
        color: var(--td-text-color-placeholder);
        font-size: 12px;
        font-style: italic;
      }
    }
    
    .teachers-cell {
      .teacher-item {
        margin-bottom: 8px;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .teacher-info {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 2px;
          
          .teacher-name {
            font-weight: 500;
            color: var(--td-text-color-primary);
          }
          
          .teacher-role {
            font-size: 12px;
          }
        }
        
        .teacher-title {
          font-size: 12px;
          color: var(--td-text-color-secondary);
          padding-left: 0;
        }
      }
      
      .no-data {
        color: var(--td-text-color-placeholder);
        font-size: 12px;
        font-style: italic;
      }
    }
    
    .week-info-cell {
      .week-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 4px;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .label {
          font-size: 12px;
          color: var(--td-text-color-secondary);
        }
        
        .value {
          font-size: 12px;
          font-weight: 500;
          color: var(--td-text-color-primary);
        }
      }
    }
    
    .empty-state {
      padding: 40px;
      text-align: center;
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .task-detail-container {
    .page-content {
      .overview-section {
        :deep(.t-col) {
          &[data-span="6"] {
            flex: 0 0 50%;
            max-width: 50%;
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .task-detail-container {
    padding: 16px;
    
    .page-header {
      padding: 20px;
      
      .header-content {
        .title-info {
          .title-row {
            flex-direction: column;
            align-items: flex-start;
            gap: 12px;
            
            .page-title {
              font-size: 20px;
            }
          }
          
          .page-info {
            margin-top: 12px;
          }
        }
      }
    }
    
    .page-content {
      .overview-section {
        :deep(.t-col) {
          &[data-span="6"] {
            flex: 0 0 100%;
            max-width: 100%;
          }
        }
        
        .overview-card {
          padding: 16px;
          
          .card-value {
            font-size: 24px;
          }
        }
      }
    }
  }
}
</style>
