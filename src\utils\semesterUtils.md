# 学期工具类使用说明

## 概述

`SemesterUtils` 是一个用于处理学期相关计算和转换的工具类，提供了学期列表生成、当前学期判断、学期类型计算、学期序号换算等功能。

## 核心功能

### 1. 学期类型判断
- **春季学期**: 2-7月 (type: 0)
- **秋季学期**: 9-1月 (type: 1)
- **暑假**: 8月 (默认为秋季学期)

### 2. 学期序号计算规则
根据入学年份计算当前是第几学期：
- 2023年入学，2023年春季是第1学期
- 2023年入学，2023年秋季是第2学期
- 2023年入学，2024年春季是第3学期
- 以此类推...

## 主要方法

### 获取当前学期信息
```typescript
import SemesterUtils from '@/utils/semesterUtils'

// 获取当前学期信息
const currentSemester = SemesterUtils.getCurrentSemester()
console.log(currentSemester)
// 输出: { year: 2024, type: 0, number: 0, displayName: '2024春' }

// 获取当前学期显示名称
const displayName = SemesterUtils.getCurrentSemesterDisplayName()
console.log(displayName) // '2024春'

// 获取当前学期类型（0/1）
const type = SemesterUtils.getCurrentSemesterType()
console.log(type) // 0 或 1
```

### 学期类型判断
```typescript
// 根据年份和月份判断学期类型
const type = SemesterUtils.getSemesterType(2024, 3) // 0 (春季)
const type2 = SemesterUtils.getSemesterType(2024, 9) // 1 (秋季)
const type3 = SemesterUtils.getSemesterType(2024, 8) // 1 (秋季，暑假)

// 根据学期序号判断学期类型
const type4 = SemesterUtils.getSemesterTypeByNumber(1) // 1 (秋季)
const type5 = SemesterUtils.getSemesterTypeByNumber(2) // 0 (春季)
```

### 学期状态判断
```typescript
// 判断学期状态（准备中/进行中/已结课）
const status = SemesterUtils.getSemesterStatus(2024, 0) // 'ongoing' (当前学期)
const status2 = SemesterUtils.getSemesterStatus(2025, 0) // 'preparing' (未来学期)
const status3 = SemesterUtils.getSemesterStatus(2023, 0) // 'completed' (过去学期)

// 获取状态显示名称
const displayName = SemesterUtils.getSemesterStatusDisplayName(status) // '进行中'
```

### 学期序号计算
```typescript
// 根据入学年份计算学期序号
const number = SemesterUtils.calculateSemesterNumber(2023, 2024, 0) // 3 (2023年入学，2024年春季)
const number2 = SemesterUtils.calculateSemesterNumber(2023, 2024, 1) // 4 (2023年入学，2024年秋季)

// 根据入学年份获取完整学期信息
const semester = SemesterUtils.getSemesterByEntranceYear(2023, 2024, 0)
console.log(semester)
// 输出: { year: 2024, type: 0, number: 3, displayName: '2024春', entranceYear: 2023 }
```

### 学期列表生成
```typescript
// 生成学期列表（从最新到最旧排序）
const semesters = SemesterUtils.getSemesterList(2023, 2024)
console.log(semesters)
// 输出: [
//   { year: 2024, type: 1, number: 0, displayName: '2024秋' },
//   { year: 2024, type: 0, number: 0, displayName: '2024春' },
//   { year: 2023, type: 1, number: 0, displayName: '2023秋' },
//   { year: 2023, type: 0, number: 0, displayName: '2023春' }
// ]

// 生成学期选项（用于下拉选择，默认从2010年开始）
const options = SemesterUtils.getSemesterOptions()
console.log(options)
// 输出: [
//   { label: '2024秋', value: '2024秋', year: 2024, type: 1 },
//   { label: '2024春', value: '2024春', year: 2024, type: 0 },
//   { label: '2023秋', value: '2023秋', year: 2023, type: 1 },
//   { label: '2023春', value: '2023春', year: 2023, type: 0 },
//   // ... 更多历史学期，从2010年开始
// ]

// 自定义年份范围
const customOptions = SemesterUtils.getSemesterOptions(2020, 2024)
```

### 学期字符串解析
```typescript
// 解析学期字符串
const semester = SemesterUtils.parseSemester('2024春')
console.log(semester)
// 输出: { year: 2024, type: 0, number: 0, displayName: '2024春' }

// 验证学期格式
const isValid = SemesterUtils.isValidSemesterFormat('2024春') // true
const isValid2 = SemesterUtils.isValidSemesterFormat('2024夏') // false
```

### 格式化显示
```typescript
// 格式化学期显示名称
const displayName = SemesterUtils.formatSemester(2024, 0) // '2024春'
const displayName2 = SemesterUtils.formatSemester(2024, 1) // '2024秋'

// 获取学期类型名称
const typeName = SemesterUtils.getSemesterTypeName(0) // '春'
const typeName2 = SemesterUtils.getSemesterTypeName(1) // '秋'
```

## 在Vue组件中的使用示例

### 1. 教师工作台页面
```typescript
import SemesterUtils from '@/utils/semesterUtils'

// 获取当前学期
const getCurrentSemester = () => {
  return SemesterUtils.getCurrentSemesterDisplayName()
}

// 生成学期选项列表
const generateSemesterOptions = () => {
  // 从2010年开始到当前年份，按最新到最旧排序
  return SemesterUtils.getSemesterOptions()
}

// 加载当前学期课程数据
const loadCurrentSemesterCourses = async () => {
  try {
    // 使用学期工具类解析学期信息
    const semesterInfo = SemesterUtils.parseSemester(currentSemester.value)
    if (!semesterInfo) {
      console.error('学期格式无效:', currentSemester.value)
      return
    }
    
    const response = await getTeacherSemesterCourses(semesterInfo.year, semesterInfo.type)
    // ... 处理响应数据
  } catch (error) {
    console.error('加载当前学期课程数据失败:', error)
  }
}
```

### 2. 班级管理页面
```typescript
import SemesterUtils from '@/utils/semesterUtils'

// 根据班级入学年份计算当前学期
const calculateCurrentSemester = (entranceYear: number) => {
  const currentYear = new Date().getFullYear()
  const currentType = SemesterUtils.getCurrentSemesterType()
  
  return SemesterUtils.getSemesterByEntranceYear(entranceYear, currentYear, currentType)
}

// 示例：2023年入学的班级，当前是第几学期
const semester = calculateCurrentSemester(2023)
console.log(`当前是第${semester.number}学期`) // 根据当前时间显示具体学期
```

## 数据结构

### SemesterInfo 接口
```typescript
interface SemesterInfo {
  year: number;           // 学年
  type: number;           // 学期类型（0: 春季, 1: 秋季）
  number: number;         // 学期序号（1-8）
  displayName: string;    // 显示名称（如"2024春"）
  entranceYear?: number;  // 入学年份（可选）
}
```

### SemesterOption 接口
```typescript
interface SemesterOption {
  label: string;    // 显示标签
  value: string;    // 选择值
  year: number;     // 年份
  type: number;     // 学期类型
}
```

## 注意事项

1. **学期类型**: 0表示春季，1表示秋季
2. **学期序号**: 1-8表示大学四年的8个学期
3. **入学年份**: 用于计算当前是第几学期
4. **当前学期**: 根据系统当前时间自动判断
5. **格式验证**: 学期字符串格式为"YYYY春"或"YYYY秋"
6. **排序规则**: 学期列表按从最新到最旧排序，每年内秋季在前、春季在后
7. **默认年份范围**: 学期选项默认从2010年开始到当前年份
8. **学期状态**: 支持准备中、进行中、已结课三种状态判断
9. **时间范围**: 春季学期为2-7月，秋季学期为9-1月，8月为暑假

## 扩展功能

工具类支持以下扩展功能：
- 自定义学期范围
- 学期列表排序
- 学期格式验证
- 学期类型转换
- 学期序号计算

通过使用 `SemesterUtils`，可以统一管理项目中的学期相关逻辑，避免重复代码，提高代码的可维护性。 