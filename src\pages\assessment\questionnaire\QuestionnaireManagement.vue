questionnaire_management.vue<template>
  <div class="questionnaire-management">
    <div class="header">
      <div class="header-left">
        <t-button variant="outline" @click="goback">
          <template #icon><t-icon name="arrow-left" /></template>
          返回首页
        </t-button>
      </div>
      <h2>问卷管理</h2>
      <div class="header-right">
        <t-space>
          <t-button theme="primary" @click="create">
            <template #icon><t-icon name="add" /></template>
            新建问卷
          </t-button>
        </t-space>
      </div>
    </div>

    <t-card class="search-card" :bordered="false">
      <t-form layout="inline" @submit.prevent="handleSearch">
        <t-form-item label="问卷名称">
          <t-input v-model="searchParams.questionnaire_name" placeholder="请输入问卷名称" clearable />
        </t-form-item>
        <t-form-item label="问卷类型">
          <t-select v-model="searchParams.questionnaire_type" clearable placeholder="请选择问卷类型">
            <t-option v-for="type in questionnaireTypes" :key="type.value" :value="type.value" :label="type.label" />
          </t-select>
        </t-form-item>
        <t-form-item label="状态">
          <t-select v-model="searchParams.status" clearable placeholder="请选择状态">
            <t-option v-for="status in statusOptions" :key="status.value" :value="status.value" :label="status.label" />
          </t-select>
        </t-form-item>
        <t-form-item>
          <t-button theme="primary" type="submit">搜索</t-button>
          <t-button variant="base" theme="default" @click="resetSearch">重置</t-button>
        </t-form-item>
      </t-form>
    </t-card>

    <t-card :bordered="false">
      <t-table
        :data="questionnaireList"
        :columns="columns"
        :loading="loading"
        :pagination="pagination"
        row-key="questionnaire_id"
        hover
        @page-change="handlePageChange"
      >
        <template #status="{ row }">
          <t-tag :theme="getStatusTagTheme(row.status)" variant="light">
            {{ getStatusText(row.status) }}
          </t-tag>
        </template>
        <template #operation="{ row }">
          <t-space>
            <t-link theme="primary" hover="color" @click="handleEdit(row)">编辑</t-link>
            <t-link theme="primary" hover="color" @click="handleView(row)">查看</t-link>
            <t-popconfirm content="确认删除该问卷吗？" @confirm="handleDelete(row)">
              <t-link theme="danger" hover="color">删除</t-link>
            </t-popconfirm>
            <t-link
              v-if="row.status === 0"
              theme="primary"
              hover="color"
              @click="handlePublish(row.questionnaire_id)"
            >
              发布
            </t-link>
            <t-link
              v-else
              theme="danger"
              hover="color"
              @click="handleCancelPublish(row.questionnaire_id)"
            >
              取消发布
            </t-link>
          </t-space>
        </template>
      </t-table>
    </t-card>


  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { MessagePlugin } from 'tdesign-vue-next';
import {
  addQuestionnaire,
  deleteQuestionnaire,
  batchUpdateQuestionScores,
  type Questionnaire,
  type Questionnaire_Question_Set,
  type CreateQuestionnaireRequest,
} from '@/api/directers/questionnaire';
import {status} from "nprogress";

const router = useRouter();

// 问卷类型选项
const questionnaireTypes = [
  { value: 1, label: '学生问卷' },
  { value: 2, label: '教师问卷' },
  { value: 3, label: '毕业生问卷' },
];

// 状态选项
const statusOptions = [
  { value: 0, label: '草稿' },
  { value: 1, label: '已发布' },
  { value: -1, label: '已删除' },
];

// 表格列配置
const columns = [
  { colKey: 'questionnaire_id', title: '问卷ID', width: 100 },
  { colKey: 'questionnaire_name', title: '问卷名称', width:220 ,ellipsis: true},
  { colKey: 'questionnaire_type', title: '问卷类型', width: 120, cell: (h, { row }) => {
      const type = questionnaireTypes.find(t => t.value === row.questionnaire_type);
      return type ? type.label : '未知';
    }},
  { colKey: 'graduation_year', title: '毕业年份', width: 120 },
  { colKey: 'status', title: '状态', width: 100, slots: { customRender: 'status' } },
  { colKey: 'create_time', title: '创建时间', width: 180 },
  { colKey: 'update_time', title: '更新时间', width: 180 },
  { colKey: 'operation', title: '操作', width: 240, slots: { customRender: 'operation' } },
];

// 搜索参数
const searchParams = reactive({
  questionnaire_name: '',
  questionnaire_type: null,
  status: null,
});

// 分页参数
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
});

// 问卷列表数据
const questionnaireList = ref<Questionnaire[]>([]);
const loading = ref(false);

// 表单验证规则
const rules = {
  questionnaire_name: [{ required: true, message: '请输入问卷名称', type: 'error' }],
  questionnaire_type: [{ required: true, message: '请选择问卷类型', type: 'error' }],
};

// 获取状态文本
const getStatusText = (status: number) => {
  const option = statusOptions.find(item => item.value === status);
  return option ? option.label : '未知';
};

// 获取状态标签主题
const getStatusTagTheme = (status: number) => {
  switch (status) {
    case 0: return 'warning';
    case 1: return 'success';
    case -1: return 'danger';
    default: return 'default';
  }
};

// 加载问卷列表
const loadQuestionnaireList = async () => {
  try {
    loading.value = true;

    // 模拟数据
    questionnaireList.value = [
      {
        questionnaire_id: 1,
        questionnaire_name: '2023届毕业生调查问卷',
        questionnaire_type: 3,
        graduation_year: '2023',
        status: 1,
        create_time: '2023-01-01 10:00:00',
        update_time: '2023-01-01 10:00:00',
      },
      {
        questionnaire_id: 2,
        questionnaire_name: '教师满意度调查',
        questionnaire_type: 2,
        graduation_year: '',
        status: 0,
        create_time: '2023-02-01 10:00:00',
        update_time: '2023-02-01 10:00:00',
      },
    ];
    pagination.total = 2;
  } catch (error) {
    MessagePlugin.error('获取问卷列表失败');
    console.error(error);
  } finally {
    loading.value = false;
  }
};

// 搜索问卷
const handleSearch = () => {
  pagination.current = 1;
  loadQuestionnaireList();
};

// 重置搜索
const resetSearch = () => {
  Object.assign(searchParams, {
    questionnaire_name: '',
    questionnaire_type: null,
    status: null,
  });
  handleSearch();
};

// 分页变化
const handlePageChange = (pageInfo: { current: number; pageSize: number }) => {
  Object.assign(pagination, pageInfo);
  loadQuestionnaireList();
};


// 编辑问卷
const handleEdit = (row: Questionnaire) => {
  // isEditMode.value = true;
  // Object.assign(currentQuestionnaire, row);
  // // 这里应该调用API获取关联的问题集
  // currentQuestionSets.value = [
  //   { question_id: 1, score: 10 },
  //   { question_id: 2, score: 20 },
  // ];
  // showCreateDialog.value = true;

  router.push(`./create/${row.questionnaire_id}`)
};

// 查看问卷详情
const handleView = (row: Questionnaire) => {
  router.push(`./questionnaire_detail/${row.questionnaire_id}`);
};

const goback = () => {
  router.push('./questionnaire');
};

const create = (questionnaire_id:number) => {
  router.push(`./create/${questionnaire_id}`)
}

// 删除问卷
const handleDelete = async (row:Questionnaire) => {
  try {
    // await deleteQuestionnaire(id);
    MessagePlugin.success('删除问卷成功');
    loadQuestionnaireList();
  } catch (error) {
    MessagePlugin.error('删除问卷失败');
    console.error(error);
  }
};

// 发布问卷
const handlePublish = async (id: number | string) => {
  try {
    // 这里应该调用API更新问卷状态为已发布
    MessagePlugin.success('发布问卷成功');
    loadQuestionnaireList();
  } catch (error) {
    MessagePlugin.error('发布问卷失败');
    console.error(error);
  }
};

const handleCancelPublish = async(id:number | string) =>{
  console.log("1")
}


// 初始化加载数据
onMounted(() => {
  loadQuestionnaireList();
});
</script>

<style lang="less" scoped>
.questionnaire-management {
  padding: 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .header-left {
      display: flex;
      align-items: center;
    }

    .header-right {
      display: flex;
      align-items: center;
    }
  }

  .search-card {
    :deep(.t-form) {
      margin-bottom: 0;
    }
  }

  .question-set-container {
    border: 1px solid var(--td-component-stroke);
    border-radius: var(--td-radius-default);
    padding: 12px;

    .question-set-item {
      display: flex;
      gap: 8px;
      margin-bottom: 8px;
      align-items: center;
    }
  }

  :deep(.t-table) {
    flex: 1;
  }
}
</style>
