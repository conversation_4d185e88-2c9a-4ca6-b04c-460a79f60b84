
import { request } from '@/utils/request';

// 定义数据结构
const BASE_URL = '/teaching/assessment';
/**
 * 考核查询参数
 */
export interface AssessmentQuery {
    taskId?: number; // 教学任务id
    courseId?: number; // 课程id
    assessmentName?: string; // 考核名称（模糊查询）
    assessmentType?: number; // 考核类型
    startTime?: string; // 考核开始时间
    endTime?: string; // 考核结束时间
    assessmentYear?: number; // 考核年份
    assessmentTerm?: number; // 考核学期
    scoreType?: number; // 成绩录入方式（0:直接录入；1:详细录入）
    pageNum?: number; // 页码
    pageSize?: number; // 每页大小
    orderBy?: string; // 排序字段
    orderType?: string; // 排序方式：asc/desc
}

/**
 * 考核信息
 */
export interface AssessmentInfo {
  id: number;
  taskId: number;
  courseId: number;
  assessmentName: string;
  description: string;
  assessmentMethod: number;
  assessmentDate: string;
  assessmentWeight: number;
  assessmentDetailList?: DirectEntryConfig[];
  assessmentYear: number;
  assessmentTerm: number;
  scoreType: number;
  status: number;
  creator: number;
  createTime: string;
  modifier: number;
  modifyTime: string;
  achievement:boolean;//是否参与达成度计算
  // 兼容前端原有字段
  courseName?: string;
  grade?: string;
  majors?: string[];
  studentCount?: number;
  teachingTeam?: { name: string; role: string }[];
  assessmentInputMode?: 'direct' | 'detailed';
  examStatus?: string;
  assessmentStatus?: number;
}

/**
 * 分页响应数据
 */
export interface PageResponse<T> {
  records: T[];
  total: number;
  size: number;
  current: number;
  pages: number;
}

/**
 * 通用API响应格式
 */
export interface ApiResponse<T> {
  code: number;
  message: string;
  data: T;
}

/**
 * 根据课程ID分页查询考核信息
 * @param query 查询参数
 * @returns 考核信息列表
 */
export const pageAssessmentExamListByCourseId = (query: AssessmentQuery): Promise<ApiResponse<PageResponse<AssessmentInfo>>> => {
  return request<ApiResponse<PageResponse<AssessmentInfo>>>({
    url: '/teaching/assessment/page',
    method: 'POST',
    data: query
  });
};

/**
 * 
 * @param id 根据考核id获取考核的基本配置信息
 * @returns 
 */
export const getAssessmentById = (id: number): Promise<ApiResponse<AssessmentInfo>> => {
  return request<ApiResponse<AssessmentInfo>>({
    url: `/teaching/assessment/${id}`,
    method: 'GET'
  });
};
/**
 * 删除考核
 */
export const deleteBatchAssessment = (ids: number[]): Promise<ApiResponse<any>> => {
  return request<ApiResponse<any>>({
    url: `/teaching/assessment/physical/batch`,
    method: 'DELETE',
    data: ids,
  });
};

// /physical/{id}
/**
 * 删除考核
 */
export const deleteAssessment = (id: number): Promise<ApiResponse<any>> => {
  return request<ApiResponse<any>>({
    url: `/teaching/assessment/physical/${id}`,
    method: 'DELETE',
  });
};
/**
 * 考核DTO，用于创建和更新考核信息
 */
export interface AssessmentDTO {
  id?: number; // 记录id，更新时必填
  taskId: number; // 教学任务id
  courseId: number; // 课程id
  assessmentName: string; // 考核名称
  assessmentMethod: number; // 考核类型
  assessmentDate?: string; // 考核时间，json格式
  assessmentWeight?: number; // 考核权重
  assessmentDetail?: string; // 考核试卷内容详情
  assessmentYear: number; // 考核年份
  assessmentTerm: number; // 考核学期
  scoreType: number; // 成绩录入方式（0:直接录入方式；1:详细录入方式）
  description?: string; // 考核描述，json格式
  achievement?: boolean; // 是否参与达成度计算
}

/**
 * 保存考核信息（创建或更新）
 * 根据 data.id 是否存在来判断操作类型：
 * - 如果 data.id 存在且不为空：执行编辑操作（PUT请求）
 * - 如果 data.id 不存在或为空：执行新增操作（POST请求）
 * @param data 考核数据
 * @returns 操作结果
 */
export const saveAssessment = (data: AssessmentDTO): Promise<ApiResponse<any>> => {
  // 判断操作模式：根据是否有 id 字段来决定是新增还是编辑
  const isEditMode = data.id !== undefined && data.id !== null && data.id > 0;

  if (isEditMode) {
    // 编辑模式：发送 PUT 请求
    console.log('执行考核编辑操作，ID:', data.id);
    return request<ApiResponse<any>>({
      url: '/teaching/assessment',
      method: 'PUT',
      data
    });
  } else {
    // 新增模式：发送 POST 请求
    console.log('执行考核新增操作');
    return request<ApiResponse<any>>({
      url: '/teaching/assessment',
      method: 'POST',
      data
    });
  }
};

const CONTENT_BASE_URL = '/assessment/content';

/**
 * 直接录入配置项
 */
export interface DirectEntryConfig {
  id?: string;
  number?: number; // 序号
  courseObjectiveId: string; // 课程目标ID
  identifier: string; // 课程目标标识符，例如 CO1, CO2
  description: string; // 课程目标描述
  totalScore: number; // 分值
  percentage?: number; // 百分比（可选，后端可计算）
}

/**
 * 直接录入配置保存请求
 */
export interface DirectEntryConfigRequest {
  assessmentId: string; // 考核ID
  configs: DirectEntryConfig[]; // 配置列表
}

/**
 * 保存直接录入模式的课程目标分值配置
 * @param data 直接录入配置数据
 * @returns 操作结果
 */
export const saveDirectEntryConfig = (directEntryConfig: DirectEntryConfigRequest): Promise<ApiResponse<any>> => {
  return request<ApiResponse<any>>({
    url: `${CONTENT_BASE_URL}/direct/save`,
    method: 'POST',
    data: directEntryConfig
  });
};

/**
 * 获取详细录入考核内容详情
 * @param assessmentId 考核ID
 * @returns AssessmentContentDetailVO
 */
export const getAssessmentContentDetail = (assessmentId: number | string): Promise<ApiResponse<any>> => {
  return request<ApiResponse<any>>({
    url: `${CONTENT_BASE_URL}/detail/${assessmentId}`,
    method: 'GET'
  });
};
/**
 * 详细录入模式-题目明细保存接口
 * @param data 后端DTO参数
 * @returns 操作结果
 */
export const saveAssessmentContentDetail = (data: any): Promise<ApiResponse<any>> => {
  return request<ApiResponse<any>>({
    url: `${CONTENT_BASE_URL}/detail/save`,
    method: 'POST',
    data
  });
};

/**获取直接录入配置 */
export const getDirectEntryConfig = (assessmentId: string): Promise<ApiResponse<DirectEntryConfig[]>> => {
  return request<ApiResponse<DirectEntryConfig[]>>({
    url: `${CONTENT_BASE_URL}/direct/${assessmentId}`,
    method: 'GET'
  });
};

/**
 * 考核发布请求数据
 */
export interface AssessmentPublishRequest {
  assessmentId: number; // 考核ID
  taskIds: number[]; // 教学任务ID列表，-1表示发布到所有任务
  //courseIds: number[]; // 课程ID列表
  courseId: number; // 课程ID
  publishToAll?: boolean; // 是否发布到所有教学任务
}

/**
 * 发布考核到教学任务
 * @param data 发布数据
 * @returns 操作结果
 */
export const publishAssessmentToTasks = (data: AssessmentPublishRequest): Promise<ApiResponse<any>> => {
  return request<ApiResponse<any>>({
    url: `${BASE_URL}/publish`,
    method: 'POST',
    data
  });
};

/**
 * 获取考核发布状态
 * @param assessmentId 考核ID
 * @returns 发布状态信息
 */
export const getAssessmentPublishStatus = (assessmentId: number): Promise<ApiResponse<any>> => {
  return request<ApiResponse<any>>({
    url: `${BASE_URL}/publish/status/${assessmentId}`,
    method: 'GET'
  });
};

/**
 * 取消考核发布
 * @param assessmentId 考核ID
 * @param taskIds 要取消发布的教学任务ID列表
 * @returns 操作结果
 */
export const cancelAssessmentPublish = (assessmentId: number, taskIds: number[]): Promise<ApiResponse<any>> => {
  return request<ApiResponse<any>>({
    url: `${BASE_URL}/publish/cancel`,
    method: 'POST',
    data: { assessmentId, taskIds }
  });
};

/**
 * 撤回考核任务
 * @param assessmentId 考核ID
 * @returns 操作结果
 */
export const withdrawAssessment = (assessmentId: number): Promise<ApiResponse<any>> => {
  return request<ApiResponse<any>>({
    url: `${BASE_URL}/publish/${assessmentId}`,
    method: 'DELETE'
  });
};
/**
 * 查询可发布的教学任务列表，前端调用，课程负责人考核发布前首先查看该内容
 */
export const getPublishableTaskList = (assessmentId: number): Promise<ApiResponse<any>> => {
  return request<ApiResponse<any>>({
    url: `${BASE_URL}/publishable-tasks/${assessmentId}`,
    method: 'GET'
  });
};

 