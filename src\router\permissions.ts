/**
 * @description 路由守卫，目前两种模式：all模式与intelligence模式
 */
import {useUserStore} from '@/store/modules/user'
import {useRoutesStore} from '@/store/modules/routes'
import 'nprogress/nprogress.css'
import getPageTitle from '@/utils/pageTitle'
import {toLoginRoute} from '@/utils/routes'
import {authentication, loginRouter, routesWhiteList,} from '@/config'
import type {Router} from 'vue-router'
import NProgress from "nprogress";
import { cacheWorkspaceFromRoute } from '@/layouts/composables/useWorkspaceCaching';
NProgress.configure({ showSpinner: false });

// 防止重复重置的标志
let isResetting = false;
// 防止无限重定向的标志
const redirectCount = new Map<string, number>();

export function setupPermissions(router: Router) {
  router.beforeEach(async (to, from, next) => {
    NProgress.start();

    // 防止无限重定向：检查重定向次数
    const routeKey = `${from.path}->${to.path}`;
    const count = redirectCount.get(routeKey) || 0;
    if (count > 3) {
      console.error('检测到可能的无限重定向:', routeKey);
      redirectCount.delete(routeKey);
      next(false); // 取消导航
      NProgress.done();
      return;
    }
    redirectCount.set(routeKey, count + 1);

    // 清理过期的重定向计数
    setTimeout(() => {
      redirectCount.delete(routeKey);
    }, 5000);

    const { routes, setRoutes } = useRoutesStore()
    const { token, getUserInfo, resetAll } = useUserStore()
    if (token) {
      if (routes.length > 0) {
        // 禁止已登录用户返回登录页
        if (to.path === loginRouter) {
          next({ path: '/' })
        } else {
          // 检查路由是否存在
          if (to.matched.length === 0) {
            console.error('❌ 路由未匹配到任何组件:', {
              路径: to.path,
              可用路由: router.getRoutes().map(route => ({ name: route.name, path: route.path }))
            });
          }
          next()
        }
      } else {
        try {
          await getUserInfo()
          // 根据路由模式获取路由并根据权限过滤
          await setRoutes(authentication)
          // 避免无限重定向：只有路径不同时才重定向
          if (to.path !== from.path) {
            next({ ...to, replace: true })
          } else {
            next()
          }
        } catch (error) {
          console.error('错误拦截:', error)
          if (!isResetting) {
            isResetting = true;
            try {
              await resetAll()
            } finally {
              isResetting = false;
            }
          }
          next(toLoginRoute(to.path))
        }
      }
    } else {
      if (routesWhiteList.includes(to.path)) {
        next()
      } else {
        next(toLoginRoute(to.path))
      }
    }
  })
  router.afterEach((to: any) => {
    document.title = getPageTitle(to.meta.title)

    // 打印路由访问成功信息
    console.log('✅ 路由导航成功:', {
      路径: to.path,
      名称: to.name,
      标题: to.meta?.title,
      组件: to.matched[to.matched.length - 1]?.components?.default?.name || '未知组件'
    });

    // 工作台缓存逻辑
    cacheWorkspaceFromRoute(to);

    // 移除登出逻辑，避免重复触发
    NProgress.done();
  })
}
