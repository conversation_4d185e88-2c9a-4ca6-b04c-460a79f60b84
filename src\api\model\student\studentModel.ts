// 学生信息接口 - 与后端StudentVO兼容
export interface StudentInfo {
  id: number | string;
  name: string;
  studentNumber: string;
  gender: number;
  phone?: string;
  email?: string;
  college: string;
  collegeId: string | number;
  majorName: string;
  majorId: string | number;
  classValue: string;
  classId: string | number;
  entranceYear: string;
  studentStatus: number;
  status: number;
  creator: string;
  createTime: string;
  modifier: string;
  modifyTime: string;
}

// 查询参数接口 - 与后端StudentQueryDTO兼容
export interface StudentQueryParams {
  current?: number;
  size?: number;
  studentId?: number;
  studentName?: string;
  studentNumber?: string;
  gender?: number;
  classId?: number;
  majorId?: number;
  academyId?: number;
  entranceYear?: string;
  studentStatus?: number;
  status?: number;
  // 前端扩展字段
  phone?: string;
  email?: string;
}

// 分页结果接口
export interface StudentListResult {
  code: number;
  message: string;
  data: {
    records: StudentInfo[];
    total: number;
    current: number;
    pageSize: number;
  };
}

// 详情结果接口
export interface StudentDetailResult {
  code: number;
  message: string;
  data: StudentInfo;
}

// 操作结果接口
export interface StudentOperationResult {
  code: number;
  message: string;
  data?: any;
}

// 表单数据接口
export interface StudentFormData {
  studentId?: number | string;
  number: string;
  user: {
    realName: string;
    gender: number;
    phone?: string;
    email?: string;
    username: string;
  };
  classId: number | string;
  majorId: number | string;
  academyId: number | string;
  entranceYear: string;
  studentStatus: number;
}

// 导入结果接口（新格式）
export interface ImportResult {
  code: number;
  message: string;
  data: {
    success: boolean;
    successCount: number;
    failCount: number;
    totalCount: number;
    successMessage?: string;
    errorMessages?: string[];
  } | string; // 兼容旧格式的字符串响应
} 