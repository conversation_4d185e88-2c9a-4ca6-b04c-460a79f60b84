<template>
  <div>
    <!-- 直接录入模式 - 普通弹窗 -->
    <t-dialog
      v-model:visible="directDialogVisible"
      :header="directDialogTitle"
      width="90vw"
      :top="50"
      
      :footer="false"
      @close="handleClose"
    >
      <div class="grade-management-dialog">
        <!-- 考核内容信息 -->
        <div class="assessment-info">
          <div class="info-content">
            <h3>{{ assessmentContent?.title || assessmentContent?.name }}</h3>
            <div class="info-meta">
              <!-- 班级信息为空时不显示 -->
              <div v-if="classInfo?.className" class="meta-item">
                <span class="label">班级：</span>
                <span class="value">{{ classInfo.className }}</span>
              </div>
              <div v-if="classInfo?.studentCount" class="meta-item">
                <span class="label">人数：</span>
                <span class="value">{{ classInfo.studentCount }}人</span>
              </div>
              <div class="meta-item">
                <span class="label">录入方式：</span>
                <t-tag size="small" theme="primary">直接录入</t-tag>
              </div>
            </div>
          </div>
          <div class="info-actions">
            <t-button theme="primary" @click="handleImportGrades">
              <template #icon>
                <t-icon name="upload" />
              </template>
              导入成绩
            </t-button>
            <t-button theme="default" variant="outline" @click="handleExportGrades">
              <template #icon>
                <t-icon name="file-export" />
              </template>
              导出成绩
            </t-button>
            <t-button theme="default" variant="outline" @click="handleRefresh">
              <template #icon>
                <t-icon name="refresh" />
              </template>
              刷新
            </t-button>
          </div>
        </div>

        <!-- 成绩统计卡片 -->
        <div class="stats-cards">
          <div class="stat-card">
            <div class="stat-icon average">
              <t-icon name="chart-line" />
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ directGradeStats.averageScore }}</div>
              <div class="stat-label">平均分</div>
            </div>
          </div>
          
          <div class="stat-card">
            <div class="stat-icon highest">
              <t-icon name="arrow-up" />
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ directGradeStats.maxScore }}</div>
              <div class="stat-label">最高分</div>
            </div>
          </div>
          
          <div class="stat-card">
            <div class="stat-icon lowest">
              <t-icon name="arrow-down" />
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ directGradeStats.minScore }}</div>
              <div class="stat-label">最低分</div>
            </div>
          </div>
          
          <div class="stat-card">
            <div class="stat-icon submitted">
              <t-icon name="check-circle" />
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ directGradeStats.submittedCount }}</div>
              <div class="stat-label">已录入</div>
            </div>
          </div>
          
          <div class="stat-card">
            <div class="stat-icon pending">
              <t-icon name="time" />
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ directGradeStats.pendingCount }}</div>
              <div class="stat-label">待录入</div>
            </div>
          </div>
        </div>

        <!-- 搜索工具栏 -->
        <div class="search-toolbar">
          <t-input
            v-model="searchKeyword"
            placeholder="搜索学号或姓名"
            style="width: 300px"
            clearable
          >
            <template #prefix-icon>
              <t-icon name="search" />
            </template>
          </t-input>
        </div>

        <!-- 学生成绩表格 -->
        <div class="grade-table">
          <t-table
            :data="filteredDirectGradeList"
            :columns="directTableColumns"
            row-key="studentId"
            stripe
            hover
            :loading="loading"
            :pagination="directPagination"
            @page-change="handleDirectPageChange"
          >
            <!-- 课程目标列的行内编辑 -->
            <template v-for="objective in courseObjectives" :key="objective.id" #[`objectives.${objective.id}`]="{ row }">
              <div class="inline-edit-cell">
                <t-input-number
                  v-if="editingRows[row.studentId]"
                  v-model="row.objectives[objective.id]"
                  :min="0"
                  :max="100"
                  :decimal-places="1"
                  placeholder="请输入分数"
                  style="width: 120px"
                  @blur="handleSaveRowEdit(row)"
                  @enter="handleSaveRowEdit(row)"
                />
                <span v-else class="score-display" @click="handleStartEdit(row)">
                  {{ row.objectives[objective.id] || '-' }}
                </span>
              </div>
            </template>

            <template #totalScore="{ row }">
              <span class="total-score" :class="{ 'high-score': (row.totalScore || 0) >= 80 }">
                {{ row.totalScore || '-' }}
              </span>
            </template>
            
            <template #actions="{ row }">
              <t-space>
                <t-button
                  v-if="!editingRows[row.studentId]"
                  theme="primary"
                  variant="text"
                  size="small"
                  @click="handleStartEdit(row)"
                >
                  编辑
                </t-button>
                <template v-else>
                  <t-button
                    theme="primary"
                    variant="text"
                    size="small"
                    @click="handleSaveRowEdit(row)"
                  >
                    保存
                  </t-button>
                  <t-button
                    theme="default"
                    variant="text"
                    size="small"
                    @click="handleCancelEdit(row)"
                  >
                    取消
                  </t-button>
                </template>
              </t-space>
            </template>
          </t-table>
        </div>
      </div>
    </t-dialog>

    <!-- 文件导入对话框 -->
    <ImportDialog
      v-model:visible="importDialogVisible"
      :config="importConfig"
      :callbacks="importCallbacks"
      :dialog-props="{ zIndex: 3100 }"
    />

    <!-- 详细录入模式 - 全屏弹窗 -->
    <FullScreenDialog
      v-model:visible="detailedDialogVisible"
      :title="detailedDialogTitle"
      @close="handleClose"
    >
      <div class="detailed-grade-management">
        <!-- 考核内容信息 -->
        <div class="assessment-info">
          <div class="info-content">
            <h3>{{ assessmentContent?.title || assessmentContent?.name }}</h3>
            <div class="info-meta">
              <!-- 班级信息为空时不显示 -->
              <div v-if="classInfo?.className" class="meta-item">
                <span class="label">班级：</span>
                <span class="value">{{ classInfo.className }}</span>
              </div>
              <div v-if="classInfo?.studentCount" class="meta-item">
                <span class="label">人数：</span>
                <span class="value">{{ classInfo.studentCount }}人</span>
              </div>
              <div class="meta-item">
                <span class="label">录入方式：</span>
                <t-tag size="small" theme="success">详细录入</t-tag>
              </div>
            </div>
          </div>
          <div class="info-actions">
            <t-button theme="default" variant="outline" @click="handleRefresh">
              <template #icon>
                <t-icon name="refresh" />
              </template>
              刷新
            </t-button>
          </div>
        </div>

        <!-- 成绩统计卡片 -->
        <div class="stats-cards">
          <div class="stat-card">
            <div class="stat-icon average">
              <t-icon name="chart-line" />
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ detailedGradeStats.averageScore }}</div>
              <div class="stat-label">平均分</div>
            </div>
          </div>
          
          <div class="stat-card">
            <div class="stat-icon highest">
              <t-icon name="arrow-up" />
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ detailedGradeStats.maxScore }}</div>
              <div class="stat-label">最高分</div>
            </div>
          </div>
          
          <div class="stat-card">
            <div class="stat-icon lowest">
              <t-icon name="arrow-down" />
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ detailedGradeStats.minScore }}</div>
              <div class="stat-label">最低分</div>
            </div>
          </div>
          
          <div class="stat-card">
            <div class="stat-icon submitted">
              <t-icon name="check-circle" />
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ detailedGradeStats.submittedCount }}</div>
              <div class="stat-label">已录入</div>
            </div>
          </div>
          
          <div class="stat-card">
            <div class="stat-icon pending">
              <t-icon name="time" />
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ detailedGradeStats.pendingCount }}</div>
              <div class="stat-label">待录入</div>
            </div>
          </div>
        </div>

        <!-- 搜索和筛选工具栏 -->
        <div class="search-toolbar">
          <t-input
            v-model="searchKeyword"
            placeholder="搜索学号或姓名"
            style="width: 300px"
            clearable
          >
            <template #prefix-icon>
              <t-icon name="search" />
            </template>
          </t-input>
          <div class="filter-section">
            <t-select
              v-model="gradeFilter"
              placeholder="成绩筛选"
              style="width: 150px"
              clearable
            >
              <t-option value="all" label="全部" />
              <t-option value="submitted" label="已录入" />
              <t-option value="pending" label="待录入" />
            </t-select>
            
            <t-button variant="outline" @click="columnSettingsVisible = true">
              <template #icon>
                <t-icon name="view-list" />
              </template>
              列设置
            </t-button>
            
            <t-button theme="primary" @click="handleImportDetailedGrades">
              <template #icon>
                <t-icon name="upload" />
              </template>
              导入成绩
            </t-button>
            
            <t-button theme="default" variant="outline" @click="handleExportDetailedGrades">
              <template #icon>
                <t-icon name="file-export" />
              </template>
              导出成绩
            </t-button>
          </div>
        </div>

        <!-- 详细成绩表格 -->
        <div class="detailed-grade-table">
          <t-table
            :data="filteredDetailedGradeList"
            :columns="detailedTableColumns"
            row-key="studentId"
            stripe
            hover
            :loading="loading"
            bordered
            :pagination="detailedPagination"
            @page-change="handleDetailedPageChange"
          >
            <template #totalScore="{ row }">
              <span class="total-score" :class="{ 'high-score': (row.totalScore || 0) >= 80 }">
                {{ row.totalScore || '-' }}
              </span>
            </template>
            
            <template #actions="{ row }">
              <t-space>
                <t-button
                  v-if="!editingRows[row.studentId]"
                  theme="primary"
                  variant="text"
                  size="small"
                  @click="handleStartDetailedEdit(row)"
                >
                  编辑
                </t-button>
                <template v-else>
                  <t-button
                    theme="primary"
                    variant="text"
                    size="small"
                    @click="handleSaveDetailedRowEdit(row)"
                  >
                    保存
                  </t-button>
                  <t-button
                    theme="default"
                    variant="text"
                    size="small"
                    @click="handleCancelDetailedEdit(row)"
                  >
                    取消
                  </t-button>
                </template>
              </t-space>
            </template>
          </t-table>
        </div>
      </div>
    </FullScreenDialog>

    <!-- 直接录入编辑弹窗 -->
    <t-dialog
      v-model:visible="directEditVisible"
      title="编辑成绩"
      width="600px"
      @confirm="handleSaveDirectGrade"
      @close="handleCloseDirectEdit"
    >
      <div class="direct-grade-edit" v-if="editingDirectGrade">
        <div class="student-info">
          <h4>{{ editingDirectGrade.studentName }} ({{ editingDirectGrade.studentId }})</h4>
        </div>
        <div class="grade-form">
          <div class="form-section">
            <h5>课程目标成绩</h5>
            <div class="objective-grades">
              <div
                v-for="(objective, index) in courseObjectives"
                :key="objective.id"
                class="objective-item"
              >
                <div class="objective-label">
                  {{ objective.name }}
                </div>
                <t-input-number
                  v-model="editingDirectGrade.objectives[objective.id]"
                  :min="0"
                  :max="100"
                  :decimal-places="1"
                  placeholder="请输入分数"
                  style="width: 120px"
                />
              </div>
            </div>
          </div>
          <div class="total-score-section">
            <div class="total-label">总分：</div>
            <div class="total-value">{{ calculateDirectTotal(editingDirectGrade) }}</div>
          </div>
        </div>
      </div>
    </t-dialog>

    <!-- 详细录入编辑弹窗 -->
    <t-dialog
      v-model:visible="detailedEditVisible"
      title="编辑详细成绩"
      width="800px"
      @confirm="handleSaveDetailedGrade"
      @close="handleCloseDetailedEdit"
    >
      <div class="detailed-grade-edit" v-if="editingDetailedGrade">
        <div class="student-info">
          <h4>{{ editingDetailedGrade.studentName }} ({{ editingDetailedGrade.studentId }})</h4>
        </div>
        <div class="grade-form">
          <div class="form-section">
            <h5>题目成绩</h5>
            <div class="question-grades">
              <div
                v-for="question in questionStructure"
                :key="question.id"
                class="question-group"
              >
                <div class="question-header">
                  <span class="question-number">{{ question.questionNumber }}</span>
                  <span class="question-type">{{ question.questionType }}</span>
                </div>
                <div class="sub-items">
                  <div
                    v-for="subItem in question.subItems"
                    :key="subItem.id"
                    class="sub-item"
                  >
                    <div class="sub-label">
                      {{ subItem.subNumber }}
                    </div>
                    <t-input-number
                      v-model="editingDetailedGrade.questions[subItem.id]"
                      :min="0"
                      :max="subItem.maxScore"
                      :decimal-places="1"
                      :placeholder="`满分${subItem.maxScore}`"
                      style="width: 100px"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="total-score-section">
            <div class="total-label">总分：</div>
            <div class="total-value">{{ calculateDetailedTotal(editingDetailedGrade) }}</div>
          </div>
        </div>
      </div>
    </t-dialog>

    <!-- 详细录入导入对话框 -->
    <ImportDialog
      v-model:visible="detailedImportDialogVisible"
      :config="detailedImportConfig"
    
      :callbacks="detailedImportCallbacks"
      :dialog-props="{ zIndex: 3300 }"
    />

    <!-- 列设置弹窗 -->
    <t-dialog
      v-model:visible="columnSettingsVisible"
      header="题目类型设置"
      width="500px"
      :z-index="3100"
      attach="body"
      placement="center"
      destroy-on-close
    >
      <div class="column-settings">
        <p class="settings-description">选择要显示的题目类型</p>
        <div class="checkbox-group">
          <t-checkbox
            v-for="type in allQuestionTypes"
            :key="type"
            :checked="visibleQuestionTypes.includes(type)"
            @change="(checked) => handleQuestionTypeVisibilityChange(type, checked)"
          >
            {{ type }}
          </t-checkbox>
        </div>
      </div>
      <template #footer>
        <t-button @click="columnSettingsVisible = false">取消</t-button>
        <t-button theme="primary" @click="handleSaveColumnSettings">确定</t-button>
      </template>
    </t-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, reactive } from 'vue'
import { MessagePlugin } from 'tdesign-vue-next'
import FullScreenDialog from '@/components/FullScreenDialog/index.vue'
import ImportDialog from '@/components/ImportDialog/index.vue'
import type { ImportConfig, ImportCallbacks } from '@/components/ImportDialog/types'
// import type { AssessmentContent } from '../types'
// import type { WorklistItem } from '@/api/base/classes'
import * as XLSX from 'xlsx'
import {
  Button as TButton,
  Icon as TIcon,
  Tag as TTag,
  Dialog as TDialog,
  Input as TInput,
  InputNumber as TInputNumber,
  Select as TSelect,
  Option as TOption,
  Table as TTable,
  Space as TSpace,
  Checkbox as TCheckbox
} from 'tdesign-vue-next'

// 只接收一个data对象
const props = defineProps<{ data: {
  visible: boolean,
  assessmentContent: any,
  classInfo: any,
  gradeList: any[],
  status: number
} }>()
const emit = defineEmits(['close'])

// 解构data，只用props.data.visible，不再用props.visible
const visible = computed(() => props.data.visible)
const assessmentContent = computed(() => props.data.assessmentContent)
const classInfo = computed(() => props.data.classInfo)
const gradeList = computed(() => props.data.gradeList)
const status = computed(() => props.data.status)

// 响应式数据
const loading = ref(false)
const searchKeyword = ref('')
const gradeFilter = ref('all')

// 弹窗显示状态
const directDialogVisible = ref(false)
const detailedDialogVisible = ref(false)
const directEditVisible = ref(false)
const detailedEditVisible = ref(false)
const importDialogVisible = ref(false)
const detailedImportDialogVisible = ref(false)
const columnSettingsVisible = ref(false)

// 列设置状态
const visibleQuestionTypes = ref<string[]>([
  '单选题',
  '多选题',
  '填空题',
  '简答题',
  '论述题',
  '编程题'
])

// 获取所有题目类型
const allQuestionTypes = computed(() => {
  const types = new Set<string>()
  questionStructure.value.forEach(question => {
    types.add(question.questionType)
  })
  return Array.from(types)
})

// 行内编辑状态
const editingRows = ref<Record<string, boolean>>({})
const originalRowData = ref<Record<string, any>>({})

// 分页配置
const directPagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showJumper: true,
  showSizeChanger: true,
  pageSizeOptions: [10, 20, 50, 100]
})
const detailedPagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showJumper: true,
  showSizeChanger: true,
  pageSizeOptions: [10, 20, 50, 100]
})

// 编辑数据
const editingDirectGrade = ref<any>(null)
const editingDetailedGrade = ref<any>(null)

// 弹窗标题
const directDialogTitle = computed(() => {
  return `成绩管理 - ${assessmentContent.value?.title || ''}`
})

const detailedDialogTitle = computed(() => {
  return `详细成绩管理 - ${assessmentContent.value?.title || ''}`
})

// 课程目标数据
const courseObjectives = ref([
  { id: 'obj1', name: '课程目标1', description: '掌握基本概念' },
  { id: 'obj2', name: '课程目标2', description: '理解核心原理' },
  { id: 'obj3', name: '课程目标3', description: '应用实践能力' }
])

// 题目结构数据
const questionStructure = ref([
  {
    id: 'q1',
    questionNumber: '1',
    questionType: '单选题',
    subItems: [
      { id: 'q1_1', subNumber: '1.1', maxScore: 4 },
      { id: 'q1_2', subNumber: '1.2', maxScore: 4 },
      { id: 'q1_3', subNumber: '1.3', maxScore: 4 },
      { id: 'q1_4', subNumber: '1.4', maxScore: 4 },
      { id: 'q1_5', subNumber: '1.5', maxScore: 4 }
    ]
  },
  {
    id: 'q2',
    questionNumber: '2',
    questionType: '多选题',
    subItems: [
      { id: 'q2_1', subNumber: '2.1', maxScore: 5 },
      { id: 'q2_2', subNumber: '2.2', maxScore: 5 },
      { id: 'q2_3', subNumber: '2.3', maxScore: 5 }
    ]
  },
  {
    id: 'q3',
    questionNumber: '3',
    questionType: '填空题',
    subItems: [
      { id: 'q3_1', subNumber: '3.1', maxScore: 3 },
      { id: 'q3_2', subNumber: '3.2', maxScore: 3 },
      { id: 'q3_3', subNumber: '3.3', maxScore: 3 },
      { id: 'q3_4', subNumber: '3.4', maxScore: 3 }
    ]
  },
  {
    id: 'q4',
    questionNumber: '4',
    questionType: '简答题',
    subItems: [
      { id: 'q4_1', subNumber: '4.1', maxScore: 10 },
      { id: 'q4_2', subNumber: '4.2', maxScore: 10 }
    ]
  },
  {
    id: 'q5',
    questionNumber: '5',
    questionType: '论述题',
    subItems: [
      { id: 'q5_1', subNumber: '5.1', maxScore: 20 }
    ]
  },
  {
    id: 'q6',
    questionNumber: '6',
    questionType: '编程题',
    subItems: [
      { id: 'q6_1', subNumber: '6.1', maxScore: 15 }
    ]
  }
])

// 移除模拟成绩数据，成绩数据全部用gradeList.value
// const directGradeList = ref([...])
// const detailedGradeList = ref([...])

// 直接录入成绩统计
const directGradeStats = computed(() => {
  const submittedGrades = gradeList.value.filter(item => item.status === 'submitted' && item.totalScore !== null)
  const totalScores = submittedGrades.map(item => item.totalScore).filter(score => score !== null) as number[]
  
  return {
    averageScore: totalScores.length > 0 ? (totalScores.reduce((a, b) => a + b, 0) / totalScores.length).toFixed(1) : '-',
    maxScore: totalScores.length > 0 ? Math.max(...totalScores).toFixed(1) : '-',
    minScore: totalScores.length > 0 ? Math.min(...totalScores).toFixed(1) : '-',
    submittedCount: submittedGrades.length,
    pendingCount: gradeList.value.length - submittedGrades.length
  }
})

// 详细录入统计数据
const detailedGradeStats = computed(() => {
  const submitted = gradeList.value.filter(item => item.status === 'submitted')
  const scores = submitted.map(item => item.totalScore).filter(score => score !== null) as number[]
  
  return {
    averageScore: scores.length > 0 ? (scores.reduce((a, b) => a + b, 0) / scores.length).toFixed(1) : '-',
    maxScore: scores.length > 0 ? Math.max(...scores).toFixed(1) : '-',
    minScore: scores.length > 0 ? Math.min(...scores).toFixed(1) : '-',
    submittedCount: submitted.length,
    pendingCount: gradeList.value.length - submitted.length
  }
})

// 过滤后的直接录入成绩列表（支持搜索）
const filteredDirectGradeList = computed(() => {
  let filtered = [...gradeList.value]
  
  // 搜索过滤
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(item => 
      item.studentId.toLowerCase().includes(keyword) ||
      item.studentName.toLowerCase().includes(keyword)
    )
  }
  
  // 更新分页总数
  directPagination.total = filtered.length
  
  // 分页处理
  const start = (directPagination.current - 1) * directPagination.pageSize
  const end = start + directPagination.pageSize
  
  return filtered.slice(start, end)
})

// 直接录入表格列定义（移除cell属性，使用template）
const directTableColumns = computed(() => {
  const columns: any[] = [
    {
      colKey: 'studentId',
      title: '学号',
      width: 120,
      align: 'center' as const
    },
    {
      colKey: 'studentName',
      title: '姓名',
      width: 100,
      align: 'center' as const
    }
  ]
  
  // 添加课程目标列
  courseObjectives.value.forEach(objective => {
    columns.push({
      colKey: `objectives.${objective.id}`,
      title: objective.name,
      width: 120,
      align: 'center' as const
    })
  })
  
  columns.push(
    {
      colKey: 'totalScore',
      title: '总分',
      width: 100,
      align: 'center' as const
    },
    {
      colKey: 'actions',
      title: '操作',
      width: 120,
      align: 'center' as const
    }
  )
  
  return columns
})

// 过滤后的详细成绩列表
const filteredDetailedGradeList = computed(() => {
  let filtered = [...gradeList.value]
  
  // 搜索过滤
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(item => 
      item.studentId.toLowerCase().includes(keyword) ||
      item.studentName.toLowerCase().includes(keyword)
    )
  }
  
  // 状态过滤
  if (gradeFilter.value !== 'all') {
    filtered = filtered.filter(item => item.status === gradeFilter.value)
  }
  
  // 更新分页总数
  detailedPagination.total = filtered.length
  
  // 分页处理
  const start = (detailedPagination.current - 1) * detailedPagination.pageSize
  const end = start + detailedPagination.pageSize
  
  return filtered.slice(start, end)
})

// 详细录入表格列定义
const detailedTableColumns = computed(() => {
  const columns: any[] = [
    {
      colKey: 'studentId',
      title: '学号',
      width: 120,
      align: 'center' as const,
      fixed: 'left' as const
    },
    {
      colKey: 'studentName',
      title: '姓名',
      width: 100,
      align: 'center' as const,
      fixed: 'left' as const
    }
  ]
  
  // 按题目类型分组添加列
  const questionTypeGroups = new Map<string, any[]>()
  
  questionStructure.value.forEach(question => {
    if (!questionTypeGroups.has(question.questionType)) {
      questionTypeGroups.set(question.questionType, [])
    }
    questionTypeGroups.get(question.questionType)!.push(question)
  })
  
  // 为每个题目类型添加列（仅添加visibleQuestionTypes中的类型）
  questionTypeGroups.forEach((questions, questionType) => {
    // 如果题型不在可见列表中，跳过
    if (!visibleQuestionTypes.value.includes(questionType)) {
      return
    }
    
    // 创建题型分组列
    const typeColumns: any[] = []
    
    // 为每个题目添加子列
    questions.forEach(question => {
      question.subItems.forEach((subItem: any) => {
        typeColumns.push({
          colKey: `questions.${subItem.id}`,
          title: `${subItem.subNumber.split('.')[1]}`,
          width: 140,
          align: 'center' as const,
          cell: (h: any, { row }: { row: any }) => {
            if (editingRows.value[row.studentId]) {
              return h(TInputNumber, {
                modelValue: row.questions[subItem.id],
                'onUpdate:modelValue': (value: number) => {
                  row.questions[subItem.id] = value
                },
                min: 0,
                max: subItem.maxScore,
                decimalPlaces: 1,
                placeholder: `/${subItem.maxScore}`,
                style: { width: '120px' },
                onBlur: () => handleSaveDetailedRowEdit(row),
                onEnter: () => handleSaveDetailedRowEdit(row)
              })
            } else {
              return h('span', {
                class: 'score-display',
                style: { cursor: 'pointer', padding: '4px 8px', borderRadius: '4px' },
                onClick: () => handleStartDetailedEdit(row)
              }, row.questions[subItem.id] || '-')
            }
          }
        })
      })
    })
    
    // 添加题型分组列
    columns.push({
      colKey: `type_${questionType}`,
      title: questionType,
      align: 'center' as const,
      children: typeColumns
    })
  })
  
  columns.push(
    {
      colKey: 'totalScore',
      title: '总分',
      width: 80,
      align: 'center' as const,
      fixed: 'right' as const
    },
    {
      colKey: 'actions',
      title: '操作',
      width: 120,
      align: 'center' as const,
      fixed: 'right' as const
    }
  )
  
  return columns
})

// 文件导入配置
const importConfig: ImportConfig = {
  title: '导入成绩',
  tips: '请按照模板格式填写学生成绩信息，支持批量导入成绩',
  templateFileName: '成绩导入模板.xlsx',
  templateData: [
    ['学号', '姓名', '课程目标1', '课程目标2', '课程目标3', '备注'],
    ['2021001', '张三', '85', '88', '82', ''],
    ['2021002', '李四', '78', '85', '80', ''],
    ['2021003', '王五', '90', '87', '85', '']
  ],
  acceptTypes: ['.xlsx', '.xls', '.csv'],
  maxFileSize: 10
}

// 文件导入回调函数
const importCallbacks: ImportCallbacks = {
  onImport: async (file: File) => {
    // 模拟导入API调用
    return new Promise((resolve) => {
      setTimeout(() => {
        const random = Math.random()
        if (random > 0.2) {
          resolve({
            success: true,
            successMessage: `成功导入 ${Math.floor(random * 30 + 20)} 名学生成绩`,
            successCount: Math.floor(random * 30 + 20),
            failCount: 0
          })
        } else {
          resolve({
            success: false,
            successCount: Math.floor(random * 10),
            failCount: Math.floor(random * 5 + 3),
            errorMessages: [
              '第3行：学号不能为空',
              '第5行：课程目标1分数超出范围',
              '第8行：学号不存在'
            ]
          })
        }
      }, 2000)
    })
  },
  onSuccess: () => {
    loadGradeData()
    MessagePlugin.success('成绩导入成功')
  },
  onError: (error: Error) => {
    console.error('导入失败:', error)
    MessagePlugin.error('成绩导入失败')
  },
  onComplete: () => {
    importDialogVisible.value = false
  }
}

// 详细录入导入配置
const detailedImportConfig: ImportConfig = {
  title: '导入详细成绩',
  tips: '请按照模板格式填写学生详细成绩信息，支持批量导入成绩',
  templateFileName: '详细成绩导入模板.xlsx',
  templateData: generateDetailedImportTemplate(),
  acceptTypes: ['.xlsx', '.xls', '.csv'],
  maxFileSize: 10
}

// 生成详细导入模板
function generateDetailedImportTemplate() {
  // 表头行
  const headers = ['学号', '姓名']
  
  // 添加题目列 - 按题型分组
  const questionTypeGroups = new Map<string, any[]>()
  
  questionStructure.value.forEach(question => {
    if (!questionTypeGroups.has(question.questionType)) {
      questionTypeGroups.set(question.questionType, [])
    }
    questionTypeGroups.get(question.questionType)!.push(question)
  })
  
  // 按题型顺序添加列
  questionTypeGroups.forEach((questions, questionType) => {
    // 只为可见的题型添加列
    if (visibleQuestionTypes.value.includes(questionType)) {
      questions.forEach(question => {
        question.subItems.forEach((subItem: any) => {
          headers.push(`${question.questionNumber}.${subItem.subNumber.split('.')[1]}(${subItem.maxScore}分)`)
        })
      })
    }
  })
  
  headers.push('备注')
  
  // 示例数据行
  const exampleRow1 = ['2021001', '张三']
  const exampleRow2 = ['2021002', '李四']
  
  // 添加示例分数 - 按题型分组
  questionTypeGroups.forEach((questions, questionType) => {
    // 只为可见的题型添加示例分数
    if (visibleQuestionTypes.value.includes(questionType)) {
      questions.forEach(question => {
        question.subItems.forEach((subItem: any) => {
          // 随机生成示例分数，但不超过最大分数
          const maxScore = subItem.maxScore
          exampleRow1.push(String(Math.floor(Math.random() * maxScore * 0.8 + maxScore * 0.2)))
          exampleRow2.push(String(Math.floor(Math.random() * maxScore * 0.8 + maxScore * 0.2)))
        })
      })
    }
  })
  
  exampleRow1.push('')
  exampleRow2.push('')
  
  return [headers, exampleRow1, exampleRow2]
}

// 详细录入导入回调函数
const detailedImportCallbacks: ImportCallbacks = {
  onImport: async (file: File) => {
    // 模拟导入API调用
    return new Promise((resolve) => {
      setTimeout(() => {
        const random = Math.random()
        if (random > 0.2) {
          resolve({
            success: true,
            successMessage: `成功导入 ${Math.floor(random * 30 + 20)} 名学生详细成绩`,
            successCount: Math.floor(random * 30 + 20),
            failCount: 0
          })
        } else {
          resolve({
            success: false,
            successCount: Math.floor(random * 10),
            failCount: Math.floor(random * 5 + 3),
            errorMessages: [
              '第3行：学号不能为空',
              '第5行：题目1.2分数超出范围',
              '第8行：学号不存在'
            ]
          })
        }
      }, 2000)
    })
  },
  onSuccess: () => {
    loadGradeData()
    MessagePlugin.success('详细成绩导入成功')
  },
  onError: (error: Error) => {
    console.error('导入失败:', error)
    MessagePlugin.error('详细成绩导入失败')
  },
  onComplete: () => {
    detailedImportDialogVisible.value = false
  }
}

// 监听props变化
watch(() => props.data.visible, (newVisible) => {
  console.log('GradeManagementDialog - watch visible:', newVisible, 'assessmentContent:', assessmentContent.value)
  
  if (newVisible && assessmentContent.value) {
    if (assessmentContent.value.inputMode === 'direct') {
      directDialogVisible.value = true
      detailedDialogVisible.value = false
      console.log('GradeManagementDialog - 显示直接录入弹窗')
    } else {
      detailedDialogVisible.value = true
      directDialogVisible.value = false
      console.log('GradeManagementDialog - 显示详细录入弹窗')
    }
    loadGradeData()
  } else {
    directDialogVisible.value = false
    detailedDialogVisible.value = false
    console.log('GradeManagementDialog - 关闭弹窗')
  }
})

// 加载成绩数据
const loadGradeData = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    MessagePlugin.success('成绩数据加载成功')
  } catch (error) {
    MessagePlugin.error('加载成绩数据失败')
  } finally {
    loading.value = false
  }
}

// 计算直接录入总分
const calculateDirectTotal = (gradeData: any) => {
  if (!gradeData || !gradeData.objectives) return '0'
  
  const scores = Object.values(gradeData.objectives).filter(score => score !== null) as number[]
  if (scores.length === 0) return '0'
  
  return (scores.reduce((a, b) => a + b, 0) / scores.length).toFixed(1)
}

// 计算详细录入总分
const calculateDetailedTotal = (gradeData: any) => {
  if (!gradeData || !gradeData.questions) return 0
  
  const scores = Object.values(gradeData.questions).filter(score => score !== null) as number[]
  return scores.reduce((a, b) => a + b, 0)
}

// 事件处理
const handleClose = () => {
  emit('close')
}

const handleRefresh = () => {
  loadGradeData()
}

const handleDetailedGradeEntry = () => {
  MessagePlugin.info('跳转到详细批量录入页面')
}

const handleExportGrades = () => {
  MessagePlugin.info('导出直接录入成绩')
}

const handleExportDetailedGrades = () => {
  try {
    MessagePlugin.info('正在导出详细成绩...')
    
    // 准备导出数据
    const exportData: string[][] = []
    
    // 添加表头行
    const headers = ['学号', '姓名']
    
    // 按题型分组添加列标题
    const questionTypeGroups = new Map<string, any[]>()
    
    questionStructure.value.forEach(question => {
      if (!questionTypeGroups.has(question.questionType)) {
        questionTypeGroups.set(question.questionType, [])
      }
      questionTypeGroups.get(question.questionType)!.push(question)
    })
    
    // 只导出可见题型的数据
    questionTypeGroups.forEach((questions, questionType) => {
      if (visibleQuestionTypes.value.includes(questionType)) {
        questions.forEach(question => {
          question.subItems.forEach((subItem: any) => {
            headers.push(`${question.questionNumber}.${subItem.subNumber.split('.')[1]}(${subItem.maxScore}分)`)
          })
        })
      }
    })
    
    headers.push('总分')
    headers.push('状态')
    
    exportData.push(headers)
    
    // 添加学生数据行
    gradeList.value.forEach(student => {
      const row = [student.studentId, student.studentName]
      
      // 添加成绩数据
      questionTypeGroups.forEach((questions, questionType) => {
        if (visibleQuestionTypes.value.includes(questionType)) {
          questions.forEach(question => {
            question.subItems.forEach((subItem: any) => {
              // 使用类型断言处理索引类型问题
              const questions = student.questions as Record<string, number | null>
              const score = questions[subItem.id]
              row.push(score !== null && score !== undefined ? String(score) : '')
            })
          })
        }
      })
      
      // 添加总分和状态
      row.push(student.totalScore !== null ? String(student.totalScore) : '')
      row.push(student.status === 'submitted' ? '已录入' : '待录入')
      
      exportData.push(row)
    })
    
    // 使用xlsx库导出数据
    const ws = XLSX.utils.aoa_to_sheet(exportData)
    const wb = XLSX.utils.book_new()
    XLSX.utils.book_append_sheet(wb, ws, '详细成绩')
    
    // 生成文件名
    const fileName = `${assessmentContent.value?.title || '详细成绩'}_${classInfo.value?.className || ''}_${new Date().toISOString().slice(0, 10)}.xlsx`
    
    // 导出文件
    XLSX.writeFile(wb, fileName)
    
    MessagePlugin.success('详细成绩导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    MessagePlugin.error('详细成绩导出失败')
  }
}

const handleEditDetailedGrade = (row: any) => {
  editingDetailedGrade.value = { ...row }
  detailedEditVisible.value = true
}

const handleSaveDirectGrade = async () => {
  try {
    // 重新计算总分
    if (editingDirectGrade.value) {
      const scores = Object.values(editingDirectGrade.value.objectives).filter(score => score !== null && score !== undefined) as number[]
      if (scores.length > 0) {
        editingDirectGrade.value.totalScore = parseFloat(scores.reduce((a, b) => a + b, 0).toFixed(1))
        editingDirectGrade.value.status = 'submitted'
      }
      
      // 更新原数据
      const index = gradeList.value.findIndex(item => item.studentId === editingDirectGrade.value.studentId)
      if (index !== -1) {
        gradeList.value[index] = { ...editingDirectGrade.value }
      }
    }
    
    directEditVisible.value = false
    editingDirectGrade.value = null
    MessagePlugin.success('成绩保存成功')
  } catch (error) {
    MessagePlugin.error('保存成绩失败')
  }
}

const handleSaveDetailedGrade = async () => {
  try {
    // 重新计算总分
    if (editingDetailedGrade.value) {
      const scores = Object.values(editingDetailedGrade.value.questions).filter(score => score !== null && score !== undefined) as number[]
      if (scores.length > 0) {
        editingDetailedGrade.value.totalScore = parseFloat(scores.reduce((a, b) => a + b, 0).toFixed(1))
        editingDetailedGrade.value.status = 'submitted'
      }
      
      // 更新原数据
      const index = gradeList.value.findIndex(item => item.studentId === editingDetailedGrade.value.studentId)
      if (index !== -1) {
        gradeList.value[index] = { ...editingDetailedGrade.value }
      }
    }
    
    detailedEditVisible.value = false
    editingDetailedGrade.value = null
    MessagePlugin.success('成绩保存成功')
  } catch (error) {
    MessagePlugin.error('保存成绩失败')
  }
}

const handleCloseDirectEdit = () => {
  directEditVisible.value = false
  editingDirectGrade.value = null
}

const handleCloseDetailedEdit = () => {
  detailedEditVisible.value = false
  editingDetailedGrade.value = null
}

// 分页处理
const handleDirectPageChange = (pageInfo: any) => {
  directPagination.current = pageInfo.current
  directPagination.pageSize = pageInfo.pageSize
}

const handleDetailedPageChange = (pageInfo: any) => {
  detailedPagination.current = pageInfo.current
  detailedPagination.pageSize = pageInfo.pageSize
}

// 行内编辑相关函数
const handleStartEdit = (row: any) => {
  // 保存原始数据用于取消编辑
  originalRowData.value[row.studentId] = { ...row }
  editingRows.value[row.studentId] = true
}

const handleSaveRowEdit = async (row: any) => {
  try {
    // 重新计算总分 - 改为求和
    const scores = Object.values(row.objectives).filter(score => score !== null && score !== undefined) as number[]
    if (scores.length > 0) {
      row.totalScore = parseFloat(scores.reduce((a, b) => a + b, 0).toFixed(1))
      row.status = 'submitted'
    }
    
    // 模拟保存API
    await new Promise(resolve => setTimeout(resolve, 300))
    
    editingRows.value[row.studentId] = false
    delete originalRowData.value[row.studentId]
    
    MessagePlugin.success('成绩保存成功')
  } catch (error) {
    MessagePlugin.error('保存成绩失败')
  }
}

const handleCancelEdit = (row: any) => {
  // 恢复原始数据
  const original = originalRowData.value[row.studentId]
  if (original) {
    Object.assign(row, original)
  }
  
  editingRows.value[row.studentId] = false
  delete originalRowData.value[row.studentId]
}

const handleStartDetailedEdit = (row: any) => {
  // 保存原始数据用于取消编辑
  originalRowData.value[row.studentId] = { ...row }
  editingRows.value[row.studentId] = true
}

const handleSaveDetailedRowEdit = async (row: any) => {
  try {
    // 重新计算总分 - 改为求和
    const scores = Object.values(row.questions).filter(score => score !== null && score !== undefined) as number[]
    if (scores.length > 0) {
      row.totalScore = parseFloat(scores.reduce((a, b) => a + b, 0).toFixed(1))
      row.status = 'submitted'
    }
    
    // 模拟保存API
    await new Promise(resolve => setTimeout(resolve, 300))
    
    editingRows.value[row.studentId] = false
    delete originalRowData.value[row.studentId]
    
    MessagePlugin.success('成绩保存成功')
  } catch (error) {
    MessagePlugin.error('保存成绩失败')
  }
}

const handleCancelDetailedEdit = (row: any) => {
  // 恢复原始数据
  const original = originalRowData.value[row.studentId]
  if (original) {
    Object.assign(row, original)
  }
  
  editingRows.value[row.studentId] = false
  delete originalRowData.value[row.studentId]
}

// 导入和导出相关函数
const handleImportGrades = () => {
  importDialogVisible.value = true
}

const handleImportDetailedGrades = () => {
  // 更新模板数据，确保使用最新的题目结构
  detailedImportConfig.templateData = generateDetailedImportTemplate()
  detailedImportDialogVisible.value = true
}

// 页面初始化
onMounted(() => {
  if (props.data.visible && assessmentContent.value) {
    loadGradeData()
  }
})

// 列设置相关函数
const handleCloseColumnSettings = () => {
  columnSettingsVisible.value = false
}

const handleQuestionTypeVisibilityChange = (type: string, checked: boolean) => {
  if (checked && !visibleQuestionTypes.value.includes(type)) {
    visibleQuestionTypes.value.push(type)
  } else if (!checked) {
    visibleQuestionTypes.value = visibleQuestionTypes.value.filter(t => t !== type)
  }
}

const handleSaveColumnSettings = () => {
  // 如果所有类型都被取消，至少保留一个
  if (visibleQuestionTypes.value.length === 0 && allQuestionTypes.value.length > 0) {
    visibleQuestionTypes.value = [allQuestionTypes.value[0]]
    MessagePlugin.warning('至少需要保留一种题目类型')
  }
  
  columnSettingsVisible.value = false
}
</script>

<style lang="less" scoped>
.grade-management-dialog,
.detailed-grade-management {
  .assessment-info {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;
    padding: 20px;
    background: var(--td-bg-color-container);
    border-radius: 6px;
    border: 1px solid var(--td-border-level-1-color);

    .info-content {
      h3 {
        margin: 0 0 12px 0;
        font-size: 18px;
        font-weight: 600;
        color: var(--td-text-color-primary);
      }

      .info-meta {
        display: flex;
        gap: 24px;

        .meta-item {
          display: flex;
          align-items: center;

          .label {
            color: var(--td-text-color-secondary);
            margin-right: 8px;
          }

          .value {
            color: var(--td-text-color-primary);
            font-weight: 500;
          }
        }
      }
    }

    .info-actions {
      display: flex;
      gap: 12px;
    }
  }

  // 新的统计卡片样式
  .stats-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-bottom: 24px;

    .stat-card {
      display: flex;
      align-items: center;
      padding: 20px;
      background: var(--td-bg-color-container);
      border-radius: 8px;
      border: 1px solid var(--td-border-level-1-color);
      transition: all 0.3s ease;

      &:hover {
        box-shadow: var(--td-shadow-2);
        transform: translateY(-2px);
      }

      .stat-icon {
        width: 48px;
        height: 48px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 16px;

        .t-icon {
          font-size: 24px;
          color: white;
        }

        &.average {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        &.highest {
          background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        &.lowest {
          background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        &.submitted {
          background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }

        &.pending {
          background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }
      }

      .stat-content {
        .stat-value {
          font-size: 24px;
          font-weight: 600;
          color: var(--td-text-color-primary);
          line-height: 1;
          margin-bottom: 4px;
        }

        .stat-label {
          font-size: 14px;
          color: var(--td-text-color-secondary);
        }
      }
    }
  }

  // 搜索工具栏样式
  .search-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding: 16px 20px;
    background: var(--td-bg-color-container);
    border-radius: 6px;
    border: 1px solid var(--td-border-level-1-color);

    .filter-section {
      display: flex;
      align-items: center;
      gap: 12px;
    }
  }

  .grade-table,
  .detailed-grade-table {
    background: var(--td-bg-color-container);
    border-radius: 6px;
    padding: 20px;
    border: 1px solid var(--td-border-level-1-color);

    .total-score {
      font-weight: 600;
      
      &.high-score {
        color: var(--td-success-color);
      }
    }

    // 行内编辑样式
    .inline-edit-cell {
      .score-display {
        cursor: pointer;
        padding: 4px 8px;
        border-radius: 4px;
        transition: all 0.2s ease;

        &:hover {
          background: var(--td-bg-color-page);
          color: var(--td-brand-color);
        }
      }
    }

    // 详细录入表格特殊样式
    :deep(.t-table__content) {
      .t-table__body {
        .score-display:hover {
          background: var(--td-bg-color-page);
          color: var(--td-brand-color);
        }
      }
    }
  }

  .table-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding: 16px 20px;
    background: var(--td-bg-color-container);
    border-radius: 6px;
    border: 1px solid var(--td-border-level-1-color);

    .search-section,
    .filter-section {
      display: flex;
      align-items: center;
      gap: 12px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .grade-management-dialog,
  .detailed-grade-management {
    .assessment-info {
      flex-direction: column;
      gap: 16px;

      .info-meta {
        flex-direction: column;
        gap: 8px;
      }

      .info-actions {
        flex-wrap: wrap;
      }
    }

    .stats-cards {
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 12px;

      .stat-card {
        padding: 16px;

        .stat-icon {
          width: 40px;
          height: 40px;
          margin-right: 12px;

          .t-icon {
            font-size: 20px;
          }
        }

        .stat-content .stat-value {
          font-size: 20px;
        }
      }
    }

    .search-toolbar {
      flex-direction: column;
      gap: 12px;
      align-items: stretch;

      .filter-section {
        justify-content: center;
      }
    }

    .table-controls {
      flex-direction: column;
      gap: 12px;
    }
  }

  .direct-grade-edit,
  .detailed-grade-edit {
    .grade-form .form-section {
      .objective-grades {
        grid-template-columns: 1fr;
      }

      .question-grades .question-group .sub-items {
        grid-template-columns: 1fr;
      }
    }
  }
}

.column-settings {
  padding: 16px 0;
  
  .settings-description {
    margin: 0 0 16px;
    color: var(--td-text-color-secondary);
  }
  
  .checkbox-group {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    
    .t-checkbox {
      margin-right: 0;
      min-width: 100px;
    }
  }
}
</style> 