<template>
  <div class="header-only-footer">
    <div class="footer-content">
      <!-- 左侧版权信息 -->
      <div class="footer-left">
        <div class="copyright">
          <span class="copyright-text">© 2024 {{ settingConfig.title }}</span>
          <span class="divider">|</span>
          <span class="company">创业中心</span>
        </div>
      </div>
      
      <!-- 右侧内容已移除，实现居中显示 -->
    </div>
  </div>
</template>

<script setup lang="ts">
import { settingConfig } from '@/config/setting.config';
</script>

<style lang="less" scoped>
.header-only-footer {
  width: 100%;
  height: 60px;
  background: linear-gradient(0deg, 
    rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.03) 0%, 
    rgba(var(--td-brand-color-8-rgb, 5, 148, 250), 0.015) 50%,
    transparent 100%);
  backdrop-filter: blur(25px) saturate(1.2);
  position: relative;
  
  // 添加柔和的边缘羽化效果
  &::before {
    content: '';
    position: absolute;
    top: -10px;
    left: 0;
    right: 0;
    height: 10px;
    background: linear-gradient(0deg, 
      rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.02) 0%, 
      transparent 100%);
    pointer-events: none;
  }
  
  // 添加底部柔和光晕
  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60%;
    height: 2px;
    background: linear-gradient(90deg, 
      transparent 0%, 
      rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.15) 50%, 
      transparent 100%);
    border-radius: 50px 50px 0 0;
    filter: blur(1px);
  }
  
  .footer-content {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    padding: 0 32px;
    position: relative;
    z-index: 1;
  }
  
  .footer-left {
    display: flex;
    align-items: center;
    
    .copyright {
      display: flex;
      align-items: center;
      gap: 12px;
      font-size: 14px;
      
      .copyright-text {
        color: var(--td-text-color-primary);
        font-weight: 500;
      }
      
      .divider {
        color: var(--td-text-color-placeholder);
        font-weight: 300;
      }
      
      .company {
        color: var(--td-brand-color);
        font-weight: 600;
        background: linear-gradient(135deg, 
          var(--td-brand-color) 0%, 
          var(--td-brand-color-8) 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }
    }
  }
  

}

// 响应式适配
@media (max-width: 768px) {
  .header-only-footer {
    height: 50px;
    
    .footer-content {
      padding: 0 16px;
      
      .footer-left .copyright {
        gap: 8px;
        font-size: 12px;
      }
    }
  }
}

// 暗色主题适配
@media (prefers-color-scheme: dark) {
  .header-only-footer {
    background: linear-gradient(135deg, 
      rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.15) 0%, 
      rgba(var(--td-brand-color-8-rgb, 5, 148, 250), 0.08) 100%);
    border-top: 1px solid rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.2);
    
    &::before {
      background: linear-gradient(90deg, 
        transparent 0%, 
        rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.4) 50%, 
        transparent 100%);
    }
  }
}
</style> 