<template>
  <div class="graduation-standard-container">
    <t-card title="毕业要求通用标准管理">
      <!-- 搜索区域 -->
      <div class="search-container" style="margin-bottom: 20px;">
        <t-form layout="inline" :data="searchFormData">
          <t-form-item label="标准名称">
            <t-input
              v-model="searchFormData.standardName"
              placeholder="请输入标准名称"
              clearable
            />
          </t-form-item>
          <t-form-item label="学科类型">
            <t-select
              v-model="searchFormData.disciplineType"
              placeholder="请选择学科类型"
              clearable
              :options="enumData?.list?.disciplineType"
            />
          </t-form-item>
          <t-form-item label="版本">
            <t-input-number
              v-model="searchFormData.standardVersion"
              placeholder="请输入版本号"
              clearable
              :min="2000"
            />
          </t-form-item>
          <t-form-item label="状态">
            <t-select v-model="searchFormData.status" placeholder="请选择状态" clearable>
              <t-option
                v-for="option in enumData?.list?.standardStatus"
                :key="option.value"
                :value="option.value"
                :label="option.label"
              />
            </t-select>
          </t-form-item>
          <t-form-item>
            <t-space>
              <t-button theme="primary" @click="handleSearch">查询</t-button>
              <t-button theme="default" @click="resetSearch">重置</t-button>
            </t-space>
          </t-form-item>
        </t-form>
      </div>

      <template #actions>
        <t-space>
          <t-button theme="primary" @click="handleAdd">新增标准</t-button>
        </t-space>
      </template>

      <t-loading :loading="loading">
        <t-table
          :data="data"
          :columns="columns"
          row-key="id"
          hover
          stripe
          :pagination="pagination"
          @page-change="onPageChange"
        >
          <template #serial-number="{ rowIndex }">
            {{ (pagination.current - 1) * pagination.pageSize + rowIndex + 1 }}
          </template>
          <template #status="{ row }">
            <t-tag :theme="row.status === 0 ? 'success' : row.status === 1 ? 'warning' : 'danger'">
              {{ enumData?.map?.standardStatus?.[row.status] }}
            </t-tag>
          </template>
          <template #disciplineType="{ row }">
            <t-tag theme="primary">{{ enumData?.map?.disciplineType?.[row.disciplineType] }}</t-tag>
          </template>
          <template #releaseDate="{ row }">
            {{ formatDate(row.releaseDate) }}
          </template>
          <template #operation="slotProps">
            <div class="operation-container">
              <t-space size="small">
                <t-button size="small" variant="outline" theme="primary" @click="viewDetail(slotProps.row)">
                  <template #icon><t-icon name="browse" /></template>
                  查看
                </t-button>
                <t-button
                  v-if="slotProps.row.status !== -1"
                  size="small"
                  variant="outline"
                  theme="primary"
                  @click="handleEdit(slotProps.row)"
                >
                  <template #icon><t-icon name="edit" /></template>
                  编辑
                </t-button>
                <t-button
                  v-if="slotProps.row.status !== -1"
                  size="small"
                  variant="outline"
                  :theme="slotProps.row.status === 0 ? 'warning' : 'success'"
                  @click="toggleStatus(slotProps.row)"
                >
                  <template #icon>
                    <t-icon :name="slotProps.row.status === 0 ? 'close-circle' : 'check-circle'" />
                  </template>
                  {{ slotProps.row.status === 0 ? '停用' : '启用' }}
                </t-button>
                <t-button
                  v-if="slotProps.row.status !== -1"
                  size="small"
                  variant="outline"
                  theme="danger"
                  @click="handleDelete(slotProps.row)"
                >
                  <template #icon><t-icon name="delete" /></template>
                  删除
                </t-button>
              </t-space>
            </div>
          </template>
        </t-table>
      </t-loading>
    </t-card>

    <!-- 新增/编辑对话框 -->
    <t-dialog
      v-model:visible="dialogVisible"
      :header="dialogTitle"
      :width="680"
      :footer="false"
      @close="dialogVisible = false"
      :closeOnEscKeydown="false"
    >
      <t-form
        ref="formRef"
        :data="formData"
        :rules="rules"
        label-width="120px"
        @submit="handleSubmit"
      >
        <t-form-item label="标准名称" name="standardName">
          <t-input v-model="formData.standardName" placeholder="请输入标准名称"></t-input>
        </t-form-item>

        <t-form-item label="版本" name="standardVersion">
          <t-input-number v-model="formData.standardVersion" placeholder="请输入版本号" :min="2000" style="width: 240px;" />
        </t-form-item>

        <t-form-item label="学科类型" name="disciplineType">
          <t-select
            v-model="formData.disciplineType"
            placeholder="请选择学科类型"
            :options="enumData?.list?.disciplineType"
          />
        </t-form-item>

        <t-form-item label="发布日期" name="releaseDate">
          <t-date-picker
            v-model="formData.releaseDate"
            format="YYYY-MM-DD"
            placeholder="请选择发布日期"
          />
        </t-form-item>

        <t-form-item label="状态" name="status">
          <t-radio-group v-model="formData.status">
            <t-radio
              v-for="option in enumData?.list?.standardStatus"
              :key="option.value"
              :value="option.value"
            >
              {{ option.label }}
            </t-radio>
          </t-radio-group>
        </t-form-item>

        <t-form-item label="描述" name="description">
          <t-textarea
            v-model="formData.standardDescription"
            placeholder="请输入标准描述"
            :autosize="{ minRows: 3, maxRows: 5 }"
          />
        </t-form-item>

        <t-divider>毕业要求项</t-divider>

        <div v-if="formData.requirements && formData.requirements.length > 0">
          <t-table
            :data="formData.requirements"
            :columns="requirementColumns"
            row-key="order"
            bordered
            size="small"
            style="margin-bottom: 16px;"
          >
            <template #id="{ row }">
              <span>{{ row?.id }}</span>
            </template>
            <template #standardName="{ row }">
              <t-input v-model="row.standardName" placeholder="标题"></t-input>
            </template>
            <template #standardDescription="{ row }">
              <t-textarea v-model="row.standardDescription" placeholder="描述" :autosize="{ minRows: 1, maxRows: 3 }"></t-textarea>
            </template>
            <template #operation="{ rowIndex }">
              <t-space>
                <t-button theme="danger" variant="text" size="small" @click="removeRequirement(rowIndex)">
                  <template #icon><t-icon name="delete" /></template>
                </t-button>
                <t-button theme="primary" variant="text" size="small" :disabled="rowIndex === 0" @click="moveRequirement(rowIndex, -1)">
                  <template #icon><t-icon name="chevron-up" /></template>
                </t-button>
                <t-button theme="primary" variant="text" size="small" :disabled="rowIndex === formData.requirements.length - 1" @click="moveRequirement(rowIndex, 1)">
                  <template #icon><t-icon name="chevron-down" /></template>
                </t-button>
              </t-space>
            </template>
          </t-table>
        </div>
        <div v-else>
          <t-empty description="暂无毕业要求项"></t-empty>
        </div>

        <t-button theme="default" variant="dashed" style="width: 100%; margin-bottom: 16px;" @click="addRequirement">
          <template #icon><t-icon name="add" /></template>
          添加毕业要求项
        </t-button>

        <t-form-item>
          <t-space>
            <t-button theme="primary" type="submit">确认</t-button>
            <t-button theme="default" variant="base" @click="dialogVisible = false">取消</t-button>
          </t-space>
        </t-form-item>
      </t-form>
    </t-dialog>

    <!-- 详情对话框 -->
    <t-dialog
      v-model:visible="detailVisible"
      header="毕业要求标准详情"
      :width="800"
      :footer="false"
      @close="detailVisible = false"
    >
      <t-loading :loading="detailLoading">
        <div class="detail-container" v-if="standardDetail">
          <t-descriptions bordered size="medium" :column="2">
            <t-descriptions-item label="标准名称">{{ standardDetail.standardName }}</t-descriptions-item>
            <t-descriptions-item label="版本">{{ standardDetail.standardVersion }}</t-descriptions-item>
            <t-descriptions-item label="学科类型">{{ standardDetail.disciplineType }}</t-descriptions-item>
            <t-descriptions-item label="发布日期">{{ formatDate(standardDetail.releaseDate) }}</t-descriptions-item>
            <t-descriptions-item label="状态">
              <t-tag :theme="standardDetail.status === 0 ? 'success' : standardDetail.status === 1 ? 'warning' : 'danger'">
                {{ enumData?.map?.standardStatus?.[standardDetail.status] }}
              </t-tag>
            </t-descriptions-item>
            <t-descriptions-item label="创建时间">{{ formatDateTime(standardDetail.createTime) }}</t-descriptions-item>
            <t-descriptions-item label="创建人">{{ standardDetail.creator }}</t-descriptions-item>
            <t-descriptions-item label="更新时间">{{ formatDateTime(standardDetail.modifyTime) }}</t-descriptions-item>
            <t-descriptions-item label="更新人">{{ standardDetail.modifier }}</t-descriptions-item>
            <t-descriptions-item label="描述" :span="2">{{ standardDetail.standardDescription }}</t-descriptions-item>
          </t-descriptions>

          <div class="requirements-container" style="margin-top: 24px;">
            <div class="requirements-header">
              <h3>毕业要求项（共 {{ standardDetail.requirements?.length }} 项）</h3>
            </div>
            <t-table
              :data="standardDetail.requirements"
              bordered
              :columns="viewRequirementColumns"
              row-key="id"
              :max-height="400"
              style="margin-top: 16px;"
            />
          </div>
        </div>
        <div v-else>
          <t-alert theme="info" message="无法获取标准详情" />
        </div>
      </t-loading>
      <template #footer>
        <t-button theme="primary" @click="detailVisible = false">关闭</t-button>
      </template>
    </t-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, h} from 'vue';
import type {VNode} from 'vue'
import { MessagePlugin, DialogPlugin } from 'tdesign-vue-next';
import dayjs from 'dayjs';
import {
  getGraduationStandardList,
  getGraduationStandardDetail,
  createGraduationStandard,
  updateGraduationStandard,
  deleteGraduationStandard,
} from '@/api/base/standard';
import { getEnum } from '@/api/system/enum';

// 表格列定义，按序输出
const columns = [
  { colKey: 'id', title: '序号', width: 80,
    cell: (_:unknown, { rowIndex }: {rowIndex:number}) => rowIndex + 1 + (pagination.current - 1) * pagination.pageSize},
  { colKey: 'standardName', title: '标准名称', width: 200 },
  { colKey: 'standardVersion', title: '版本', width: 100 },
  { colKey: 'disciplineType', title: '学科类型', width: 120 },
  { colKey: 'releaseDate', title: '发布日期', width: 120 },
  { colKey: 'status', title: '状态', width: 80 },
  { colKey: 'standardDescription', title: '描述', width: 250 },
  { colKey: 'operation', title: '操作', width: 240, fixed: 'right' as const },
];

// 毕业要求列定义，按序输出
const requirementColumns = [
  { colKey: 'id', title: 'id', width: 80 ,
    cell: (_:unknown, { rowIndex }: {rowIndex:number}) => rowIndex + 1},
  { colKey: 'standardName', title: '标题', width: 150 },
  { colKey: 'standardDescription', title: '描述', width: 'auto' },
  { colKey: 'operation', title: '操作', width: 120 },
];

// 查看毕业要求列定义,输出序号按序输出
const viewRequirementColumns = [
  { colKey: 'id', title: 'id', width: 80,
    cell: (_:unknown, { rowIndex }: {rowIndex:number}) => rowIndex + 1},
  { colKey: 'standardName', title: '标题', width: 150 },
  { colKey: 'standardDescription', title: '描述', width: 'auto' },
];

// 数据定义
const data = ref([]);
const loading = ref(false);
const dialogVisible = ref(false);
const dialogTitle = ref('新增毕业要求标准');
const detailVisible = ref(false);
const detailLoading = ref(false);
const standardDetail = ref(null);

// 分页
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showJumper: true,
  showPageSize: true,
  pageSizeOptions: [5, 10, 15, 20],
});

const emptySearchFormData = {
  standardName: '',
  standardVersion: null as number,
  disciplineType: '',
  status: null as number,
};

// 搜索表单
const searchFormData = reactive({
  ...emptySearchFormData,
});

const emptyFormData = {
  id: null as number,
  standardName: '',
  standardVersion: 2000,
  disciplineType: '',
  releaseDate: '',
  status: 0,
  standardDescription: '',
  requirements: [],
}

// 表单数据
const formData = ref({
  ...emptyFormData,
});

// 表单规则
const rules = {
  standardName: [{ required: true, message: '请输入标准名称', type: 'error' }],
  standardVersion: [{ required: true, message: '请输入版本号', type: 'error' }],
  disciplineType: [{ required: true, message: '请选择学科类型', type: 'error' }],
  releaseDate: [{ required: true, message: '请选择发布日期', type: 'error' }],
  status: [{ required: true, message: '请选择状态', type: 'error' }],
};

// 枚举值
const enumData = ref<any>(null);

// 获取枚举值
const fetchEnumData = async () => {
  try {
    const res = await getEnum();
    enumData.value = res.data;
  } catch (error) {
    console.error('获取枚举值失败:', error);
    MessagePlugin.error('获取枚举值失败');
  }
};

// 获取数据列表
const fetchData = async () => {
  loading.value = true;
  try {
    const params = {
      page: pagination.current,
      pageSize: pagination.pageSize,
      ...searchFormData,
    };
    const response = await getGraduationStandardList(params);

    data.value = response.data.records;
    pagination.total = response.data.total;
  } catch (error) {
    console.error('获取毕业要求标准列表失败:', error);
    MessagePlugin.error('获取列表失败');
  } finally {
    loading.value = false;
  }
};

// 初始化
onMounted(async () => {
  await fetchEnumData();
  await fetchData();
});

// 分页变化
const onPageChange = (pageInfo: any) => {
  pagination.current = pageInfo.current;
  pagination.pageSize = pageInfo.pageSize;
  fetchData();
};

// 搜索
const handleSearch = () => {
  pagination.current = 1;
  fetchData();
};

// 重置搜索
const resetSearch = () => {
  Object.assign(searchFormData, emptySearchFormData);
  pagination.current = 1;
  fetchData();
};

// 添加新标准
const handleAdd = () => {
  dialogTitle.value = '新增毕业要求标准';
  Object.assign(formData.value, emptyFormData);
  dialogVisible.value = true;
};

// 编辑标准
const handleEdit = async (row: any) => {
  dialogTitle.value = '编辑毕业要求标准';
  const res = await getGraduationStandardDetail(row.id);
  formData.value = res.data;
  dialogVisible.value = true;
};

// 查看标准详情
const viewDetail = async (row: any) => {
  detailVisible.value = true;
  detailLoading.value = true;
  const res = await getGraduationStandardDetail(row.id);
  standardDetail.value = res.data;
  detailLoading.value = false;
};

// 切换状态
const toggleStatus = async (row: any) => {
  const newStatus = row.status === 0 ? 1 : 0;
  await updateGraduationStandard({
    id: row.id,
    status: newStatus,
  });
  MessagePlugin.success(`${newStatus === 0 ? '启用' : '停用'}成功`);
  await fetchData();
};

// 删除标准
const handleDelete = (row: any) => {
  const dialog = DialogPlugin.confirm({
    header: '确认删除',
    body: `确定要删除"${row.standardName}"吗？此操作不可恢复。`,
    onConfirm: async () => {
      // 调用删除接口
      await deleteGraduationStandard(row.id);
      dialog.destroy(); // 关闭对话框
      MessagePlugin.success('删除成功');
      if (data.value.length === 1 && pagination.current > 1) {
        pagination.current -= 1;
      }
      await fetchData();
    }
  });
};

// 表单提交
const handleSubmit = async ({ validateResult, firstError }) => {
  if (validateResult === true) {
    // 检查每个毕业要求项是否都填写了必要信息
    if (formData.value.requirements && formData.value.requirements.length > 0) {
      for (const req of formData.value.requirements) {
        if (!req.standardName || !req.standardDescription) {
          MessagePlugin.error('请完善所有毕业要求项信息');
          return;
        }
      }
    }

    try {
      if (formData.value.id) {
        // 将version转换为字符串
        const updateData = {
          ...formData.value,
        };
        await updateGraduationStandard(updateData);
        MessagePlugin.success('更新成功');
      } else {
        await createGraduationStandard(formData.value);
        MessagePlugin.success('创建成功');
      }
      dialogVisible.value = false;
      await fetchData();
    } catch (error) {
      console.error('保存失败:', error);
      MessagePlugin.error('保存失败');
    }
  } else {
    MessagePlugin.error(firstError);
    return;
  }
};

// 添加毕业要求项
const addRequirement = () => {
  if (!formData.value.requirements) {
    formData.value.requirements = [];
  }

  const order = formData.value.requirements.length > 0
    ? Math.max(...formData.value.requirements.map(r => r.order || 0)) + 1
    : 1;

  const newRequirement = {
    standardName: '',
    standardDescription: '',
    order: order,
  };

  formData.value.requirements.push(newRequirement);
};

// 删除毕业要求项
const removeRequirement = (index: number) => {
  if (formData.value.requirements) {
    formData.value.requirements.splice(index, 1);
  }
};

// 移动毕业要求项
const moveRequirement = (index: number, direction: number) => {
  if (!formData.value.requirements) return;

  const newIndex = index + direction;
  if (newIndex < 0 || newIndex >= formData.value.requirements.length) return;

  const temp = formData.value.requirements[index];
  formData.value.requirements[index] = formData.value.requirements[newIndex];
  formData.value.requirements[newIndex] = temp;

  // 更新order值
  formData.value.requirements.forEach((req, idx) => {
    req.order = idx + 1;
  });
};

// 格式化日期
const formatDate = (dateStr: any) => {
  if (!dateStr) return '';
  return dayjs(dateStr).format('YYYY-MM-DD');
};

// 格式化日期时间
const formatDateTime = (dateStr: any) => {
  if (!dateStr) return '';
  return dayjs(dateStr).format('YYYY-MM-DD HH:mm:ss');
};
</script>

<style scoped lang="less">
.graduation-standard-container {
  padding: 20px;

  .requirements-container {
    .requirements-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      h3 {
        margin: 0;
        font-size: 16px;
      }
    }
  }

  .operation-container {
    .t-button {
      padding: 2px 8px;
    }
  }
}
</style>
