import type { App } from 'vue'
import { initializeGlobalDict, getGlobalDictState, resetGlobalDictState } from '@/utils/dictUtil'

/**
 * 字典插件配置
 */
interface DictPluginOptions {
  /** 是否在应用启动时自动初始化字典数据 */
  autoInit?: boolean
  /** 初始化超时时间（毫秒） */
  initTimeout?: number
  /** 初始化失败时的重试次数 */
  retryCount?: number
  /** 重试间隔（毫秒） */
  retryInterval?: number
}

/**
 * 字典插件
 * 提供全局的字典数据管理和状态监控
 */
export const dictPlugin = {
  install(app: App, options: DictPluginOptions = {}) {
    const {
      autoInit = true,
      initTimeout = 30000,
      retryCount = 3,
      retryInterval = 2000
    } = options

    // 全局字典状态
    const dictState = {
      initialized: false,
      initializing: false,
      error: null as string | null,
      lastInitTime: 0,
      retryCount: 0
    }

    /**
     * 初始化字典数据（带重试机制）
     */
    const initDictWithRetry = async (): Promise<boolean> => {
      if (dictState.initializing) {
        console.log('🔄 字典数据正在初始化中，跳过重复请求')
        return false
      }

      dictState.initializing = true
      dictState.error = null

      for (let i = 0; i <= retryCount; i++) {
        try {
          console.log(`🚀 开始初始化字典数据 (第${i + 1}次尝试)...`)
          
          // 设置超时
          const timeoutPromise = new Promise<never>((_, reject) => {
            setTimeout(() => reject(new Error('初始化超时')), initTimeout)
          })

          const initPromise = initializeGlobalDict(i > 0) // 重试时强制刷新
          
          await Promise.race([initPromise, timeoutPromise])
          
          // 更新状态
          const globalState = getGlobalDictState()
          dictState.initialized = globalState.initialized
          dictState.lastInitTime = Date.now()
          dictState.retryCount = i
          
          console.log('✅ 字典数据初始化成功')
          return true
        } catch (error) {
          dictState.error = error instanceof Error ? error.message : '未知错误'
          console.error(`❌ 字典数据初始化失败 (第${i + 1}次尝试):`, error)
          
          if (i < retryCount) {
            console.log(`⏳ ${retryInterval / 1000}秒后进行第${i + 2}次重试...`)
            await new Promise(resolve => setTimeout(resolve, retryInterval))
          }
        }
      }

      dictState.initializing = false
      return false
    }

    /**
     * 重置字典状态
     */
    const resetDict = () => {
      resetGlobalDictState()
      dictState.initialized = false
      dictState.initializing = false
      dictState.error = null
      dictState.lastInitTime = 0
      dictState.retryCount = 0
      console.log('🔄 字典状态已重置')
    }

    /**
     * 获取字典状态
     */
    const getDictState = () => {
      const globalState = getGlobalDictState()
      return {
        ...dictState,
        globalState
      }
    }

    // 将字典相关方法挂载到全局属性
    app.config.globalProperties.$dict = {
      init: initDictWithRetry,
      reset: resetDict,
      getState: getDictState,
      getGlobalState: getGlobalDictState
    }

    // 提供注入
    app.provide('dict', {
      init: initDictWithRetry,
      reset: resetDict,
      getState: getDictState,
      getGlobalState: getGlobalDictState
    })

    // 如果启用自动初始化，在应用挂载后初始化字典数据
    if (autoInit) {
      app.mixin({
        async mounted() {
          // 只在根组件中执行初始化
          if (this.$root === this) {
            console.log('🌐 应用启动，开始初始化字典数据...')
            await initDictWithRetry()
          }
        }
      })
    }

    // 开发环境下提供调试信息
    if (process.env.NODE_ENV === 'development') {
      console.log('📚 字典插件已安装', {
        autoInit,
        initTimeout,
        retryCount,
        retryInterval
      })
    }
  }
}

/**
 * 使用字典插件的Composable
 */
export function useDictPlugin() {
  const { getCurrentInstance } = require('vue')
  const instance = getCurrentInstance()
  
  if (!instance) {
    throw new Error('useDictPlugin must be called within a component')
  }

  return {
    dict: instance.appContext.provides.dict
  }
}

export default dictPlugin 