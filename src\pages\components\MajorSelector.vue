<template>
  <t-dialog
    v-model:visible="dialogVisible"
    header="选择专业"
    :width="600"
    :height="500"
    :close-btn="true"
    :cancel-btn="null"
    :confirm-btn="null"
    placement="center"
    :destroy-on-close="false"
    class="major-selector"
  >
    <div class="p-6">
      <div class="flex items-center mb-8 text-gray-800">
        <t-icon name="education" class="mr-3 text-blue-600 text-xl" />
        <h2 class="text-lg font-medium">您有权限管理多个专业，请选择一个专业进入工作台</h2>
      </div>
      
      <div class="space-y-4">
        <div 
          v-for="major in majorList" 
          :key="major.id"
          class="group p-5 border rounded-lg cursor-pointer bg-white transition-all duration-300 hover:shadow-md"
          :class="[
            String(major.id) === String(currentMajorId) 
              ? 'border-blue-200 bg-blue-50 ring-2 ring-blue-200 ring-opacity-50' 
              : 'border-gray-200 hover:border-blue-300'
          ]"
        >
          <div class="flex justify-between items-center">
            <div class="space-y-2">
              <div class="flex items-center">
                <span class="text-gray-500 mr-3 font-mono">{{ major.code }}</span>
                <span class="font-semibold text-gray-900">{{ major.name }}</span>
                <t-tag 
                  v-if="String(major.id) === String(currentMajorId)"
                  theme="primary" 
                  variant="light" 
                  size="small" 
                  class="ml-3"
                >
                  当前专业
                </t-tag>
              </div>
              <div class="text-xs text-gray-500 flex items-center">
                <t-icon name="time" class="mr-1" />
                创建于：{{ major.createTime.split(' ')[0] }}
              </div>
            </div>
            <t-button 
              theme="primary" 
              size="small" 
              :variant="String(major.id) === String(currentMajorId) ? 'base' : 'outline'"
              @click="handleSelectMajor(major)"
              class="min-w-[100px] transition-all duration-300 hover:shadow-sm"
            >
              <span class="flex items-center justify-center">
                <t-icon name="enter" class="mr-1" />
                进入工作台
              </span>
            </t-button>
          </div>
        </div>
      </div>

      <div class="mt-6 pt-4 border-t border-gray-100 flex items-center text-xs text-gray-500">
        <t-icon name="info-circle" class="mr-2" />
        <span>提示：选择专业后可随时切换</span>
      </div>
    </div>
  </t-dialog>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  visible: Boolean,
  currentMajorId: [String, Number],
  majors: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:visible', 'select'])

const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const majorList = computed(() => props.majors)

const handleSelectMajor = (major) => {
  emit('select', major)
  emit('update:visible', false)
}
</script>
