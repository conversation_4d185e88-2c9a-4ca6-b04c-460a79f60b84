export interface StudentCourseAttainmentListResult {
    code:number
    message:string
  list: Array<StudentCourseAttainmentInfo>;
}
export interface StudentCourseAttainmentInfo {
  courseName: string;//课程名称
  courseGoal:string;//课程目标名称
  raeGrades: number;//原始分数
  fraction:number;//折合分数
  percentage: number;//占比
}

export interface StudentCourseGraduateListResult {
    code:number
    message:string
  list: Array<StudentCourseGraduateInfo>;
}
export interface StudentCourseGraduateInfo {
  graduateName: string;//毕业目标名称
  courseGoal:string;//课程目标名称
  percentage: number;//占比
}

export interface StudentStudiesAttainmentListResult {
    code:number
    message:string
  list: Array<StudentStudiesAttainmentInfo>;
}

export interface StudentStudiesAttainmentInfo {
  courseName: string;
  studiesType:string
  fraction:number;//计算后的分数
}


export interface StudentScoreListResult {
    code:number
    message:string
    list: Array<StudentScoreInfo>;
    headerConfig: Array<{
        category: string;   // 大类名称（如"阶段性测试"）
        items: string[];    // 具体考核项名称数组
        colspan: number;    // 合并列数
    }>;
    total:number;
}

// 成绩数据接口
export interface StudentScoreInfo {
  courseName: string;
  assessmentItems: {
    [key: string]: {  // 每个考核项包含三个字段
      originalScores: number; // 原始成绩数组
      percentage: number;       // 占比（百分比）
      fraction: number;         // 折合成绩
    }
  };
}
