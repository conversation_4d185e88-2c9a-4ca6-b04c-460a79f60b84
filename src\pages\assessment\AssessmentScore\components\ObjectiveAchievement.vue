<template>
  <div class="course-achievement">
    <t-card title="课程目标达成度分析" class="mb-4">
      <!-- <template #actions>
        <t-button>导出图表</t-button>
      </template> -->
      <t-row :gutter="[16, 16]">
        <t-col :span="12">
          <div class="flex items-center mb-4">
            <t-input-adornment prepend="课程ID">
              <t-input-number v-model="courseId" class="w-40" />
            </t-input-adornment>
            <t-button class="ml-4" @click="loadData">查询</t-button>
          </div>
        </t-col>
      </t-row>
    </t-card>

    <t-loading :loading="loading">
      <t-card v-if="courseTargets.length > 0" class="mb-4">
        <t-tabs v-model="activeTab">
          <t-tab-panel
            v-for="target in courseTargets"
            :key="target.objectiveId"
            :value="target.objectiveId.toString()"
            :label="`课程目标${target.number}`"
          >
            <div class="chart-container">
              <ScatterChart
                :chart-id="`chart-${target.objectiveId}`"
                :title="`课程目标${target.number}成绩散点图`"
                :points="getScatterPoints(target)"
                x-label="成绩序号"
                y-label="成绩"
              />
              <div class="chart-info">
                <p><strong>课程目标名称:</strong> {{ target.objectiveName }}</p>
                <p><strong>对应毕业要求:</strong> {{ target.po?.title || '无' }}</p>
                <p><strong>学生总数:</strong> {{ getStudentCount(target.objectiveId) }}人</p>
              </div>
            </div>
          </t-tab-panel>
        </t-tabs>
      </t-card>

      <t-card v-else>
        <t-empty />
      </t-card>
    </t-loading>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import ScatterChart from '@/pages/assessment/AssessmentScore/components/ScatterChart.vue';

// API imports
import { getCourseTargetList } from '@/api/training/course';
import { getTargetScoresByCourseId } from '@/api/assessment/assessmentScore';

// Types
// 后端返回的学生成绩结构
interface StudentTargetScore {
  studentId: string;
  studentName: string;
  studentNumber: string;
  targetScores: Array<{
    courseTargetNo: string;
    methodScores: any[];
    objectiveId: string;
    poId: string;
    totalScore: number;
  }>;
}

interface CourseObjectiveVO {
  objectiveId: number;
  number: number; // 课程目标序号
  objectiveName: string;
  expectedScore: number; // 预期达成度
  description?: string; // 课程目标描述
  po: {
    id: number;
    title: string;
    description: string;
  }; // 对应的毕业要求
}

// Reactive data
// 从路由参数获取 courseId 和 taskId
const route = useRoute()
const courseId = computed(() => Number(route.params.courseId) || 0)
const loading = ref<boolean>(false);
const courseTargets = ref<CourseObjectiveVO[]>([]);
const studentScores = ref<StudentTargetScore[]>([]);
const activeTab = ref<string>('');
onMounted(() => {
  loadData();
});

// Methods
const loadData = async () => {
  if (!courseId.value) {
    MessagePlugin.warning('请输入课程ID');
    return;
  }

  loading.value = true;
  try {
    // 获取课程目标列表
    const targets = await getCourseTargetList(courseId.value);
    courseTargets.value = targets;
    
    // 获取学生成绩数据
    const scoresResponse = await getTargetScoresByCourseId(courseId.value);
    studentScores.value = scoresResponse.data;
    console.log('学生成绩数据加载成功:', studentScores.value);
    if (targets.length > 0) {
      activeTab.value = targets[0].objectiveId.toString();
    }
    
    MessagePlugin.success('数据加载成功');
  } catch (err) {
    console.error('数据加载失败:', err);
    MessagePlugin.error('数据加载失败');
  } finally {
    loading.value = false;
  }
};

const getStudentCount = (targetId: number) => {
  return studentScores.value.filter(student => {
    if (!student || !Array.isArray(student.targetScores)) return false;
    return student.targetScores.some(score => score && String(score.courseTargetNo) === String(targetId));
  }).length;
};

// Tab切换时无需额外渲染，ScatterChart组件自动响应数据变化

const getScatterPoints = (target: CourseObjectiveVO) => {
  const points: Array<{ index: number; value: number; name: string; number: string }> = [];
  let idx = 1;
  studentScores.value.forEach((student) => {
    if (!student || !Array.isArray(student.targetScores)) return;
    student.targetScores.forEach(scoreObj => {
      if (scoreObj && String(scoreObj.courseTargetNo) === String(target.number)) {
        points.push({
          index: idx,
          value: scoreObj.totalScore,
          name: student.studentName,
          number: student.studentNumber
        });
        idx++;
      }
    });
  });
  return points;
};

// ...已删除原生echarts渲染方法，全部由ScatterChart组件负责...
</script>

<style scoped>
.course-achievement {
  padding: 16px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.chart-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.chart {
  width: 100%;
  height: 500px;
  margin-bottom: 20px;
}

.chart-info {
  width: 100%;
  padding: 16px;
  background-color: #f0f0f0;
  border-radius: 4px;
}

.mb-4 {
  margin-bottom: 16px;
}

.ml-4 {
  margin-left: 16px;
}

.w-40 {
  width: 10rem;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}
</style>