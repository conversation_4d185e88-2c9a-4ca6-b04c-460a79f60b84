# FullScreenDialog 全屏弹窗组件

## 概述

基于 TDesign Dialog 组件封装的全屏弹窗组件，专为复杂内容配置场景设计。支持多种动画效果和遮罩配置。

## 特性

1. **全屏显示**：弹窗占据整个视口（100vw × 100vh）
2. **动画效果**：支持 7 种预设动画效果，可自定义动画参数
3. **遮罩配置**：支持背景模糊、透明度和颜色自定义
4. **滚动优化**：右侧滚动条与内容保持间距，自定义滚动条样式
5. **最高层级**：z-index 3000，完全覆盖所有页面内容
6. **事件监听**：提供动画开始和结束事件回调
7. **响应式**：支持移动端适配和用户偏好设置
8. **无障碍**：支持 ESC 键退出和键盘导航

## 使用方式

### 基本用法

```vue
<template>
  <FullScreenDialog
    v-model:visible="visible"
    header="全屏弹窗标题"
    :confirm-btn="{ content: '保存', loading: false }"
    @confirm="handleConfirm"
    @cancel="handleCancel"
  >
    <div>
      <!-- 弹窗内容 -->
    </div>
  </FullScreenDialog>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import FullScreenDialog from '@/components/FullScreenDialog/index.vue'

const visible = ref(false)

const handleConfirm = () => {
  // 确认逻辑
}

const handleCancel = () => {
  // 取消逻辑
}
</script>
```

### 动画效果配置

```vue
<template>
  <FullScreenDialog
    v-model:visible="visible"
    header="动画效果演示"
    :animation="{
      type: 'bounce',
      duration: 600,
      easing: 'ease-out',
      delay: 100
    }"
    @animation-start="onAnimationStart"
    @animation-end="onAnimationEnd"
  >
    <!-- 内容 -->
  </FullScreenDialog>
</template>

<script setup lang="ts">
const onAnimationStart = () => {
  console.log('动画开始')
}

const onAnimationEnd = () => {
  console.log('动画结束')
}
</script>
```

### 遮罩效果配置

```vue
<template>
  <FullScreenDialog
    v-model:visible="visible"
    header="遮罩效果演示"
    :overlay="{
      blur: true,
      opacity: 0.8,
      color: 'rgba(59, 130, 246, 0.5)'
    }"
  >
    <!-- 内容 -->
  </FullScreenDialog>
</template>
```

### 综合配置示例

```vue
<template>
  <FullScreenDialog
    v-model:visible="visible"
    header="综合效果演示"
    :animation="{
      type: 'zoom',
      duration: 400,
      easing: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)'
    }"
    :overlay="{
      blur: true,
      opacity: 0.7,
      color: 'rgba(0, 0, 0, 0.7)'
    }"
    :confirm-btn="{ 
      content: '保存配置', 
      loading: isLoading,
      disabled: !isValid 
    }"
    @confirm="handleSave"
    @cancel="handleCancel"
    @animation-start="trackAnimationStart"
    @animation-end="trackAnimationEnd"
  >
    <!-- 复杂表单内容 -->
  </FullScreenDialog>
</template>
```

## API 文档

### Props

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| visible | boolean | false | 控制弹窗显示/隐藏 |
| header | string | '弹窗' | 弹窗标题 |
| confirmBtn | object | `{ content: '确定' }` | 确认按钮配置 |
| cancelBtn | object | `{ content: '取消' }` | 取消按钮配置 |
| animation | object | 见下方 | 动画配置 |
| overlay | object | 见下方 | 遮罩配置 |

#### animation 配置

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| type | string | 'fade' | 动画类型：'fade' \| 'slide' \| 'scale' \| 'zoom' \| 'bounce' \| 'flip' \| 'none' |
| duration | number | 300 | 动画时长（毫秒） |
| easing | string | 'ease-out' | 缓动函数 |
| delay | number | 0 | 动画延迟（毫秒） |

#### overlay 配置

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| blur | boolean | false | 是否启用背景模糊 |
| opacity | number | 0.6 | 遮罩透明度（0-1） |
| color | string | 'rgba(0, 0, 0, 0.6)' | 遮罩颜色 |

### Events

| 事件名 | 说明 | 参数 |
|--------|------|------|
| update:visible | 弹窗显示状态变化 | (value: boolean) |
| confirm | 点击确认按钮 | - |
| cancel | 点击取消按钮 | - |
| animation-start | 动画开始时触发 | - |
| animation-end | 动画结束时触发 | - |

## 动画效果说明

### 1. fade（淡入淡出）
- **效果**：透明度从 0 到 1 的过渡
- **适用场景**：简洁、不干扰的场景
- **推荐时长**：200-400ms

### 2. slide（滑入滑出）
- **效果**：从顶部滑入，向顶部滑出
- **移动端**：从底部滑入，向底部滑出
- **适用场景**：移动端友好的交互
- **推荐时长**：300-500ms

### 3. scale（缩放）
- **效果**：从 0.8 倍缩放到 1 倍
- **适用场景**：强调内容重要性
- **推荐时长**：250-400ms

### 4. zoom（放大）
- **效果**：从 0.3 倍放大到 1 倍，同时透明度变化
- **适用场景**：引人注目的展示
- **推荐时长**：300-500ms

### 5. bounce（弹跳）
- **效果**：弹性动画，有回弹效果
- **适用场景**：活泼、有趣的交互
- **推荐时长**：500-800ms

### 6. flip（翻转）
- **效果**：3D 翻转效果
- **适用场景**：创新、科技感的界面
- **推荐时长**：400-700ms

### 7. none（无动画）
- **效果**：立即显示/隐藏
- **适用场景**：性能优先或用户偏好
- **推荐时长**：0ms

## 遮罩效果说明

### 背景模糊（blur）
- 启用后使用 `backdrop-filter: blur(8px)` 实现背景模糊
- 现代浏览器支持，提升视觉层次感
- 建议与适当的透明度配合使用

### 透明度（opacity）
- 范围：0（完全透明）到 1（完全不透明）
- 推荐值：0.4-0.8
- 过低影响可视性，过高影响美观

### 颜色（color）
- 支持 rgba、hex、hsl 等 CSS 颜色格式
- 建议使用 rgba 格式便于控制透明度
- 常用配色：
  - 黑色：`rgba(0, 0, 0, 0.6)`
  - 白色：`rgba(255, 255, 255, 0.8)`
  - 品牌色：`rgba(59, 130, 246, 0.5)`

## 样式特点

- **全屏布局**：弹窗占据整个视口
- **Flex布局**：头部、内容、底部采用flex布局
- **滚动优化**：
  - 内容区域可滚动
  - 滚动条位于最右侧
  - 与内容保持20px间距
  - 自定义滚动条样式
- **响应式**：适配不同屏幕尺寸
- **性能优化**：使用 `will-change` 属性优化动画性能

## 无障碍支持

- **键盘导航**：支持 Tab 键在表单元素间切换
- **ESC 退出**：按 ESC 键可关闭弹窗
- **焦点管理**：弹窗打开时自动聚焦，关闭时恢复焦点
- **用户偏好**：响应 `prefers-reduced-motion` 媒体查询

## 文件结构

```
FullScreenDialog/
├── index.vue      # 主组件文件
├── index.ts       # 导出文件
├── types.ts       # TypeScript类型定义
├── override.css   # 强制样式覆盖文件
└── README.md      # 说明文档
```

## 应用场景

适用于需要大量空间展示复杂内容的场景：

- **详细配置表单**：复杂的系统配置界面
- **数据表格编辑**：大型数据表格的编辑模式
- **多步骤向导**：复杂的流程引导界面
- **复杂数据展示**：图表、报表等数据可视化
- **题目管理界面**：教育系统的题目配置
- **设计工具**：在线设计、编辑工具

## 最佳实践

### 动画选择建议

1. **内容型弹窗**：使用 `fade` 或 `scale`
2. **表单型弹窗**：使用 `slide` 或 `zoom`
3. **确认型弹窗**：使用 `bounce` 增强注意力
4. **创新型应用**：使用 `flip` 展现科技感

### 性能优化

1. **合理设置动画时长**：过长影响用户体验，过短影响视觉效果
2. **谨慎使用复杂动画**：在低性能设备上可能卡顿
3. **响应用户偏好**：自动禁用动画（prefers-reduced-motion）

### 用户体验

1. **保持一致性**：同一应用中使用相同的动画风格
2. **提供反馈**：使用动画事件提供加载状态反馈
3. **避免过度使用**：不是所有弹窗都需要复杂动画

## 注意事项

1. 弹窗会完全覆盖整个页面（包括header和footer），请谨慎使用
2. 内容过多时会自动显示滚动条
3. 建议在需要大量展示空间的场景下使用
4. 组件继承了 TDesign Dialog 的所有基础功能
5. 使用了极高的z-index值（999999），确保覆盖所有内容
6. 自动防止背景页面滚动，提升用户体验
7. 背景模糊效果需要现代浏览器支持
8. 动画效果会影响性能，在移动设备上需要谨慎使用

## 技术实现

- **多重样式覆盖**：使用 `.vue` 文件内样式 + 独立 `override.css` 双重保障
- **高优先级选择器**：使用 `!important` 和高特异性选择器确保样式生效
- **Vue Transition**：使用 Vue 3 的 Transition 组件实现动画
- **CSS 变量**：使用 CSS 自定义属性实现动态配置
- **完全定位**：使用 `inset: 0` 和 `position: fixed` 确保完全覆盖
- **事件系统**：完整的动画生命周期事件支持

## 浏览器兼容性

- **现代浏览器**：Chrome 60+、Firefox 55+、Safari 12+、Edge 79+
- **背景模糊**：需要 `backdrop-filter` 支持
- **CSS 变量**：需要 CSS Custom Properties 支持
- **Vue 3**：需要 Vue 3.0+ 环境

## 更新日志

### v2.0.0
- ✨ 新增 7 种动画效果
- ✨ 新增遮罩配置（模糊、透明度、颜色）
- ✨ 新增动画事件回调
- ✨ 新增响应式动画适配
- ✨ 新增无障碍支持
- 🐛 修复 z-index 层级问题
- 💄 优化滚动条样式
- 📝 完善文档和示例

### v1.0.0
- ✨ 基础全屏弹窗功能
- ✨ 完整的弹窗功能支持
- ✨ 自定义滚动条
- ✨ 高优先级样式覆盖
