<template>
  <t-form ref="formRef" :data="formData" :rules="rules" @submit="handleSubmit">
    <t-form-item label="题目类型" name="type">
      <t-select v-model="formData.type" placeholder="请选择题目类型">
        <t-option value="choice" label="选择题" />
        <t-option value="judgment" label="判断题" />
        <t-option value="fill" label="填空题" />
        <t-option value="answer" label="问答题" />
      </t-select>
    </t-form-item>

    <t-form-item label="难度" name="difficulty">
      <t-select v-model="formData.difficulty" placeholder="请选择难度">
        <t-option value="easy" label="简单" />
        <t-option value="medium" label="中等" />
        <t-option value="hard" label="困难" />
      </t-select>
    </t-form-item>

    <t-form-item label="题干" name="stem">
      <t-textarea v-model="formData.stem" placeholder="请输入题干内容" :autosize="{ minRows: 3 }" />
    </t-form-item>

    <!-- 动态显示选择题选项 -->
    <template v-if="formData.type === 'choice'">
      <t-form-item label="选项">
        <div class="option-list">
          <div v-for="(option, index) in formData.options" :key="option.id" class="option-item">
            <t-input
              v-model="option.label"
              placeholder="选项标签"
              style="width: 80px; margin-right: 12px"
            />
            <t-input
              v-model="option.content"
              placeholder="选项内容"
              style="flex: 1"
            />
            <t-button
              theme="danger"
              variant="text"
              @click="removeOption(index)"
            >
              <t-icon name="delete" />
            </t-button>
          </div>
          <t-button variant="dashed" @click="addOption">
            <t-icon name="add" />
            添加选项
          </t-button>
        </div>
      </t-form-item>
    </template>

    <t-form-item label="答案" name="answer">
      <t-textarea v-model="formData.answer" placeholder="请输入答案" :autosize="{ minRows: 2 }" />
    </t-form-item>

    <t-form-item label="解析" name="analysis">
      <t-textarea v-model="formData.analysis" placeholder="请输入解析" :autosize="{ minRows: 3 }" />
    </t-form-item>

    <t-form-item label="关联课程目标">
      <t-select
        v-model="formData.courseObjectives"
        multiple
        placeholder="请选择关联课程目标"
        :options="courseObjectiveOptions"
        value-key="id"
        :keys="{ value: 'id', label: 'name' }"
      />
    </t-form-item>

    <t-form-item>
      <t-space>
        <t-button theme="primary" type="submit">保存</t-button>
        <t-button theme="default" @click="$emit('cancel')">取消</t-button>
      </t-space>
    </t-form-item>
  </t-form>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue'

const props = defineProps({
  initialData: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['submit', 'cancel'])

// 表单数据
const formData = ref({
  type: 'choice',
  difficulty: 'medium',
  stem: '',
  options: [],
  answer: '',
  analysis: '',
  courseObjectives: []
})

// 表单验证规则
const rules = {
  type: [{ required: true, message: '请选择题目类型' }],
  difficulty: [{ required: true, message: '请选择难度' }],
  stem: [{ required: true, message: '请输入题干内容' }],
  answer: [{ required: true, message: '请输入答案' }]
}

// 课程目标选项（模拟数据）
const courseObjectiveOptions = ref([
  { id: 1, code: 'CO1', name: '掌握Vue 3核心概念' },
  { id: 2, code: 'CO2', name: '理解组合式API的使用' },
  { id: 3, code: 'CO3', name: '熟悉Vue Router和Pinia' }
])

// 表单引用
const formRef = ref(null)

// 初始化表单数据
onMounted(() => {
  formData.value = JSON.parse(JSON.stringify(props.initialData))
})

// 添加选项
const addOption = () => {
  const newOption = {
    id: Date.now(),
    label: String.fromCharCode(65 + formData.value.options.length),
    content: ''
  }
  formData.value.options.push(newOption)
}

// 删除选项
const removeOption = (index) => {
  formData.value.options.splice(index, 1)
}

// 提交表单
const handleSubmit = async () => {
  const result = await formRef.value.validate()
  if (result === true) {
    emit('submit', formData.value)
  }
}
</script>

<style scoped>
.option-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.option-item {
  display: flex;
  align-items: center;
  gap: 8px;
}
</style>
