import { request } from '@/utils/request';

// 查询某毕业要求下的全部指标点
export function getPoList(planId: number) {
  return request({
    url: '/tp/po/getList',
    method: 'get',
    params: { plan_id: planId }
  });
}

// 新增指标点
export function addPo(data: any) {
  return request({
    url: '/tp/po/add',
    method: 'post',
    data
  });
}

// 查询专业毕业要求一级指标
export function getPoRequirement(data: any) {
  return request({
    url: '/tp/po/getRequirement',
    method: 'post',
    data
  });
}

// 修改指标点
export function updatePo(data: any) {
  return request({
    url: '/tp/po/mod',
    method: 'put',
    data
  });
}

// 删除指标点
export function deletePo(id: number) {
  return request({
    url: '/tp/po/del',
    method: 'delete',
    params: { id }
  });
}
