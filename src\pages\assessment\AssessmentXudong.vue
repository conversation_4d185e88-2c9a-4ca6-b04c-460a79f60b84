<template>
<div>
  <div class="overview-container">

    <t-row :gutter="[16, 16]" class="card-row">
      <t-col
        v-for="plan in planList"
        :key="plan.id"
        :xs="24"
        :sm="12"
        :md="8"
        :lg="6"
      >
        <t-card
          hover-shadow
          class="data-card"
        >
        <template #header>
          <div class="card-header">
            <t-icon name="education" class="card-icon" />
            <div class="header-text">
              <h3 class="card-title">{{ plan.planName }}</h3>
              <span class="version-tag">{{ plan.planVersion }}</span>
            </div>
          </div>
        </template>
        <div>
          <t-space size="small">
            <t-button
              theme="primary"
              variant="text"
              size="small"
              @click.stop="navigateToGoal(plan.id)"
            >
              培养目标
            </t-button>
            <t-button
              theme="primary"
              variant="text"
              size="small"
              @click.stop="navigateToRequire(plan.id)"
            >
              毕业要求
            </t-button>
            <t-button
              theme="primary"
              variant="text"
              size="small"
              @click.stop="navigateToCurriculum(plan.id)"
            >
              课程体系
            </t-button>
            <t-button
              theme="primary"
              variant="text"
              size="small"
              @click.stop="navigateToGoalSupportMatrix(plan.id)"
            >
              目标支持矩阵
            </t-button>
          </t-space>
        </div>
        </t-card>
      </t-col>
      <t-col :xs="24" :sm="12" :md="8" :lg="6">
        <t-card
          hover-shadow
          class="data-card"
          @click="handleAddPlanClick()"
        >
          <template #header>
            <div class="card-header">
              <t-icon name="plus" class="card-icon" />
              <div class="header-text">
                <h3 class="card-title">新建培养目标</h3>
                <span class="version-tag">-</span>
              </div>
            </div>
          </template>
        </t-card>
      </t-col>
    </t-row>

    <!-- 图表区域 -->
    <t-row :gutter="16" class="chart-row">
      <!-- 雷达图 -->
      <t-col :xs="24" :lg="12" class="chart-col">
        <t-card class="chart-card">
          <template #header>
            <div class="chart-header">
              <t-icon name="chart-radar" class="chart-icon" />
              <span>毕业要求达成度雷达图</span>
            </div>
          </template>
          <template #actions>
            <t-popup content="展示各指标达成度对比" placement="top">
              <t-icon name="help-circle" class="chart-tooltip" />
            </t-popup>
          </template>
          <div ref="radarChart" class="chart-container"></div>
        </t-card>
      </t-col>

      <!-- 柱状图 -->
      <t-col :xs="24" :lg="12" class="chart-col">
        <t-card class="chart-card">
          <template #header>
            <div class="chart-header">
              <t-icon name="chart-bar" class="chart-icon" />
              <span>培养目标达成度</span>
            </div>
          </template>
          <template #actions>
            <t-popup content="展示各年级达成度对比" placement="top">
              <t-icon name="help-circle" class="chart-tooltip" />
            </t-popup>
          </template>
          <div ref="barChart" class="chart-container"></div>
        </t-card>
      </t-col>
    </t-row>
  </div>

  <!-- 添加培养计划对话框 -->
  <t-dialog
    v-model:visible="addPlanDialog"
    header="新建培养计划"
    :width="800"
    :footer="false"
    @close="addPlanDialog = false"
    :closeOnEscKeydown="false"
  >
    <t-form
      ref="addPlanFormRef"
      :data="addPlanData"
      :rules="addPlanRules"
      label-width="100px"
      @submit="handleAddPlan"
    >
      <t-row :gutter="[16, 16]">
        <t-col :span="12">
          <t-form-item label="计划名称" name="planName">
            <t-input
              v-model="addPlanData.planName"
              placeholder="请输入培养计划名称"
              clearable
            />
          </t-form-item>

          <t-form-item label="计划版本" name="planVersion">
            <t-input-number
              v-model="addPlanData.planVersion"
              placeholder="请输入版本号"
              :min="1"
              :max="9999"
            />
          </t-form-item>

          <t-form-item label="学科类型" name="disciplineType">
            <t-select
              v-model="addPlanData.disciplineType"
              placeholder="请选择学科类型"
              clearable
              :options="enumData?.list?.disciplineType"
            />
          </t-form-item>

          <t-form-item label="毕业要求标准" name="standardId">
            <t-select
              v-model="addPlanData.standardId"
              placeholder="请选择毕业要求标准"
              clearable
              :options="filteredStandardList.map(item => ({
                label: `${item.standardName} (${item.standardVersion})`,
                value: item.id
              }))"
              :disabled="!addPlanData.disciplineType"
            />
          </t-form-item>
        </t-col>

        <t-col :span="12">
          <t-loading :loading="standardDetailLoading">
            <div v-if="standardDetail" class="standard-preview">
              <h3 class="preview-title">标准预览</h3>
              <t-descriptions bordered size="small" :column="1">
                <t-descriptions-item label="标准名称">
                  {{ standardDetail.standardName }}
                </t-descriptions-item>
                <t-descriptions-item label="版本">
                  {{ standardDetail.standardVersion }}
                </t-descriptions-item>
                <t-descriptions-item label="学科类型">
                  {{ enumData?.map?.disciplineType?.[standardDetail.disciplineType] }}
                </t-descriptions-item>
                <t-descriptions-item label="发布日期">
                  {{ formatDate(standardDetail.releaseDate) }}
                </t-descriptions-item>
                <t-descriptions-item label="描述">
                  {{ standardDetail.standardDescription }}
                </t-descriptions-item>
              </t-descriptions>

              <div class="requirements-preview" v-if="standardDetail.requirements?.length">
                <h4 class="preview-subtitle">毕业要求项（{{ standardDetail.requirements.length }}项）</h4>
                <t-table
                  :data="standardDetail.requirements"
                  :columns="[
                    { colKey: 'standardName', title: '标题', width: 150 },
                    { colKey: 'standardDescription', title: '描述' }
                  ]"
                  size="small"
                  bordered
                  row-key="id"
                  :max-height="200"
                />
              </div>
            </div>
            <t-empty v-else description="请选择毕业要求标准" />
          </t-loading>
        </t-col>
      </t-row>

      <t-form-item>
        <t-space>
          <t-button theme="primary" type="submit" :loading="addPlanLoading">
            确认添加
          </t-button>
          <t-button theme="default" variant="base" @click="addPlanDialog = false">
            取消
          </t-button>
        </t-space>
      </t-form-item>
    </t-form>
  </t-dialog>
</div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, watch } from 'vue'
import * as echarts from 'echarts'
import { useResizeObserver } from '@vueuse/core'
import { getPlanList, addPlan } from '@/api/training/plan'
import { useRouter } from 'vue-router'
import { MessagePlugin } from 'tdesign-vue-next'
import { getEnum } from '@/api/system/enum'
import { getGraduationStandardList, getGraduationStandardDetail } from '@/api/base/standard'
import dayjs from 'dayjs'

const radarChart = ref<HTMLElement>()
const barChart = ref<HTMLElement>()
let radarChartInstance: echarts.ECharts | null = null
let barChartInstance: echarts.ECharts | null = null

const router = useRouter()

const initRadarChart = () => {
  if (!radarChart.value) return

  radarChartInstance = echarts.init(radarChart.value)
  radarChartInstance.setOption({
    tooltip: {
      trigger: 'item'
    },
    radar: {
      indicator: [
        { name: '工程知识', max: 100 },
        { name: '问题分析', max: 100 },
        { name: '设计开发', max: 100 },
        { name: '研究能力', max: 100 },
        { name: '团队协作', max: 100 },
        { name: '沟通能力', max: 100 }
      ],
      splitArea: {
        areaStyle: {
          color: ['rgba(255, 255, 255, 0.5)']
        }
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(0, 0, 0, 0.1)'
        }
      }
    },
    series: [{
      type: 'radar',
      data: [{
        value: [85, 78, 90, 82, 75, 88],
        name: '达成度',
        areaStyle: {
          color: 'rgba(44, 95, 158, 0.4)'
        },
        lineStyle: {
          width: 2,
          color: '#2c5f9e'
        },
        symbolSize: 6
      }]
    }]
  })
}

const initBarChart = () => {
  if (!barChart.value) return

  barChartInstance = echarts.init(barChart.value)
  barChartInstance.setOption({
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['目标1', '目标2', '目标3', '目标4', '目标5'],
      axisLine: {
        lineStyle: {
          color: '#ddd'
        }
      },
      axisTick: {
        alignWithLabel: true
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      splitLine: {
        lineStyle: {
          type: 'dashed'
        }
      }
    },
    series: [{
      name: '达成度',
      type: 'bar',
      barWidth: '60%',
      data: [82, 78, 85, 76, 80],
      itemStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: '#5b8cba' },
          { offset: 1, color: '#2c5f9e' }
        ]),
        borderRadius: [4, 4, 0, 0]
      }
    }]
  })
}

// Handle chart resize
const handleResize = () => {
  radarChartInstance?.resize()
  barChartInstance?.resize()
}

const planList = ref([])
const getPlanListData = async () => {
  const res = await getPlanList({
    current: 1,
    size: 100
  })
  planList.value = res.data.records
}

const navigateToGoal = (id: number) => {
  router.push({ path: '/directers/goal', query: { id } })
}

const navigateToRequire = (id: number) => {
  router.push({ path: '/directers/require', query: { id } })
}

const navigateToCurriculum = (id: number) => {
  router.push({ path: '/directers/curriculum', query: { id } })
}

const navigateToGoalSupportMatrix = (id: number) => {
  router.push({ path: '/directers/matrix', query: { id } })
}

const addPlanDialog = ref(false)
const addPlanLoading = ref(false)
const addPlanFormRef = ref(null)

const currentYear = new Date().getFullYear()

// 枚举数据
const enumData = ref<any>(null)

// 获取枚举数据
const fetchEnumData = async () => {
  try {
    const res = await getEnum()
    enumData.value = res.data
  } catch (error) {
    console.error('获取枚举数据失败:', error)
    MessagePlugin.error('获取枚举数据失败')
  }
}

// 类型定义
interface PlanData {
  planName: string
  planVersion: number
  standardId: number | null
  disciplineType: string
}

const emptyPlanData: PlanData = {
  planName: '',
  planVersion: currentYear,
  standardId: null,
  disciplineType: ''
}

const addPlanData = ref<PlanData>({
  ...emptyPlanData
})

// 标准列表
const standardList = ref([])
const filteredStandardList = ref([])

// 获取标准列表
const getStandardList = async () => {
  try {
    const res = await getGraduationStandardList({
      current: 1,
      size: 100,
      status: 0 // 只获取启用状态的标准
    })
    standardList.value = res.data.records
  } catch (error) {
    console.error('获取标准列表失败:', error)
    MessagePlugin.error('获取标准列表失败')
  }
}

// 监听学科类型变化，过滤标准列表
watch(() => addPlanData.value.disciplineType, (newType) => {
  if (newType) {
    filteredStandardList.value = standardList.value.filter(
      standard => standard.disciplineType === newType
    )
  } else {
    filteredStandardList.value = []
  }
  // 清空已选择的标准
  addPlanData.value.standardId = null
})

// 表单验证规则
const addPlanRules = {
  planName: [
    { required: true, message: '请输入培养计划名称', trigger: 'blur' as const },
    { max: 100, message: '计划名称不能超过100个字符', trigger: 'blur' as const }
  ],
  planVersion: [
    { required: true, message: '请输入计划版本', trigger: 'blur' as const }
  ],
  disciplineType: [
    { required: true, message: '请选择学科类型', trigger: 'change' as const }
  ],
  standardId: [
    { required: true, message: '请选择毕业要求标准', trigger: 'change' as const }
  ]
}

const handleAddPlanClick = () => {
  addPlanData.value = {
    ... emptyPlanData
  }
  addPlanDialog.value = true
  // 获取标准列表
  getStandardList()
}

const handleAddPlan = async (context: any) => {
  if (context.validateResult === true) {
    addPlanLoading.value = true
    try {
      const res = await addPlan(addPlanData.value)

      if (res.code === 200) {
        MessagePlugin.success('培养计划添加成功')
        addPlanDialog.value = false
        // 重新加载计划列表
        await getPlanListData()
      } else {
        MessagePlugin.error(res.message || '添加失败')
      }
    } catch (error: any) {
      console.error('添加培养计划失败:', error)
      MessagePlugin.error(error.message || '添加失败，请稍后重试')
    } finally {
      addPlanLoading.value = false
    }
  } else {
    MessagePlugin.warning(context.firstError)
  }
}

// 标准详情
const standardDetail = ref<any>(null)
const standardDetailLoading = ref(false)

// 格式化日期
const formatDate = (date: string | number | Date | null | undefined) => {
  if (!date) return ''
  return dayjs(date).format('YYYY-MM-DD')
}

// 监听标准选择变化
watch(() => addPlanData.value.standardId, async (newId) => {
  if (newId) {
    standardDetailLoading.value = true
    try {
      const res = await getGraduationStandardDetail(String(newId))
      standardDetail.value = res.data
    } catch (error) {
      console.error('获取标准详情失败:', error)
      MessagePlugin.error('获取标准详情失败')
    } finally {
      standardDetailLoading.value = false
    }
  } else {
    standardDetail.value = null
  }
})

onMounted(() => {
  getPlanListData()
  fetchEnumData()
  initRadarChart()
  initBarChart()
  window.addEventListener('resize', handleResize)
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize)
  radarChartInstance?.dispose()
  barChartInstance?.dispose()
})

// Auto resize for charts when container size changes
useResizeObserver(radarChart, handleResize)
useResizeObserver(barChart, handleResize)
</script>

<style lang="less" scoped>
// 颜色变量
@primary-color: #2c5f9e;
@secondary-color: #5b8cba;
@success-color: #67c23a;
@warning-color: #e6a23c;
@error-color: #f56c6c;
@background-color: #f5f7fa;
@text-color: #2c3e50;
@border-color: #e4e7ed;

// 响应式断点
@sm: 768px;
@md: 992px;
@lg: 1200px;

.overview-container {
  padding: 16px;
  background-color: @background-color;
  min-height: calc(100vh - 32px);

  @media (min-width: @sm) {
    padding: 24px;
  }

  .card-row {
    margin-bottom: 24px;

    @media (min-width: @sm) {
      margin-bottom: 32px;
    }
  }

  .data-card {
    height: 100%;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;
    background: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    border: 1px solid @border-color;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .card-actions {
      padding: 8px 16px;
      border-top: 1px solid fade(@border-color, 50%);
      display: flex;
      justify-content: flex-end;
    }

    &.version-missing {
      background: repeating-linear-gradient(
        45deg,
        fade(@warning-color, 5%),
        fade(@warning-color, 5%) 10px,
        white 10px,
        white 20px
      );
      border-color: @warning-color;

      .card-header {
        border-bottom-color: fade(@warning-color, 30%);
      }
    }

    .card-header {
      display: flex;
      align-items: flex-start;
      padding: 16px;
      border-bottom: 1px solid fade(@border-color, 50%);
      width: 100%;

      .card-icon {
        font-size: 20px;
        color: @primary-color;
        margin-right: 12px;
        margin-top: 2px;
      }

      .header-text {
        flex: 1;

        .card-title {
          margin: 0;
          font-size: 16px;
          font-weight: 600;
          color: @text-color;
          line-height: 1.4;
        }

        .version-tag {
          display: inline-block;
          font-size: 12px;
          color: fade(@text-color, 60%);
          margin-top: 2px;
        }
      }
    }

    .indicator-item {
      margin: 16px 0;

      .progress-label {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        span {
          font-size: 13px;
          color: fade(@text-color, 80%);
          letter-spacing: 0.3px;
        }
      }

      .custom-progress {
        ::v-deep .t-progress__bar {
          border-radius: 4px;
        }
      }
    }

    .empty-tip {
      padding: 16px;

      .missing-version {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
        padding: 16px 0;

        .warning-icon {
          font-size: 48px;
          color: @warning-color;
          margin-bottom: 12px;
        }

        .hint-text {
          color: fade(@text-color, 60%);
          font-size: 12px;
          margin-top: 8px;
        }
      }
    }
  }

  .chart-col {
    margin-bottom: 16px;

    @media (min-width: @lg) {
      margin-bottom: 0;
    }
  }

  .chart-card {
    height: 480px;
    display: flex;
    flex-direction: column;
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid @border-color;

    .chart-header {
      display: flex;
      align-items: center;
      padding: 16px 24px;
      border-bottom: 1px solid fade(@border-color, 50%);

      .chart-icon {
        font-size: 18px;
        color: @primary-color;
        margin-right: 12px;
      }

      span {
        font-size: 15px;
        font-weight: 600;
        color: @text-color;
      }
    }

    .chart-tooltip {
      color: fade(@text-color, 50%);
      cursor: pointer;
      transition: color 0.2s;

      &:hover {
        color: @primary-color;
      }
    }

    .chart-container {
      flex: 1;
      min-height: 300px;
      padding: 16px;
    }
  }
}

// Mobile optimizations
@media (max-width: @sm) {
  .overview-container {
    padding: 12px 8px;

    .data-card {
      .card-header {
        padding: 12px;

        .card-icon {
          font-size: 18px;
          margin-right: 8px;
        }

        .card-title {
          font-size: 15px;
        }
      }

      .indicator-item {
        margin: 12px 0;
      }
    }

    .chart-card {
      height: auto;
      min-height: 360px;

      .chart-container {
        min-height: 280px;
      }
    }
  }
}

// 移动端优化
@media (max-width: @sm) {
  .data-card {
    min-height: 260px !important; // 移动端稍小高度

    &.version-missing {
      min-height: 260px !important;
    }
  }

  .chart-card {
    min-height: 360px !important;
  }
}

.standard-preview {
  background: #f5f7fa;
  border-radius: 8px;
  padding: 16px;

  .preview-title {
    margin: 0 0 16px;
    font-size: 16px;
    font-weight: 600;
    color: @text-color;
  }

  .preview-subtitle {
    margin: 16px 0 8px;
    font-size: 14px;
    font-weight: 500;
    color: @text-color;
  }

  .requirements-preview {
    margin-top: 16px;
  }
}

:deep(.t-dialog__body) {
  overflow-x: hidden !important;
}
</style>
