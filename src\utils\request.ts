import axios, { AxiosRequestConfig, AxiosResponse } from 'axios';
import { MessagePlugin } from 'tdesign-vue-next';
import { useUserStore } from '@/store';
import router from '@/router'
import qs from 'qs'
import {
  baseURL,
  contentType,
  debounce,
  tokenName,
  messageName,
  timeout,
  statusName,
  loginRouter,
  successCode,
} from '@/config'
import {isArray} from "@/utils/validate";
import {gp} from "@/utils/gp";
import {refreshToken} from "@/api/system/auth";
// 环境变量和配置
let loadingInstance: any

let refreshToking = false

let requests: any[] = []
// 操作正常Code数组
const codeVerificationArray = isArray(successCode) ? [...successCode] : [...[successCode]]
const CODE_MESSAGE: any = {
  200: '服务器成功返回请求数据',
  201: '新建或修改数据成功',
  202: '一个请求已经进入后台排队(异步任务)',
  204: '删除数据成功',
  400: '发出信息有误',
  401: '用户没有权限(令牌失效、用户名、密码错误、登录过期)',
  402: '令牌过期',
  403: '用户得到授权，但是访问是被禁止的',
  404: '访问资源不存在',
  406: '请求格式不可得',
  410: '请求资源被永久删除，且不会被看到',
  500: '服务器发生错误',
  502: '网关错误',
  503: '服务不可用，服务器暂时过载或维护',
  504: '网关超时',
}
/**
 * axios请求拦截器配置
 * @param config
 * @returns {any}
 */
const requestConf: any = (config: any) => {
  const userStore = useUserStore()
  const { token } = userStore

  // 添加token到请求头
  if (token) {
    config.headers[tokenName] = `${token}`
  }

  // 处理表单数据
  if (
    config.data &&
    config.headers['Content-Type'] ===
    'application/x-www-form-urlencoded;charset=UTF-8'
  ) {
    config.data = qs.stringify(config.data)
  }

  // 添加防抖处理
  if (debounce.some((item: string) => config.url.includes(item))) {
    loadingInstance = gp.$baseLoading()
  }

  // 添加请求重试配置
  config.retry = 3
  config.retryDelay = 1000

  return config
}
/**
 * 刷新刷新令牌
 * @param config 过期请求配置
 * @returns {any} 返回结果
 */
const tryRefreshToken = async (config: any) => {
  if (!refreshToking) {
    refreshToking = true
    try {
      const {
        data: { token },
      } = await refreshToken()
      if (token) {
        const { setToken } = useUserStore()
        setToken(token)
        // 已经刷新了token，将所有队列中的请求进行重试
        requests.forEach((cb) => cb(token))
        requests = []
        return service(requestConf(config))
      }
    } catch (error) {
      console.error('refreshToken error =>', error)
      router.push({ path: loginRouter, replace: true }).then(() => {})
    } finally {
      refreshToking = false
    }
  } else {
    return new Promise((resolve) => {
      // 将resolve放进队列，用一个函数形式来保存，等token刷新后直接执行
      requests.push(() => {
        resolve(service(requestConf(config)))
      })
    })
  }
}
/**
 * axios响应拦截器
 * @param config {any} 请求配置
 * @param data {any} response数据
 * @param status {any} HTTP status
 * @param statusText {any} HTTP status text
 * @returns {Promise<*|*>}
 */
const handleData = async ({ config, data, status, statusText }: { config: any; data: any; status: any; statusText: any }) => {
  const { resetAll } = useUserStore()
  if (loadingInstance) loadingInstance.close()

  // 若data.code存在，覆盖默认code
  let code = data && data[statusName] ? data[statusName] : status

  // 若code属于操作正常code，则status修改为200
  if (codeVerificationArray.indexOf(data[statusName]) + 1) code = 200

  switch (code) {
    case 200:
      return data
    case 401:
      // 避免重复调用logout
      if (!config._retry) {
        config._retry = true
        try {
          await resetAll()
          router.push({ path: loginRouter, replace: true })
        } catch (error) {
          console.error('Logout failed:', error)
        }
      }
      break
    case 402:
      return await tryRefreshToken(config)
    case 403:
      router.push({ path: '/403' })
      break
  }

  // 异常处理
  const errMsg = `${data && data[messageName] ? data[messageName] : CODE_MESSAGE[code] ? CODE_MESSAGE[code] : statusText}`
  gp.$baseMessage(errMsg, 'error')
  return Promise.reject(data)
}
// 主服务实例
const service = axios.create({
  baseURL: `${baseURL}`,
  timeout: timeout,
  headers: {
    'Content-Type': contentType,
  },
});

/**
 * @description axios请求拦截器
 */
service.interceptors.request.use(requestConf, (error) => {
  return Promise.reject(error)
})
/**
 * @description axios响应拦截器
 */
service.interceptors.response.use(
  (response) => handleData(response),
  (error) => {
    const { response } = error
    if (response === undefined) {
      if (loadingInstance) loadingInstance.close()
      gp.$baseMessage(
        '连接后台接口失败，可能由以下原因造成：后端不支持跨域CORS、接口地址不存在、请求超时等，请联系管理员排查后端接口问题 ',
        'error',
      )
      return {}
    } else {
      // 如果返回的是blob类型，直接返回
      if (response.config.responseType === 'blob') {
        return response.data;
      }
      return handleData(response)
    }
  }
)

export function request<T = any>(config: AxiosRequestConfig): Promise<T> {
  return service(config) as Promise<T>;
}

/**
 * 文件上传请求
 * @param config 请求配置
 * @param onProgress 上传进度回调
 * @returns Promise
 */
export function uploadRequest<T = any>(
  config: AxiosRequestConfig,
  onProgress?: (progressEvent: any) => void
): Promise<T> {
  const uploadConfig = {
    ...config,
    headers: {
      'Content-Type': 'multipart/form-data',
      ...config.headers,
    },
    onUploadProgress: onProgress,
  };
  
  return service(uploadConfig) as Promise<T>;
}

export default request;
