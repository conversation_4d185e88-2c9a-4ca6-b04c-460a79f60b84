<template>
  <div class="teacher-tree-container">
    <t-card title="教师管理">
      <!-- 左右布局：左侧组织结构树，右侧教师列表 -->
      <div class="teacher-management-layout">
        <!-- 左侧组织结构树 -->
        <div class="left-panel">
          <t-card title="组织结构" :bordered="false" class="org-tree-card">
            <t-loading :loading="treeLoading">
              <t-tree
                ref="treeRef"
                :data="treeData"
                :keys="treeKeys"
                hover
                expand-all
                line
                @click="handleTreeClick"
                :active="[activeNodeKey]"
                style="height: 600px; overflow-y: auto;"
              >
                <template #icon="{ node }">
                  <t-icon
                    :name="node.data.type === 'school' ? 'home' :
                           node.data.type === 'college' ? 'institution' :
                           node.data.type === 'major' ? 'chart' : 'user'"
                    size="16px"
                  />
                </template>
                <template #label="{ node }">
                  <span class="tree-node-label">
                    {{ node.data.label }}
                    <span class="teacher-count" v-if="node.data.teacherCount !== undefined">
                      ({{ node.data.teacherCount }}人)
                    </span>
                  </span>
                </template>
              </t-tree>
            </t-loading>
          </t-card>
        </div>

        <!-- 右侧教师列表 -->
        <div class="right-panel">
          <t-card>
            <!-- 标题区域 -->
            <template #header>
              <div class="card-header">
                <h3 class="card-title">{{ rightPanelTitle }}</h3>
              </div>
            </template>

            <!-- 搜索区域 -->
            <div class="search-container">
              <t-form ref="form" :data="searchForm" layout="inline">
                <t-form-item label="学院" v-if="!isSpecificNodeSelected">
                  <t-select
                    v-model="searchForm.academy_id"
                    :options="academyOptions"
                    placeholder="请选择学院"
                    clearable
                    class="fixed-width-select"
                  />
                </t-form-item>
                <t-form-item label="专业" v-if="selectedCollegeId && !selectedMajorId">
                  <t-select
                    v-model="searchForm.major_id"
                    :options="majorOptions"
                    placeholder="请选择专业"
                    clearable
                    class="fixed-width-select"
                  />
                </t-form-item>
                <t-form-item label="教师姓名">
                  <t-input
                    v-model="searchForm.teacher_name"
                    placeholder="请输入教师姓名"
                    clearable
                    class="fixed-width-input"
                  />
                </t-form-item>
                <t-form-item label="工号">
                  <t-input
                    v-model="searchForm.teacher_number"
                    placeholder="请输入工号"
                    clearable
                    class="fixed-width-input"
                  />
                </t-form-item>
                <t-form-item label="职称">
                  <t-select
                    v-model="searchForm.teacher_title"
                    :options="titleOptions"
                    placeholder="请选择职称"
                    clearable
                    class="fixed-width-select"
                  />
                </t-form-item>
                <t-form-item label="性别">
                  <t-select
                    v-model="searchForm.gender"
                    :options="genderOptions"
                    placeholder="请选择性别"
                    clearable
                    class="fixed-width-select"
                  />
                </t-form-item>
                <t-form-item>
                  <t-space>
                    <t-button theme="primary" @click="handleSearch">查询</t-button>
                    <t-button theme="default" @click="resetSearch">重置</t-button>
                  </t-space>
                </t-form-item>
              </t-form>
            </div>

            <!-- 当前选择的节点信息 -->
            <div class="current-selection" v-if="selectedNodeInfo">
              <t-alert theme="info" :message="selectedNodeInfo" />
            </div>

            <!-- 操作按钮区域 -->
            <div class="action-buttons">
              <t-space>
                <t-button theme="primary" @click="handleAddTeacher">
                  <template #icon><t-icon name="add" /></template>新增教师
                </t-button>
                <t-button theme="default" @click="handleBatchDelete" :disabled="selectedTeachers.length === 0">
                  <template #icon><t-icon name="delete" /></template>批量删除({{ selectedTeachers.length }})
                </t-button>
                <t-button theme="default" @click="openImportDialog">
                  <template #icon><t-icon name="upload" /></template>导入
                </t-button>
                <t-button theme="default" @click="exportTeacherData">
                  <template #icon><t-icon name="download" /></template>导出
                </t-button>
                <t-button theme="default" @click="showStatistics">
                  <template #icon><t-icon name="chart" /></template>统计
                </t-button>
                <t-button theme="default" @click="refreshData">
                  <template #icon><t-icon name="refresh" /></template>刷新
                </t-button>
                <t-button theme="default" @click="columnControllerVisible = true">
                  <template #icon><t-icon name="setting" /></template>列设置
                </t-button>
              </t-space>
            </div>

            <!-- 表格区域 -->
            <t-loading :loading="loading">
              <t-table
                :data="tableData"
                :columns="columns"
                row-key="id"
                hover
                stripe
                :pagination="pagination"
                :max-height="600"
                :selected-row-keys="selectedRowKeys"
                @select-change="handleSelectChange"
              >
                <template #serial-number="{ rowIndex }">
                  {{ (pagination.current - 1) * pagination.pageSize + rowIndex + 1 }}
                </template>
                <template #gender="{ row }">
                  <t-tag :theme="row.gender === '男' ? 'primary' : 'success'">
                    {{ row.gender }}
                  </t-tag>
                </template>
                <template #teacher_title="{ row }">
                  <t-tag :theme="
                    row.teacher_title === '教授' ? 'danger' :
                    row.teacher_title === '副教授' ? 'warning' :
                    row.teacher_title === '讲师' ? 'primary' :
                    'success'
                  ">
                    {{ row.teacher_title }}
                  </t-tag>
                </template>
                <template #operation="{ row }">
                  <div class="operation-container">
                    <t-space size="small">
                      <t-button size="small" variant="outline" theme="primary" @click="viewTeacher(row)">
                        <template #icon><t-icon name="browse" /></template>
                        查看
                      </t-button>
                      <t-button size="small" variant="outline" theme="primary" @click="editTeacher(row)">
                        <template #icon><t-icon name="edit" /></template>
                        编辑
                      </t-button>
                      <t-dropdown trigger="click" placement="bottom-right">
                        <t-button size="small" variant="outline">
                          <template #icon><t-icon name="more" /></template>
                          更多
                      </t-button>
                        <t-dropdown-menu>
                          <t-dropdown-item v-if="row.status === '正常' || row.status === 0" @click="handleDisableTeacher(row)">
                            <t-icon name="poweroff" />
                            停用
                          </t-dropdown-item>
                          <t-dropdown-item v-if="row.status === '停用' || row.status === '禁用' || row.status === 1" @click="handleEnableTeacher(row)">
                            <t-icon name="play-circle" />
                            启用
                          </t-dropdown-item>
                          <t-dropdown-item @click="handleResetPassword(row)">
                            <t-icon name="lock-on" />
                            重置密码
                          </t-dropdown-item>
                          <t-dropdown-item @click="deleteTeacher(row)">
                            <t-icon name="delete" />
                            删除
                          </t-dropdown-item>
                        </t-dropdown-menu>
                      </t-dropdown>
                    </t-space>
                  </div>
                </template>
              </t-table>
            </t-loading>
          </t-card>
        </div>
      </div>
    </t-card>

    <!-- 查看详情对话框 -->
    <t-dialog
      v-model:visible="detailVisible"
      header="教师详情"
      :footer="false"
      width="800px"
      :close-on-overlay-click="true"
    >
      <template v-if="currentTeacher">
        <t-descriptions
          layout="horizontal"
          bordered
          :column="2"
          size="medium"
        >
          <t-descriptions-item label="教师ID">{{ currentTeacher.teacher_id || currentTeacher.id }}</t-descriptions-item>
          <t-descriptions-item label="教师姓名">{{ currentTeacher.teacher_name }}</t-descriptions-item>
          <t-descriptions-item label="工号">{{ currentTeacher.teacher_number }}</t-descriptions-item>
          <t-descriptions-item label="性别">
            <t-tag :theme="getGenderText(currentTeacher.gender) === '男' ? 'primary' : getGenderText(currentTeacher.gender) === '女' ? 'success' : 'default'" size="small">
              {{ getGenderText(currentTeacher.gender) }}
            </t-tag>
          </t-descriptions-item>
          <t-descriptions-item label="职称">
            <t-tag :theme="
              currentTeacher.teacher_title === '教授' ? 'danger' :
              currentTeacher.teacher_title === '副教授' ? 'warning' :
              currentTeacher.teacher_title === '讲师' ? 'primary' :
              'success'
            " size="small">
              {{ currentTeacher.teacher_title }}
            </t-tag>
          </t-descriptions-item>
          <t-descriptions-item label="状态">
            <t-tag
              :theme="getStatusText(currentTeacher.status) === '正常' ? 'success' : 'danger'"
              size="small"
            >
              {{ getStatusText(currentTeacher.status) }}
            </t-tag>
          </t-descriptions-item>
          <t-descriptions-item label="学院" :span="2">{{ currentTeacher.academy_name }}</t-descriptions-item>
          <t-descriptions-item label="联系方式">{{ currentTeacher.phone || '暂无' }}</t-descriptions-item>
          <t-descriptions-item label="邮箱">{{ currentTeacher.email || '暂无' }}</t-descriptions-item>
          <t-descriptions-item label="创建人">{{ currentTeacher.creatorName || '未知' }}</t-descriptions-item>
          <t-descriptions-item label="创建时间">{{ formatDate(currentTeacher.createTime, 'YYYY-MM-DD HH:mm') }}</t-descriptions-item>
          <t-descriptions-item label="修改人">{{ currentTeacher.modifierName || '未知' }}</t-descriptions-item>
          <t-descriptions-item label="修改时间">{{ formatDate(currentTeacher.modifyTime, 'YYYY-MM-DD HH:mm') }}</t-descriptions-item>
        </t-descriptions>
      </template>
    </t-dialog>

    <!-- 编辑对话框 -->
    <t-dialog
      v-model:visible="editVisible"
      header="编辑教师"
      :footer="false"
      width="600px"
      :close-on-overlay-click="false"
    >
      <t-form
        ref="formRef"
        :data="formData"
        :rules="rules"
        label-width="100px"
        @submit="handleSubmit"
      >
        <t-form-item label="教师姓名" name="teacher_name">
          <t-input v-model="formData.teacher_name" placeholder="请输入教师姓名" />
        </t-form-item>

        <t-form-item label="工号" name="teacher_number">
          <t-input v-model="formData.teacher_number" placeholder="请输入工号" />
        </t-form-item>

        <t-form-item label="性别" name="gender">
          <t-radio-group v-model="formData.gender">
            <t-radio :value="1">男</t-radio>
            <t-radio :value="2">女</t-radio>
            <t-radio :value="0">保密</t-radio>
          </t-radio-group>
        </t-form-item>

        <t-form-item label="职称" name="teacher_title">
          <t-select
            v-model="formData.teacher_title"
            :options="titleOptions"
            placeholder="请选择职称"
            @change="(value) => console.log('新增表单职称选择变化:', value, 'formData.teacher_title:', formData.teacher_title)"
          />
        </t-form-item>

        <t-form-item label="学院" name="academy_id">
          <t-select v-model="formData.academy_id" :options="academyOptions" placeholder="请选择学院" />
        </t-form-item>

        <t-form-item label="联系方式" name="phone">
          <t-input v-model="formData.phone" placeholder="请输入联系方式" />
        </t-form-item>

        <t-form-item label="邮箱" name="email">
          <t-input v-model="formData.email" placeholder="请输入邮箱" />
        </t-form-item>

        <t-form-item>
          <t-space>
            <t-button theme="primary" type="submit">保存</t-button>
            <t-button theme="default" variant="base" @click="editVisible = false">取消</t-button>
          </t-space>
        </t-form-item>
      </t-form>
    </t-dialog>

    <!-- 新增教师对话框 -->
    <t-dialog
      v-model:visible="addVisible"
      header="新增教师"
      :footer="false"
      width="600px"
      :close-on-overlay-click="false"
    >
      <t-form
        ref="addFormRef"
        :data="formData"
        :rules="rules"
        label-width="100px"
        @submit="handleAddSubmit"
      >
        <t-form-item label="教师姓名" name="teacher_name">
          <t-input v-model="formData.teacher_name" placeholder="请输入教师姓名" />
        </t-form-item>

        <t-form-item label="工号" name="teacher_number">
          <t-input v-model="formData.teacher_number" placeholder="请输入工号" />
        </t-form-item>

        <t-form-item label="性别" name="gender">
          <t-radio-group v-model="formData.gender">
            <t-radio :value="1">男</t-radio>
            <t-radio :value="2">女</t-radio>
            <t-radio :value="0">保密</t-radio>
          </t-radio-group>
        </t-form-item>

        <t-form-item label="职称" name="teacher_title">
          <t-select v-model="formData.teacher_title" :options="titleOptions" placeholder="请选择职称" />
        </t-form-item>

        <t-form-item label="学院" name="academy_id">
          <t-select v-model="formData.academy_id" :options="academyOptions" placeholder="请选择学院" />
        </t-form-item>

        <t-form-item label="联系方式" name="phone">
          <t-input v-model="formData.phone" placeholder="请输入联系方式" />
        </t-form-item>

        <t-form-item label="邮箱" name="email">
          <t-input v-model="formData.email" placeholder="请输入邮箱" />
        </t-form-item>

        <t-form-item>
          <t-space>
            <t-button theme="primary" type="submit">添加</t-button>
            <t-button theme="default" variant="base" @click="addVisible = false">取消</t-button>
          </t-space>
        </t-form-item>
      </t-form>
    </t-dialog>

    <!-- 导入对话框 -->
    <ImportDialog
      v-model:visible="importVisible"
      :config="importConfig"
      :callbacks="importCallbacks"
    />

    <!-- 列设置弹窗 -->
    <t-dialog
      v-model:visible="columnControllerVisible"
      title="列设置"
      width="400px"
      :footer="false"
    >
      <div style="padding: 16px 0;">
        <t-checkbox-group v-model="displayColumns" direction="vertical">
          <t-checkbox value="teacher_id">教师ID</t-checkbox>
          <t-checkbox value="teacher_name" disabled>姓名（必选）</t-checkbox>
          <t-checkbox value="teacher_number">工号</t-checkbox>
          <t-checkbox value="gender">性别</t-checkbox>
          <t-checkbox value="teacher_title">职称</t-checkbox>
          <t-checkbox value="academy_name">学院</t-checkbox>
          <t-checkbox value="phone">联系方式</t-checkbox>
          <t-checkbox value="email">邮箱</t-checkbox>
          <t-checkbox value="status">状态</t-checkbox>
          <t-checkbox value="creatorName">创建人</t-checkbox>
          <t-checkbox value="createTime">创建时间</t-checkbox>
          <t-checkbox value="modifierName">修改人</t-checkbox>
          <t-checkbox value="modifyTime">修改时间</t-checkbox>
        </t-checkbox-group>
      </div>
      <template #footer>
        <t-space>
          <t-button theme="default" @click="resetColumnSettings">重置默认</t-button>
          <t-button theme="primary" @click="columnControllerVisible = false">确定</t-button>
        </t-space>
      </template>
    </t-dialog>

    <!-- 统计信息对话框 -->
    <t-dialog
      v-model:visible="statisticsVisible"
      header="教师统计信息"
      :footer="false"
      width="600px"
    >
      <div class="statistics-content" v-if="statisticsData">
        <t-row :gutter="16">
          <t-col :span="6">
            <t-card class="stat-card stat-primary">
              <div class="stat-number">{{ statisticsData.totalCount }}</div>
              <div class="stat-label">总教师数</div>
            </t-card>
          </t-col>
          <t-col :span="6">
            <t-card class="stat-card stat-success">
              <div class="stat-number">{{ statisticsData.normalCount }}</div>
              <div class="stat-label">正常状态</div>
            </t-card>
          </t-col>
          <t-col :span="6">
            <t-card class="stat-card stat-warning">
              <div class="stat-number">{{ statisticsData.disabledCount }}</div>
              <div class="stat-label">停用状态</div>
            </t-card>
          </t-col>
          <t-col :span="6">
            <t-card class="stat-card stat-default">
              <div class="stat-number">{{ Object.keys(statisticsData.titleDistribution || {}).length }}</div>
              <div class="stat-label">职称类别</div>
            </t-card>
          </t-col>
        </t-row>

        <t-divider>职称分布</t-divider>
        <div class="title-distribution">
          <t-row :gutter="16">
            <t-col
              :span="12"
              v-for="[title, count] in Object.entries(statisticsData.titleDistribution || {})"
              :key="title"
              style="margin-bottom: 8px;"
            >
              <div class="title-item">
                <span class="title-name">{{ title }}</span>
                <t-tag theme="primary">{{ count }}人</t-tag>
              </div>
            </t-col>
          </t-row>
        </div>
      </div>

      <template #footer>
        <t-button @click="statisticsVisible = false">关闭</t-button>
      </template>
    </t-dialog>
  </div>
</template>
<script setup lang="ts">
import {ref, reactive, onMounted, computed, getCurrentInstance} from 'vue';
import { MessagePlugin, DialogPlugin } from 'tdesign-vue-next';
import { PrimaryTableCol, TableRowData } from 'tdesign-vue-next';
import { getTeacherList, addTeacher, updateTeacher, deleteTeacher as deleteTeacherApi, importTeachers, exportTeacherData as exportTeacherDataApi, TeacherItem, TeacherListParams, getTeacherTitleOptions, batchDeleteTeachers, disableTeacher as disableTeacherApi, enableTeacher as enableTeacherApi, resetTeacherPassword, getTeacherStatistics, getTeacherTree } from '@/api/base/teacher';
import { formatDate } from '@/utils/date';
import { getAcademyOptions } from '@/api/base/academy';
import ImportDialog from '@/components/ImportDialog/index.vue';
import type { ImportConfig, ImportCallbacks } from '@/components/ImportDialog/types';
import {getDictOptionsByTypeTitle} from '@/utils/dictUtil'; // 添加此行以引入dictUtils
//const { proxy } = getCurrentInstance();
// 定义教师信息接口
interface TeacherInfo extends TeacherItem {}

// 搜索条件
const searchForm = reactive({
  academy_id: '',
  major_id: '',
  teacher_name: '',
  teacher_number: '',
  teacher_title: '',
  gender: null as number | null,
});

// 组织结构树相关
const treeRef = ref();
const treeLoading = ref(false);
const treeData = ref([]);
const activeNodeKey = ref('all');
const selectedCollegeId = ref('');
const selectedMajorId = ref('');
const selectedNodeInfo = ref('');

// 表格选择相关
const selectedRowKeys = ref<(string | number)[]>([]);
const selectedTeachers = computed(() => selectedRowKeys.value);

// 树形控件配置
const treeKeys = {
  value: 'id',
  label: 'label',
  children: 'children'
};

// 专业选项数据
const majorOptions = ref([]);

// 计算属性
const isSpecificNodeSelected = computed(() => {
  return selectedCollegeId.value || selectedMajorId.value;
});

const rightPanelTitle = computed(() => {
  if (selectedMajorId.value) {
    const majorNode = findNodeById(treeData.value, selectedMajorId.value);
    return `教师列表 - ${majorNode?.label || '未知专业'}`;
  } else if (selectedCollegeId.value) {
    const collegeNode = findNodeById(treeData.value, selectedCollegeId.value);
    return `教师列表 - ${collegeNode?.label || '未知学院'}`;
  }
  return '教师列表 - 全部';
});

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  pageSizeOptions: [10, 20, 50],
  showJumper: true,
  showPageSize: true,
  onChange: (pageInfo: any) => {
    pagination.current = pageInfo.current;
    pagination.pageSize = pageInfo.pageSize;
    fetchTeacherList();
  },
});

// 表格数据
const tableData = ref<TeacherInfo[]>([]);
const loading = ref(false);
const academyLoading = ref(false);

// 学院数据
const academies = ref<{ id: string | number; name: string }[]>([]);

// 职称选项数据
const titleOptions = ref([]);

// 计算属性 - 学院选项
const academyOptions = computed(() => {
  return academies.value.map(academy => ({
    label: academy.name,
    value: academy.id
  }));
});

// 性别选项
const genderOptions = [
  { label: '保密', value: 0 },
  { label: '男', value: 1 },
  { label: '女', value: 2 }
];

// 从API获取学院数据
const fetchAcademies = async () => {
  academyLoading.value = true;
  try {
    const academyResult: any = await getAcademyOptions();
    console.log('学院数据:', academyResult);
    if (academyResult.data) {
      // 根据实际API返回的数据格式 {value, label} 进行映射
      academies.value = academyResult.data.map((item: any) => ({
        id: item.value,  // API返回的是value字段作为ID
        name: item.label // API返回的是label字段作为名称
      }));
      console.log('处理后的学院数据:', academies.value);
    } else {
      academies.value = [];
    }
  } catch (error) {
    console.error('获取学院数据失败:', error);
    academies.value = [];
  } finally {
    academyLoading.value = false;
  }
};

// 从API获取教师职称选项
const fetchTeacherTitleOptions = async () => {
  titleOptions.value = await getDictOptionsByTypeTitle('教师职称');
  console.log('职称选项:', titleOptions.value);
};

// 性别转换函数
const getGenderText = (gender: number | string): string => {
  if (gender === 0 || gender === '0') return '保密';
  if (gender === 1 || gender === '1') return '男';
  if (gender === 2 || gender === '2') return '女';
  return '保密';
};

// 状态转换函数
const getStatusText = (status: number | string): string => {
  if (status === 0 || status === '0') return '正常';
  if (status === 1 || status === '1') return '停用';
  return '未知';
};

// 列显示控制
const columnControllerVisible = ref(false);
const displayColumns = ref([
  'row-select',
  'serial-number',
  'teacher_name',
  'teacher_number',
  'gender',
  'teacher_title',
  'academy_name',
  'phone',
  'email',
  'status',
  'operation'
]);

// 所有可用的列配置
const allColumns: PrimaryTableCol<TableRowData>[] = [
  { colKey: 'row-select', type: 'multiple', width: 64, fixed: 'left' },
  { colKey: 'serial-number', title: '序号', width: 80, align: 'center' },
  { colKey: 'teacher_id', title: '教师ID', width: 100 },
  { colKey: 'teacher_name', title: '姓名', width: 120 },
  { colKey: 'teacher_number', title: '工号', width: 120 },
  {
    colKey: 'gender',
    title: '性别',
    width: 80,
    align: 'center',
    cell: (h, { row }) => {
      const genderText = getGenderText(row.gender);
      const theme = genderText === '男' ? 'primary' : genderText === '女' ? 'success' : 'default';
      return h('t-tag', { theme, size: 'small' }, genderText);
    }
  },
  { colKey: 'teacher_title', title: '职称', width: 100 },
  { colKey: 'academy_name', title: '学院', width: 180 },
  { colKey: 'phone', title: '联系方式', width: 150 },
  { colKey: 'email', title: '邮箱', width: 200 },
  {
    colKey: 'status',
    title: '状态',
    width: 100,
    cell: (h, { row }) => {
      const statusText = getStatusText(row.status);
      const theme = statusText === '正常' ? 'success' : statusText === '停用' ? 'danger' : 'default';
      return h('t-tag', { theme, size: 'small' }, statusText);
    }
  },
  { colKey: 'creatorName', title: '创建人', width: 100 },
  {
    colKey: 'createTime',
    title: '创建时间',
    width: 140,
    cell: (h, params) => formatDate(params.row.createTime as string, 'YYYY-MM-DD HH:mm')
  },
  { colKey: 'modifierName', title: '修改人', width: 100 },
  {
    colKey: 'modifyTime',
    title: '修改时间',
    width: 140,
    cell: (h, params) => formatDate(params.row.modifyTime as string, 'YYYY-MM-DD HH:mm')
  },
  { colKey: 'operation', title: '操作', width: 300, fixed: 'right', align: 'center' },
];

// 获取当前显示的列
const columns = computed(() => {
  return allColumns.filter(col => displayColumns.value.includes(col.colKey));
});

// 列设置选项
const columnOptions = [
  { value: 'teacher_id', label: '教师ID' },
  { value: 'teacher_name', label: '姓名' },
  { value: 'teacher_number', label: '工号' },
  { value: 'gender', label: '性别' },
  { value: 'teacher_title', label: '职称' },
  { value: 'academy_name', label: '学院' },
  { value: 'phone', label: '联系方式' },
  { value: 'email', label: '邮箱' },
  { value: 'status', label: '状态' },
  { value: 'creatorName', label: '创建人' },
  { value: 'createTime', label: '创建时间' },
  { value: 'modifierName', label: '修改人' },
  { value: 'modifyTime', label: '修改时间' },
];

// 数据转换函数：将后端返回的TeacherVO转换为前端期望的格式
const transformTeacherData = (teacher: any): TeacherInfo => {
  return {
    // 主要字段映射
    id: teacher.id,
    number: teacher.number,
    title: teacher.title,
    academyId: teacher.academyId,
    status: teacher.status,
    creator: teacher.creator,
    creatorName: teacher.creatorName,
    createTime: teacher.createTime,
    modifier: teacher.modifier,
    modifierName: teacher.modifierName,
    modifyTime: teacher.modifyTime,
    user: teacher.user,
    academy: teacher.academy,

    // 兼容字段映射（为了兼容现有代码）
    teacher_id: teacher.id,
    teacher_name: teacher.user?.name || '',
    teacher_number: teacher.number,
    gender: teacher.user?.gender,
    teacher_title: teacher.title,
    academy_id: teacher.academyId,
    academy_name: teacher.academy?.academyName || '',
    phone: teacher.user?.phone || '',
    email: teacher.user?.email || '',
    image: teacher.user?.avatar
  };
};

// 获取教师列表数据
const fetchTeacherList = async () => {
  loading.value = true;
  try {
    const params: TeacherListParams = {
      current: pagination.current,
      size: pagination.pageSize,
    };

    // 设置搜索条件
    if (searchForm.academy_id) params.academyId = searchForm.academy_id;
    if (searchForm.teacher_name) params.teacherName = searchForm.teacher_name;
    if (searchForm.teacher_number) params.teacherNumber = searchForm.teacher_number;
    if (searchForm.teacher_title) params.teacherTitle = searchForm.teacher_title;
    if (searchForm.gender !== null) params.gender = searchForm.gender;

    console.log('=== 教师列表请求开始 ===');
    console.log('查询参数:', params);

    const result: any = await getTeacherList(params);

    console.log('=== API响应原始数据 ===');
    console.log('完整响应:', result);

    // 正确处理返回的数据结构
    console.log('=== 数据解析成功 ===');
    console.log('data字段:', result.data);
    console.log('records字段:', result.data.records);
    console.log('records长度:', result.data.records?.length);
    console.log('total字段:', result.data.total);

    // 转换数据格式
    const transformedData = (result.data.records || []).map(transformTeacherData);
    tableData.value = transformedData;
    pagination.total = result.data.total || 0;

    console.log('=== 设置表格数据后 ===');
    console.log('tableData长度:', tableData.value.length);
    console.log('转换后的数据示例:', tableData.value[0]);
    console.log('pagination.total:', pagination.total);

    console.log('=== 最终表格数据 ===');
    console.log('tableData.value:', tableData.value);
    console.log('=== 教师列表请求结束 ===');
  } catch (error) {
    console.error('获取教师列表失败:', error);
    tableData.value = [];
    pagination.total = 0;
  } finally {
    loading.value = false;
  }
};

// 重置搜索条件
const resetSearch = () => {
  // 重置所有搜索条件
  searchForm.academy_id = '';
  searchForm.teacher_name = '';
  searchForm.teacher_number = '';
  searchForm.teacher_title = '';
  searchForm.gender = null;

  // 重置分页
  pagination.current = 1;

  // 获取教师列表
  fetchTeacherList();
};

// 搜索
const handleSearch = () => {
  // 重置分页
  pagination.current = 1;

  // 获取教师列表
  fetchTeacherList();
};

// 对话框控制
const detailVisible = ref(false);
const editVisible = ref(false);
const addVisible = ref(false);
const currentTeacher = ref<TeacherInfo | null>(null);
const formRef = ref(null);
const addFormRef = ref(null);

// 表单数据
const formData = reactive({
  teacher_id: '',
  teacher_name: '',
  teacher_number: '',
  gender: 0,
  teacher_title: '',
  academy_id: '',
  academy_name: '',
  phone: '',
  email: '',
});

// 重置表单数据
const resetFormData = () => {
  formData.teacher_id = '';
  formData.teacher_name = '';
  formData.teacher_number = '';
  formData.gender = 0;
  formData.teacher_title = '';
  formData.academy_id = '';
  formData.academy_name = '';
  formData.phone = '';
  formData.email = '';
};

// 表单校验规则
const rules = {
  teacher_name: [
    { required: true, message: '请输入教师姓名', trigger: 'blur' as const },
    { max: 50, message: '教师姓名不能超过50个字符', trigger: 'blur' as const }
  ],
  teacher_number: [
    { required: true, message: '请输入工号', trigger: 'blur' as const },
    { max: 20, message: '工号不能超过20个字符', trigger: 'blur' as const }
  ],
  gender: [
    { required: true, message: '请选择性别', trigger: 'change' as const }
  ],
  teacher_title: [
    { required: true, message: '请选择职称', trigger: 'change' as const }
  ],
  academy_id: [
    { required: true, message: '请选择学院', trigger: 'change' as const }
  ],
  phone: [
    { required: true, message: '请输入联系方式', trigger: 'blur' as const },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' as const }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' as const },
    { pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/, message: '请输入正确的邮箱格式', trigger: 'blur' as const }
  ],
};

// 查看教师详情
const viewTeacher = (row: TeacherInfo) => {
  // 直接使用传入的row数据，无需API调用
  currentTeacher.value = row;
  detailVisible.value = true;
};

// 编辑教师
const editTeacher = (row: TeacherInfo) => {
  // 直接使用传入的row数据，无需API调用
  currentTeacher.value = row;

  // 复制数据到表单，注意性别字段的转换
  Object.assign(formData, {
    teacher_name: row.teacher_name,
    teacher_number: row.teacher_number || '', // 工号已经是字符串类型
    // 处理性别字段：根据后端定义 0=保密, 1=男, 2=女
    gender: typeof row.gender === 'number' ? row.gender :
            row.gender === '男' ? 1 : row.gender === '女' ? 2 : 0,
    teacher_title: row.teacher_title,
    academy_id: row.academy_id,
    phone: row.phone,
    email: row.email || ''
  });

  console.log('编辑教师 - 设置表单数据:', formData);
  editVisible.value = true;
};

// 删除教师
const deleteTeacher = (row: TeacherInfo) => {
  console.log('=== 删除教师操作开始 ===');
  console.log('row数据:', row);
  console.log('row.id:', row.id);
  console.log('row.teacher_id:', row.teacher_id);

  const confirmDialog = DialogPlugin.confirm({
    header: '删除确认',
    body: `确定要删除教师"${row.teacher_name || row.user?.name}"吗？`,
    confirmBtn: '确认删除',
    cancelBtn: '取消',
    onConfirm: async () => {
      try {
        // 使用正确的ID字段
        const teacherId = row.id || row.teacher_id;
        console.log('准备删除的教师ID:', teacherId);

        if (!teacherId) {
          console.error('教师ID为空，无法删除');
          MessagePlugin.error('教师ID为空，无法删除');
          return;
        }

        console.log('调用删除API，ID:', teacherId);
        const result = await deleteTeacherApi(teacherId);
        console.log('删除API返回结果:', result);

        MessagePlugin.success('删除成功');
        fetchTeacherList();
        buildTreeData()
        confirmDialog.destroy(); // 显式关闭对话框
      } catch (error) {
        console.error('删除教师失败:', error);
        MessagePlugin.error('删除教师失败');
      }
    }
  });
};

// 处理添加教师提交
const handleAddSubmit = async (submitContext: any) => {
  // 如果验证失败，直接返回
  if (submitContext.validateResult !== true) {
    console.log('表单验证失败:', submitContext.validateResult);
    return;
  }
  try {
    const data = {
      number: formData.teacher_number, // 工号保持字符串类型
      title: formData.teacher_title,
      academyId: formData.academy_id,
      user: {
        realName: formData.teacher_name,
        gender: formData.gender,
        phone: formData.phone,
        email: formData.email
      }
    };
    console.log('添加教师数据:', data);
    const res = await addTeacher(data);
    console.log('新增教师API返回的添加结果:', res);
    MessagePlugin.success('添加教师成功');
    addVisible.value = false;
    resetFormData();
    fetchTeacherList();
    buildTreeData()
  } catch (error) {
    console.error('添加教师失败:', error);
    MessagePlugin.error('添加教师失败');
  }
};

// 处理更新教师提交
const handleSubmit = async (submitContext: any) => {
  // 如果验证失败，直接返回
  if (submitContext.validateResult !== true) {
    console.log('表单验证失败:', submitContext.validateResult);
    return;
  }

  try {
    const teacherId = currentTeacher.value?.id || currentTeacher.value?.teacher_id;
    if (!currentTeacher.value || !teacherId) {
      MessagePlugin.error('教师ID不能为空');
      return;
    }

    // 构建更新数据，包含teacherId
    const data = {
      teacherId: teacherId,
      number: formData.teacher_number, // 工号保持字符串类型
      title: formData.teacher_title,
      academyId: formData.academy_id,
      user: {
        realName: formData.teacher_name,
        gender: formData.gender,
        phone: formData.phone,
        email: formData.email
      }
    };

    console.log('更新教师数据:', data);

    // 调用API更新教师
    const res = await updateTeacher(data);
    MessagePlugin.success('更新教师成功');
    editVisible.value = false;
    fetchTeacherList();
    buildTreeData()
  } catch (error) {
    console.error('更新教师失败:', error);
    MessagePlugin.error('更新教师失败');
  }
};

// 打开新增教师对话框
const handleAddTeacher = () => {
  resetFormData();
  console.log('打开新增教师对话框 - formData:', formData);
  console.log('当前titleOptions:', titleOptions.value);
  addVisible.value = true;
};

// 导入对话框控制
const importVisible = ref(false);

// 导入配置
const importConfig: ImportConfig = {
  title: '导入教师数据',
  tips: '请按照模板格式填写教师信息，支持批量导入。注意：学院名称请填写准确的名称，系统将自动匹配对应的学院',
  templateFileName: '教师信息导入模板.xlsx',
  templateData: [
    ['工号', '姓名', '性别', '手机号', '邮箱', '职称', '学院名称'],
    ['2000', '张三', '男', '13800000000', '<EMAIL>', '教授', '计算机科学与技术学院'],
    ['2001', '李四', '女', '13800000001', '<EMAIL>', '副教授', '计算机科学与技术学院'],
    ['2002', '王五', '男', '13800000002', '<EMAIL>', '讲师', '电子信息工程学院']
  ],
  acceptTypes: ['.xlsx', '.xls'],
  maxFileSize: 5
};

// 导入回调函数
const importCallbacks: ImportCallbacks = {
  onImport: async (file: File) => {
    return await importTeachers(file);
  },
  onSuccess: (result: any) => {
    fetchTeacherList(); // 刷新列表
  },
  onError: (error: Error) => {
    console.error('导入失败:', error);
  },
  onComplete: () => {
    // 导入完成后的处理
  }
};

// 统计信息对话框控制
const statisticsVisible = ref(false);
const statisticsData = ref<any>(null);

// 打开导入对话框
const openImportDialog = () => {
  importVisible.value = true;
};
// 导出教师数据
const exportTeacherData = async () => {
  MessagePlugin.info('正在导出...');
  try {
    // 构建查询参数，按照API文档要求的参数格式
    const params = {
      academy_id: searchForm.academy_id || undefined,
      teacher_name: searchForm.teacher_name || undefined,
      teacher_number: searchForm.teacher_number || undefined,
      teacher_title: searchForm.teacher_title || undefined,
      gender: searchForm.gender !== null ? searchForm.gender : undefined
    };
    // 调用API导出数
    const blob = await exportTeacherDataApi(params);
    if (!(blob instanceof Blob)) {
      throw new Error('导出失败，返回格式错误');
    }

    // 创建下载链接并触发下载
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.setAttribute("href", url);
    link.setAttribute("download", `教师数据_${new Date().toLocaleDateString()}.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    MessagePlugin.success(`导出成功`);
  } catch (error) {
    console.error('导出教师数据失败:', error);
    MessagePlugin.error('导出教师数据失败');
  }
};



// 构建组织结构树数据
const buildTreeData = async () => {
  treeLoading.value = true;
  try {
    console.log('开始获取教师组织结构树数据...');
    const result: any = await getTeacherTree();
    console.log('教师组织结构树API返回:', result);

    if (result && result.code === 200 && result.data) {
      treeData.value = result.data;
      console.log('设置树形数据:', treeData.value);
    } else {
      console.error('获取组织结构树失败:', result?.msg);
      MessagePlugin.error('获取组织结构树失败');
      // 使用备用的简单数据结构
      treeData.value = [{
        id: 'all',
        label: '全部教师',
        type: 'school',
        teacherCount: 0,
        children: []
      }];
    }

    // 同时构建专业选项数据
    buildMajorOptions();

  } catch (error) {
    console.error('构建树形数据失败:', error);
    MessagePlugin.error('加载组织结构失败');
    // 使用备用的简单数据结构
    treeData.value = [{
      id: 'all',
      label: '全部教师',
      type: 'school',
      teacherCount: 0,
      children: []
    }];
  } finally {
    treeLoading.value = false;
  }
};

// 构建专业选项数据
const buildMajorOptions = () => {
  const majors: any[] = [];

  const extractMajors = (nodes: any[]) => {
    nodes.forEach(node => {
      if (node.type === 'major') {
        majors.push({
          label: node.label,
          value: node.id
        });
      }
      if (node.children && node.children.length > 0) {
        extractMajors(node.children);
      }
    });
  };

  extractMajors(treeData.value);
  majorOptions.value = majors;
};

// 查找树节点
const findNodeById = (nodes: any[], id: string): any => {
  for (const node of nodes) {
    if (node.id === id) {
      return node;
    }
    if (node.children && node.children.length > 0) {
      const found = findNodeById(node.children, id);
      if (found) return found;
    }
  }
  return null;
};

// 处理树节点点击
const handleTreeClick = (context: any) => {
  const { node } = context;
  const nodeData = node.data;

  activeNodeKey.value = nodeData.id;

  // 重置选择状态
  selectedCollegeId.value = '';
  selectedMajorId.value = '';
  selectedNodeInfo.value = '';

  // 根据节点类型设置过滤条件
  if (nodeData.type === 'school') {
    // 选择根节点，显示所有教师
    searchForm.academy_id = '';
    searchForm.major_id = '';
    selectedNodeInfo.value = '当前显示：全部教师';
  } else if (nodeData.type === 'college') {
    // 选择学院节点
    selectedCollegeId.value = nodeData.id;
    // 使用实际的学院ID，而不是显示用的college_前缀ID
    searchForm.academy_id = nodeData.academyId || '';
    searchForm.major_id = '';
    selectedNodeInfo.value = `当前显示：${nodeData.label} 的所有教师`;
  } else if (nodeData.type === 'major') {
    // 选择专业节点
    selectedMajorId.value = nodeData.id;
    selectedCollegeId.value = nodeData.collegeId;
    // 使用实际的学院ID
    searchForm.academy_id = nodeData.collegeId || '';
    searchForm.major_id = nodeData.id;
    selectedNodeInfo.value = `当前显示：${nodeData.label} 专业的教师`;
  }

  // 重置分页并重新加载数据
  pagination.current = 1;
  fetchTeacherList();
};

// 刷新数据
const refreshData = async () => {
  await Promise.all([
    buildTreeData(),
    fetchTeacherList(),
    fetchAcademies(),
    fetchTeacherTitleOptions()
  ]);
  MessagePlugin.success('数据已刷新');
};

// 初始化
onMounted(async () => {
  // 获取基础数据
  await Promise.all([
    buildTreeData(),
    fetchAcademies(),
    fetchTeacherTitleOptions()
  ]);
  fetchTeacherList();
});

// 表格选择变化处理
const handleSelectChange = (selectedKeys: (string | number)[]) => {
  selectedRowKeys.value = selectedKeys;
};

// 批量删除处理
const handleBatchDelete = () => {
  if (selectedRowKeys.value.length === 0) {
    MessagePlugin.warning('请先选择要删除的教师');
    return;
  }

  console.log('=== 批量删除操作开始 ===');
  console.log('选中的行键:', selectedRowKeys.value);
  console.log('当前表格数据:', tableData.value);

  // 验证选中的ID是否有效
  const validIds = selectedRowKeys.value.filter(id => id !== null && id !== undefined);
  console.log('有效的ID列表:', validIds);

  const confirmDialog = DialogPlugin.confirm({
    header: '批量删除确认',
    body: `确定要删除选中的 ${selectedRowKeys.value.length} 个教师吗？`,
    confirmBtn: '确认删除',
    cancelBtn: '取消',
    onConfirm: async () => {
      try {
        console.log('调用批量删除API，ID列表:', validIds);
        const result = await batchDeleteTeachers(validIds);
        console.log('批量删除API返回结果:', result);

        MessagePlugin.success('批量删除成功');
        selectedRowKeys.value = [];
        fetchTeacherList();
        buildTreeData()
        confirmDialog.destroy(); // 显式关闭对话框
      } catch (error) {
        console.error('批量删除教师失败:', error);
        MessagePlugin.error('批量删除教师失败');
      }
    }
  });
};

// 停用教师
const handleDisableTeacher = (row: TeacherInfo) => {
  const confirmDialog = DialogPlugin.confirm({
    header: '停用确认',
    body: `确定要停用教师"${row.teacher_name || row.user?.name}"吗？`,
    confirmBtn: '确认停用',
    cancelBtn: '取消',
    onConfirm: async () => {
      try {
        const teacherId = row.id || row.teacher_id;
        await disableTeacherApi(teacherId);
        MessagePlugin.success('停用成功');
        fetchTeacherList();
        confirmDialog.destroy(); // 显式关闭对话框
      } catch (error) {
        console.error('停用教师失败:', error);
        MessagePlugin.error('停用教师失败');
      }
    }
  });
};

// 启用教师
const handleEnableTeacher = (row: TeacherInfo) => {
  const confirmDialog = DialogPlugin.confirm({
    header: '启用确认',
    body: `确定要启用教师"${row.teacher_name || row.user?.name}"吗？`,
    confirmBtn: '确认启用',
    cancelBtn: '取消',
    onConfirm: async () => {
      try {
        const teacherId = row.id || row.teacher_id;
        await enableTeacherApi(teacherId);
        MessagePlugin.success('启用成功');
        fetchTeacherList();
        confirmDialog.destroy(); // 显式关闭对话框
      } catch (error) {
        console.error('启用教师失败:', error);
        MessagePlugin.error('启用教师失败');
      }
    }
  });
};

// 重置密码
const handleResetPassword = (row: TeacherInfo) => {
  const confirmDialog = DialogPlugin.confirm({
    header: '重置密码确认',
    body: `确定要重置教师"${row.teacher_name || row.user?.name}"的密码吗？密码将重置为默认密码123456。`,
    confirmBtn: '确认重置',
    cancelBtn: '取消',
    onConfirm: async () => {
      try {
        const teacherId = row.id || row.teacher_id;
        await resetTeacherPassword(teacherId);
        MessagePlugin.success('密码重置成功');
        confirmDialog.destroy(); // 显式关闭对话框
      } catch (error) {
        console.error('重置密码失败:', error);
        MessagePlugin.error('重置密码失败');
      }
    }
  });
};

// 显示统计信息
const showStatistics = async () => {
  try {
    const result: any = await getTeacherStatistics();
    console.log('统计信息API返回:', result);

    if (result && result.code === 200 && result.data) {
      statisticsData.value = result.data;
      statisticsVisible.value = true;
    } else {
      MessagePlugin.warning('获取统计信息失败');
    }
  } catch (error) {
    console.error('获取统计信息失败:', error);
    MessagePlugin.error('获取统计信息失败');
  }
};

// 重置列设置为默认值
const resetColumnSettings = () => {
  displayColumns.value = [
    'row-select',
    'serial-number',
    'teacher_name',
    'teacher_number',
    'gender',
    'teacher_title',
    'academy_name',
    'phone',
    'email',
    'status',
    'operation'
  ];
  MessagePlugin.success('已重置为默认列设置');
};
</script>

<style scoped lang="less">
.teacher-tree-container {
  padding: 20px;
  background-color: var(--td-bg-color-container);
  border-radius: var(--td-radius-medium);

  .teacher-management-layout {
    display: flex;
    gap: 20px;
    min-height: 700px;

    .left-panel {
      width: 300px;
      flex-shrink: 0;

      .org-tree-card {
        height: 100%;

        :deep(.t-card__body) {
          padding: 16px;
          height: calc(100% - 60px);
        }
      }

      .tree-node-label {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;

        .teacher-count {
          font-size: 12px;
          color: var(--td-text-color-placeholder);
          margin-left: 8px;
        }
      }
    }

    .right-panel {
      flex: 1;
      min-width: 0; // 防止flex子项溢出
    }
  }



  .teacher-content {
    border-radius: var(--td-radius-medium);
    overflow: hidden;
  }

  .operation-container {
    display: flex;
    justify-content: center;
    gap: 8px;

    :deep(.t-button) {
      min-width: unset;
      transition: all 0.2s ease;

      &:hover {
        transform: translateY(-2px);
      }
    }

    @media (max-width: 768px) {
      gap: 4px;
    }
  }

  // 响应式布局
  @media (max-width: 1200px) {
    .teacher-management-layout {
      .left-panel {
        width: 280px;
      }
    }
  }

  @media (max-width: 768px) {
    .teacher-management-layout {
      flex-direction: column;

      .left-panel {
        width: 100%;

        .org-tree-card {
          height: 300px;
        }
      }
    }
  }
}

// 卡片标题样式
.card-header {
  .card-title {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
    color: var(--td-text-color-primary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
  }
}

// 搜索容器样式
.search-container {
  margin-bottom: 20px;
  padding: 16px;
  background-color: var(--td-bg-color-container-hover);
  border-radius: var(--td-radius-medium);

  // 固定宽度的输入框
  :deep(.fixed-width-input) {
    width: 180px !important;
    min-width: 180px !important;
    max-width: 180px !important;

    .t-input__inner {
      width: 100% !important;
    }

    // 确保清除按钮不影响输入框宽度
    .t-input__suffix {
      position: absolute;
      right: 8px;
    }
  }

  // 固定宽度的下拉框
  :deep(.fixed-width-select) {
    width: 180px !important;
    min-width: 180px !important;
    max-width: 180px !important;

    .t-select__wrap {
      width: 100% !important;
    }

    .t-input {
      width: 100% !important;
    }

    .t-input__inner {
      width: 100% !important;
    }

    // 确保清除按钮不影响下拉框宽度
    .t-input__suffix {
      position: absolute;
      right: 8px;
    }
  }
}

// 当前选择信息样式
.current-selection {
  margin-bottom: 16px;

  :deep(.t-alert) {
    border-radius: 6px;
  }
}

// 操作按钮区域样式
.action-buttons {
  margin-bottom: 20px;
  padding: 16px;
  background-color: var(--td-bg-color-container-hover);
  border-radius: var(--td-radius-medium);
  border-top: 1px solid var(--td-border-level-1-color);
}



.statistics-content {
  .stat-card {
    text-align: center;
    border-radius: 8px;

    :deep(.t-card__body) {
      padding: 16px 8px;
    }

    .stat-number {
      font-size: 24px;
      font-weight: bold;
      color: var(--td-text-color-primary);
      margin-bottom: 4px;
    }

    .stat-label {
      font-size: 12px;
      color: var(--td-text-color-secondary);
    }

    &.stat-primary {
      border-left: 4px solid var(--td-brand-color);
      background: var(--td-brand-color-light);
    }

    &.stat-success {
      border-left: 4px solid var(--td-success-color);
      background: var(--td-success-color-light);
    }

    &.stat-warning {
      border-left: 4px solid var(--td-warning-color);
      background: var(--td-warning-color-light);
    }

    &.stat-default {
      border-left: 4px solid var(--td-gray-color-6);
      background: var(--td-bg-color-container-hover);
    }
  }

  .title-distribution {
    .title-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 12px;
      background: var(--td-bg-color-container-hover);
      border-radius: 4px;

      .title-name {
        font-weight: 500;
      }
    }
  }
}
</style>
