import { useRoute, useRouter } from 'vue-router'
import { watch, onMounted } from 'vue'
import { storage } from '@/utils/storage'

/**
 * 工作台缓存服务
 * 用于记录和恢复用户最后访问的工作台
 */

// storage缓存键名
const WORKSPACE_CACHE_KEY = 'obe_last_workspace'

// 工作台路由信息接口
interface WorkspaceInfo {
  path: string
  fullPath: string
  name?: string
  params?: Record<string, any>
  query?: Record<string, any>
  timestamp: number // 缓存时间戳
}

/**
 * 判断路径是否为工作台页面
 * 工作台路由都是固定的以/dashboard开头
 */
export function isWorkspacePage(path: string): boolean {
  return path.startsWith('/dashboard')
}

/**
 * 保存工作台信息到storage
 */
function saveWorkspaceToCache(workspaceInfo: WorkspaceInfo): void {
  try {
    storage.set(WORKSPACE_CACHE_KEY, workspaceInfo)
    console.log('✅ 工作台缓存已保存:', workspaceInfo)
  } catch (error) {
    console.warn('❌ 保存工作台缓存失败:', error)
  }
}

/**
 * 从storage读取工作台信息
 */
function loadWorkspaceFromCache(): WorkspaceInfo | null {
  try {
    const workspaceInfo = storage.get(WORKSPACE_CACHE_KEY) as WorkspaceInfo | null
    
    if (workspaceInfo) {
      // 检查缓存是否过期（24小时）
      const isExpired = Date.now() - workspaceInfo.timestamp > 24 * 60 * 60 * 1000
      if (isExpired) {
        console.log('⏰ 工作台缓存已过期，清理缓存')
        clearWorkspaceCache()
        return null
      }
      
      console.log('📖 从缓存加载工作台信息:', workspaceInfo)
      return workspaceInfo
    }
  } catch (error) {
    console.warn('❌ 读取工作台缓存失败:', error)
  }
  return null
}

/**
 * 清理工作台缓存
 */
function clearWorkspaceCache(): void {
  try {
    storage.remove(WORKSPACE_CACHE_KEY)
    console.log('🗑️ 工作台缓存已清理')
  } catch (error) {
    console.warn('❌ 清理工作台缓存失败:', error)
  }
}

/**
 * 工作台缓存Hook
 */
export function useWorkspaceCaching() {
  const route = useRoute()
  const router = useRouter()

  /**
   * 缓存当前工作台信息
   */
  const cacheCurrentWorkspace = () => {
    const currentPath = route.path
    
    console.log('🔍 检查是否为工作台页面:', currentPath)
    
    // 只有当前页面是工作台时才缓存
    if (isWorkspacePage(currentPath)) {
      const workspaceInfo: WorkspaceInfo = {
        path: currentPath,
        fullPath: route.fullPath,
        name: route.name as string,
        params: { ...route.params },
        query: { ...route.query },
        timestamp: Date.now()
      }
      
      console.log('📝 缓存工作台信息:', workspaceInfo)
      saveWorkspaceToCache(workspaceInfo)
    } else {
      console.log('⏭️ 非工作台页面，跳过缓存')
    }
  }

  /**
   * 获取缓存的工作台信息
   */
  const getCachedWorkspace = (): WorkspaceInfo | null => {
    return loadWorkspaceFromCache()
  }

  /**
   * 跳转到缓存的工作台
   */
  const goToCachedWorkspace = (): boolean => {
    const cachedWorkspace = getCachedWorkspace()
    
    if (cachedWorkspace) {
      console.log('🚀 跳转到缓存的工作台:', cachedWorkspace)
      
      if (cachedWorkspace.name) {
        // 优先使用路由名称导航
        router.push({
          name: cachedWorkspace.name,
          params: cachedWorkspace.params,
          query: cachedWorkspace.query
        })
      } else {
        // 否则使用完整路径
        router.push(cachedWorkspace.fullPath)
      }
      return true
    }
    
    return false
  }

  /**
   * 自动监听路由变化并缓存工作台
   */
  const startWorkspaceTracking = () => {
    // 立即检查当前路由
    cacheCurrentWorkspace()
    
    // 监听路由变化
    watch(
      () => route.path,
      (newPath, oldPath) => {
        if (newPath !== oldPath) {
          // 延迟缓存，确保路由完全切换
          setTimeout(() => {
            cacheCurrentWorkspace()
          }, 100)
        }
      }
    )
    
   // console.log('🎯 工作台追踪已启动')
  }

  return {
    // 手动操作
    cacheCurrentWorkspace,
    getCachedWorkspace,
    goToCachedWorkspace,
    clearWorkspaceCache,
    
    // 自动追踪
    startWorkspaceTracking,
    
    // 工具方法
    isWorkspacePage
  }
}

/**
 * 基于路由对象的工作台缓存（用于路由守卫）
 * 不依赖于Vue组合式API，可以在路由守卫中直接使用
 */
export function cacheWorkspaceFromRoute(to: any) {
  try {
    const currentPath = to.path
    
   // console.log('🔍 [路由守卫] 检查是否为工作台页面:', currentPath)
    
    // 只有当前页面是工作台时才缓存
    if (isWorkspacePage(currentPath)) {
      const workspaceInfo: WorkspaceInfo = {
        path: currentPath,
        fullPath: to.fullPath,
        name: to.name as string,
        params: { ...to.params },
        query: { ...to.query },
        timestamp: Date.now()
      }
      
     // console.log('📝 [路由守卫] 缓存工作台信息:', workspaceInfo)
      saveWorkspaceToCache(workspaceInfo)
    } else {
     // console.log('⏭️ [路由守卫] 非工作台页面，跳过缓存')
    }
  } catch (error) {
    console.warn('❌ [路由守卫] 缓存工作台信息失败:', error)
  }
}

/**
 * 获取缓存的工作台信息（用于路由守卫或其他非Vue组件上下文）
 */
export function getCachedWorkspaceInfo(): WorkspaceInfo | null {
  return loadWorkspaceFromCache()
} 