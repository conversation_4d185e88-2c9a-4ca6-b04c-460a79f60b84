<template>
    <div class="grading-detail-container">
        <div class="page-header">
            <t-button theme="default" @click="goBack">
                <template #icon><t-icon name="arrow-left" /></template>
                返回批阅列表
            </t-button>
            <h2>批阅: {{ studentInfo.name }} 的{{ isExam ? '考试' : '作业' }}</h2>
        </div>

        <div class="student-info-bar">
            <div class="student-info">
                <div class="student-avatar">
                    <img :src="studentInfo.avatar || '/image/avatar/default-avatar.jpg'" alt="学生头像">
                </div>
                <div class="student-details">
                    <div class="student-name">{{ studentInfo.name }}</div>
                    <div class="student-meta">
                        <span>学号: {{ studentInfo.studentId }}</span>
                        <span>班级: {{ studentInfo.className }}</span>
                    </div>
                </div>
            </div>

            <div class="submission-info">
                <div class="submission-time">提交时间: {{ submissionInfo.submissionTime }}</div>
                <div class="status-info" v-if="submissionInfo.status === 'graded'">
                    <span>得分: </span>
                    <span :class="getScoreClass(submissionInfo.score)">{{ submissionInfo.score }}</span>
                    <span> / {{ assignmentInfo.totalScore }}</span>
                </div>
            </div>
        </div>

        <!-- 导航栏 -->
        <div class="navigation-bar">
            <div class="student-nav">
                <t-button :disabled="!hasPrevious" @click="navigateToPrevious">
                    <template #icon><t-icon name="chevron-left" /></template>
                    上一个学生
                </t-button>
                <div class="student-counter">{{ currentIndex + 1 }} / {{ totalStudents }}</div>
                <t-button :disabled="!hasNext" @click="navigateToNext">
                    下一个学生
                    <template #suffix><t-icon name="chevron-right" /></template>
                </t-button>
            </div>
            <div class="progress-bar">
                <div class="progress-indicator" :style="{ width: `${progressPercentage}%` }"></div>
            </div>
        </div>

        <!-- 主要批阅区域 -->
        <div class="grading-tabs-container">
            <t-tabs v-model="activeTab" :before-change="handleTabChange">
                <t-tab-panel value="manual" label="需要批阅的题目">
                    <div v-if="loading" class="loading-state">
                        <t-loading />
                    </div>
                    <template v-else>
                        <div v-if="manualQuestions.length === 0" class="empty-state">
                            <t-empty description="没有需要手动批阅的题目" />
                        </div>
                        <div v-else class="questions-list">
                            <!-- 手动批阅题目 -->
                            <t-card v-for="(question, index) in manualQuestions" :key="question.id"
                                class="question-card">
                                <div class="question-header">
                                    <div class="question-info">
                                        <span class="question-number">{{ index + 1 }}</span>
                                        <span class="question-type">{{ getQuestionTypeName(question.type) }}</span>
                                        <span class="question-score">{{ question.score }}分</span>
                                    </div>
                                    <div class="grading-status"
                                        :class="question.graded ? 'status-graded' : 'status-ungraded'">
                                        {{ question.graded ? '已批阅' : '未批阅' }}
                                    </div>
                                </div>

                                <div class="question-content">{{ question.content }}</div>

                                <div class="student-answer">
                                    <div class="answer-label">学生答案:</div>
                                    <div class="answer-content">{{ question.studentAnswer || '(未作答)' }}</div>
                                </div>

                                <div class="reference-answer">
                                    <div class="answer-label">参考答案:</div>
                                    <div class="answer-content">{{ question.referenceAnswer }}</div>
                                </div>

                                <div class="grading-area">
                                    <div class="score-input">
                                        <t-input-number v-model="question.givenScore" :min="0" :max="question.score"
                                            @change="(val) => updateTotalScore(val, manualQuestions.indexOf(question))" />
                                        <span class="score-max">/ {{ question.score }}分</span>
                                    </div>
                                    <t-input v-model="question.comment" class="comment-input" placeholder="添加评语(选填)" />
                                </div>
                            </t-card>
                        </div>
                    </template>
                </t-tab-panel>

                <t-tab-panel value="auto" label="自动批阅的题目">
                    <div v-if="loading" class="loading-state">
                        <t-loading />
                    </div>
                    <template v-else>
                        <div v-if="autoQuestions.length === 0" class="empty-state">
                            <t-empty description="没有自动批阅的题目" />
                        </div>
                        <div v-else class="questions-list">
                            <!-- 自动批阅题目 -->
                            <t-card v-for="(question, index) in autoQuestions" :key="question.id" class="question-card">
                                <div class="question-header">
                                    <div class="question-info">
                                        <span class="question-number">{{ index + 1 }}</span>
                                        <span class="question-type">{{ getQuestionTypeName(question.type) }}</span>
                                        <span class="question-score">{{ question.score }}分</span>
                                    </div>
                                    <div class="auto-graded-badge">
                                        <t-tag theme="success">自动批阅</t-tag>
                                        <span class="auto-score">
                                            得分:
                                            <span
                                                :class="question.givenScore === question.score ? 'score-full' : 'score-partial'">
                                                {{ question.givenScore }}
                                            </span>
                                            / {{ question.score }}
                                        </span>
                                    </div>
                                </div>

                                <div class="question-content">{{ question.content }}</div>

                                <!-- 选择题选项 -->
                                <div v-if="question.type === 'single' || question.type === 'multiple'"
                                    class="options-list">
                                    <div v-for="(option, optIndex) in question.options" :key="optIndex"
                                        class="option-item" :class="{
                                            'student-selected': isOptionSelected(question, option),
                                            'correct-option': option.isCorrect,
                                            'wrong-option': isOptionSelected(question, option) && !option.isCorrect
                                        }">
                                        <span class="option-marker">{{ String.fromCharCode(65 + optIndex) }}</span>
                                        <span class="option-content">{{ option.content }}</span>
                                        <span v-if="option.isCorrect" class="correct-icon">✓</span>
                                        <span v-else-if="isOptionSelected(question, option)" class="wrong-icon">✗</span>
                                    </div>
                                </div>

                                <!-- 填空题 -->
                                <div v-else-if="question.type === 'fill_blank'" class="fill-blank-answer">
                                    <div class="student-answer">
                                        <div class="answer-label">学生答案:</div>
                                        <div class="blanks-list">
                                            <div v-for="(blank, blankIndex) in question.blanks" :key="blankIndex"
                                                class="blank-item"
                                                :class="blank.isCorrect ? 'correct-blank' : 'wrong-blank'">
                                                <span class="blank-number">空{{ blankIndex + 1 }}:</span>
                                                <span class="blank-answer">{{ blank.studentAnswer || '(未填写)' }}</span>
                                                <span v-if="blank.isCorrect" class="correct-icon">✓</span>
                                                <span v-else class="wrong-icon">✗</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="reference-answer">
                                        <div class="answer-label">参考答案:</div>
                                        <div class="blanks-list">
                                            <div v-for="(blank, blankIndex) in question.blanks" :key="blankIndex"
                                                class="blank-item">
                                                <span class="blank-number">空{{ blankIndex + 1 }}:</span>
                                                <span class="blank-answer">{{ blank.correctAnswer }}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 判断题 -->
                                <div v-else-if="question.type === 'true_false'" class="true-false-answer">
                                    <div class="student-answer">
                                        <div class="answer-label">学生答案:</div>
                                        <div class="true-false-option" :class="{
                                            'correct-answer': question.studentAnswer === question.correctAnswer,
                                            'wrong-answer': question.studentAnswer !== question.correctAnswer
                                        }">
                                            {{ question.studentAnswer ? '√ 正确' : '× 错误' }}
                                            <span v-if="question.studentAnswer === question.correctAnswer"
                                                class="correct-icon">✓</span>
                                            <span v-else class="wrong-icon">✗</span>
                                        </div>
                                    </div>
                                    <div class="reference-answer">
                                        <div class="answer-label">参考答案:</div>
                                        <div class="true-false-option">
                                            {{ question.correctAnswer ? '√ 正确' : '× 错误' }}
                                        </div>
                                    </div>
                                </div>
                            </t-card>
                        </div>
                    </template>
                </t-tab-panel>

                <t-tab-panel value="all" label="全部题目">
                    <div v-if="loading" class="loading-state">
                        <t-loading />
                    </div>
                    <template v-else>
                        <div v-if="allQuestions.length === 0" class="empty-state">
                            <t-empty description="没有题目" />
                        </div>
                        <div v-else class="questions-list">
                            <!-- 这里混合显示所有题目，按题号排序 -->
                            <t-card v-for="question in allQuestions" :key="question.id" class="question-card">
                                <!-- 内容类似上面，但是混合了手动和自动批阅的题目 -->
                                <div class="question-header">
                                    <div class="question-info">
                                        <span class="question-number">{{ question.questionNumber }}</span>
                                        <span class="question-type">{{ getQuestionTypeName(question.type) }}</span>
                                        <span class="question-score">{{ question.score }}分</span>
                                    </div>
                                    <div v-if="question.needManualGrading" class="grading-status"
                                        :class="question.graded ? 'status-graded' : 'status-ungraded'">
                                        {{ question.graded ? '已批阅' : '未批阅' }}
                                    </div>
                                    <div v-else class="auto-graded-badge">
                                        <t-tag theme="success">自动批阅</t-tag>
                                        <span class="auto-score">
                                            得分:
                                            <span
                                                :class="question.givenScore === question.score ? 'score-full' : 'score-partial'">
                                                {{ question.givenScore }}
                                            </span>
                                            / {{ question.score }}
                                        </span>
                                    </div>
                                </div>

                                <!-- 题目内容和答案显示类似上面的代码 -->
                                <div class="question-content">{{ question.content }}</div>

                                <!-- 根据不同题型显示不同内容 -->
                                <!-- ... 这里省略与前面类似的代码 ... -->
                            </t-card>
                        </div>
                    </template>
                </t-tab-panel>
            </t-tabs>
        </div>

        <!-- 底部操作栏 -->
        <div class="actions-bar">
            <div class="total-score-display">
                <span>总分: </span>
                <span class="current-total-score" :class="getScoreClass(totalScore)">{{ totalScore }}</span>
                <span> / {{ assignmentInfo.totalScore }}</span>
            </div>

            <div class="action-buttons">
                <t-button theme="default" @click="goBack">返回</t-button>
                <t-button theme="primary" @click="submitGrading">保存批阅</t-button>
                <t-button theme="primary" @click="saveAndNext" :disabled="!hasNext">保存并批阅下一份</t-button>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
    Button as TButton,
    Card as TCard,
    Tabs as TTabs,
    TabPanel as TTabPanel,
    Input as TInput,
    InputNumber as TInputNumber,
    Loading as TLoading,
    Empty as TEmpty,
    Tag as TTag,
    Icon as TIcon,
    MessagePlugin,
    DialogPlugin
} from 'tdesign-vue-next'
import type { InputNumberValue } from 'tdesign-vue-next'

const route = useRoute()
const router = useRouter()

const isExam = computed(() => route.query.type === 'exam')
const assignmentId = ref(route.params.id as string)
const studentId = ref(route.query.studentId as string)
const loading = ref(true)
const activeTab = ref('manual')

// 作业/考试信息
const assignmentInfo = ref({
    id: '',
    title: '',
    type: isExam.value ? 'exam' : 'homework',
    totalScore: 100
})

// 学生信息
const studentInfo = ref({
    id: '',
    name: '',
    studentId: '',
    className: '',
    avatar: ''
})

// 提交信息
const submissionInfo = ref({
    id: '',
    submissionTime: '',
    status: 'submitted',
    score: 0,
    comment: ''
})

// 题目列表
const questions = ref([])

// 手动批阅题目
const manualQuestions = computed(() => {
    return questions.value.filter(q => q.needManualGrading)
})

// 自动批阅题目
const autoQuestions = computed(() => {
    return questions.value.filter(q => !q.needManualGrading)
})

// 所有题目，按照题号排序
const allQuestions = computed(() => {
    return [...questions.value].sort((a, b) => a.questionNumber - b.questionNumber)
})

// 计算总分
const totalScore = computed(() => {
    return questions.value.reduce((sum, q) => sum + (q.givenScore || 0), 0)
})

// 更新总分
const updateTotalScore = (newValue: InputNumberValue, questionIndex: number) => {
    // 检查总分是否会超过限制
    const newValueAsNumber = typeof newValue === 'string' ? parseFloat(newValue) : newValue;

    const allScores = questions.value.map(q => q.givenScore || 0);
    const totalWithoutCurrent = allScores.reduce((sum, score, index) => {
        if (index === questionIndex) return sum; // 排除当前题目分数
        return sum + score;
    }, 0);
    const newTotal = totalWithoutCurrent + newValueAsNumber;

    // 如果总分超过限制，调整当前题目分数
    if (newTotal > assignmentInfo.value.totalScore) {
        const maxAllowed = assignmentInfo.value.totalScore - totalWithoutCurrent;
        questions.value[questionIndex].givenScore = Math.max(0, maxAllowed);
        MessagePlugin.warning(`总分不能超过${assignmentInfo.value.totalScore}分，已自动调整该题得分`);
    }

    // 标记题目为已批阅
    if (questions.value[questionIndex].needManualGrading) {
        if (questions.value[questionIndex].givenScore !== null &&
            questions.value[questionIndex].givenScore !== undefined) {
            questions.value[questionIndex].graded = true;
        }
    }
}

// 判断选项是否被选中
const isOptionSelected = (question: any, option: any) => {
    if (question.type === 'single') {
        return question.studentAnswer === option.value
    } else if (question.type === 'multiple') {
        return question.studentAnswer && question.studentAnswer.includes(option.value)
    }
    return false
}

// 获取题型名称
const getQuestionTypeName = (type: string) => {
    const typeMap: Record<string, string> = {
        'single': '单选题',
        'multiple': '多选题',
        'true_false': '判断题',
        'fill_blank': '填空题',
        'essay': '简答题'
    }
    return typeMap[type] || type
}

// 获取分数样式
const getScoreClass = (score: number) => {
    if (score >= 90) return 'score-excellent'
    if (score >= 60) return 'score-pass'
    return 'score-fail'
}

// 导航相关
const currentIndex = ref(0)
const totalStudents = ref(1)
const studentIds = ref([])

const hasPrevious = computed(() => currentIndex.value > 0)
const hasNext = computed(() => currentIndex.value < totalStudents.value - 1)
const progressPercentage = computed(() => ((currentIndex.value + 1) / totalStudents.value) * 100)

// 监听路由参数变化
watch(
    () => route.query.studentId,
    (newStudentId) => {
        if (newStudentId) {
            studentId.value = newStudentId as string
            fetchSubmissionDetails() // 重新获取数据
        }
    }
)

// 导航到上一个学生
const navigateToPrevious = () => {
    if (hasPrevious.value) {
        // 检查是否有未保存的改动
        const hasUnsavedChanges = manualQuestions.value.some(q =>
            !q.graded && q.givenScore > 0
        )

        if (hasUnsavedChanges) {
            DialogPlugin.confirm({
                header: '未保存的改动',
                body: '您有未保存的批阅，是否保存后再继续？',
                onConfirm: () => {
                    submitGrading()
                    setTimeout(() => {
                        router.push({
                            path: `/teachers/grade-submission/${assignmentId.value}`,
                            query: {
                                studentId: studentIds.value[currentIndex.value - 1],
                                type: isExam.value ? 'exam' : 'homework'
                            }
                        })
                    }, 800)
                },
                onClose: () => {
                    router.push({
                        path: `/teachers/grade-submission/${assignmentId.value}`,
                        query: {
                            studentId: studentIds.value[currentIndex.value - 1],
                            type: isExam.value ? 'exam' : 'homework'
                        }
                    })
                }
            })
        } else {
            router.push({
                path: `/teachers/grade-submission/${assignmentId.value}`,
                query: {
                    studentId: studentIds.value[currentIndex.value - 1],
                    type: isExam.value ? 'exam' : 'homework'
                }
            })
        }
    }
}

// 导航到下一个学生
const navigateToNext = () => {
    if (hasNext.value) {
        // 检查是否有未保存的改动
        const hasUnsavedChanges = manualQuestions.value.some(q =>
            !q.graded && q.givenScore > 0
        )

        if (hasUnsavedChanges) {
            DialogPlugin.confirm({
                header: '未保存的改动',
                body: '您有未保存的批阅，是否保存后再继续？',
                onConfirm: () => {
                    submitGrading()
                    setTimeout(() => {
                        router.push({
                            path: `/teachers/grade-submission/${assignmentId.value}`,
                            query: {
                                studentId: studentIds.value[currentIndex.value + 1],
                                type: isExam.value ? 'exam' : 'homework'
                            }
                        })
                    }, 800)
                },
                onClose: () => {
                    router.push({
                        path: `/teachers/grade-submission/${assignmentId.value}`,
                        query: {
                            studentId: studentIds.value[currentIndex.value + 1],
                            type: isExam.value ? 'exam' : 'homework'
                        }
                    })
                }
            })
        } else {
            router.push({
                path: `/teachers/grade-submission/${assignmentId.value}`,
                query: {
                    studentId: studentIds.value[currentIndex.value + 1],
                    type: isExam.value ? 'exam' : 'homework'
                }
            })
        }
    }
}

// 标签切换处理
const handleTabChange = () => {
    return true // 允许切换
}

// 返回批阅列表
const goBack = () => {
    router.push(`/teachers/grading-list/${assignmentId.value}?type=${isExam.value ? 'exam' : 'homework'}`)
}

// 获取学生作业/考试信息
const fetchSubmissionDetails = () => {
    loading.value = true

    // 调用模拟API，传递正确的assignmentId和studentId
    fetch(`/api/submission-detail?assignmentId=${assignmentId.value}&studentId=${studentId.value}&type=${isExam.value ? 'exam' : 'homework'}`)
        .then(response => {
            if (!response.ok) {
                throw new Error('网络响应不正确')
            }
            return response.json()
        })
        .then(data => {
            if (data.code === 0) {
                // 设置作业信息，使用API返回的真实标题
                assignmentInfo.value = data.data.assignmentInfo

                // 其余部分保持不变
                studentInfo.value = data.data.studentInfo
                submissionInfo.value = data.data.submissionInfo
                questions.value = data.data.questions

                // 设置用于导航的学生ID列表
                studentIds.value = data.data.studentIds
                totalStudents.value = studentIds.value.length
                currentIndex.value = studentIds.value.indexOf(studentId.value)
            } else {
                MessagePlugin.error('获取数据失败')
            }
        })
        .catch(error => {
            console.error('获取提交详情失败:', error)
            MessagePlugin.error('获取数据失败')
        })
        .finally(() => {
            loading.value = false
        })
}

// 提交批阅
const submitGrading = () => {
    loading.value = true

    // 检查总分是否超过限制
    const calculatedTotal = questions.value.reduce((sum, q) => sum + (q.givenScore || 0), 0);
    if (calculatedTotal > assignmentInfo.value.totalScore) {
        MessagePlugin.error(`总分(${calculatedTotal})超过了限制(${assignmentInfo.value.totalScore})，请调整分数后再提交`);
        loading.value = false;
        return;
    }

    // 构建要提交的数据
    const gradingData = {
        assignmentId: assignmentId.value,
        studentId: studentId.value,
        type: isExam.value ? 'exam' : 'homework',
        questions: questions.value.map(q => ({
            id: q.id,
            givenScore: q.givenScore,
            teacherComment: q.teacherComment
        })),
        totalScore: calculatedTotal
    }

    // 调用保存API
    fetch('/api/save-grading', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(gradingData)
    })
        .then(response => response.json())
        .then(data => {
            if (data.code === 0) {
                // 更新提交状态
                submissionInfo.value.status = 'graded'
                submissionInfo.value.score = calculatedTotal

                // 标记所有手动题目为已批阅
                manualQuestions.value.forEach(q => {
                    if (q.givenScore !== null && q.givenScore !== undefined) {
                        q.graded = true
                    }
                })

                MessagePlugin.success('批阅已保存')
            } else {
                MessagePlugin.error('保存失败')
            }
            loading.value = false
        })
        .catch(error => {
            console.error('保存批阅失败:', error)
            MessagePlugin.error('保存失败')
            loading.value = false
        })
}

// 保存并批阅下一份
const saveAndNext = () => {
    submitGrading()

    // 等保存完成后再导航
    setTimeout(() => {
        if (hasNext.value) {
            router.push({
                path: `/teachers/grade-submission/${assignmentId.value}`,
                query: {
                    studentId: studentIds.value[currentIndex.value + 1],
                    type: isExam.value ? 'exam' : 'homework'
                }
            })
        } else {
            MessagePlugin.info('已是最后一份')
        }
    }, 800)
}

// 批量自动批阅
const autoGradeAll = () => {
    loading.value = true

    // 检查是否有未批阅的手动题目
    const hasUngraded = manualQuestions.value.some(q => !q.graded)

    if (hasUngraded) {
        MessagePlugin.warning('请先完成所有手动批阅题目')
        loading.value = false
        return
    }

    // 使用模拟API保存批阅
    submitGrading()
}

// 确保页面加载时获取数据
onMounted(() => {
    fetchSubmissionDetails()
})
</script>

<style scoped lang="less">
.grading-detail-container {
    padding: 20px;
}

.page-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;

    h2 {
        margin: 0 0 0 16px;
    }
}

.student-info-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: white;
    padding: 16px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.student-info {
    display: flex;
    align-items: center;
}

.student-avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 16px;

    img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
}

.student-details {
    .student-name {
        font-size: 18px;
        font-weight: 500;
        margin-bottom: 4px;
    }

    .student-meta {
        color: #666;
        font-size: 14px;

        span {
            margin-right: 16px;
        }
    }
}

.submission-info {
    text-align: right;

    .submission-time {
        color: #666;
        font-size: 14px;
        margin-bottom: 4px;
    }

    .status-info {
        font-size: 16px;

        .score-excellent {
            color: #00a870;
            font-weight: 600;
        }

        .score-pass {
            color: #0052d9;
            font-weight: 600;
        }

        .score-fail {
            color: #e34d59;
            font-weight: 600;
        }
    }
}

.navigation-bar {
    margin-bottom: 20px;
}

.student-nav {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;

    .student-counter {
        font-size: 14px;
        color: #666;
    }
}

.progress-bar {
    height: 4px;
    background-color: #e5e5e5;
    border-radius: 2px;
    overflow: hidden;

    .progress-indicator {
        height: 100%;
        background-color: #0052d9;
        transition: width 0.3s ease;
    }
}

.grading-tabs-container {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    padding: 16px;
}

.loading-state,
.empty-state {
    padding: 40px 0;
    text-align: center;
}

.questions-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.question-card {
    padding: 20px;
    border: 1px solid #eaeaea;

    .question-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;

        .question-info {
            display: flex;
            align-items: center;
            gap: 12px;

            .question-number {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 28px;
                height: 28px;
                background-color: #f0f0f0;
                border-radius: 50%;
                font-weight: 500;
            }

            .question-type {
                background-color: #e6f7ff;
                color: #1890ff;
                padding: 2px 8px;
                border-radius: 4px;
                font-size: 12px;
            }

            .question-score {
                color: #f56c6c;
                font-weight: 500;
            }
        }

        .grading-status {
            font-size: 14px;
            padding: 2px 8px;
            border-radius: 4px;

            &.status-graded {
                background-color: #f0fff9;
                color: #00a870;
            }

            &.status-ungraded {
                background-color: #fff2e8;
                color: #fa8c16;
            }
        }

        .auto-graded-badge {
            display: flex;
            align-items: center;
            gap: 8px;

            .auto-score {
                font-size: 14px;

                .score-full {
                    color: #00a870;
                    font-weight: 500;
                }

                .score-partial {
                    color: #fa8c16;
                    font-weight: 500;
                }
            }
        }
    }

    .question-content {
        font-size: 16px;
        margin-bottom: 20px;
    }

    .options-list {
        margin-bottom: 20px;
    }

    .option-item {
        padding: 8px 12px;
        margin-bottom: 8px;
        border-radius: 4px;
        display: flex;
        align-items: center;
        border: 1px solid #eaeaea;

        &.selected {
            background-color: rgba(0, 82, 217, 0.05);
            border-color: rgba(0, 82, 217, 0.1);
        }

        &.correct {
            background-color: rgba(0, 168, 112, 0.05);
            border-color: #00a870;
        }

        &.incorrect {
            background-color: rgba(227, 77, 89, 0.05);
            border-color: #e34d59;
        }
    }

    .option-label {
        font-weight: 500;
        margin-right: 8px;
        min-width: 24px;
    }

    .student-answer,
    .reference-answer {
        margin-bottom: 16px;

        .answer-label {
            font-weight: 500;
            margin-bottom: 8px;
        }

        .answer-content {
            padding: 12px;
            border-radius: 4px;
            background-color: #f5f5f5;
            white-space: pre-wrap;
        }
    }

    .grading-area {
        border-top: 1px solid #eaeaea;
        padding-top: 16px;
        margin-top: 16px;

        .score-input {
            display: flex;
            align-items: center;
            margin-bottom: 12px;

            .score-max {
                margin-left: 8px;
                color: #666;
            }
        }

        .comment-input {
            margin-bottom: 16px;
        }
    }
}

.all-questions-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.actions-bar {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #eaeaea;
}

@media (max-width: 768px) {
    .student-info-bar {
        flex-direction: column;
        align-items: flex-start;
    }

    .submission-info {
        margin-top: 16px;
        text-align: left;
    }

    .student-nav {
        flex-wrap: wrap;
        gap: 10px;
    }
}
</style>
