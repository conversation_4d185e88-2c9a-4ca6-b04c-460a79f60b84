# Vue 自定义指令使用说明

本文档详细说明了项目中自定义指令的使用方法。这些指令包括权限控制、节流、防抖和自动聚焦等功能。

## 1. 权限指令 (v-permissions)

用于控制元素的显示权限，如果用户没有指定权限，元素将被移除。

### 使用方式
```vue
<template>
  <!-- 单个权限 -->
  <button v-permissions="'admin'">管理员按钮</button>
  
  <!-- 多个权限（满足任一权限即可） -->
  <button v-permissions="['admin', 'editor']">编辑按钮</button>
</template>
```

### 说明
- 当用户没有指定权限时，元素会被自动从 DOM 中移除
- 权限值可以是字符串或字符串数组
- 需要配合 `hasPermission` 方法使用

## 2. 节流指令 (v-throttle)

用于限制事件触发的频率，在指定时间内只执行一次。

### 使用方式
```vue
<template>
  <button v-throttle="handleClick">节流按钮</button>
</template>

<script setup lang="ts">
const handleClick = () => {
  console.log('按钮被点击')
  // 你的业务逻辑
}
</script>
```

### 说明
- 默认节流时间为 2000ms（2秒）
- 适用于按钮点击、滚动等频繁触发的事件
- 在节流时间内重复触发事件，只会执行一次

## 3. 防抖指令 (v-debounce)

用于延迟执行事件，直到用户停止操作一段时间后才执行。

### 使用方式
```vue
<template>
  <button v-debounce="handleClick">防抖按钮</button>
</template>

<script setup lang="ts">
const handleClick = () => {
  console.log('按钮被点击')
  // 你的业务逻辑
}
</script>
```

### 说明
- 默认防抖时间为 1000ms（1秒）
- 适用于搜索输入、窗口调整等需要等待用户操作完成的场景
- 在防抖时间内重复触发事件，会重置计时器

## 4. 自动聚焦指令 (v-focus)

用于在元素挂载后自动聚焦到输入框。

### 使用方式
```vue
<template>
  <div v-focus>
    <input type="text" placeholder="自动聚焦的输入框" />
  </div>
</template>
```

### 说明
- 指令会自动查找容器内的第一个 input 元素并聚焦
- 适用于需要自动聚焦的搜索框、登录表单等场景

## 注意事项

1. 所有指令都需要在 main.ts 中正确注册后才能使用
2. 权限指令需要配合后端权限系统使用
3. 节流和防抖指令的时间间隔可以根据业务需求调整
4. 自动聚焦指令需要确保容器内有 input 元素

## 最佳实践

1. 权限控制
```vue
<template>
  <!-- 使用权限指令控制功能按钮的显示 -->
  <div class="action-buttons">
    <button v-permissions="'admin'" @click="handleAdminAction">管理员操作</button>
    <button v-permissions="['editor', 'admin']" @click="handleEditAction">编辑操作</button>
  </div>
</template>
```

2. 表单提交
```vue
<template>
  <form @submit.prevent="handleSubmit">
    <div v-focus>
      <input type="text" v-model="formData.username" placeholder="用户名" />
    </div>
    <button v-debounce="handleSubmit">提交</button>
  </form>
</template>
```

3. 列表操作
```vue
<template>
  <div class="list-actions">
    <button v-throttle="handleRefresh">刷新列表</button>
    <button v-permissions="'admin'" v-debounce="handleDelete">删除选中项</button>
  </div>
</template>
``` 