<template>
  <div class="teacher-course-class-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h2 class="page-title">班级管理</h2>
        <t-button theme="primary" @click="handleCreateClass">
          <template #icon>
            <t-icon name="add" />
          </template>
          新建班级
        </t-button>
      </div>
      
      <!-- 搜索和筛选区域 -->
      <div class="filter-section">
        <t-input 
          v-model="searchKeyword" 
          placeholder="搜索班级名称或教师姓名"
          clearable
          style="width: 300px;"
        >
          <template #prefix-icon>
            <t-icon name="search" />
          </template>
        </t-input>
        
        <t-select 
          v-model="filterType" 
          placeholder="班级类型"
          clearable
          style="width: 150px; margin-left: 16px;"
        >
          <t-option value="" label="全部类型" />
          <t-option value="0" label="教学计划班" />
          <t-option value="1" label="自建班级" />
        </t-select>
      </div>
    </div>

    <!-- 班级卡片列表 -->
    <div class="class-cards-container">
      <div class="cards-grid">
        <div 
          v-for="classItem in filteredClasses" 
          :key="classItem.id"
          class="class-card"
          @click="handleCardClick(classItem)"
        >
          <div class="card-header">
            <div class="class-name">{{ classItem.name }}</div>
            <t-tag 
              :theme="classItem.type === 0 ? 'primary' : 'success'"
              variant="light"
            >
              {{ classItem.type === 0 ? '教学计划班' : '自建班级' }}
            </t-tag>
          </div>
          
          <div class="card-content">
            <div class="info-item">
              <t-icon name="user" class="info-icon" />
              <span class="info-label">教师：</span>
              <span class="info-value">{{ classItem.teacherName }}</span>
            </div>
            
            <div class="info-item">
              <t-icon name="usergroup" class="info-icon" />
              <span class="info-label">学生人数：</span>
              <span class="info-value">{{ classItem.studentCount }}人</span>
            </div>
            
            <div class="info-item">
              <t-icon name="user-setting" class="info-icon" />
              <span class="info-label">助教：</span>
              <span class="info-value">{{ classItem.assistants.length > 0 ? classItem.assistants.join('、') : '暂无助教' }}</span>
            </div>
            
            <div class="info-item">
              <t-icon name="time" class="info-icon" />
              <span class="info-label">创建时间：</span>
              <span class="info-value">{{ formatDate(classItem.createTime) }}</span>
            </div>
          </div>
          
          <div class="card-footer">
            <t-button variant="text" theme="primary" size="small">
              管理学生
              <template #suffix>
                <t-icon name="chevron-right" />
              </template>
            </t-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 新建班级对话框 -->
    <t-dialog
      v-model:visible="createDialogVisible"
      title="新建班级"
      width="500px"
      @confirm="handleConfirmCreate"
    >
      <t-form ref="createFormRef" :model="newClassForm" :rules="formRules">
        <t-form-item label="班级名称" name="name">
          <t-input v-model="newClassForm.name" placeholder="请输入班级名称" />
        </t-form-item>
        
        <t-form-item label="所属专业" name="majorId">
          <t-select 
            v-model="newClassForm.majorId" 
            placeholder="请选择所属专业"
            :options="majorOptions"
          />
        </t-form-item>
        
        <t-form-item label="入学年份" name="entranceYear">
          <t-select 
            v-model="newClassForm.entranceYear" 
            placeholder="请选择入学年份"
            :options="yearOptions"
          />
        </t-form-item>
        
        <t-form-item label="班主任" name="headteacherId">
          <t-select 
            v-model="newClassForm.headteacherId" 
            placeholder="请选择班主任"
            :options="teacherOptions"
          />
        </t-form-item>
        
        <t-form-item label="助教" name="assistants">
          <t-textarea 
            v-model="assistantsText"
            placeholder="请输入助教姓名，多个助教用逗号分隔"
            :autosize="{ minRows: 2, maxRows: 4 }"
          />
        </t-form-item>
      </t-form>
    </t-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { MessagePlugin } from 'tdesign-vue-next'

// 定义班级数据接口
interface ClassInfo {
  id: string
  name: string
  type: 0 | 1 // 0: 教学计划班, 1: 自建班级
  studentCount: number
  teacherName: string
  headteacherId: string
  assistants: string[]
  courseId: string
  majorId: string
  majorName: string
  entranceYear: string
  createTime: string
}

// 新建班级表单接口
interface NewClassForm {
  name: string
  majorId: string
  entranceYear: string
  headteacherId: string
  type: 0 | 1
}

const router = useRouter()
const route = useRoute()

// 响应式数据
const searchKeyword = ref('')
const filterType = ref('')
const createDialogVisible = ref(false)
const createFormRef = ref()
const newClassForm = ref<NewClassForm>({
  name: '',
  majorId: '',
  entranceYear: new Date().getFullYear().toString(),
  headteacherId: '',
  type: 1
})
const assistantsText = ref('')

// 模拟选项数据
const majorOptions = ref([
  { value: '1', label: '软件工程' },
  { value: '2', label: '计算机科学与技术' },
  { value: '3', label: '数据科学与大数据技术' },
  { value: '4', label: '人工智能' }
])

const teacherOptions = ref([
  { value: '1', label: '张老师' },
  { value: '2', label: '李老师' },
  { value: '3', label: '王老师' },
  { value: '4', label: '陈老师' },
  { value: '5', label: '刘老师' }
])

const yearOptions = computed(() => {
  const currentYear = new Date().getFullYear()
  const years = []
  for (let i = currentYear; i >= currentYear - 10; i--) {
    years.push({ value: i.toString(), label: `${i}年` })
  }
  return years
})

// 表单验证规则
const formRules = {
  name: [{ required: true, message: '请输入班级名称', trigger: 'blur' as const }],
  majorId: [{ required: true, message: '请选择所属专业', trigger: 'change' as const }],
  entranceYear: [{ required: true, message: '请选择入学年份', trigger: 'change' as const }],
  headteacherId: [{ required: true, message: '请选择班主任', trigger: 'change' as const }]
}

// 模拟班级数据
const classList = ref<ClassInfo[]>([
  {
    id: '1',
    name: '软件工程2021级1班',
    type: 0,
    studentCount: 35,
    teacherName: '张老师',
    headteacherId: '1',
    assistants: ['李助教', '王助教'],
    courseId: route.params.courseId as string || '1',
    majorId: '1',
    majorName: '软件工程',
    entranceYear: '2021',
    createTime: '2023-09-01'
  },
  {
    id: '2',
    name: '软件工程2021级2班',
    type: 0,
    studentCount: 32,
    teacherName: '李老师',
    headteacherId: '2',
    assistants: ['刘助教'],
    courseId: route.params.courseId as string || '1',
    majorId: '1',
    majorName: '软件工程',
    entranceYear: '2021',
    createTime: '2023-09-01'
  },
  {
    id: '3',
    name: '特色实验班',
    type: 1,
    studentCount: 25,
    teacherName: '王老师',
    headteacherId: '3',
    assistants: ['陈助教', '赵助教', '孙助教'],
    courseId: route.params.courseId as string || '1',
    majorId: '2',
    majorName: '计算机科学与技术',
    entranceYear: '2022',
    createTime: '2024-01-15'
  },
  {
    id: '4',
    name: '自主学习班',
    type: 1,
    studentCount: 18,
    teacherName: '陈老师',
    headteacherId: '4',
    assistants: [],
    courseId: route.params.courseId as string || '1',
    majorId: '3',
    majorName: '数据科学与大数据技术',
    entranceYear: '2023',
    createTime: '2024-02-20'
  }
])

// 计算属性：过滤后的班级列表
const filteredClasses = computed(() => {
  let result = classList.value

  // 关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    result = result.filter(item => 
      item.name.toLowerCase().includes(keyword) || 
      item.teacherName.toLowerCase().includes(keyword)
    )
  }

  // 类型筛选
  if (filterType.value !== '') {
    result = result.filter(item => item.type === Number(filterType.value))
  }

  return result
})

// 方法
const handleCreateClass = () => {
  createDialogVisible.value = true
  // 重置表单
  newClassForm.value = {
    name: '',
    majorId: '',
    entranceYear: new Date().getFullYear().toString(),
    headteacherId: '',
    type: 1
  }
  assistantsText.value = ''
}

const handleConfirmCreate = async () => {
  try {
    await createFormRef.value?.validate()
    
    // 处理助教数据
    const assistants = assistantsText.value
      ? assistantsText.value.split(',').map(name => name.trim()).filter(name => name)
      : []
    
    // 获取选中的专业和教师名称
    const selectedMajor = majorOptions.value.find(m => m.value === newClassForm.value.majorId)
    const selectedTeacher = teacherOptions.value.find(t => t.value === newClassForm.value.headteacherId)
    
    // 创建新班级
    const newClass: ClassInfo = {
      id: Date.now().toString(),
      name: newClassForm.value.name,
      type: newClassForm.value.type,
      studentCount: 0,
      teacherName: selectedTeacher?.label || '',
      headteacherId: newClassForm.value.headteacherId,
      assistants,
      courseId: route.params.courseId as string || '1',
      majorId: newClassForm.value.majorId,
      majorName: selectedMajor?.label || '',
      entranceYear: newClassForm.value.entranceYear,
      createTime: new Date().toISOString().split('T')[0]
    }
    
    classList.value.push(newClass)
    createDialogVisible.value = false
    
    MessagePlugin.success('班级创建成功')
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

const handleCardClick = (classItem: ClassInfo) => {
  // 跳转到学生管理页面
  router.push(`/teacher/course/class/${classItem.courseId}/student/${classItem.id}`)
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

onMounted(() => {
  // 页面初始化逻辑
})
</script>

<style scoped lang="less">
.teacher-course-class-management {
  padding: 24px;
  background-color: var(--td-bg-color-page);
  min-height: calc(100vh - 64px);

  .page-header {
    background: var(--td-bg-color-container);
    border-radius: var(--td-radius-default);
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: var(--td-shadow-1);

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;

      .page-title {
        margin: 0;
        font-size: 20px;
        font-weight: 600;
        color: var(--td-text-color-primary);
      }
    }

    .filter-section {
      display: flex;
      align-items: center;
    }
  }

  .class-cards-container {
    .cards-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(360px, 1fr));
      gap: 24px;

      .class-card {
        background: var(--td-bg-color-container);
        border-radius: var(--td-radius-medium);
        padding: 24px;
        box-shadow: var(--td-shadow-1);
        cursor: pointer;
        transition: all 0.3s ease;
        border: 1px solid var(--td-border-level-1-color);

        &:hover {
          transform: translateY(-4px);
          box-shadow: var(--td-shadow-3);
          border-color: var(--td-brand-color);
        }

        .card-header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: 20px;

          .class-name {
            font-size: 18px;
            font-weight: 600;
            color: var(--td-text-color-primary);
            flex: 1;
            margin-right: 12px;
          }
        }

        .card-content {
          .info-item {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            font-size: 14px;

            .info-icon {
              color: var(--td-text-color-secondary);
              margin-right: 8px;
              font-size: 16px;
            }

            .info-label {
              color: var(--td-text-color-secondary);
              margin-right: 4px;
              min-width: fit-content;
            }

            .info-value {
              color: var(--td-text-color-primary);
              font-weight: 500;
            }
          }
        }

        .card-footer {
          margin-top: 20px;
          padding-top: 16px;
          border-top: 1px solid var(--td-border-level-1-color);
          display: flex;
          justify-content: flex-end;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .teacher-course-class-management {
    padding: 16px;

    .page-header {
      padding: 16px;

      .header-content {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
      }

      .filter-section {
        flex-direction: column;
        gap: 12px;
        align-items: stretch;

        .t-input,
        .t-select {
          width: 100% !important;
        }
      }
    }

    .class-cards-container .cards-grid {
      grid-template-columns: 1fr;
      gap: 16px;
    }
  }
}
</style> 