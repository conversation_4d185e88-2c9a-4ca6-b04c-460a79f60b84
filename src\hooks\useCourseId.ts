import { ref, watch, Ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

export type UseCourseIdOptions = {
  onCourseIdChange?: (courseId: string | undefined) => void;
  autoLoad?: boolean;
};

/**
 * 课程ID处理钩子函数
 * 统一处理课程负责人课程管理模块中的courseId参数获取和监听
 */
export function useCourseId(options: UseCourseIdOptions = {}) {
  const route = useRoute();
  const router = useRouter();
  
  const { onCourseIdChange, autoLoad = true } = options;
  
  // 从路由参数或查询参数中获取courseId
  const courseId: Ref<string | undefined> = ref(
    (route.params.courseId as string) || (route.query.courseId as string)
  );
  
  // 加载状态
  const loading = ref(false);
  
  // 监听路由参数变化
  watch(
    () => [route.params.courseId, route.query.courseId], 
    ([newParamId, newQueryId]) => {
      const newId = (newParamId || newQueryId) as string | undefined;
      
      // 只有当ID实际变化时才触发变更
      if (newId !== courseId.value) {
        courseId.value = newId;
        
        if (courseId.value && onCourseIdChange && autoLoad) {
          onCourseIdChange(courseId.value);
        }
      }
    }
  );
  
  return {
    courseId,
    loading,
    route,
    router
  };
}
