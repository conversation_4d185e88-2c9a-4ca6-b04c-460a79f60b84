export interface GoalInfo {
    courseId: number;
    graduateName:string;//毕业目标名称
    graduateId:number;//毕业目标ID
    IndicatorPoint: string;//二级指标点名称
    IndicatorPointId: number;//二级指标点ID
    courseObjectives:string;
    courseObjectivesId:number
}



export interface basicInformationInfo {
  courseCode: string;
  /**
   * 课程名称
   */
  courseName: string;
  /**
   * 课程性质
   */
  courseNature: string;
  /**
   * 学分
   */
  credits: number;
  /**
   * 课程简介
   */
  introduction: string;
  /**
   * 大纲版本
   */
  outline: string;
  /**
   * 实践学时
   */
  practicalHours: number;
  /**
   * 专业
   */
  professional: string;
  /**
   * 学期
   */
  semester: string;
  /**
   * 教学内容
   */
  teachingContent: string;
  /**
   * 理论学时
   */
  theoryHours: number;
  /**
   * 总学时
   */
  totalHours: number;
}
export interface basicSubmitFromInfo {
  //课程id
  courseId:number;
  /**
   * 课程简介
   */
  introduction: string;
  /**
   * 大纲版本
   */
  outline: string;
  /**
   * 教学内容
   */
  teachingContent: string;
}


// teachingSyllabus.ts 或相关类型文件
export interface AssessmentMethodResponse {
    headers: string[];
    items: AssessmentItem[];
}

export interface AssessmentItem {
    id?: number;
    assessmentMethod: string;
    overallScores: number;
    [key: string]: any; // 动态字段
    editable?: boolean;
    isOriginal?: boolean;
    rowIndex?: number;
}
