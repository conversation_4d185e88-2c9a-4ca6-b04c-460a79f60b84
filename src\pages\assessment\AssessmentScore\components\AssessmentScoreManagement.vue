<template>
  <div class="assessment-score-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <!-- 返回按钮 -->
        <div class="header-navigation">
          <t-button
            theme="default"
            variant="text"
            @click="handleBackToTaskManagement"
            class="back-button"
          >
            <template #icon><t-icon name="arrow-left" /></template>
            返回教学任务管理
          </t-button>
        </div>

        <div class="title-section">
          <h2 class="page-title">
            <t-icon name="assignment" size="20px" />
            成绩管理 - {{ assessmentInfo.assessmentName }}
          </h2>
          <div class="assessment-info">
            <t-tag theme="primary" variant="light">{{ assessmentInfo.courseName }}</t-tag>
            <t-tag theme="default" variant="light">{{ assessmentInfo.academicYear }}</t-tag>
            <t-tag theme="default" variant="light">{{ assessmentInfo.semester }}</t-tag>
          </div>
        </div>
      </div>
    </div>

    <!-- 考核基本信息展示区域 -->
    <div class="assessment-info-section">
      <t-card class="assessment-info-card" :loading="assessmentDetailLoading">
        <template #header>
          <div class="card-header">
            <h3>
              <t-icon name="info-circle" />
              考核基本信息
            </h3>
            <div class="header-actions">
              <t-button
                v-if="assessmentDetail"
                theme="primary"
                variant="outline"
                size="small"
                @click="showDetailConfigDialog"
              >
                <template #icon>
                  <t-icon name="view-list" />
                </template>
                查看目标配置
              </t-button>
            </div>
          </div>
        </template>

        <div v-if="assessmentDetail" class="assessment-detail-content">
          <!-- 基本信息网格 -->
          <div class="basic-info-grid">
            <div class="info-item">
              <label class="info-label">考核名称：</label>
              <span class="info-value">{{ assessmentDetail.assessmentName || '未设置' }}</span>
            </div>
            <div class="info-item">
              <label class="info-label">考核年份：</label>
              <span class="info-value">{{ assessmentDetail.assessmentYear || '未设置' }}</span>
            </div>
            <div class="info-item">
              <label class="info-label">考核学期：</label>
              <span class="info-value">{{ getSemesterLabel(assessmentDetail.assessmentTerm) }}</span>
            </div>
            <div class="info-item">
              <label class="info-label">录入方式：</label>
              <span class="info-value">
                <t-tag :theme="assessmentDetail.scoreType === 0 ? 'success' : 'primary'" variant="light">
                  {{ assessmentDetail.scoreType === 0 ? '直接录入' : '详细录入' }}
                </t-tag>
              </span>
            </div>
          </div>


        </div>

        <div v-else-if="!assessmentDetailLoading" class="no-data">
          <t-icon name="info-circle" size="48px" />
          <p>暂无考核详情数据</p>
        </div>
      </t-card>
    </div>

    <!-- 教学任务列表视图 -->
    <div v-if="!selectedTask" class="task-list-container">
      <t-card class="task-list-card">
        <template #header>
          <div class="card-header">
            <h3>教学任务列表</h3>
            <div class="header-actions">
              <t-button theme="primary" @click="refreshTaskList">
                <template #icon><t-icon name="refresh" /></template>
                刷新
              </t-button>
            </div>
          </div>
        </template>

        <div class="filter-section">
          <t-form :data="filterForm" layout="inline" @submit="handleFilter">
            <t-form-item label="录入状态" name="status">
              <t-select 
                v-model="filterForm.status" 
                placeholder="请选择录入状态"
                clearable
                style="width: 150px"
              >
                <t-option value="" label="全部状态" />
                <t-option value="not_started" label="未开始" />
                <t-option value="in_progress" label="录入中" />
                <t-option value="submitted" label="已提交" />
              </t-select>
            </t-form-item>
            <t-form-item label="班级" name="classId">
              <t-select 
                v-model="filterForm.classId" 
                placeholder="请选择班级"
                clearable
                style="width: 200px"
              >
                <t-option value="" label="全部班级" />
                <t-option 
                  v-for="cls in classList" 
                  :key="cls.classId" 
                  :value="cls.classId" 
                  :label="cls.className" 
                />
              </t-select>
            </t-form-item>
            <t-form-item>
              <t-button theme="primary" type="submit">
                <template #icon><t-icon name="search" /></template>
                查询
              </t-button>
              <t-button theme="default" @click="resetFilter">
                <template #icon><t-icon name="refresh" /></template>
                重置
              </t-button>
            </t-form-item>
          </t-form>
        </div>

        <t-table
          :data="filteredTaskList"
          :columns="taskColumns"
          :loading="taskListLoading"
          row-key="taskId"
          bordered
          stripe
          hover
          :pagination="taskPagination"
          @page-change="handleTaskPageChange"
        >
          <template #status="{ row }">
            <t-tag 
              :theme="getStatusTheme(row.status)" 
              :variant="getStatusVariant(row.status)"
            >
              {{ getStatusText(row.status) }}
            </t-tag>
          </template>
          
          <template #progress="{ row }">
            <div class="progress-info">
              <span class="progress-text">{{ row.enteredCount }}/{{ row.totalCount }}</span>
              <t-progress
                :percentage="getProgressPercentage(row)"
                size="small"
                style="margin-top: 4px; width: 100px;"
              />
            </div>
          </template>

          <template #action="{ row }">
            <t-space>
              <t-button 
                theme="primary" 
                variant="text" 
                size="small"
                @click="enterScoreManagement(row)"
              >
                <template #icon><t-icon name="edit" /></template>
                目标成绩管理
              </t-button>
              <t-button 
                theme="default" 
                variant="text" 
                size="small"
                @click="viewTaskDetail(row)"
              >
                <template #icon><t-icon name="view" /></template>
                查看详情
              </t-button>
            </t-space>
          </template>
        </t-table>
      </t-card>
    </div>

    <!-- 成绩管理详情页面 -->
    <div v-else class="score-detail-container">
      <!-- 返回按钮和任务信息 -->
      <div class="detail-header">
        <t-button 
          theme="default" 
          variant="text" 
          @click="backToTaskList"
          class="back-button"
        >
          <template #icon><t-icon name="arrow-left" /></template>
          返回任务列表
        </t-button>
        
        <div class="task-info">
          <h3>{{ selectedTask.taskName }}</h3>
          <div class="task-meta">
            <t-tag theme="primary" variant="light">{{ selectedTask.className }}</t-tag>
            <t-tag theme="default" variant="light">总人数: {{ selectedTask.totalCount }}</t-tag>
            <t-tag 
              :theme="getStatusTheme(selectedTask.status)" 
              :variant="getStatusVariant(selectedTask.status)"
            >
              {{ getStatusText(selectedTask.status) }}
            </t-tag>
          </div>
        </div>
      </div>

      <!-- 操作按钮区域 -->
      <div class="action-section">
        <t-card>
          <div class="action-buttons">
            <t-button 
              theme="primary" 
              @click="showImportDialog = true"
              :disabled="selectedTask.status === 'submitted'"
            >
              <template #icon><t-icon name="upload" /></template>
              导入成绩
            </t-button>
            <t-button 
              theme="success" 
              @click="saveAllScores"
              :loading="saveLoading"
              :disabled="!hasUnsavedChanges || selectedTask.status === 'submitted'"
            >
              <template #icon><t-icon name="save" /></template>
              保存修改 {{ hasUnsavedChanges ? `(${Object.keys(unsavedScores).length})` : '' }}
            </t-button>
            <t-button 
              theme="default" 
              @click="exportScoreTemplate"
            >
              <template #icon><t-icon name="download" /></template>
              下载模板
            </t-button>
            <t-button 
              theme="warning" 
              @click="submitScores"
              :disabled="selectedTask.status === 'submitted' || hasUnsavedChanges"
            >
              <template #icon><t-icon name="check" /></template>
              提交成绩
            </t-button>
          </div>
        </t-card>
      </div>

      <!-- 学生成绩表格 -->
      <div class="score-table-container">
        <t-card>
          <template #header>
            <div class="table-header">
              <h4>学生成绩录入</h4>
              <div class="table-info">
                <span>共 {{ studentList.length }} 名学生</span>
                <span v-if="hasUnsavedChanges" class="unsaved-tip">
                  <t-icon name="info-circle" />
                  有未保存的修改
                </span>
              </div>
            </div>
          </template>

          <t-table
            :data="studentList"
            :columns="scoreColumns"
            :loading="scoreTableLoading"
            row-key="studentId"
            bordered
            stripe
            hover
            size="small"
            max-height="600px"
          >
            <!-- 动态生成课程目标成绩列的插槽 -->
            <template
              v-for="objective in courseObjectives"
              :key="`slot_${objective.number}`"
              #[`objective_${objective.number}`]="{ row }"
            >
              <div class="score-cell-container">
                <!-- 编辑模式 -->
                <input
                  v-if="editingCell === `${row.studentId}_${objective.number}`"
                  v-model="tempInputValue"
                  type="number"
                  :min="0"
                  :max="100"
                  placeholder="0-100"
                  class="score-input native-input"
                  autofocus
                  @blur="safeHandleScoreBlur(row.studentId, objective.number)"
                  @keyup="handleKeyboardEvent($event, row.studentId, objective.number)"
                />
                <!-- 显示模式 -->
                <div
                  v-else
                  :class="getScoreCellDisplayClass(row.studentId, objective.number)"
                  @click="startEdit(row.studentId, objective.number, getDisplayScore(row, objective.number))"
                >
                  {{ getDisplayScore(row, objective.number) || '-' }}
                </div>
              </div>
            </template>

            <template #totalScore="{ row }">
              <div class="total-score" :class="getTotalScoreClass(row.totalScore)">
                {{ row.totalScore || 0 }}
              </div>
            </template>
          </t-table>
        </t-card>
      </div>
    </div>

    <!-- 导入成绩对话框 -->
    <t-dialog
      v-model:visible="showImportDialog"
      title="导入成绩"
      width="500px"
      :confirm-btn="null"
      :cancel-btn="null"
    >
      <div class="import-dialog-content">
        <t-upload
          v-model="uploadFiles"
          theme="file-input"
          accept=".xlsx,.xls"
          :multiple="false"
          :auto-upload="false"
          @change="handleFileChange"
        >
          <template #file-input>
            <t-button theme="primary" variant="outline">
              <template #icon><t-icon name="upload" /></template>
              选择Excel文件
            </t-button>
          </template>
        </t-upload>
        
        <div class="import-tips">
          <h5>导入说明：</h5>
          <ul>
            <li>支持 .xlsx 和 .xls 格式文件</li>
            <li>第一行为表头：学号、姓名、课程目标1、课程目标2...</li>
            <li>学号必须与系统中的学生学号匹配</li>
            <li>成绩范围：0-100</li>
          </ul>
        </div>
      </div>
      
      <template #footer>
        <t-space>
          <t-button theme="default" @click="showImportDialog = false">取消</t-button>
          <t-button 
            theme="primary" 
            @click="confirmImport"
            :loading="importLoading"
            :disabled="!uploadFiles.length"
          >
            确认导入
          </t-button>
        </t-space>
      </template>
    </t-dialog>

    <!-- 课程目标分值详细配置对话框 -->
    <t-dialog
      v-model:visible="detailConfigDialogVisible"
      header="课程目标分值详细配置"
      width="900px"
      :footer="false"
      destroy-on-close
    >
      <div v-if="assessmentDetail?.assessmentDetailList" class="detail-config-content">
        <!-- 配置概览 -->
        <div class="config-overview">
          <div class="overview-item">
            <span class="label">考核名称：</span>
            <span class="value">{{ assessmentDetail.assessmentName }}</span>
          </div>
          <div class="overview-item">
            <span class="label">录入方式：</span>
            <t-tag :theme="assessmentDetail.scoreType === 0 ? 'success' : 'primary'" variant="light">
              {{ assessmentDetail.scoreType === 0 ? '直接录入' : '详细录入' }}
            </t-tag>
          </div>
          <div class="overview-item">
            <span class="label">总分值：</span>
            <span class="value total-score">
              {{ assessmentDetail.assessmentDetailList.reduce((sum, item) => sum + (item.totalScore || 0), 0) }} 分
            </span>
          </div>
        </div>

        <!-- 详细配置表格 -->
        <div class="detail-config-table">
          <t-table
            :data="sortedDetailList"
            :columns="detailConfigColumns"
            row-key="courseObjectiveId"
            size="medium"
            :pagination="{ pageSize: 20 }"
            stripe
            hover
          />
        </div>
      </div>

      <div v-else class="no-config-data">
        <t-icon name="info-circle" size="48px" />
        <p>暂无课程目标配置数据</p>
      </div>
    </t-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, reactive, watch, h, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { MessagePlugin, DialogPlugin } from 'tdesign-vue-next'
import * as XLSX from 'xlsx'

// API 导入
import { getCourseTargetList, type CourseObjectiveVO } from '@/api/training/course'
import { getAssessmentRelatedTasks, getAssessmentTaskScores, exportTargetScoreTemplate } from '@/api/assessment/assessmentScore'
import { getAssessmentById, type AssessmentInfo as AssessmentDetailInfo } from '@/api/assessment/assessment'
import { getClassOptions } from '@/api/base/classes'
// 单独导入批量保存函数
import * as assessmentScoreAPI from '@/api/assessment/assessmentScore'

// 定义班级选项接口
interface ClassOption {
  classId: number
  className: string
  value?: string | number
  label?: string
}

// 批量保存成绩数据结构
interface BatchSaveTargetScoresDTO {
  assessmentId: number        // 当前考核ID
  taskId: number             // 当前教学任务ID
  studentScores: StudentTargetScoreDTO[]
}

interface StudentTargetScoreDTO {
  studentId: number          // 学生ID（Long类型）
  courseTargetNo: number     // 课程目标编号（Integer类型）
  repositoryAnswerId: number // 题目答案ID（Long类型）
  score: number              // 学生得分（BigDecimal类型）
  remark?: string            // 备注（可选）
}

// 后端数据结构定义
interface AssessmentTaskDetailVO {
  taskId: number
  taskName: string
  classes: {
    classId: number
    className: string
    studentNumber?: number
  }[]
  scoreStatistics: {
    totalStudentCount: number
    scoredStudentCount: number
    unscoredStudentCount: number
  }
  submissionStatus: 'NOT_STARTED' | 'IN_PROGRESS' | 'COMPLETED'
}

// 成绩提交状态枚举
enum ScoreSubmissionStatus {
  NOT_STARTED = 'NOT_STARTED',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED'
}

// 录入状态枚举
enum EntryStatus {
  NOT_ENTERED = 'NOT_ENTERED',
  PARTIALLY_ENTERED = 'PARTIALLY_ENTERED',
  FULLY_ENTERED = 'FULLY_ENTERED',
  SUBMITTED = 'SUBMITTED'
}

// 后端学生成绩详情数据结构
interface StudentScoreDetailVO {
  studentId: string
  studentNumber: string
  studentName: string
  classId: number
  className: string
  totalScore?: number
  fullScore?: number
  scoreRate?: number
  scoreGrade?: string
  courseTargetScores: {
    courseTargetNo: number      // 后端使用 courseTargetNo 而不是 targetId
    courseTargetName: string    // 后端使用 courseTargetName 而不是 targetName
    score: number
    fullScore: number
    scoreRate?: number
    poId?: number
    poName?: string
    weight?: number
  }[]
  detailScores: {
    itemId: string
    itemName: string
    score: number
    fullScore: number
  }[]
  entryStatus: 'NOT_ENTERED' | 'PARTIALLY_ENTERED' | 'FULLY_ENTERED' | 'SUBMITTED'
  entryTime?: string
  lastModifyTime?: string
  entryUserId?: string
  entryUserName?: string
}

// 接口定义
interface AssessmentInfo {
  assessmentId: number
  assessmentName: string
  courseId: number
  courseName: string
  academicYear: string
  semester: string
}

interface TaskItem {
  taskId: number
  taskName: string
  className: string
  classId: number
  totalCount: number
  enteredCount: number
  notEnteredCount: number
  status: 'not_started' | 'in_progress' | 'submitted'
}

interface StudentItem {
  studentId: string
  studentNumber?: string
  studentName: string
  classId: number
  className: string
  totalScore: number
  fullScore?: number
  scoreRate?: number
  scoreGrade?: string
  entryStatus?: 'NOT_ENTERED' | 'PARTIALLY_ENTERED' | 'FULLY_ENTERED' | 'SUBMITTED'
  entryTime?: string
  lastModifyTime?: string
  entryUserId?: string
  entryUserName?: string
  [key: string]: any // 动态课程目标分数
}

interface ScoreCell {
  studentId: string
  objectiveId: string
  score: number
  isModified: boolean
  remark?: string // 可选备注
}

// Props (可选，支持通过 props 或路由参数传递)
interface Props {
  assessmentId?: number
  courseId?: number
  courseName?: string
  year?: string
  term?: string
}

const props = defineProps<Props>()
const route = useRoute()
const router = useRouter()

// 从路由参数或 props 中获取数据
const assessmentId = computed(() => {
  return props.assessmentId || Number(route.params.assessmentId)
})

const courseId = computed(() => {
  return props.courseId || Number(route.params.courseId)
})

const courseName = computed(() => {
  return props.courseName || (route.query.courseName as string) || ''
})
const assessmentYear = computed(() => {
  return props.year || (route.query.year as string) || ''
})
const assessmentTerm = computed(() => {
  return props.term || (route.query.term as string) || ''
})

// 响应式数据
const assessmentInfo = ref<AssessmentInfo>({
  assessmentId: assessmentId.value,
  assessmentName: '期末考试',
  courseId: courseId.value,
  courseName: courseName.value || '高等数学',
  academicYear: '2024-2025',
  semester: '秋季学期'
})

const selectedTask = ref<TaskItem | null>(null)
const taskList = ref<TaskItem[]>([])
const filteredTaskList = ref<TaskItem[]>([])
const classList = ref<ClassOption[]>([])
const courseObjectives = ref<CourseObjectiveVO[]>([])
const studentList = ref<StudentItem[]>([])
const unsavedScores = ref<Record<string, ScoreCell>>({}) // key: studentId_objectiveId

// 考核详情数据
const assessmentDetail = ref<AssessmentDetailInfo | null>(null)

// 详细配置对话框状态
const detailConfigDialogVisible = ref(false)

// 加载状态
const taskListLoading = ref(false)
const scoreTableLoading = ref(false)
const saveLoading = ref(false)
const importLoading = ref(false)
const assessmentDetailLoading = ref(false)

// 对话框状态
const showImportDialog = ref(false)
const uploadFiles = ref<File[]>([])

// 编辑状态管理
const editingCell = ref<string | null>(null) // 当前编辑的单元格 key
const editingValue = ref<number | null>(null) // 当前编辑的值
const tempInputValue = ref<string>('') // 临时输入值

// TDesign 可编辑表格状态
const editableCellState = ref<Record<string, any>>({})

// 筛选表单
const filterForm = reactive({
  status: '',
  classId: ''
})

// 分页
const taskPagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showJumper: true,
  showSizer: true
})



// 获取学期标签
const getSemesterLabel = (term: number | string) => {
  const termNum = Number(term)
  switch (termNum) {
    case 1:
      return '春季学期'
    case 2:
      return '秋季学期'
    default:
      return `第${term}学期`
  }
}

// 显示详细配置对话框
const showDetailConfigDialog = () => {
  if (!assessmentDetail.value?.assessmentDetailList || assessmentDetail.value.assessmentDetailList.length === 0) {
    MessagePlugin.warning('暂无课程目标配置数据')
    return
  }
  detailConfigDialogVisible.value = true
}

// 排序后的详细配置列表
const sortedDetailList = computed(() => {
  if (!assessmentDetail.value?.assessmentDetailList) return []

  return [...assessmentDetail.value.assessmentDetailList].sort((a, b) => {
    // 按课程目标编号排序
    const aNum = extractObjectiveNumber(a.identifier || a.courseObjectiveId)
    const bNum = extractObjectiveNumber(b.identifier || b.courseObjectiveId)
    return aNum - bNum
  })
})

// 从标识符中提取目标编号
const extractObjectiveNumber = (identifier: string): number => {
  const match = identifier.match(/(\d+)/)
  return match ? parseInt(match[1]) : 0
}

// 详细配置表格列定义
const detailConfigColumns = [
  {
    colKey: 'identifier',
    title: '目标编号',
    width: 120,
    align: 'center' as const,
    cell: (h: any, { row }: any) => {
      const identifier = row.identifier || row.courseObjectiveId
      return h('t-tag', { theme: 'primary', variant: 'light' }, identifier)
    }
  },
  {
    colKey: 'objectiveName',
    title: '目标名称',
    width: 200,
    ellipsis: true,
    cell: (h: any, { row }: any) => {
      // 从课程目标列表中查找对应的名称
      const objective = courseObjectives.value.find(obj => obj.objectiveId === row.courseObjectiveId)
      return objective?.objectiveName || `课程目标${extractObjectiveNumber(row.courseObjectiveId)}`
    }
  },
  {
    colKey: 'description',
    title: '目标描述',
    ellipsis: true,
    cell: (h: any, { row }: any) => {
      // 优先使用配置中的描述，其次使用课程目标中的描述
      if (row.description) return row.description
      const objective = courseObjectives.value.find(obj => obj.objectiveId === row.courseObjectiveId)
      return objective?.description || '暂无描述'
    }
  },
  {
    colKey: 'totalScore',
    title: '分值',
    width: 100,
    align: 'center' as const,
    cell: (h: any, { row }: any) => {
      const score = row.totalScore || 0
      return h('span', { class: 'score-value' }, `${score} 分`)
    }
  },
  {
    colKey: 'percentage',
    title: '占比',
    width: 100,
    align: 'center' as const,
    cell: (h: any, { row }: any) => {
      if (!assessmentDetail.value?.assessmentDetailList) return '0%'
      const total = assessmentDetail.value.assessmentDetailList.reduce((sum, item) => sum + (item.totalScore || 0), 0)
      const percentage = total > 0 ? ((row.totalScore || 0) / total * 100).toFixed(1) : '0'
      const theme = parseFloat(percentage) >= 20 ? 'success' : parseFloat(percentage) >= 10 ? 'warning' : 'default'
      return h('t-tag', { theme, variant: 'light' }, `${percentage}%`)
    }
  },
  {
    colKey: 'weight',
    title: '权重系数',
    width: 100,
    align: 'center' as const,
    cell: (h: any, { row }: any) => {
      // 如果有权重字段则显示，否则显示默认值
      const weight = row.weight || row.percentage || 1
      return h('span', { class: 'weight-value' }, weight.toFixed(2))
    }
  }
]

// 计算属性
const hasUnsavedChanges = computed(() => {
  return Object.keys(unsavedScores.value).length > 0
})

// 教学任务表格列定义
const taskColumns = [
  { colKey: 'taskName', title: '教学任务名称', width: 200 },
  { colKey: 'className', title: '班级信息', width: 150 },
  { colKey: 'totalCount', title: '总人数', width: 100, align: 'center' as const },
  { colKey: 'enteredCount', title: '已录入', width: 100, align: 'center' as const },
  { colKey: 'notEnteredCount', title: '未录入', width: 100, align: 'center' as const },
  { colKey: 'status', title: '录入状态', width: 120, align: 'center' as const },
  { colKey: 'progress', title: '录入进度', width: 150, align: 'center' as const },
  { colKey: 'action', title: '操作', width: 200, align: 'center' as const, fixed: 'right' as const }
]

// 学生成绩表格列定义
const scoreColumns = computed(() => {
  const baseColumns = [
    {
      colKey: 'studentNumber',
      title: '学生学号',
      width: 120,
      fixed: 'left' as const
    },
    {
      colKey: 'studentName',
      title: '学生姓名',
      width: 100,
      fixed: 'left' as const
    }
  ]

  // 动态添加课程目标列
  const objectiveColumns = courseObjectives.value.map((objective, index) => ({
    colKey: `objective_${objective.number}`,  // 使用 number 而不是 objectiveId
    title: `目标${objective.number}`,
    width: 100,
    align: 'center' as const
  }))

  const totalColumn = {
    colKey: 'totalScore',
    title: '总成绩',
    width: 100,
    align: 'center' as const,
    fixed: 'right' as const
  }

  return [...baseColumns, ...objectiveColumns, totalColumn]
})

// 状态相关方法
const getStatusTheme = (status: string): 'default' | 'warning' | 'success' | 'primary' | 'danger' => {
  const themeMap: Record<string, 'default' | 'warning' | 'success' | 'primary' | 'danger'> = {
    'not_started': 'default',
    'in_progress': 'warning',
    'submitted': 'success'
  }
  return themeMap[status] || 'default'
}

const getStatusVariant = (status: string): 'dark' | 'light' | 'outline' | 'light-outline' => {
  return status === 'submitted' ? 'dark' : 'light'
}

const getStatusText = (status: string) => {
  const textMap = {
    'not_started': '未开始',
    'in_progress': '录入中',
    'submitted': '已提交'
  }
  return textMap[status as keyof typeof textMap] || '未知'
}

const getProgressPercentage = (task: TaskItem) => {
  if (task.totalCount === 0) return 0
  return Math.round((task.enteredCount / task.totalCount) * 100)
}

const getProgressTheme = (task: TaskItem): 'success' | 'warning' | 'default' => {
  const percentage = getProgressPercentage(task)
  if (percentage === 100) return 'success'
  if (percentage >= 50) return 'warning'
  return 'default'
}

const getTotalScoreClass = (score: number) => {
  if (score >= 90) return 'score-excellent'
  if (score >= 80) return 'score-good'
  if (score >= 60) return 'score-pass'
  return 'score-fail'
}

// 状态转换方法
const convertSubmissionStatus = (status: string): 'not_started' | 'in_progress' | 'submitted' => {
  switch (status) {
    case ScoreSubmissionStatus.NOT_STARTED:
      return 'not_started'
    case ScoreSubmissionStatus.IN_PROGRESS:
      return 'in_progress'
    case ScoreSubmissionStatus.COMPLETED:
      return 'submitted'
    default:
      console.warn('未知的提交状态:', status)
      return 'not_started'
  }
}

// 学生成绩数据转换方法
const convertStudentScoreToStudentItem = (studentScore: StudentScoreDetailVO): StudentItem => {
  try {
    const student: StudentItem = {
      studentId: studentScore.studentId,
      studentNumber: studentScore.studentNumber,
      studentName: studentScore.studentName || '未知学生',
      classId: studentScore.classId || 0,
      className: studentScore.className || '未知班级',
      totalScore: studentScore.totalScore || 0,
      fullScore: studentScore.fullScore,
      scoreRate: studentScore.scoreRate,
      scoreGrade: studentScore.scoreGrade,
      entryStatus: studentScore.entryStatus,
      entryTime: studentScore.entryTime,
      lastModifyTime: studentScore.lastModifyTime,
      entryUserId: studentScore.entryUserId,
      entryUserName: studentScore.entryUserName
    }

    // 处理课程目标成绩
    console.log(`学生 ${studentScore.studentName} 的课程目标成绩:`, studentScore.courseTargetScores)

    if (studentScore.courseTargetScores && Array.isArray(studentScore.courseTargetScores)) {
      console.log(`开始处理学生 ${studentScore.studentName} 的 ${studentScore.courseTargetScores.length} 个课程目标成绩`)

      studentScore.courseTargetScores.forEach((targetScore, index) => {
        console.log(`处理第 ${index + 1} 个课程目标:`, targetScore)

        // 后端使用 courseTargetNo 而不是 targetId
        if (targetScore.courseTargetNo) {
          const key = `objective_${targetScore.courseTargetNo}`
          student[key] = targetScore.score || 0
          console.log(`设置 ${key} = ${student[key]}`)
        } else {
          console.warn('课程目标缺少 courseTargetNo:', targetScore)
        }
      })
    } else {
      console.warn(`学生 ${studentScore.studentName} 没有课程目标成绩数据或数据格式错误`)
    }

    // 处理考核详情成绩
    if (studentScore.detailScores && Array.isArray(studentScore.detailScores)) {
      studentScore.detailScores.forEach(detailScore => {
        if (detailScore.itemId) {
          student[`detail_${detailScore.itemId}`] = detailScore.score || 0
        }
      })
    }

    return student
  } catch (error) {
    console.error('转换学生成绩数据失败:', error)
    // 返回默认数据结构，避免页面崩溃
    return {
      studentId: studentScore.studentId || '',
      studentName: studentScore.studentName || '数据异常',
      classId: studentScore.classId || 0,
      className: studentScore.className || '数据异常',
      totalScore: 0,
      entryStatus: 'NOT_ENTERED'
    }
  }
}

// 数据转换方法
const convertAssessmentTaskToTaskItem = (task: AssessmentTaskDetailVO): TaskItem => {
  try {
    // 处理班级信息
    const classNames = task.classes?.map(cls => cls.className).join(', ') || '未知班级'
    const firstClass = task.classes?.[0]

    // 数据验证和转换
    const totalCount = task.scoreStatistics?.totalStudentCount || 0
    const enteredCount = task.scoreStatistics?.scoredStudentCount || 0
    const notEnteredCount = task.scoreStatistics?.unscoredStudentCount || 0

    // 验证数据一致性
    if (totalCount !== enteredCount + notEnteredCount) {
      console.warn('成绩统计数据不一致:', task.taskId)
    }

    return {
      taskId: task.taskId,
      taskName: task.taskName || '未知任务',
      className: classNames,
      classId: firstClass?.classId || 0,
      totalCount,
      enteredCount,
      notEnteredCount,
      status: convertSubmissionStatus(task.submissionStatus)
    }
  } catch (error) {
    console.error('转换教学任务数据失败:', error)
    // 返回默认数据结构，避免页面崩溃
    return {
      taskId: task.taskId || 0,
      taskName: task.taskName || '数据异常',
      className: '数据异常',
      classId: 0,
      totalCount: 0,
      enteredCount: 0,
      notEnteredCount: 0,
      status: 'not_started'
    }
  }
}

// 数据加载方法
const loadTaskList = async () => {
  try {
    taskListLoading.value = true
    const response = await getAssessmentRelatedTasks(assessmentId.value)

    if (response?.code === 200) {
      const taskDetailList: AssessmentTaskDetailVO[] = response.data || []

      if (!Array.isArray(taskDetailList)) {
        MessagePlugin.error('教学任务数据格式错误')
        taskList.value = []
        return
      }

      // 转换数据格式
      taskList.value = taskDetailList.map(convertAssessmentTaskToTaskItem)

      // 应用筛选（会自动更新分页信息）
      applyFilter()

      MessagePlugin.success(`成功加载 ${taskList.value.length} 个教学任务`)
    } else {
      MessagePlugin.error(response?.message || '加载教学任务列表失败')
      taskList.value = []
    }
  } catch (error) {
    console.error('加载教学任务列表失败:', error)
    MessagePlugin.error('加载教学任务列表失败，请稍后重试')
    // 应用筛选（会自动更新分页信息）
    applyFilter()
  } finally {
    taskListLoading.value = false
  }
}

const loadCourseObjectives = async () => {
  try {
    console.log('开始加载课程目标，courseId:', courseId.value)
    const objectives = await getCourseTargetList(courseId.value)
    console.log('加载到的课程目标:', objectives)
    courseObjectives.value = objectives

    // 输出课程目标的ID信息
    if (objectives && objectives.length > 0) {
      console.log('课程目标ID列表:', objectives.map(obj => ({
        objectiveId: obj.objectiveId,
        number: obj.number,
        title: obj.title
      })))
    }
  } catch (error) {
    console.error('加载课程目标失败:', error)
    MessagePlugin.error('加载课程目标失败')
  }
}

// 从教学任务数据中提取班级信息
const extractClassesFromTasks = (): ClassOption[] => {
  try {
    // 检查是否有任务数据
    if (!taskList.value || taskList.value.length === 0) {
      return []
    }

    // 从所有任务中收集班级信息
    const allClasses: { classId: number; className: string }[] = []

    // 从 taskList 中提取班级信息（基于已转换的数据）
    taskList.value.forEach(task => {
      // 处理 className 字段（可能包含多个班级，用逗号分隔）
      if (task.className && task.className !== '未知班级' && task.className !== '数据异常') {
        const classNames = task.className.split(',').map(name => name.trim())

        classNames.forEach(className => {
          // 对于单个班级的任务，classId 就是该班级的ID
          if (classNames.length === 1 && task.classId > 0) {
            allClasses.push({
              classId: task.classId,
              className: className
            })
          } else {
            // 对于多班级任务，我们需要生成唯一的ID
            // 这里使用班级名称的哈希值作为临时ID
            const tempClassId = generateClassIdFromName(className)
            allClasses.push({
              classId: tempClassId,
              className: className
            })
          }
        })
      }
    })

    // 去重处理：基于 classId 和 className 进行去重
    const uniqueClasses = allClasses.reduce((unique: { classId: number; className: string }[], current) => {
      const exists = unique.find(cls =>
        cls.classId === current.classId || cls.className === current.className
      )
      if (!exists) {
        unique.push(current)
      }
      return unique
    }, [])

    // 转换为 ClassOption 格式
    const classOptions: ClassOption[] = uniqueClasses.map(cls => ({
      classId: cls.classId,
      className: cls.className,
      value: cls.classId,
      label: cls.className
    }))

    // 按班级名称排序
    classOptions.sort((a, b) => a.className.localeCompare(b.className))

    return classOptions

  } catch (error) {
    console.error('从任务数据中提取班级信息失败:', error)
    return []
  }
}

// 生成班级名称的哈希ID（用于多班级任务的临时ID生成）
const generateClassIdFromName = (className: string): number => {
  let hash = 0
  for (let i = 0; i < className.length; i++) {
    const char = className.charCodeAt(i)
    hash = ((hash << 5) - hash) + char
    hash = hash & hash // 转换为32位整数
  }
  return Math.abs(hash)
}

// 重新设计的 loadClassList 方法
const loadClassList = () => {
  try {
    // 从教学任务数据中提取班级信息
    const extractedClasses = extractClassesFromTasks()
    classList.value = extractedClasses
  } catch (error) {
    console.error('加载班级列表失败:', error)
    classList.value = []
    MessagePlugin.warning('班级列表加载失败')
  }
}

// 加载考核详情
const loadAssessmentDetail = async () => {
  if (!assessmentId.value) {
    console.warn('考核ID为空，无法加载考核详情')
    return
  }

  try {
    assessmentDetailLoading.value = true
    console.log('开始加载考核详情，ID:', assessmentId.value)

    const response = await getAssessmentById(assessmentId.value)
    console.log('考核详情API响应:', response)

    if (response?.code === 200 && response.data) {
      assessmentDetail.value = response.data
      console.log('考核详情加载成功:', assessmentDetail.value)
    } else {
      console.warn('考核详情API返回异常:', response)
      MessagePlugin.warning('考核详情加载失败')
    }
  } catch (error) {
    console.error('加载考核详情失败:', error)
    MessagePlugin.error('加载考核详情失败')
  } finally {
    assessmentDetailLoading.value = false
  }
}

const loadStudentList = async (taskId: number) => {
  try {
    scoreTableLoading.value = true

    // 验证必要参数
    if (!taskId) {
      MessagePlugin.error('教学任务ID不能为空')
      return
    }

    if (!assessmentId.value) {
      MessagePlugin.error('考核ID不能为空')
      return
    }

    // 调用后端API获取学生成绩数据
    const response = await getAssessmentTaskScores(assessmentId.value, taskId)

    if (response?.code === 200) {
      const studentScoreList: StudentScoreDetailVO[] = response.data || []

      console.log('后端返回的学生成绩数据:', studentScoreList)

      if (!Array.isArray(studentScoreList)) {
        MessagePlugin.error('学生成绩数据格式错误')
        studentList.value = []
        return
      }

      // 检查第一个学生的数据结构
      if (studentScoreList.length > 0) {
        console.log('第一个学生的数据结构:', studentScoreList[0])
        console.log('课程目标成绩数据:', studentScoreList[0].courseTargetScores)
      }

      // 转换数据格式
      const convertedStudents = studentScoreList.map(convertStudentScoreToStudentItem)

      // 数据验证：检查转换后的数据完整性
      const validStudents = convertedStudents.filter(student => {
        if (!student.studentId || !student.studentName) {
          console.warn('学生数据不完整:', student)
          return false
        }
        return true
      })

      studentList.value = validStudents

      // 成功提示
      MessagePlugin.success(`成功加载 ${validStudents.length} 名学生的成绩数据`)

      // 数据质量检查
      if (validStudents.length !== studentScoreList.length) {
        MessagePlugin.warning(`${studentScoreList.length - validStudents.length} 条学生数据存在问题已过滤`)
      }

    } else {
      MessagePlugin.error(response?.message || '获取学生成绩数据失败')
      studentList.value = []
    }
  } catch (error) {
    console.error('加载学生成绩列表失败:', error)
    MessagePlugin.error('加载学生成绩列表失败，请稍后重试')

    // 错误兜底：清空学生列表
    studentList.value = []
  } finally {
    scoreTableLoading.value = false
  }
}

// 计算总分
const calculateTotalScore = (student: StudentItem): number => {
  try {
    // 根据课程目标分数计算总分（求和）
    let total = 0

    courseObjectives.value.forEach(objective => {
      const score = student[`objective_${objective.number}`]
      if (typeof score === 'number' && !isNaN(score)) {
        total += score
      }
    })

    // 保留一位小数
    return Math.round(total * 10) / 10
  } catch (error) {
    console.error('计算总分时发生错误:', error)
    return 0
  }
}

// TDesign 可编辑表格事件处理
const handleEditClick = (context: any) => {
  console.log('开始编辑:', context)
  const { row, col } = context
  const key = `${row.studentId}_${col.colKey.replace('objective_', '')}`
  editingCell.value = key
}

const handleSave = (context: any) => {
  console.log('保存编辑:', context)
  const { row, col, value, editedRow } = context
  const objectiveId = col.colKey.replace('objective_', '')
  const key = `${row.studentId}_${objectiveId}`

  // 数据验证
  if (value !== null && value !== undefined) {
    if (value < 0 || value > 100) {
      MessagePlugin.error('分数范围应在 0-100 之间')
      return false
    }

    // 保存到未保存状态
    unsavedScores.value[key] = {
      studentId: row.studentId,
      objectiveId: objectiveId,
      score: value,
      isModified: true
    }

    // 更新行数据
    if (editedRow) {
      editedRow[col.colKey] = value
    }

    MessagePlugin.success('成绩已更新')
    editingCell.value = null
  }
}

const handleCancel = (context: any) => {
  console.log('取消编辑:', context)
  editingCell.value = null
}

// 简化的编辑功能实现
// 开始编辑
const startEdit = (studentId: string, objectiveNumber: number, currentScore: number | string) => {
  try {
    console.log('开始编辑:', { studentId, objectiveNumber, currentScore })

    if (selectedTask.value?.status === 'submitted') {
      MessagePlugin.warning('已提交的成绩不能修改')
      return
    }

    const key = `${studentId}_${objectiveNumber}`
    editingCell.value = key
    // 确保设置为字符串类型
    const scoreValue = currentScore === null || currentScore === undefined ? '' : String(currentScore)
    tempInputValue.value = scoreValue

    console.log('开始编辑设置值:', { currentScore, scoreValue, type: typeof scoreValue })
  } catch (error) {
    console.error('开始编辑时发生错误:', error)
    MessagePlugin.error('无法进入编辑模式，请重试')
  }
}

// 取消编辑
const cancelEdit = () => {
  try {
    editingCell.value = null
    tempInputValue.value = ''
  } catch (error) {
    console.error('取消编辑时发生错误:', error)
    // 强制重置状态
    editingCell.value = null
    tempInputValue.value = ''
  }
}

// 安全的事件处理包装器
const safeHandleScoreBlur = (studentId: string, objectiveNumber: number) => {
  try {
    handleScoreBlur(studentId, objectiveNumber)
  } catch (error) {
    console.error('处理失去焦点事件时发生错误:', error)
    MessagePlugin.error('保存失败，请重试')
  }
}

const safeCancelEdit = () => {
  try {
    cancelEdit()
  } catch (error) {
    console.error('取消编辑时发生错误:', error)
    // 强制重置状态
    editingCell.value = null
    tempInputValue.value = ''
  }
}

// 统一的键盘事件处理函数
const handleKeyboardEvent = (event: KeyboardEvent, studentId: string, objectiveNumber: number) => {
  try {
    if (event.key === 'Enter') {
      event.preventDefault()
      safeHandleScoreBlur(studentId, objectiveNumber)
    } else if (event.key === 'Escape') {
      event.preventDefault()
      safeCancelEdit()
    }
  } catch (error) {
    console.error('处理键盘事件时发生错误:', error)
    MessagePlugin.error('操作失败，请重试')
  }
}

// 处理失去焦点保存
const handleScoreBlur = (studentId: string, objectiveNumber: number) => {
  try {
    // 安全地获取输入值并转换为字符串
    const rawValue = tempInputValue.value
    const valueStr = String(rawValue || '').trim()

    console.log('处理输入值:', { rawValue, valueStr, type: typeof rawValue })

    // 空值处理
    if (valueStr === '' || valueStr === 'null' || valueStr === 'undefined') {
      editingCell.value = null
      return
    }

    const value = parseFloat(valueStr)

    // 数据验证
    if (isNaN(value)) {
      MessagePlugin.error('请输入有效的数字')
      return
    }

    if (value < 0 || value > 100) {
      MessagePlugin.error('分数范围应在 0-100 之间')
      return
    }

    // 使用现有的 handleScoreChange 函数来保存和计算
    handleScoreChange(studentId, String(objectiveNumber), value)

    // 退出编辑模式
    editingCell.value = null
    MessagePlugin.success('成绩已更新')
  } catch (error) {
    console.error('保存成绩时发生错误:', error)
    MessagePlugin.error('保存成绩失败，请重试')
    // 不退出编辑模式，让用户可以重试
  }
}

// 获取显示分数
const getDisplayScore = (row: StudentItem, objectiveNumber: number): number | string => {
  const key = `${row.studentId}_${objectiveNumber}`
  return unsavedScores.value[key]?.score ?? row[`objective_${objectiveNumber}`] ?? ''
}

// 获取显示样式类
const getScoreCellDisplayClass = (studentId: string, objectiveNumber: number): string[] => {
  const key = `${studentId}_${objectiveNumber}`
  const isModified = !!unsavedScores.value[key]

  const classes = ['score-display', 'clickable']
  if (isModified) {
    classes.push('modified-score')
  }

  return classes
}

// 事件处理方法
const handleScoreChange = (studentId: string, objectiveId: string, value: number) => {
  const key = `${studentId}_${objectiveId}`

  if (value === null || value === undefined || isNaN(value)) {
    delete unsavedScores.value[key]
  } else {
    unsavedScores.value[key] = {
      studentId,
      objectiveId,
      score: value,
      isModified: true
    }
  }

  // 重新计算该学生的总分
  const student = studentList.value.find(s => s.studentId === studentId)
  if (student) {
    // 更新学生的课程目标分数
    courseObjectives.value.forEach(objective => {
      const scoreKey = `${studentId}_${objective.objectiveId}`
      if (unsavedScores.value[scoreKey]) {
        student[`objective_${objective.objectiveId}`] = unsavedScores.value[scoreKey].score
      }
    })

    // 重新计算总分
    student.totalScore = calculateTotalScore(student)
  }
}

const handleFilter = () => {
  applyFilter()
}

const resetFilter = () => {
  filterForm.status = ''
  filterForm.classId = ''
  applyFilter()
}

const applyFilter = () => {
  let filtered = [...taskList.value]

  // 应用状态筛选
  if (filterForm.status) {
    filtered = filtered.filter(task => task.status === filterForm.status)
  }

  // 应用班级筛选
  if (filterForm.classId) {
    filtered = filtered.filter(task => task.classId === Number(filterForm.classId))
  }

  // 更新总数
  taskPagination.total = filtered.length

  // 前端分页处理
  const startIndex = (taskPagination.current - 1) * taskPagination.pageSize
  const endIndex = startIndex + taskPagination.pageSize

  filteredTaskList.value = filtered.slice(startIndex, endIndex)
}

const handleTaskPageChange = (pageInfo: any) => {
  taskPagination.current = pageInfo.current
  taskPagination.pageSize = pageInfo.pageSize

  // 由于 getAssessmentRelatedTasks 不支持分页，这里只更新分页状态
  // 实际的分页逻辑由前端的 applyFilter 方法处理
  applyFilter()
}

const refreshTaskList = () => {
  loadTaskList()
}

const enterScoreManagement = (task: TaskItem) => {
  selectedTask.value = task
  loadStudentList(task.taskId)
}

const backToTaskList = () => {
  if (hasUnsavedChanges.value) {
    // 显示自定义确认对话框
    showExitConfirmDialog()
  } else {
    // 直接返回
    doBackToTaskList()
  }
}

// 显示退出确认对话框
const showExitConfirmDialog = () => {
  const dialog = DialogPlugin({
    header: '有未保存的修改',
    body: '检测到您有未保存的成绩修改，请选择您的操作：',
    confirmBtn: null,
    cancelBtn: null,
    footer: () => {
      return [
        h('t-button', {
          theme: 'primary',
          onClick: async () => {
            // 保存并退出
            try {
              await saveAllScores()
              doBackToTaskList()
              dialog.destroy()
            } catch (error) {
              // 保存失败，不退出
            }
          }
        }, '保存并退出'),
        h('t-button', {
          theme: 'danger',
          variant: 'outline',
          onClick: () => {
            // 直接退出
            doBackToTaskList()
            dialog.destroy()
          }
        }, '直接退出'),
        h('t-button', {
          theme: 'default',
          onClick: () => {
            // 取消
            dialog.destroy()
          }
        }, '取消')
      ]
    }
  })
}

// 执行返回任务列表
const doBackToTaskList = () => {
  selectedTask.value = null
  unsavedScores.value = {}
  studentList.value = []
  editingCell.value = null
  tempInputValue.value = ''
}

const viewTaskDetail = (task: TaskItem) => {
  // 查看任务详情的逻辑
  MessagePlugin.info('查看任务详情功能待实现')
}

// 返回教学任务管理页面
const handleBackToTaskManagement = () => {
  // 检查是否有未保存的修改
  if (hasUnsavedChanges.value) {
    DialogPlugin.confirm({
      header: '确认返回',
      body: '当前有未保存的修改，确定要返回教学任务管理页面吗？',
      onConfirm: () => {
        navigateBackToTaskManagement()
      }
    })
  } else {
    navigateBackToTaskManagement()
  }
}

// 执行返回导航
const navigateBackToTaskManagement = () => {
  try {
    // 方式1: 如果是从 EvaluationTasksTab 跳转过来的，返回到该页面
    if (route.query.from === 'evaluation-tasks') {
      router.push({
        name: 'Evaluation',
        params: {
          courseId: String(courseId.value),
          taskId: String(assessmentId.value)
        },
        query: {
          courseName: courseName.value,
          year: assessmentYear.value,
          term: assessmentTerm.value,
          isReturn: 'true'
        }
      })
      return
    }

    // 方式2: 根据路由历史返回
    if (window.history.length > 1) {
      router.go(-1)
      return
    }
  } catch (error) {
    console.error('返回教学任务管理页面失败:', error)
    MessagePlugin.error('返回教学任务管理页面失败')
  }
}

// 成绩操作方法
const saveAllScores = async () => {
  if (!hasUnsavedChanges.value) {
    MessagePlugin.warning('没有需要保存的修改')
    return
  }

  // 数据验证
  if (!assessmentId.value) {
    MessagePlugin.error('考核ID不能为空')
    return
  }

  if (!selectedTask.value?.taskId) {
    MessagePlugin.error('教学任务ID不能为空')
    return
  }

  try {
    saveLoading.value = true

    // 数据收集和转换
    const studentScores: StudentTargetScoreDTO[] = []

    // 遍历所有未保存的成绩修改
    Object.entries(unsavedScores.value).forEach(([key, scoreData]) => {
      // 解析 key: studentId_objectiveNumber
      const [studentIdStr, objectiveNumberStr] = key.split('_')

      if (!studentIdStr || !objectiveNumberStr) {
        console.warn('无效的成绩数据key:', key)
        return
      }

      // 查找对应的课程目标信息
      const objectiveNumber = parseInt(objectiveNumberStr, 10)
      const objective = courseObjectives.value.find(obj => obj.number === objectiveNumber)
      if (!objective) {
        console.warn('找不到对应的课程目标:', objectiveNumberStr)
        return
      }

      // 数据类型转换
      const studentTargetScore: StudentTargetScoreDTO = {
        studentId: parseInt(studentIdStr, 10),           // 转换为数字类型
        courseTargetNo: objective.number,                // 使用课程目标编号
        repositoryAnswerId: 0, // TODO: 需要确认如何获取题目答案ID，暂时设为0
        score: Number(scoreData.score),                  // 确保为数字类型
        remark: scoreData.remark || undefined            // 可选备注
      }

      // 数据验证
      if (isNaN(studentTargetScore.studentId)) {
        console.warn('无效的学生ID:', studentIdStr)
        return
      }

      if (isNaN(studentTargetScore.score)) {
        console.warn('无效的分数:', scoreData.score)
        return
      }

      studentScores.push(studentTargetScore)
    })

    if (studentScores.length === 0) {
      MessagePlugin.warning('没有有效的成绩数据需要保存')
      return
    }

    // 构建批量保存数据
    const batchSaveData: BatchSaveTargetScoresDTO = {
      assessmentId: assessmentId.value,
      taskId: selectedTask.value.taskId,
      studentScores: studentScores
    }

    console.log('准备保存的数据:', batchSaveData)

    // 调用批量保存API
    const response = await assessmentScoreAPI.batchSaveTargetScores(batchSaveData)

    if (response.code === 200) {
      // 保存成功处理
      // 清空未保存的修改
      unsavedScores.value = {}

      // 更新任务状态
      if (selectedTask.value) {
        selectedTask.value.status = 'in_progress'
        selectedTask.value.enteredCount = studentList.value.length
        selectedTask.value.notEnteredCount = 0
      }

      // 刷新学生成绩数据以反映最新状态
      await loadStudentList(selectedTask.value.taskId)

      MessagePlugin.success(`成绩保存成功，共保存 ${studentScores.length} 条记录`)
    } else {
      throw new Error(response.message || '保存失败')
    }
  } catch (error) {
    console.error('保存成绩失败:', error)

    // 错误处理
    if (error instanceof Error) {
      MessagePlugin.error(`保存成绩失败: ${error.message}`)
    } else {
      MessagePlugin.error('保存成绩失败，请稍后重试')
    }

    // 可以考虑添加重试机制
    throw error
  } finally {
    saveLoading.value = false
  }
}

const submitScores = async () => {
  if (hasUnsavedChanges.value) {
    MessagePlugin.warning('请先保存所有修改再提交')
    return
  }

  DialogPlugin.confirm({
    header: '确认提交',
    body: '提交后将无法再修改成绩，确定要提交吗？',
    onConfirm: async () => {
      try {
        // 这里应该调用提交成绩的API
        // await submitAssessmentScores(props.assessmentId, selectedTask.value?.taskId)

        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000))

        if (selectedTask.value) {
          selectedTask.value.status = 'submitted'
        }

        MessagePlugin.success('成绩提交成功')
      } catch (error) {
        console.error('提交成绩失败:', error)
        MessagePlugin.error('提交成绩失败')
      }
    }
  })
}

const exportScoreTemplate = async () => {
  try {
    // 参数验证
    if (!assessmentId.value) {
      MessagePlugin.error('考核ID不能为空')
      return
    }

    if (!selectedTask.value?.taskId) {
      MessagePlugin.error('请先选择教学任务')
      return
    }

    // 显示加载状态
    MessagePlugin.loading('正在生成模板，请稍候...')

    // 调用后端API导出模板
    const response = await exportTargetScoreTemplate(assessmentId.value, selectedTask.value.taskId)

    // 检查响应数据
    let blobData: Blob
    if (response instanceof Blob) {
      // 直接是 Blob 数据
      blobData = response
    } else if (response && typeof response === 'object' && 'data' in response) {
      // 包装在 ApiResponse 中的 Blob 数据
      blobData = response.data as Blob
    } else {
      throw new Error('服务器返回的文件格式错误')
    }

    // 验证 Blob 数据
    if (!blobData || !(blobData instanceof Blob)) {
      throw new Error('无效的文件数据')
    }

    // 创建下载链接
    const url = window.URL.createObjectURL(blobData)
    const link = document.createElement('a')
    link.href = url

    // 生成文件名
    const fileName = `${assessmentInfo.value?.courseName || '课程'}_${assessmentInfo.value?.assessmentName || '考核'}_${selectedTask.value.taskName || '任务'}_成绩模板.xlsx`
    link.download = fileName

    // 触发下载
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    // 释放URL对象
    window.URL.revokeObjectURL(url)

    MessagePlugin.success('模板下载成功')
  } catch (error) {
    console.error('导出模板失败:', error)

    // 错误处理
    if (error instanceof Error) {
      MessagePlugin.error(`导出模板失败: ${error.message}`)
    } else {
      MessagePlugin.error('导出模板失败，请稍后重试')
    }
  }
}

const handleFileChange = (files: any[]) => {
  uploadFiles.value = files.map(file => file.raw || file)
}

const confirmImport = async () => {
  if (!uploadFiles.value.length) {
    MessagePlugin.warning('请选择要导入的文件')
    return
  }

  try {
    importLoading.value = true

    const file = uploadFiles.value[0]
    const data = await readExcelFile(file)

    if (!data || data.length < 2) {
      MessagePlugin.error('文件格式错误或数据为空')
      return
    }

    // 解析Excel数据
    const headers = data[0]
    const rows = data.slice(1)

    // 验证表头格式
    if (headers[0] !== '学号' || headers[1] !== '姓名') {
      MessagePlugin.error('表头格式错误，前两列必须是"学号"和"姓名"')
      return
    }

    // 处理导入数据
    let successCount = 0
    let errorCount = 0

    rows.forEach((row: any[]) => {
      const studentId = String(row[0])
      const student = studentList.value.find(s => s.studentId === studentId)

      if (student) {
        // 更新学生成绩
        courseObjectives.value.forEach((objective, index) => {
          const scoreIndex = index + 2 // 跳过学号和姓名列
          if (scoreIndex < row.length && row[scoreIndex] !== null && row[scoreIndex] !== undefined) {
            const score = Number(row[scoreIndex])
            if (!isNaN(score) && score >= 0 && score <= 100) {
              handleScoreChange(studentId, String(objective.objectiveId), score)
            }
          }
        })
        successCount++
      } else {
        errorCount++
      }
    })

    showImportDialog.value = false
    uploadFiles.value = []

    MessagePlugin.success(`导入完成：成功 ${successCount} 条，失败 ${errorCount} 条`)
  } catch (error) {
    console.error('导入失败:', error)
    MessagePlugin.error('导入失败')
  } finally {
    importLoading.value = false
  }
}

// 辅助方法
const readExcelFile = (file: File): Promise<any[][]> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target?.result as ArrayBuffer)
        const workbook = XLSX.read(data, { type: 'array' })
        const sheetName = workbook.SheetNames[0]
        const worksheet = workbook.Sheets[sheetName]
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 })
        resolve(jsonData as any[][])
      } catch (error) {
        reject(error)
      }
    }
    reader.onerror = reject
    reader.readAsArrayBuffer(file)
  })
}

// 生命周期
onMounted(async () => {
  await Promise.all([
    loadAssessmentDetail(),
    loadTaskList(),
    loadCourseObjectives(),
    loadClassList()
  ])
})

// 监听路由参数变化
watch(
  () => [
    assessmentId.value,
    courseId.value,
    courseName.value,
    assessmentYear.value,
    assessmentTerm.value,
    route.query.from
  ],
  (newValues, oldValues) => {
    // 检查是否有关键参数发生变化
    const hasKeyChanges = newValues[0] !== oldValues?.[0] ||  // assessmentId
                         newValues[1] !== oldValues?.[1];    // courseId

    if (hasKeyChanges) {
      // 1. 清空当前状态
      selectedTask.value = null
      unsavedScores.value = {}
      studentList.value = []
      taskList.value = []

      // 2. 更新 assessmentInfo
      assessmentInfo.value = {
        assessmentId: assessmentId.value,
        assessmentName: (route.query.assessmentName as string) || '考核任务',
        courseId: courseId.value,
        courseName: courseName.value || '课程',
        academicYear: assessmentYear.value || '2024-2025',
        semester: assessmentTerm.value === '1' ? '秋季学期' :
                 assessmentTerm.value === '2' ? '春季学期' : '秋季学期'
      }

      // 3. 重新加载数据（只在必要参数存在时）
      if (assessmentId.value && courseId.value) {
        Promise.all([
          loadAssessmentDetail(),
          loadTaskList(),
          loadCourseObjectives(),
          loadClassList()
        ]).catch(error => {
          console.error('数据加载失败:', error)
        })
      }
    } else {
      // 只更新 assessmentInfo，不重新加载数据
      assessmentInfo.value = {
        ...assessmentInfo.value,
        assessmentName: (route.query.assessmentName as string) || assessmentInfo.value.assessmentName,
        courseName: courseName.value || assessmentInfo.value.courseName,
        academicYear: assessmentYear.value || assessmentInfo.value.academicYear,
        semester: assessmentTerm.value === '1' ? '秋季学期' :
                 assessmentTerm.value === '2' ? '春季学期' : assessmentInfo.value.semester
      }
    }
  },
  {
    immediate: false,
    deep: true
  }
)
</script>

<style scoped>
.assessment-score-management {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 页面头部 */
.page-header {
  margin-bottom: 24px;
}

.header-content {
  background: white;
  padding: 20px 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-navigation {
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--td-border-level-1-color);
}

.back-button {
  font-size: 14px;
  color: var(--td-text-color-secondary);
  transition: color 0.2s ease;
}

.back-button:hover {
  color: var(--td-brand-color);
}

.title-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 20px;
  font-weight: 600;
  color: var(--td-text-color-primary);
  margin: 0;
}

.assessment-info {
  display: flex;
  gap: 8px;
  align-items: center;
}

/* 任务列表容器 */
.task-list-container {
  margin-bottom: 24px;
}

.task-list-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--td-text-color-primary);
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* 筛选区域 */
.filter-section {
  padding: 16px 0;
  border-bottom: 1px solid var(--td-border-level-1-color);
  margin-bottom: 16px;
}

/* 进度信息 */
.progress-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.progress-text {
  font-size: 12px;
  color: var(--td-text-color-secondary);
}

/* 成绩详情容器 */
.score-detail-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* 详情头部 */
.detail-header {
  display: flex;
  align-items: center;
  gap: 16px;
  background: white;
  padding: 16px 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.back-button {
  flex-shrink: 0;
}

.task-info {
  flex: 1;
}

.task-info h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--td-text-color-primary);
}

.task-meta {
  display: flex;
  gap: 8px;
  align-items: center;
}

/* 操作区域 */
.action-section {
  margin-bottom: 16px;
}

.action-buttons {
  display: flex;
  gap: 12px;
  align-items: center;
  padding: 16px 20px;
}

/* 成绩表格容器 */
.score-table-container {
  flex: 1;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--td-text-color-primary);
}

.table-info {
  display: flex;
  gap: 16px;
  align-items: center;
  font-size: 14px;
  color: var(--td-text-color-secondary);
}

.unsaved-tip {
  display: flex;
  align-items: center;
  gap: 4px;
  color: var(--td-warning-color);
  font-weight: 500;
}

/* 学生信息 */
.student-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.student-name {
  font-weight: 500;
  color: var(--td-text-color-primary);
}

.student-id {
  font-size: 12px;
  color: var(--td-text-color-placeholder);
}

/* 成绩单元格容器 */
.score-cell-container {
  width: 100%;
  min-height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 成绩显示模式 */
.score-display {
  width: 100%;
  padding: 6px 8px;
  min-height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s ease;
  border: 1px solid transparent;
}

.score-display.clickable {
  cursor: pointer;
}

.score-display.clickable:hover {
  background-color: var(--td-bg-color-container-hover);
  border-color: var(--td-border-level-1-color);
}

/* 修改状态样式 */
.score-display.modified-score {
  background-color: var(--td-warning-color-1);
  border-color: var(--td-warning-color);
  color: var(--td-warning-color);
  font-weight: 500;
}

.score-display.modified-score:hover {
  background-color: var(--td-warning-color-2);
}

/* 输入框样式 */
.score-input {
  width: 100%;
}

.score-input.native-input {
  width: 100%;
  height: 32px;
  padding: 4px 8px;
  border: 1px solid var(--td-border-level-1-color);
  border-radius: 4px;
  text-align: center;
  font-size: 14px;
  background-color: var(--td-bg-color-container);
  color: var(--td-text-color-primary);
  outline: none;
  transition: border-color 0.2s ease;
}

.score-input.native-input:focus {
  border-color: var(--td-brand-color);
  box-shadow: 0 0 0 2px var(--td-brand-color-1);
}

.score-input.native-input:hover {
  border-color: var(--td-brand-color-hover);
}

:deep(.score-input .t-input__inner) {
  text-align: center;
}

/* 成绩单元格 */
.score-cell {
  width: 100%;
  padding: 4px;
  min-height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  transition: all 0.2s ease;
}

.score-value {
  display: inline-block;
  min-width: 20px;
  text-align: center;
}

:deep(.modified-score) {
  border-color: var(--td-warning-color) !important;
  background-color: var(--td-warning-color-1) !important;
}

/* 总分样式 */
.total-score {
  font-weight: 600;
  text-align: center;
}

.score-excellent {
  color: var(--td-success-color);
}

.score-good {
  color: var(--td-brand-color);
}

.score-pass {
  color: var(--td-warning-color);
}

.score-fail {
  color: var(--td-error-color);
}

/* 导入对话框 */
.import-dialog-content {
  padding: 16px 0;
}

.import-tips {
  margin-top: 16px;
  padding: 12px;
  background-color: var(--td-bg-color-container-select);
  border-radius: 6px;
}

.import-tips h5 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--td-text-color-primary);
}

.import-tips ul {
  margin: 0;
  padding-left: 16px;
}

.import-tips li {
  font-size: 13px;
  color: var(--td-text-color-secondary);
  line-height: 1.5;
  margin-bottom: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .assessment-score-management {
    padding: 16px;
  }

  .header-content {
    padding: 16px 20px;
  }

  .header-navigation {
    margin-bottom: 12px;
    padding-bottom: 8px;
  }

  .back-button {
    font-size: 13px;
  }

  .title-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .page-title {
    font-size: 18px;
  }

  .action-buttons {
    flex-wrap: wrap;
    gap: 8px;
  }

  .detail-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .task-meta {
    flex-wrap: wrap;
  }
}

/* 考核基本信息展示区域样式 */
.assessment-info-section {
  margin-bottom: 24px;

  .assessment-info-card {
    :deep(.t-card__header) {
      padding: 16px 20px;
      border-bottom: 1px solid var(--td-border-level-1-color);
    }

    :deep(.t-card__body) {
      padding: 20px;
    }
  }

  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 16px;

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: var(--td-text-color-primary);
      display: flex;
      align-items: center;
      gap: 8px;

      :deep(.t-icon) {
        color: var(--td-brand-color);
      }
    }

    .header-actions {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }

  .assessment-detail-content {
    .basic-info-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 16px 24px;
      margin-bottom: 24px;

      .info-item {
        display: flex;
        align-items: center;

        .info-label {
          font-weight: 500;
          color: var(--td-text-color-secondary);
          min-width: 80px;
          margin-right: 8px;
        }

        .info-value {
          color: var(--td-text-color-primary);
          font-weight: 500;
        }
      }
    }

    .objectives-section {
      .section-title {
        margin: 0 0 16px 0;
        font-size: 14px;
        font-weight: 600;
        color: var(--td-text-color-primary);
        display: flex;
        align-items: center;
        gap: 8px;

        :deep(.t-icon) {
          color: var(--td-brand-color);
        }
      }

      .objectives-table {
        :deep(.t-table__header) {
          background-color: var(--td-bg-color-container-select);
        }

        :deep(.t-table__body) {
          tr:hover {
            background-color: var(--td-bg-color-container-hover);
          }
        }
      }
    }
  }

  .no-data {
    text-align: center;
    padding: 40px 20px;
    color: var(--td-text-color-placeholder);

    :deep(.t-icon) {
      color: var(--td-text-color-disabled);
      margin-bottom: 12px;
    }

    p {
      margin: 0;
      font-size: 14px;
    }
  }
}

@media (max-width: 768px) {
  .assessment-info-section {
    .assessment-detail-content {
      .basic-info-grid {
        grid-template-columns: 1fr;
        gap: 12px;
      }
    }

    .card-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 12px;

      .header-actions {
        width: 100%;
        justify-content: flex-start;
      }
    }
  }
}

/* 详细配置对话框样式 */
.detail-config-content {
  .config-overview {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    padding: 16px;
    background-color: var(--td-bg-color-container-select);
    border-radius: 6px;
    margin-bottom: 20px;

    .overview-item {
      display: flex;
      align-items: center;
      gap: 8px;

      .label {
        font-weight: 500;
        color: var(--td-text-color-secondary);
        white-space: nowrap;
      }

      .value {
        color: var(--td-text-color-primary);
        font-weight: 500;

        &.total-score {
          color: var(--td-brand-color);
          font-size: 16px;
          font-weight: 600;
        }
      }
    }
  }

  .detail-config-table {
    :deep(.t-table__header) {
      background-color: var(--td-bg-color-container-select);
    }

    :deep(.t-table__body) {
      tr:hover {
        background-color: var(--td-bg-color-container-hover);
      }
    }

    .score-value {
      color: var(--td-brand-color);
      font-weight: 600;
    }

    .weight-value {
      color: var(--td-text-color-secondary);
      font-family: monospace;
    }
  }
}

.no-config-data {
  text-align: center;
  padding: 60px 20px;
  color: var(--td-text-color-placeholder);

  :deep(.t-icon) {
    color: var(--td-text-color-disabled);
    margin-bottom: 16px;
  }

  p {
    margin: 0;
    font-size: 14px;
  }
}



@media (max-width: 768px) {
  .detail-config-content {
    .config-overview {
      flex-direction: column;
      gap: 12px;
    }
  }
}

@media (max-width: 480px) {
  .assessment-score-management {
    padding: 12px;
  }

  .header-content {
    padding: 12px 16px;
  }

  .header-navigation {
    margin-bottom: 8px;
    padding-bottom: 6px;
  }

  .back-button {
    font-size: 12px;
  }

  .page-title {
    font-size: 16px;
  }

  .assessment-info {
    flex-wrap: wrap;
    gap: 6px;
  }

  .action-buttons {
    flex-direction: column;
    align-items: stretch;
  }

  .table-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}
</style>
