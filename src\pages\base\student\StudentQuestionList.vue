<template>
  <div class="detail-container">
    <t-card :title="'问卷'">
      <div class="filter-container">
        <input
          v-model="searchExamName"
          placeholder="搜索问卷名称"
          class="search-input"
        />
        <input
          v-model="searchCourseName"
          placeholder="搜索发布人"
          class="search-input"
        />
        <select v-model="selectedStatus" class="status-select">
          <option value="">全部状态</option>
          <option value="0">未提交</option>
          <option value="1">已提交</option>
        </select>
      </div>
      <div class="table-container">
        <table class="exam-table">
          <thead>
          <tr>
            <th>问卷名称</th>
            <th>课程名称</th>
            <th>发布人名称</th>
            <th>开始时间</th>
            <th>截止时间</th>
            <th>状态</th>
          </tr>
          </thead>
          <tbody>
          <tr
            v-for="paper in filteredPapers"
            :key="paper.pid"
            @click="handleCourseClick(paper.pid, paper.status)"
            class="exam-row"
          >
            <td>{{ paper.paperName }}</td>
            <td>{{ paper.courseName }}</td>
            <td>{{ paper.teacher }}</td>
            <td>{{ paper.startTime }}</td>
            <td>{{ paper.endTime }}</td>
            <td>
              <div class="status-cell">
                  <span
                    class="status-box"
                    :class="`status-${paper.status}`"
                  ></span>
                {{ getStatusText(paper.status) }}
              </div>
            </td>
          </tr>
          <tr v-if="!filteredPapers.length" class="empty-row">
            <td colspan="4">
              <p class="empty-tip">
                <span class="emoji">📚</span>
                暂无问卷
              </p>
            </td>
          </tr>
          </tbody>
        </table>
      </div>
    </t-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { getList } from '@/api/base/student/studentQuestion';
import {StudentPaperInfo} from "@/api/model/student/studentPaperModel";

const router = useRouter();

const paperList = ref<StudentPaperInfo[]>([]);
const searchExamName = ref('');
const searchCourseName = ref('');
const selectedStatus = ref('');

const filteredPapers = computed(() => {
  return paperList.value.filter(paper => {
    const examMatch = paper.paperName.toLowerCase().includes(searchExamName.value.toLowerCase());
    const courseMatch = paper.courseName.toLowerCase().includes(searchCourseName.value.toLowerCase());
    const statusMatch = !selectedStatus.value || paper.status.toString() === selectedStatus.value;
    return examMatch && courseMatch && statusMatch;
  });
});

const getStatusText = (status: number) => {
  const statusMap = {
    0: '未提交',
    1: '已提交',
  }
  return statusMap[status] || ''
}

const fetchDetail = async () => {
  try {
    const res = await getList();
    paperList.value = res.list;
  } catch (e) {
    console.error('获取详情失败', e);
  }
};

const handleCourseClick = (pid: number, status: number) => {
  if (status === 0) {
    router.push({
      path: '/studentsQuestion/write',
      query: { pid }
    });
  } else if (status === 1) {
    router.push({
      path: '/studentsQuestion/modify',
      query: { pid }
    });
  }
};

onMounted(() => {
  fetchDetail();
});
</script>

<style scoped>
/* 全局样式变量 */
.detail-container {
  /* 颜色变量 */
  --primary-color: #4f46e5;       /* 主色-紫色 */
  --secondary-color: #64748b;     /* 次要色 */
  --surface-color: #ffffff;       /* 背景色 */
  --hover-color: #06cdd3;         /* 悬停色 */
  --shadow-color: rgba(99, 99, 99, 0.1);
  --status-colors: #ffd700, #ff4444, #2d8cf0, #28a745; /* 状态色数组 */

  /* 渐变背景 */
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  min-height: 100vh;
}

/* 筛选容器 */
.filter-container {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
  padding: 0 16px;
  flex-wrap: wrap;

  /* 公共输入样式 */
  .search-input, .status-select {
    padding: 10px 16px;
    border: 2px solid var(--td-brand-color-5);
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;

    &:focus {
      outline: none;
      box-shadow: 0 0 0 2px ;
    }
  }

  .search-input {
    flex: 1;
    min-width: 200px;
  }

  .status-select {
    background: var(--surface-color);
    cursor: pointer;

    &:hover {
      border-color: var(--td-brand-color-5);
    }
  }
}

/* 表格容器 */
.table-container {
  overflow-x: auto;
  padding: 0 16px;

  .exam-table {
    width: 100%;
    border-collapse: collapse;
    margin: 16px 0;
    background: var(--surface-color);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 1px 3px var(--shadow-color);

    th {
      background: var(--td-brand-color-5);
      color: white;
      font-weight: 600;
      padding: 16px;
      text-align: left;
    }

    td {
      padding: 16px;
      border-bottom: 1px solid #f0f0f0;
      transition: background 0.2s ease;
    }

    /* 表格行交互 */
    .exam-row {
      cursor: pointer;

      &:hover td {
        background: var(--td-brand-color-2);
      }
    }
  }
}

/* 状态指示器 */
.status-cell {
  display: flex;
  align-items: center;
  gap: 8px;

  .status-box {
    width: 12px;
    height: 12px;
    border-radius: 50%;

  }
}

/* 生成状态颜色 */
.status-0 { background: #ffd700; } /* 未开始 */
.status-1 { background: #ff4444; } /* 待完成 */
.status-2 { background: #2d8cf0; } /* 待批阅 */
.status-3 { background: #28a745; } /* 已结束 */

/* 空状态提示 */
.empty-row td {
  padding: 40px;
  text-align: center;
  background: #f8f9fa;

  .empty-tip {
    color: var(--secondary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin: 0;

    .emoji {
      font-size: 1.4em;
    }
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .exam-table {
    min-width: 600px;
  }

  .filter-container {
    flex-direction: column;

    .search-input, .status-select {
      width: 100%;
    }
  }
}
</style>
