<template>
  <div class="total-hours-summary-card">
    <t-card>
      <template #header>
        <div class="card-header">
          <div class="header-title">
            <t-icon name="chart-pie" class="header-icon" />
            总学时统计
          </div>
        </div>
      </template>

      <div class="summary-grid">
        <!-- 教学内容学时统计 -->
        <div class="summary-section">
          <div class="section-title">教学内容学时</div>
          <div class="stats-grid">
            <div class="stat-item">
              <div class="stat-label">理论学时</div>
              <div class="stat-value theory">{{ teachingHours.theory }}</div>
            </div>
            <div class="stat-item">
              <div class="stat-label">实验学时</div>
              <div class="stat-value experiment">{{ teachingHours.experiment }}</div>
            </div>
            <div class="stat-item">
              <div class="stat-label">小计</div>
              <div class="stat-value total">{{ teachingHours.total }}</div>
            </div>
          </div>
        </div>

        <!-- 实践教学学时统计 -->
        <div class="summary-section">
          <div class="section-title">实践教学学时</div>
          <div class="stats-grid">
            <div class="stat-item">
              <div class="stat-label">课内实践</div>
              <div class="stat-value practice">{{ practicalHours.internal }}</div>
            </div>
            <div class="stat-item">
              <div class="stat-label">课外实践</div>
              <div class="stat-value practice">{{ practicalHours.external }}</div>
            </div>
            <div class="stat-item">
              <div class="stat-label">小计</div>
              <div class="stat-value total">{{ practicalHours.total }}</div>
            </div>
          </div>
        </div>

        <!-- 课程思政学时统计 -->
        <div class="summary-section">
          <div class="section-title">课程思政学时</div>
          <div class="stats-grid">
            <div class="stat-item">
              <div class="stat-label">思政融入</div>
              <div class="stat-value ideological">{{ ideologicalHours.integration }}</div>
            </div>
            <div class="stat-item">
              <div class="stat-label">专题讲座</div>
              <div class="stat-value ideological">{{ ideologicalHours.lecture }}</div>
            </div>
            <div class="stat-item">
              <div class="stat-label">小计</div>
              <div class="stat-value total">{{ ideologicalHours.total }}</div>
            </div>
          </div>
        </div>

        <!-- 总计统计 -->
        <div class="summary-section total-section">
          <div class="section-title">总计</div>
          <div class="total-stats">
            <div class="total-item">
              <div class="total-label">课程总学时</div>
              <div class="total-value">{{ grandTotal }}</div>
            </div>
            <div class="progress-info">
              <div class="progress-label">学时分配进度</div>
              <t-progress 
                :percentage="progressPercentage" 
                :color="progressColor"
                :show-info="true"
                size="medium"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- 学时分配图表 -->
      <div class="chart-section">
        <div class="chart-title">学时分配占比</div>
        <div class="chart-container">
          <div class="chart-legend">
            <div class="legend-item">
              <div class="legend-color theory"></div>
              <span>理论学时 ({{ teachingHours.theory }}h)</span>
            </div>
            <div class="legend-item">
              <div class="legend-color experiment"></div>
              <span>实验学时 ({{ teachingHours.experiment }}h)</span>
            </div>
            <div class="legend-item">
              <div class="legend-color practice"></div>
              <span>实践学时 ({{ practicalHours.total }}h)</span>
            </div>
            <div class="legend-item">
              <div class="legend-color ideological"></div>
              <span>思政学时 ({{ ideologicalHours.total }}h)</span>
            </div>
          </div>
        </div>
      </div>
    </t-card>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

// Props
interface Props {
  teachingContents: any[]
  practicalTeaching: any[]
  ideologicalEducation: any[]
}

const props = withDefaults(defineProps<Props>(), {
  teachingContents: () => [],
  practicalTeaching: () => [],
  ideologicalEducation: () => []
})

// 教学内容学时统计
const teachingHours = computed(() => {
  const theory = props.teachingContents.reduce((sum, content) => {
    return sum + (content.hours || 0)
  }, 0)
  
  // 假设实验学时为理论学时的30%（可根据实际情况调整）
  const experiment = Math.round(theory * 0.3)
  
  return {
    theory,
    experiment,
    total: theory + experiment
  }
})

// 实践教学学时统计
const practicalHours = computed(() => {
  const internal = props.practicalTeaching
    .filter(item => ['课内上机', '实验课'].includes(item.type))
    .reduce((sum, item) => sum + (item.hours || 0), 0)
  
  const external = props.practicalTeaching
    .filter(item => ['课外实践', '项目实训', '综合实践'].includes(item.type))
    .reduce((sum, item) => sum + (item.hours || 0), 0)
  
  return {
    internal,
    external,
    total: internal + external
  }
})

// 课程思政学时统计
const ideologicalHours = computed(() => {
  // 思政融入学时（基于章节数量估算）
  const integration = props.ideologicalEducation.length * 2
  
  // 专题讲座学时（固定值或可配置）
  const lecture = props.ideologicalEducation.length > 0 ? 4 : 0
  
  return {
    integration,
    lecture,
    total: integration + lecture
  }
})

// 总学时
const grandTotal = computed(() => {
  return teachingHours.value.total + practicalHours.value.total + ideologicalHours.value.total
})

// 进度百分比（假设目标学时为64）
const progressPercentage = computed(() => {
  const targetHours = 64 // 可以从props传入或配置
  return Math.min(Math.round((grandTotal.value / targetHours) * 100), 100)
})

// 进度颜色
const progressColor = computed(() => {
  const percentage = progressPercentage.value
  if (percentage < 50) return '#f56c6c'
  if (percentage < 80) return '#e6a23c'
  if (percentage < 100) return '#409eff'
  return '#67c23a'
})
</script>

<style scoped lang="less">
.total-hours-summary-card {
  margin-bottom: 24px;

  .card-header {
    .header-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 16px;
      font-weight: 600;
      color: var(--td-text-color-primary);

      .header-icon {
        color: var(--td-brand-color);
      }
    }
  }

  .summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 24px;
  }

  .summary-section {
    padding: 16px;
    background: var(--td-bg-color-container-hover);
    border-radius: var(--td-radius-medium);
    border: 1px solid var(--td-border-level-1-color);

    .section-title {
      font-size: 14px;
      font-weight: 600;
      color: var(--td-text-color-primary);
      margin-bottom: 12px;
      text-align: center;
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 12px;
    }

    .stat-item {
      text-align: center;

      .stat-label {
        font-size: 12px;
        color: var(--td-text-color-secondary);
        margin-bottom: 4px;
      }

      .stat-value {
        font-size: 18px;
        font-weight: 600;

        &.theory { color: var(--td-brand-color); }
        &.experiment { color: var(--td-warning-color); }
        &.practice { color: var(--td-success-color); }
        &.ideological { color: var(--td-error-color); }
        &.total { color: var(--td-text-color-primary); }
      }
    }
  }

  .total-section {
    grid-column: 1 / -1;

    .total-stats {
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 20px;
    }

    .total-item {
      text-align: center;

      .total-label {
        font-size: 14px;
        color: var(--td-text-color-secondary);
        margin-bottom: 8px;
      }

      .total-value {
        font-size: 24px;
        font-weight: 700;
        color: var(--td-brand-color);
      }
    }

    .progress-info {
      flex: 1;
      max-width: 300px;

      .progress-label {
        font-size: 12px;
        color: var(--td-text-color-secondary);
        margin-bottom: 8px;
      }
    }
  }

  .chart-section {
    border-top: 1px solid var(--td-border-level-1-color);
    padding-top: 20px;

    .chart-title {
      font-size: 14px;
      font-weight: 600;
      color: var(--td-text-color-primary);
      margin-bottom: 16px;
      text-align: center;
    }

    .chart-legend {
      display: flex;
      justify-content: center;
      gap: 20px;
      flex-wrap: wrap;

      .legend-item {
        display: flex;
        align-items: center;
        gap: 6px;
        font-size: 12px;
        color: var(--td-text-color-secondary);

        .legend-color {
          width: 12px;
          height: 12px;
          border-radius: 2px;

          &.theory { background: var(--td-brand-color); }
          &.experiment { background: var(--td-warning-color); }
          &.practice { background: var(--td-success-color); }
          &.ideological { background: var(--td-error-color); }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .total-hours-summary-card {
    .summary-grid {
      grid-template-columns: 1fr;
    }

    .total-section .total-stats {
      flex-direction: column;
      gap: 16px;
    }

    .chart-section .chart-legend {
      flex-direction: column;
      align-items: center;
      gap: 8px;
    }
  }
}
</style>
