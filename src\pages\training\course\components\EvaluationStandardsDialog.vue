<template>
  <t-dialog
    v-model:visible="dialogVisible"
    header="表3：综合成绩评价标准"
    :width="1200"
    :footer="false"
    placement="center"
    :destroy-on-close="true"
    class="evaluation-standards-dialog"
  >
    <div class="dialog-content">
      <!-- 对话框头部信息 -->
      <div class="dialog-header-info">
        <div class="info-item">
          <t-icon name="info-circle" />
          <span>本表展示了课程综合成绩的评价标准和等级划分</span>
        </div>
        <div class="dialog-actions">
          <t-button theme="primary" size="small" @click="handleExport">
            <t-icon name="download" />
            导出标准
          </t-button>
          <t-button theme="warning" size="small" @click="handleEdit">
            <t-icon name="edit" />
            编辑标准
          </t-button>
        </div>
      </div>

      <!-- 评价标准表格 -->
      <div class="evaluation-table-container">
        <table class="evaluation-table">
          <thead>
            <tr>
              <th>成绩等级</th>
              <th>分数范围</th>
              <th>等级描述</th>
              <th>评价标准</th>
              <th>课程目标达成度</th>
            </tr>
          </thead>
          <tbody>
            <tr 
              v-for="(standard, index) in evaluationStandards" 
              :key="index" 
              :class="standard.gradeClass"
            >
              <td class="grade-cell">
                <div class="grade-info">
                  <span class="grade-name">{{ standard.gradeName }}</span>
                  <div class="grade-indicator" :class="standard.gradeClass"></div>
                </div>
              </td>
              <td class="score-range">{{ standard.scoreRange }}</td>
              <td class="description">{{ standard.description }}</td>
              <td class="criteria">{{ standard.criteria }}</td>
              <td class="achievement-level">
                <div class="achievement-display">
                  <span class="achievement-value">{{ standard.achievementLevel }}</span>
                  <div class="achievement-bar">
                    <div 
                      class="achievement-progress" 
                      :style="{ width: standard.achievementPercentage + '%' }"
                    ></div>
                  </div>
                  <span class="achievement-percentage">{{ standard.achievementPercentage }}%</span>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 评价标准说明 -->
      <div class="evaluation-notes">
        <div class="notes-header">
          <t-icon name="info-circle" />
          <span>评价标准说明</span>
        </div>
        <div class="notes-content">
          <div class="notes-grid">
            <div class="note-item">
              <div class="note-icon">
                <t-icon name="chart-pie" />
              </div>
              <div class="note-text">
                <strong>综合成绩计算：</strong>根据各考核方式的权重分配计算得出
              </div>
            </div>
            <div class="note-item">
              <div class="note-icon">
                <t-icon name="target" />
              </div>
              <div class="note-text">
                <strong>目标达成度：</strong>基于学生在各项考核中的表现评价
              </div>
            </div>
            <div class="note-item">
              <div class="note-icon">
                <t-icon name="setting" />
              </div>
              <div class="note-text">
                <strong>标准调整：</strong>可根据课程特点和教学要求进行调整
              </div>
            </div>
            <div class="note-item">
              <div class="note-icon">
                <t-icon name="chart-line" />
              </div>
              <div class="note-text">
                <strong>质量提升：</strong>评价结果作为课程改进的重要依据
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 成绩分布统计 -->
      <div class="grade-distribution">
        <div class="distribution-header">
          <h4>成绩分布统计</h4>
          <div class="distribution-summary">
            <t-tag theme="primary" size="small">
              总计学生数：{{ totalStudents }}人
            </t-tag>
          </div>
        </div>
        <div class="distribution-cards">
          <div 
            v-for="(distribution, index) in gradeDistribution" 
            :key="index" 
            class="distribution-card"
            :class="distribution.gradeClass"
          >
            <div class="card-header">
              <span class="grade-name">{{ distribution.gradeName }}</span>
              <span class="grade-count">{{ distribution.count }}人</span>
            </div>
            <div class="card-content">
              <div class="percentage-display">
                <span class="percentage-value">{{ distribution.percentage }}%</span>
              </div>
              <div class="progress-bar">
                <div 
                  class="progress-fill" 
                  :style="{ width: distribution.percentage + '%' }"
                ></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </t-dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';

// Props
interface Props {
  visible: boolean;
  evaluationStandards?: Array<any>;
  gradeDistribution?: Array<any>;
  totalStudents?: number;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  evaluationStandards: () => [],
  gradeDistribution: () => [],
  totalStudents: 0
});

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean];
  'export': [];
  'edit': [];
}>();

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value: boolean) => emit('update:visible', value)
});

// 默认评价标准数据（如果没有传入props）
const defaultEvaluationStandards = [
  {
    gradeName: '优秀',
    scoreRange: '90-100',
    description: '全面掌握课程内容，能够熟练运用所学知识',
    criteria: '课程目标达成度≥90%，各项考核表现优异',
    achievementLevel: '优秀',
    achievementPercentage: 95,
    gradeClass: 'excellent'
  },
  {
    gradeName: '良好',
    scoreRange: '80-89',
    description: '较好掌握课程内容，能够运用所学知识',
    criteria: '课程目标达成度80%-89%，各项考核表现良好',
    achievementLevel: '良好',
    achievementPercentage: 85,
    gradeClass: 'good'
  },
  {
    gradeName: '中等',
    scoreRange: '70-79',
    description: '基本掌握课程内容，能够理解主要概念',
    criteria: '课程目标达成度70%-79%，各项考核表现中等',
    achievementLevel: '中等',
    achievementPercentage: 75,
    gradeClass: 'medium'
  },
  {
    gradeName: '及格',
    scoreRange: '60-69',
    description: '达到课程基本要求，掌握基础知识',
    criteria: '课程目标达成度60%-69%，各项考核达到及格要求',
    achievementLevel: '及格',
    achievementPercentage: 65,
    gradeClass: 'pass'
  },
  {
    gradeName: '不及格',
    scoreRange: '0-59',
    description: '未达到课程基本要求，需要重新学习',
    criteria: '课程目标达成度<60%，各项考核未达到要求',
    achievementLevel: '不及格',
    achievementPercentage: 40,
    gradeClass: 'fail'
  }
];

// 默认成绩分布数据
const defaultGradeDistribution = [
  { gradeName: '优秀', count: 18, percentage: 15.0, gradeClass: 'excellent' },
  { gradeName: '良好', count: 36, percentage: 30.0, gradeClass: 'good' },
  { gradeName: '中等', count: 42, percentage: 35.0, gradeClass: 'medium' },
  { gradeName: '及格', count: 20, percentage: 16.7, gradeClass: 'pass' },
  { gradeName: '不及格', count: 4, percentage: 3.3, gradeClass: 'fail' }
];

// 计算属性
const evaluationStandards = computed(() => 
  props.evaluationStandards.length > 0 ? props.evaluationStandards : defaultEvaluationStandards
);

const gradeDistribution = computed(() => 
  props.gradeDistribution.length > 0 ? props.gradeDistribution : defaultGradeDistribution
);

const totalStudents = computed(() => 
  props.totalStudents > 0 ? props.totalStudents : 120
);

// 事件处理
const handleExport = () => {
  emit('export');
  MessagePlugin.success('评价标准导出成功');
};

const handleEdit = () => {
  emit('edit');
  MessagePlugin.info('进入编辑模式');
};
</script>

<style lang="less" scoped>
.evaluation-standards-dialog {
  :deep(.t-dialog__body) {
    padding: 0;
  }
}

.dialog-content {
  padding: 24px;
  max-height: 80vh;
  overflow-y: auto;
}

// 对话框头部信息
.dialog-header-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #1890ff;

  .info-item {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #666;
    font-size: 14px;
  }

  .dialog-actions {
    display: flex;
    gap: 8px;
  }
}

// 评价标准表格
.evaluation-table-container {
  margin-bottom: 24px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.evaluation-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
  background: white;

  th,
  td {
    border: 1px solid #e0e0e0;
    padding: 12px 8px;
    text-align: center;
    vertical-align: middle;
  }

  th {
    background: #fafafa;
    font-weight: 600;
    color: #333;
    position: sticky;
    top: 0;
    z-index: 10;
  }

  td {
    background: white;
  }

  .grade-cell {
    .grade-info {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;

      .grade-name {
        font-weight: 600;
        color: #333;
      }

      .grade-indicator {
        width: 12px;
        height: 12px;
        border-radius: 50%;

        &.excellent {
          background: #52c41a;
        }

        &.good {
          background: #1890ff;
        }

        &.medium {
          background: #fa8c16;
        }

        &.pass {
          background: #faad14;
        }

        &.fail {
          background: #f5222d;
        }
      }
    }
  }

  .score-range {
    font-weight: 600;
    color: #1890ff;
    font-family: 'Courier New', monospace;
  }

  .description {
    color: #666;
    line-height: 1.5;
    text-align: left;
    max-width: 200px;
  }

  .criteria {
    color: #333;
    line-height: 1.5;
    text-align: left;
    max-width: 250px;
  }

  .achievement-level {
    .achievement-display {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 0 8px;

      .achievement-value {
        font-weight: 600;
        color: #333;
        min-width: 40px;
      }

      .achievement-bar {
        flex: 1;
        height: 6px;
        background: #f0f0f0;
        border-radius: 3px;
        overflow: hidden;
        min-width: 60px;

        .achievement-progress {
          height: 100%;
          background: linear-gradient(90deg, #52c41a 0%, #73d13d 100%);
          transition: width 0.3s ease;
        }
      }

      .achievement-percentage {
        font-size: 12px;
        color: #666;
        min-width: 35px;
      }
    }
  }

  // 不同等级的行样式
  tr {
    transition: background-color 0.3s ease;

    &:hover {
      background: rgba(24, 144, 255, 0.05);
    }

    &.excellent {
      background: rgba(82, 196, 26, 0.03);

      &:hover {
        background: rgba(82, 196, 26, 0.08);
      }
    }

    &.good {
      background: rgba(24, 144, 255, 0.03);

      &:hover {
        background: rgba(24, 144, 255, 0.08);
      }
    }

    &.medium {
      background: rgba(250, 140, 22, 0.03);

      &:hover {
        background: rgba(250, 140, 22, 0.08);
      }
    }

    &.pass {
      background: rgba(250, 173, 20, 0.03);

      &:hover {
        background: rgba(250, 173, 20, 0.08);
      }
    }

    &.fail {
      background: rgba(245, 34, 45, 0.03);

      &:hover {
        background: rgba(245, 34, 45, 0.08);
      }
    }
  }
}

// 评价标准说明
.evaluation-notes {
  margin-bottom: 24px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #1890ff;

  .notes-header {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: #333;
    margin-bottom: 16px;
    font-size: 16px;
  }

  .notes-content {
    .notes-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 16px;

      .note-item {
        display: flex;
        align-items: flex-start;
        gap: 12px;
        padding: 12px;
        background: white;
        border-radius: 6px;
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);

        .note-icon {
          color: #1890ff;
          font-size: 16px;
          margin-top: 2px;
        }

        .note-text {
          flex: 1;
          line-height: 1.5;
          color: #666;

          strong {
            color: #333;
            font-weight: 600;
          }
        }
      }
    }
  }
}

// 成绩分布统计
.grade-distribution {
  .distribution-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    h4 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #333;
    }

    .distribution-summary {
      font-size: 14px;
    }
  }

  .distribution-cards {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 16px;

    .distribution-card {
      background: #fafafa;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      padding: 16px;
      text-align: center;
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        transform: translateY(-2px);
      }

      &.excellent {
        border-left: 4px solid #52c41a;
      }

      &.good {
        border-left: 4px solid #1890ff;
      }

      &.medium {
        border-left: 4px solid #fa8c16;
      }

      &.pass {
        border-left: 4px solid #faad14;
      }

      &.fail {
        border-left: 4px solid #f5222d;
      }

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;

        .grade-name {
          font-weight: 600;
          color: #333;
          font-size: 14px;
        }

        .grade-count {
          font-size: 18px;
          font-weight: 700;
          color: #1890ff;
        }
      }

      .card-content {
        .percentage-display {
          margin-bottom: 8px;

          .percentage-value {
            font-size: 14px;
            font-weight: 600;
            color: #666;
          }
        }

        .progress-bar {
          height: 6px;
          background: #f0f0f0;
          border-radius: 3px;
          overflow: hidden;

          .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #1890ff 0%, #40a9ff 100%);
            transition: width 0.3s ease;
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1024px) {
  .dialog-content {
    padding: 16px;
  }

  .evaluation-table {
    font-size: 12px;

    th,
    td {
      padding: 8px 4px;
    }

    .description,
    .criteria {
      max-width: 150px;
      font-size: 12px;
    }
  }

  .notes-grid {
    grid-template-columns: 1fr;
  }

  .distribution-cards {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 768px) {
  .dialog-header-info {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .evaluation-table {
    .achievement-display {
      flex-direction: column;
      gap: 4px;

      .achievement-bar {
        min-width: 40px;
      }
    }
  }

  .distribution-cards {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .distribution-cards {
    grid-template-columns: 1fr;
  }
}
</style>
