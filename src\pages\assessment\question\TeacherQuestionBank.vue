<template>
    <div class="question-bank-container">
        <div class="page-header">
            <t-button theme="default" @click="goBack">
                <template #icon><t-icon name="arrow-left" /></template>
                返回{{ isExam ? '考试' : '作业' }}管理
            </t-button>
            <h2>{{ isExam ? '考试' : '作业' }}题库</h2>
        </div>

        <div class="bank-content">
            <!-- 左侧科目导航 -->
            <div class="subject-nav">
                <t-card title="学科分类">
                    <t-input v-model="searchSubject" placeholder="搜索学科" class="search-subject" >
                      <template #prefix-icon>
                        <img src="/image/task/search.svg" alt="search" class="search-icon" />
                      </template>
                    </t-input>
                    <div class="subject-list">
                        <div class="subject-item" :class="{ active: currentSubject === 'all' }"
                            @click="switchSubject('all')">
                            全部科目
                        </div>
                        <div v-for="subject in filteredSubjects" :key="subject.id" class="subject-item"
                            :class="{ active: currentSubject === subject.id }" @click="switchSubject(subject.id)">
                            {{ subject.name }}
                        </div>
                    </div>
                </t-card>
            </div>
            <!-- 右侧题目内容 -->
            <div class="question-content">
                <t-card>
                    <!-- 题目搜索和操作区 -->
                    <div class="search-bar">
                        <t-input v-model="searchKeyword" placeholder="搜索题目" class="search-input"  >
                          <template #prefix-icon>
                            <img src="/image/task/search.svg" alt="search" class="search-icon" />
                          </template>
                        </t-input>
                        <div class="filter-actions">
                            <t-select v-model="questionTypeFilter" placeholder="题型" :options="questionTypeOptions"
                                clearable />
                            <t-select v-model="difficultyFilter" placeholder="难度" :options="difficultyOptions"
                                clearable />
                        </div>
                        <div class="question-actions">
                            <t-button theme="primary" @click="addSelectedQuestions">添加选中题目</t-button>
                            <t-button theme="default" @click="selectAll">{{ allSelected ? '取消全选' : '全选' }}</t-button>
                        </div>
                    </div>
                    <!-- 题目列表区 -->
                    <div class="question-list">
                        <t-tabs :value="activeTab" @change="handleTabChange">
                            <t-tab-panel v-for="type in questionTypes" :key="type.value" :value="type.value"
                                :label="type.label">
                                <div v-if="filteredQuestions(type.value).length === 0" class="empty-questions">
                                    <t-empty description="暂无题目" />
                                </div>
                                <div v-else class="questions-wrapper">
                                    <div v-for="question in filteredQuestions(type.value)" :key="question.id"
                                        class="question-item"
                                        :class="{ selected: selectedQuestions.includes(question.id) }">
                                        <div class="question-select">
                                            <t-checkbox :value="selectedQuestions.includes(question.id)"
                                                @change="toggleSelectQuestion(question.id)" />
                                        </div>
                                        <div class="question-details" @click="viewQuestionDetail(question)">
                                            <div class="question-title">{{ question.title }}</div>
                                            <div class="question-metadata">
                                                <span class="difficulty">{{ getDifficultyText(question.difficulty)
                                                    }}</span>
                                                <span class="subject">{{ getSubjectName(question.subjectId) }}</span>
                                                <span class="score">{{ question.score }}分</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </t-tab-panel>
                        </t-tabs>
                    </div>
                    <!-- 分页 -->
                    <div class="pagination-container">
                        <t-pagination v-model="currentPage" :total="totalQuestions" :page-size="pageSize"
                            :page-size-options="[10, 20, 50]" @change="handlePageChange"
                            @page-size-change="handlePageSizeChange" />
                    </div>
                </t-card>
            </div>
        </div>

        <!-- 题目详情预览弹窗 -->
        <t-dialog v-model:visible="showQuestionDetail" :header="currentDetail ? '题目详情' : ''" width="680px"
            :footer="false">
            <div v-if="currentDetail" class="question-detail-container">
                <!-- 单选题预览 -->
                <div v-if="currentDetail.type === 'single'" class="question-preview single-choice">
                    <div class="question-header">
                        <span class="question-type-tag">单选题</span>
                        <span class="question-score">{{ currentDetail.score }}分</span>
                    </div>
                    <div class="question-title">{{ currentDetail.title }}</div>
                    <div class="options-list">
                        <div v-for="(option, index) in currentDetail.options" :key="index" class="option-item"
                            :class="{ 'correct-option': option.isCorrect }">
                            <span class="option-label">{{ String.fromCharCode(65 + index) }}.</span>
                            <span class="option-content">{{ option.content }}</span>
                            <span v-if="option.isCorrect" class="correct-marker">✓</span>
                        </div>
                    </div>
                </div>

                <!-- 其他题型预览 (多选题、判断题、填空题、简答题) -->
                <!-- 类似单选题的结构，根据题型不同显示不同内容 -->

                <div class="detail-actions">
                    <t-button theme="primary" @click="addCurrentDetailToSelection">添加此题</t-button>
                    <t-button theme="default" @click="showQuestionDetail = false">关闭</t-button>
                </div>
            </div>
        </t-dialog>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { MessagePlugin } from 'tdesign-vue-next'

const router = useRouter()
const route = useRoute()

// 从URL参数获取课程和班级ID
const courseId = ref(route.query.courseId || route.params.courseId)
const classId = ref(route.query.classId || 'all')

// 判断是考试还是作业模式
const mode = ref(route.query.mode?.toString() || 'homework')
const isExam = computed(() => mode.value === 'exam')

// 返回上一页
const goBack = () => {
    // 构建返回路径，包含课程ID和班级ID参数
    const path = isExam.value 
        ? `/teachers/course/${courseId.value}/exam` 
        : `/teachers/course/${courseId.value}/homework`
    
    // 添加班级ID查询参数（如果不是'all'）
    const query = classId.value !== 'all' ? { classId: classId.value } : {}
    
    router.push({ path, query })
}

// 学科相关
const searchSubject = ref('')
const currentSubject = ref('all')
const subjects = ref([
    { id: 'math', name: '数学' },
    { id: 'physics', name: '物理' },
    { id: 'chemistry', name: '化学' },
    { id: 'biology', name: '生物' },
    { id: 'history', name: '历史' },
    { id: 'geography', name: '地理' },
    { id: 'politics', name: '政治' },
    { id: 'english', name: '英语' },
    { id: 'chinese', name: '语文' },
    { id: 'programming', name: '编程' }
])

// 过滤学科
const filteredSubjects = computed(() => {
    if (!searchSubject.value) return subjects.value
    return subjects.value.filter(subject =>
        subject.name.toLowerCase().includes(searchSubject.value.toLowerCase())
    )
})

// 切换学科
const switchSubject = (subjectId: string) => {
    currentSubject.value = subjectId
    currentPage.value = 1 // 重置分页
}

// 题型定义
const questionTypes = [
    { label: '单选题', value: 'single' },
    { label: '多选题', value: 'multiple' },
    { label: '判断题', value: 'truefalse' },
    { label: '填空题', value: 'fillblank' },
    { label: '简答题', value: 'essay' }
]

// 难度选项
const difficultyOptions = [
    { label: '简单', value: 'easy' },
    { label: '中等', value: 'medium' },
    { label: '困难', value: 'hard' }
]

// 获取难度文本
const getDifficultyText = (difficulty: string) => {
    const map: Record<string, string> = {
        easy: '简单',
        medium: '中等',
        hard: '困难'
    }
    return map[difficulty] || '未知'
}

// 获取学科名称
const getSubjectName = (subjectId: string) => {
    const subject = subjects.value.find(s => s.id === subjectId)
    return subject ? subject.name : '未分类'
}

// 搜索和筛选
const searchKeyword = ref('')
const questionTypeFilter = ref('')
const difficultyFilter = ref('')
const activeTab = ref('single')
const questionTypeOptions = questionTypes.map(type => ({ label: type.label, value: type.value }))

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const totalQuestions = computed(() => {
    return mockQuestions.value.filter(q => {
        if (currentSubject.value !== 'all' && q.subjectId !== currentSubject.value) return false
        if (questionTypeFilter.value && q.type !== questionTypeFilter.value) return false
        if (difficultyFilter.value && q.difficulty !== difficultyFilter.value) return false
        if (searchKeyword.value && !q.title.includes(searchKeyword.value)) return false
        return true
    }).length
})

// 题目选择
const selectedQuestions = ref<string[]>([])
const allSelected = computed(() => {
    const currentQuestions = getCurrentPageQuestions()
    return currentQuestions.length > 0 && currentQuestions.every(q => selectedQuestions.value.includes(q.id))
})

// 全选/取消全选
const selectAll = () => {
    if (allSelected.value) {
        // 取消全选
        const currentQuestionIds = getCurrentPageQuestions().map(q => q.id)
        selectedQuestions.value = selectedQuestions.value.filter(id => !currentQuestionIds.includes(id))
    } else {
        // 全选
        const currentQuestionIds = getCurrentPageQuestions().map(q => q.id)
        selectedQuestions.value = [...new Set([...selectedQuestions.value, ...currentQuestionIds])]
    }
}

// 切换选择单个题目
const toggleSelectQuestion = (questionId: string) => {
    const index = selectedQuestions.value.indexOf(questionId)
    if (index > -1) {
        selectedQuestions.value.splice(index, 1)
    } else {
        selectedQuestions.value.push(questionId)
    }
}

// 添加选中题目
const addSelectedQuestions = () => {
    if (selectedQuestions.value.length === 0) {
        MessagePlugin.warning('请选择至少一道题目')
        return
    }

    // 获取选中的题目
    const selectedItems = mockQuestions.value.filter(q => selectedQuestions.value.includes(q.id))

    // 将选中题目添加到本地存储或状态管理中，以便在作业/考试编辑页面使用
    localStorage.setItem('selectedBankQuestions', JSON.stringify(selectedItems))

    // 根据模式跳转到相应页面
    MessagePlugin.success(`已选中${selectedItems.length}道题目，正在跳转...`)
    setTimeout(() => {
        if (isExam.value) {
            router.push('/teachersPaper/newtask?type=exam&fromBank=true')
        } else {
            router.push('/teachersHomeWork/newtask?type=homework&fromBank=true')
        }
    }, 1000)
}

// 题目详情预览
const showQuestionDetail = ref(false)
const currentDetail = ref<any>(null)

// 查看题目详情
const viewQuestionDetail = (question: any) => {
    currentDetail.value = question
    showQuestionDetail.value = true
}

// 将当前详情题目添加到选择中
const addCurrentDetailToSelection = () => {
    if (currentDetail.value && !selectedQuestions.value.includes(currentDetail.value.id)) {
        selectedQuestions.value.push(currentDetail.value.id)
    }
    showQuestionDetail.value = false
    MessagePlugin.success('已添加到选择列表')
}

// 处理分页变化
const handlePageChange = (newPage: number) => {
    currentPage.value = newPage
}

const handlePageSizeChange = (newSize: number) => {
    pageSize.value = newSize
    currentPage.value = 1
}

// 处理标签页变化
const handleTabChange = (value: string) => {
    activeTab.value = value
    currentPage.value = 1
}

// 获取当前页的题目
const getCurrentPageQuestions = () => {
    const filtered = mockQuestions.value.filter(q => {
        if (activeTab.value && q.type !== activeTab.value) return false
        if (currentSubject.value !== 'all' && q.subjectId !== currentSubject.value) return false
        if (questionTypeFilter.value && q.type !== questionTypeFilter.value) return false
        if (difficultyFilter.value && q.difficulty !== difficultyFilter.value) return false
        if (searchKeyword.value && !q.title.includes(searchKeyword.value)) return false
        return true
    })

    const start = (currentPage.value - 1) * pageSize.value
    const end = start + pageSize.value
    return filtered.slice(start, end)
}

// 过滤当前类型的题目
const filteredQuestions = (type: string) => {
    if (activeTab.value !== type) return []
    return getCurrentPageQuestions()
}

// 模拟题库数据
const mockQuestions = ref([
    {
        id: '1',
        type: 'single',
        title: '以下哪种数据结构适合用于实现队列？',
        content: '以下哪种数据结构适合用于实现队列？',
        subjectId: 'programming',
        difficulty: 'medium',
        score: 5,
        options: [
            { content: '栈', isCorrect: false },
            { content: '链表', isCorrect: true },
            { content: '二叉树', isCorrect: false },
            { content: '哈希表', isCorrect: false }
        ],
        blanks: [],
        referenceAnswer: '',
        answer: ''
    },
    {
        id: '2',
        type: 'multiple',
        title: '以下哪些排序算法的平均时间复杂度是O(nlogn)？',
        content: '以下哪些排序算法的平均时间复杂度是O(nlogn)？',
        subjectId: 'programming',
        difficulty: 'hard',
        score: 5,
        options: [
            { content: '冒泡排序', isCorrect: false },
            { content: '快速排序', isCorrect: true },
            { content: '归并排序', isCorrect: true },
            { content: '堆排序', isCorrect: true }
        ],
        blanks: [],
        referenceAnswer: '',
        answer: ''
    },
    // 更多题目...
])

// 初始化时多生成一些模拟题目数据
onMounted(() => {
    const subjects = ['math', 'physics', 'chemistry', 'biology', 'programming']
    const difficulties = ['easy', 'medium', 'hard']
    const types = ['single', 'multiple', 'truefalse', 'fillblank', 'essay']

    const generatedQuestions = []

    // 每个类型各生成20个题目
    for (let i = 0; i < 100; i++) {
        const type = types[i % 5]
        const subjectId = subjects[Math.floor(Math.random() * subjects.length)]
        const difficulty = difficulties[Math.floor(Math.random() * difficulties.length)]

        let question: any = {
            id: (i + 3).toString(),
            type,
            title: `${getSubjectName(subjectId)}题目 ${i + 1}：这是一道${getDifficultyText(difficulty)}的${type === 'single' ? '单选题' :
                type === 'multiple' ? '多选题' :
                    type === 'truefalse' ? '判断题' :
                        type === 'fillblank' ? '填空题' : '简答题'}`,
            content: `题目内容 ${i + 1}`,
            subjectId,
            difficulty,
            score: Math.floor(Math.random() * 10) + 1,
            options: [],
            blanks: [],
            referenceAnswer: '',
            answer: ''
        }

        // 根据题型添加特定属性
        if (type === 'single' || type === 'multiple') {
            question.options = [
                { content: '选项A', isCorrect: type === 'single' ? true : Math.random() > 0.5 },
                { content: '选项B', isCorrect: type === 'single' ? false : Math.random() > 0.5 },
                { content: '选项C', isCorrect: type === 'single' ? false : Math.random() > 0.5 },
                { content: '选项D', isCorrect: type === 'single' ? false : Math.random() > 0.5 }
            ]
        } else if (type === 'truefalse') {
            question.answer = Math.random() > 0.5 ? 'true' : 'false'
        } else if (type === 'fillblank') {
            question.blanks = [{ answer: '填空答案' }]
        } else if (type === 'essay') {
            question.referenceAnswer = '这是参考答案'
        }

        generatedQuestions.push(question)
    }

    mockQuestions.value = [...mockQuestions.value, ...generatedQuestions]
})
</script>

<style scoped>
.question-bank-container {
    padding: 20px;
}

.page-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.page-header h2 {
    margin: 0 0 0 20px;
}

.bank-content {
    display: flex;
    gap: 20px;
}

.subject-nav {
    width: 250px;
}

.search-subject {
    margin-bottom: 16px;
}

.subject-list {
    max-height: calc(100vh - 200px);
    overflow-y: auto;
}

.subject-item {
    padding: 10px 12px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s;
    margin-bottom: 4px;
}

.subject-item:hover {
    background-color: #f5f5f5;
}

.subject-item.active {
    background-color: #e6f7ff;
    color: #1890ff;
    font-weight: 500;
}

.question-content {
    flex: 1;
}

.search-bar {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    gap: 16px;
}

.search-input {
    width: 300px;
}

.filter-actions {
    display: flex;
    gap: 8px;
    flex: 1;
}

.question-actions {
    display: flex;
    gap: 8px;
}

.questions-wrapper {
    max-height: calc(100vh - 300px);
    overflow-y: auto;
}

.question-item {
    display: flex;
    padding: 12px;
    border-bottom: 1px solid #eee;
    transition: all 0.2s;
}

.question-item:hover {
    background-color: #f9f9f9;
}

.question-item.selected {
    background-color: #e6f7ff;
}

.question-select {
    margin-right: 12px;
    display: flex;
    align-items: center;
}

.question-details {
    flex: 1;
    cursor: pointer;
}

.question-title {
    margin-bottom: 8px;
    font-weight: 500;
}

.question-metadata {
    display: flex;
    gap: 16px;
    color: #999;
    font-size: 12px;
}

.difficulty,
.subject,
.score {
    background-color: rgba(0, 0, 0, 0.04);
    padding: 2px 8px;
    border-radius: 10px;
}

.pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: center;
}

.question-detail-container {
    padding: 16px;
}

.question-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;
}

.question-type-tag {
    background-color: #e6f7ff;
    color: #1890ff;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
}

.question-score {
    color: #f56c6c;
    font-weight: 500;
}

.question-title {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 16px;
}

.options-list {
    margin-bottom: 20px;
}

.option-item {
    padding: 8px 12px;
    margin-bottom: 8px;
    border-radius: 4px;
    display: flex;
    align-items: center;
}

.option-item.correct-option {
    background-color: rgba(82, 196, 26, 0.1);
}

.option-label {
    font-weight: 500;
    margin-right: 8px;
    width: 24px;
}

.option-content {
    flex: 1;
}

.correct-marker {
    color: #52c41a;
    font-weight: bold;
    margin-left: 8px;
}

.detail-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
    gap: 8px;
}

.empty-questions {
    padding: 40px 0;
    text-align: center;
}

.search-icon {
  width: 16px;
  height: 16px;
}
</style>
