# Base模块 Vue页面说明

## 全局目录结构

```
/base/
├── AcademyManagement.vue      # 学院管理
├── classes/                   # 班级管理
│   ├── ClassesDetail.vue      # 班级详情
│   └── ClassesManagement.vue  # 班级管理
├── major/                     # 专业管理
│   ├── MajorIndex.vue         # 专业首页
│   ├── MajorManagement.vue    # 专业管理
│   └── MajorOverview.vue      # 专业概览
├── standard/                  # 标准管理
│   └── GraduationStandard.vue # 毕业标准管理
├── student/                   # 学生管理
│   ├── StudentModify.vue      # 学生信息修改
│   ├── StudentQuestionList.vue # 学生问题列表
│   ├── StudentQuestionSuccess.vue # 学生问题成功页
│   ├── StudentTree.vue        # 学生树形管理
│   └── StudentUser.vue        # 学生用户管理
└── teacher/                   # 教师管理
    ├── TeacherCourseDetail.vue # 教师课程详情
    ├── TeacherDocumentSubmit.vue # 教师文档提交
    ├── TeacherGradeSubmission.vue # 教师成绩提交
    ├── TeacherHome.vue         # 教师首页
    ├── TeacherHomework.vue     # 教师作业管理
    ├── TeacherNewTask.vue      # 教师新建任务
    ├── TeacherPaperTask.vue    # 教师试卷任务
    ├── TeacherProfile.vue      # 教师个人资料
    ├── TeacherTree.vue         # 教师树形管理
    ├── TeacherWorkspace.vue    # 教师工作空间
    ├── TeacherWorkspaceDetail.vue # 教师工作空间详情
    ├── TeacherWorkspaceDetailEdit.vue # 教师工作空间编辑
    ├── TeacherWorkspaceDetailManage.vue # 教师工作空间管理
    └── TeacherWorkspaceDetailView.vue # 教师工作空间查看
```

## 模块概述

Base模块（基础数据模块）负责管理系统的基础数据，包括学院、专业、班级、学生、教师等核心业务实体的管理，为其他业务模块提供基础数据支撑。

### 数据库表对应关系

- `base_academy` - 学院表
- `base_major` - 专业表
- `base_classes` - 班级表
- `base_student` - 学生表
- `base_teacher` - 教师表
- `base_standard` - 标准表

## 功能分类

### 1. 组织架构管理
负责学院、专业、班级等组织架构的管理。

### 2. 人员管理
管理学生和教师的基本信息和相关功能。

### 3. 标准管理
管理各类教学和评价标准。

## 详细页面说明

### 根目录页面

#### /base/AcademyManagement.vue
- **功能**: 学院管理
- **描述**: 管理高校的学院信息，包括学院的创建、编辑和组织结构
- **主要功能**:
  - 学院信息管理
  - 学院层级结构
  - 学院负责人设置
  - 学院状态控制
  - 学院统计信息

### Classes目录 - 班级管理

#### /base/classes/ClassesDetail.vue
- **功能**: 班级详情
- **描述**: 展示班级的详细信息，包括学生名单、课程安排等
- **主要功能**:
  - 班级基本信息展示
  - 学生名单管理
  - 班级课程安排
  - 班级统计数据
  - 班级活动记录

#### /base/classes/ClassesManagement.vue
- **功能**: 班级管理
- **描述**: 管理班级的创建、编辑和维护
- **主要功能**:
  - 班级信息管理
  - 班级创建和编辑
  - 班主任分配
  - 学生批量导入
  - 班级状态管理

### Major目录 - 专业管理

#### /base/major/MajorIndex.vue
- **功能**: 专业首页
- **描述**: 专业管理的入口页面，提供专业管理的概览
- **主要功能**:
  - 专业概览信息
  - 快速操作入口
  - 专业统计数据
  - 最新动态展示
  - 常用功能导航

#### /base/major/MajorManagement.vue
- **功能**: 专业管理
- **描述**: 管理专业的基本信息和配置
- **主要功能**:
  - 专业信息管理
  - 专业创建和编辑
  - 专业负责人设置
  - 专业认证管理
  - 专业状态控制

#### /base/major/MajorOverview.vue
- **功能**: 专业概览
- **描述**: 展示专业的整体情况和统计信息
- **主要功能**:
  - 专业数据统计
  - 学生分布情况
  - 教师配置情况
  - 课程体系概览
  - 专业发展趋势

### Standard目录 - 标准管理

#### /base/standard/GraduationStandard.vue
- **功能**: 毕业标准管理
- **描述**: 管理专业的毕业标准和要求
- **主要功能**:
  - 毕业标准设置
  - 标准版本管理
  - 标准审核流程
  - 标准对比分析
  - 标准应用统计

### Student目录 - 学生管理

#### /base/student/StudentModify.vue
- **功能**: 学生信息修改
- **描述**: 修改学生的基本信息和学籍信息
- **主要功能**:
  - 学生基本信息编辑
  - 学籍状态变更
  - 照片上传更新
  - 联系方式维护
  - 修改历史记录

#### /base/student/StudentQuestionList.vue
- **功能**: 学生问题列表
- **描述**: 展示学生提出的问题和咨询列表
- **主要功能**:
  - 问题列表展示
  - 问题分类筛选
  - 问题状态跟踪
  - 回复管理
  - 问题统计分析

#### /base/student/StudentQuestionSuccess.vue
- **功能**: 学生问题成功页
- **描述**: 学生成功提交问题后的确认页面
- **主要功能**:
  - 提交成功确认
  - 问题编号显示
  - 后续流程说明
  - 相关链接导航
  - 反馈收集

#### /base/student/StudentTree.vue
- **功能**: 学生树形管理
- **描述**: 以树形结构展示和管理学生信息
- **主要功能**:
  - 按组织结构展示学生
  - 树形节点操作
  - 批量选择功能
  - 层级筛选
  - 快速搜索定位

#### /base/student/StudentUser.vue
- **功能**: 学生用户管理
- **描述**: 管理学生的用户账号和权限
- **主要功能**:
  - 学生账号管理
  - 密码重置
  - 权限分配
  - 登录状态监控
  - 账号状态控制

### Teacher目录 - 教师管理

#### /base/teacher/TeacherCourseDetail.vue
- **功能**: 教师课程详情
- **描述**: 展示教师负责的课程详细信息
- **主要功能**:
  - 课程基本信息展示
  - 教学计划查看
  - 学生名单管理
  - 教学资源访问
  - 课程进度跟踪

#### /base/teacher/TeacherDocumentSubmit.vue
- **功能**: 教师文档提交
- **描述**: 教师提交教学相关文档的页面
- **主要功能**:
  - 文档上传提交
  - 文档类型选择
  - 提交状态跟踪
  - 文档版本管理
  - 审核结果查看

#### /base/teacher/TeacherGradeSubmission.vue
- **功能**: 教师成绩提交
- **描述**: 教师提交学生成绩的管理页面
- **主要功能**:
  - 成绩录入界面
  - 成绩批量导入
  - 成绩审核提交
  - 成绩修改申请
  - 提交状态查看

#### /base/teacher/TeacherHome.vue
- **功能**: 教师首页
- **描述**: 教师个人工作台首页
- **主要功能**:
  - 个人工作概览
  - 待办事项提醒
  - 教学日程安排
  - 快速操作入口
  - 消息通知中心

#### /base/teacher/TeacherHomework.vue
- **功能**: 教师作业管理
- **描述**: 教师管理作业布置和批改的页面
- **主要功能**:
  - 作业创建发布
  - 作业批改管理
  - 作业统计分析
  - 学生提交情况
  - 作业模板管理

#### /base/teacher/TeacherNewTask.vue
- **功能**: 教师新建任务
- **描述**: 教师创建新的教学任务
- **主要功能**:
  - 任务信息填写
  - 任务类型选择
  - 任务计划设置
  - 资源需求配置
  - 任务提交审核

#### /base/teacher/TeacherPaperTask.vue
- **功能**: 教师试卷任务
- **描述**: 教师管理试卷相关任务
- **主要功能**:
  - 试卷任务列表
  - 试卷创建编辑
  - 考试安排管理
  - 阅卷进度跟踪
  - 成绩发布管理

#### /base/teacher/TeacherProfile.vue
- **功能**: 教师个人资料
- **描述**: 教师个人信息管理页面
- **主要功能**:
  - 个人信息维护
  - 教学履历管理
  - 学术成果记录
  - 照片头像更新
  - 联系方式管理

#### /base/teacher/TeacherTree.vue
- **功能**: 教师树形管理
- **描述**: 以树形结构展示和管理教师信息
- **主要功能**:
  - 按部门展示教师
  - 树形节点操作
  - 教师层级管理
  - 批量操作功能
  - 快速搜索定位

#### /base/teacher/TeacherWorkspace.vue
- **功能**: 教师工作空间
- **描述**: 教师的个人工作空间主页
- **主要功能**:
  - 工作空间概览
  - 个人文件管理
  - 工作进度跟踪
  - 协作工具访问
  - 个性化设置

#### /base/teacher/TeacherWorkspaceDetail.vue
- **功能**: 教师工作空间详情
- **描述**: 工作空间的详细信息展示
- **主要功能**:
  - 工作空间详情展示
  - 项目进度查看
  - 文件资源浏览
  - 协作记录查看
  - 空间使用统计

#### /base/teacher/TeacherWorkspaceDetailEdit.vue
- **功能**: 教师工作空间编辑
- **描述**: 编辑工作空间的配置和内容
- **主要功能**:
  - 工作空间配置编辑
  - 权限设置管理
  - 内容组织调整
  - 布局自定义
  - 设置保存应用

#### /base/teacher/TeacherWorkspaceDetailManage.vue
- **功能**: 教师工作空间管理
- **描述**: 工作空间的管理和维护功能
- **主要功能**:
  - 空间成员管理
  - 权限分配设置
  - 资源配额管理
  - 使用情况监控
  - 空间维护操作

#### /base/teacher/TeacherWorkspaceDetailView.vue
- **功能**: 教师工作空间查看
- **描述**: 工作空间的只读查看模式
- **主要功能**:
  - 只读模式浏览
  - 内容预览查看
  - 文件下载访问
  - 信息检索查询
  - 使用记录查看

## 与其他模块的关系

### 与System模块的关系
- 使用System模块的用户权限管理
- 基础数据的权限控制依赖System模块
- 用户账号信息与System模块关联

### 与Training模块的关系
- 为Training模块提供专业、学院等基础数据
- 培养方案基于Base模块的组织结构
- 课程体系与专业信息关联

### 与Assessment模块的关系
- 为Assessment模块提供学生、教师等基础数据
- 考核评价基于Base模块的人员信息
- 成绩数据按Base模块的组织结构统计

### 与Task模块的关系
- 为Task模块提供教师、学生、班级等基础数据
- 教学任务基于Base模块的人员和组织信息
- 任务分配按Base模块的结构进行

## 目录结构规范

### 文件命名规范
- 所有Vue文件使用PascalCase命名法
- 文件名应体现页面功能
- 组件名称应与文件名保持一致

### 功能分类体系
1. **组织管理**: 学院、专业、班级等组织架构管理
2. **人员管理**: 学生、教师等人员信息管理
3. **标准管理**: 各类教学和评价标准管理
4. **工作空间**: 教师个人工作环境管理

## 注意事项

1. **数据一致性**: 基础数据的变更需要考虑对其他模块的影响
2. **权限控制**: 不同角色对基础数据的操作权限要严格控制
3. **数据完整性**: 确保基础数据的完整性和准确性
4. **性能优化**: 大量基础数据的查询和展示需要优化性能
5. **数据备份**: 重要的基础数据需要定期备份保护 