# ETL模块 Vue页面说明

## 全局目录结构

```
/etl/
└── data/                      # 数据管理
    └── DataManagement.vue     # 数据管理
```

## 模块概述

ETL模块（数据抽取转换加载模块）负责系统数据的导入、导出、转换和同步等功能，是系统与外部数据源交互的重要通道。

### 数据库表对应关系

- `etl_task` - ETL任务表
- `etl_log` - ETL执行日志表
- `etl_config` - ETL配置表

## 功能分类

### 1. 数据管理 (data/)
负责数据的抽取、转换、加载和管理。

## 详细页面说明

### Data目录 - 数据管理

#### /etl/data/DataManagement.vue
- **功能**: 数据管理
- **描述**: 管理系统的数据导入导出、数据转换和数据同步功能
- **主要功能**:
  - 数据导入导出
  - 数据格式转换
  - 数据清洗和验证
  - 数据同步任务
  - 数据质量监控

## 页面功能详细说明

### 数据管理功能特点
1. **多格式支持**: 支持Excel、CSV、JSON、XML等多种数据格式
2. **数据验证**: 自动验证数据格式和完整性
3. **批量处理**: 支持大批量数据的导入导出
4. **增量同步**: 支持增量数据同步，提高效率
5. **错误处理**: 完善的错误处理和回滚机制

### 数据转换功能特点
1. **字段映射**: 灵活的字段映射和转换规则
2. **数据清洗**: 自动清理无效和重复数据
3. **格式标准化**: 统一数据格式和编码标准
4. **业务规则**: 应用业务规则进行数据校验
5. **转换日志**: 详细的转换过程日志记录

### 数据同步功能特点
1. **定时任务**: 支持定时自动同步数据
2. **实时同步**: 支持实时数据同步
3. **冲突处理**: 智能处理数据冲突和重复
4. **状态监控**: 实时监控同步状态和进度
5. **异常报警**: 同步异常时自动报警通知

## 支持的数据源

### 文件数据源
- Excel文件(.xlsx, .xls)
- CSV文件(.csv)
- JSON文件(.json)
- XML文件(.xml)
- 文本文件(.txt)

### 数据库数据源
- MySQL数据库
- PostgreSQL数据库
- Oracle数据库
- SQL Server数据库

### 外部系统
- 教务管理系统
- 学籍管理系统
- 其他OBE系统

## 与其他模块的关系

### 与Base模块的关系
- 为Base模块提供基础数据的导入功能
- 同步学生、教师、班级等基础信息

### 与Training模块的关系
- 导入培养方案和课程体系数据
- 同步教学计划和课程安排

### 与Assessment模块的关系
- 导入题库和考核数据
- 同步成绩和评价结果

### 与System模块的关系
- 提供系统配置数据的备份恢复
- 同步用户权限和菜单配置

## 数据处理流程

### 数据导入流程
1. **文件上传**: 用户上传数据文件
2. **格式检查**: 验证文件格式和结构
3. **数据预览**: 预览待导入的数据
4. **字段映射**: 配置字段映射关系
5. **数据验证**: 验证数据的完整性和正确性
6. **执行导入**: 执行数据导入操作
7. **结果反馈**: 显示导入结果和错误信息

### 数据导出流程
1. **条件设置**: 设置导出条件和范围
2. **格式选择**: 选择导出文件格式
3. **字段选择**: 选择要导出的字段
4. **数据生成**: 生成导出数据
5. **文件下载**: 提供文件下载链接

### 数据同步流程
1. **连接配置**: 配置数据源连接信息
2. **同步规则**: 设置同步规则和策略
3. **执行同步**: 执行数据同步任务
4. **状态监控**: 监控同步进度和状态
5. **结果处理**: 处理同步结果和异常

## 目录结构规范

### 文件命名规范
- 所有Vue文件使用PascalCase命名法
- 文件名应体现页面功能
- 组件名称应与文件名保持一致

### 功能分类体系
1. **数据导入**: 外部数据的导入和转换
2. **数据导出**: 系统数据的导出和备份
3. **数据同步**: 与外部系统的数据同步
4. **数据监控**: 数据质量和处理状态监控

## 技术特点

### 性能优化
- 分批处理大数据量
- 异步处理避免页面阻塞
- 进度条显示处理进度
- 内存优化避免内存溢出

### 安全性
- 文件类型验证
- 数据脱敏处理
- 访问权限控制
- 操作日志记录

## 注意事项

1. **数据安全**: 敏感数据的加密和脱敏处理
2. **性能考虑**: 大数据量处理时的性能优化
3. **错误处理**: 完善的错误处理和恢复机制
4. **日志记录**: 详细的操作日志便于问题追踪
5. **权限控制**: 严格的数据访问权限控制 