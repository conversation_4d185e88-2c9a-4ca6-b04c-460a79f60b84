export default {
  title: '概览仪表盘',
  outputOverview: {
    title: '出入库概览',
    subtitle: '(件)',
    export: '导出数据',
    month: {
      input: '本月入库总计（件）',
      output: '本月出库总计（件）',
    },
    since: '自从上周以来',
  },
  rankList: {
    title: '销售订单排名',
    week: '本周',
    month: '近三月',
    info: '详情',
  },
  topPanel: {
    card1: '总收入',
    card2: '总退款',
    card3: '活跃用户（个）',
    card4: '产生订单（个）',
    cardTips: '自从上周以来',
    analysis: {
      title: '统计数据',
      unit: '万元',
      series1: '本月',
      series2: '上月',
      channels: '销售渠道',
      channel1: '线上',
      channel2: '门店',
      channelTips: '销售占比',
    },
  },
  saleColumns: {
    index: '排名',
    productName: '客户名称',
    growUp: '较上周',
    count: '订单量',
    operation: '操作',
  },
  buyColumns: {
    index: '排名',
    productName: '供应商名称',
    growUp: '较上周',
    count: '订单量',
    operation: '操作',
  },
  chart: {
    week1: '周一',
    week2: '周二',
    week3: '周三',
    week4: '周四',
    week5: '周五',
    week6: '周六',
    week7: '周日',
    max: '最大值',
    min: '最小值',
    thisMonth: '本月',
    lastMonth: '上月',
  },
};
