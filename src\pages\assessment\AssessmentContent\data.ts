// 课程基本信息模拟数据
export const mockCourseBasicInfo = {
  courseId: '1',
  courseName: 'Java程序设计',
  courseCode: 'RB7001152',
  courseCredit: 4,
  semester: '2024-2025学年第1学期',
  academicYear: '2024-2025',
  teacherId: 'teacher001',
  teacherName: '张教授',
  classId: 'class001',
  className: '计算机科学与技术2022级1班',
  studentCount: 45,
  createTime: '2024-09-01 08:00:00',
  updateTime: '2024-12-01 10:30:00'
};

// 考核环节模拟数据
export const mockAssessmentSections = [
  {
    id: '1',
    courseId: '1',
    name: '平时作业',
    type: '作业',
    weight: 30,
    description: '课后练习作业，包含编程题和理论题',
    status: 'published',
    inputMode: 'detailed',
    sortOrder: 1,
    createTime: '2024-10-01 09:00:00',
    updateTime: '2024-11-01 14:20:00',
    creatorId: 'teacher001',
    creatorName: '张教授',
    objectiveDistribution: [
      { objectiveId: 'obj1', name: '课程目标1', percentage: 40 },
      { objectiveId: 'obj2', name: '课程目标2', percentage: 60 }
    ]
  },
  {
    id: '2',
    courseId: '1',
    name: '阶段测验',
    type: '阶段性测验',
    weight: 20,
    description: '期中阶段性测验，检验学习效果',
    status: 'draft',
    inputMode: 'direct',
    sortOrder: 2,
    createTime: '2024-10-15 10:30:00',
    updateTime: '2024-11-15 16:45:00',
    creatorId: 'teacher001',
    creatorName: '张教授',
    objectiveDistribution: [
      { objectiveId: 'obj1', name: '课程目标1', percentage: 50 },
      { objectiveId: 'obj2', name: '课程目标2', percentage: 50 }
    ]
  },
  {
    id: '3',
    courseId: '1',
    name: '期末考试',
    type: '期末考试',
    weight: 50,
    description: '期末综合考试',
    status: 'published',
    inputMode: 'detailed',
    sortOrder: 3,
    createTime: '2024-10-20 16:45:00',
    updateTime: '2024-12-01 09:15:00',
    creatorId: 'teacher001',
    creatorName: '张教授',
    objectiveDistribution: [
      { objectiveId: 'obj1', name: '课程目标1', percentage: 30 },
      { objectiveId: 'obj2', name: '课程目标2', percentage: 70 }
    ]
  }
];

// 课程目标模拟数据
export const mockCourseObjectives = [
  {
    id: 'obj1',
    courseId: '1',
    objectiveCode: '课程目标1',
    objectiveName: '掌握Java基础语法',
    description: '掌握JAVA程序设计语法，能够按照要求编写程序，运行出正确的结果。',
    weight: 40,
    graduationRequirement: '毕业要求3：设计/开发解决方案',
    knowledgePoints: ['变量与数据类型', '控制结构', '数组', '方法'],
    sortOrder: 1,
    status: 'active',
    createTime: '2024-09-01 08:00:00',
    updateTime: '2024-09-01 08:00:00'
  },
  {
    id: 'obj2',
    courseId: '1',
    objectiveCode: '课程目标2',
    objectiveName: '理解面向对象思想',
    description: '理解面向对象程序设计思想，完成软件需求的抽象分析，设计合理的软件类图。',
    weight: 60,
    graduationRequirement: '毕业要求4：研究',
    knowledgePoints: ['类与对象', '继承', '多态', '封装', '接口'],
    sortOrder: 2,
    status: 'active',
    createTime: '2024-09-01 08:00:00',
    updateTime: '2024-09-01 08:00:00'
  }
];

// 考核内容模拟数据
export const mockAssessmentContents = [
  {
    id: 'content1',
    sectionId: '1',
    courseId: '1',
    name: '第一次作业',
    description: 'Java基础语法练习',
    source: 'teacher',
    sourceInfo: {
      type: '教师自建',
      creatorId: 'teacher001',
      creatorName: '张教授'
    },
    inputMode: 'detailed',
    examTime: {
      startTime: '2024-11-15 08:00:00',
      endTime: '2024-11-15 18:00:00',
      duration: 600
    },
    status: 'published',
    publishTime: '2024-11-15 10:30:00',
    totalScore: 100,
    passScore: 60,
    configStatus: 'configured',
    createTime: '2024-10-01 09:00:00',
    updateTime: '2024-11-15 10:30:00',
    creatorId: 'teacher001',
    creatorName: '张教授'
  },
  {
    id: 'content2',
    sectionId: '1',
    courseId: '1',
    name: '第二次作业',
    description: '面向对象编程练习',
    source: 'teacher',
    sourceInfo: {
      type: '教师自建',
      creatorId: 'teacher001',
      creatorName: '张教授'
    },
    inputMode: 'direct',
    examTime: {
      startTime: '2024-11-22 08:00:00',
      endTime: '2024-11-22 18:00:00',
      duration: 600
    },
    status: 'draft',
    totalScore: 100,
    passScore: 60,
    configStatus: 'partially_configured',
    createTime: '2024-10-08 10:30:00',
    updateTime: '2024-11-20 14:15:00',
    creatorId: 'teacher001',
    creatorName: '张教授'
  },
  {
    id: 'content3',
    sectionId: '2',
    courseId: '1',
    name: '期中测验',
    description: '第1-6章内容测验',
    source: 'unified',
    sourceInfo: {
      type: '统一发放'
    },
    inputMode: 'detailed',
    examTime: {
      startTime: '2024-11-20 14:00:00',
      endTime: '2024-11-20 16:00:00',
      duration: 120
    },
    status: 'published',
    publishTime: '2024-11-20 14:00:00',
    totalScore: 100,
    passScore: 60,
    configStatus: 'configured',
    createTime: '2024-10-15 14:20:00',
    updateTime: '2024-11-20 13:45:00',
    creatorId: 'admin001',
    creatorName: '系统管理员'
  },
  {
    id: 'content4',
    sectionId: '3',
    courseId: '1',
    name: '期末考试',
    description: '综合考试',
    source: 'unified',
    sourceInfo: {
      type: '统一发放'
    },
    inputMode: 'detailed',
    examTime: {
      startTime: '2024-12-25 09:00:00',
      endTime: '2024-12-25 11:00:00',
      duration: 120
    },
    status: 'published',
    publishTime: '2024-12-25 09:00:00',
    totalScore: 100,
    passScore: 60,
    configStatus: 'configured',
    createTime: '2024-10-20 16:45:00',
    updateTime: '2024-12-20 10:00:00',
    creatorId: 'admin001',
    creatorName: '系统管理员'
  }
];

// 班级模拟数据
export const mockClassList = [
  {
    classId: 'class_001',
    className: '计算机科学与技术2022级1班',
    majorId: 'major_001',
    majorName: '计算机科学与技术',
    studentCount: 45,
    scheduleInfo: {
      classTime: '周一 1-2节',
      classroom: '教学楼A-101',
      weekday: '周一',
      timeSlot: '08:00-09:40'
    },
    teacherInfo: {
      teacherId: 'teacher_001',
      teacherName: '张教授',
      role: '主讲教师'
    },
    publishStatus: 'not_published',
    entranceYear: '2022',
    classStatus: 'active',
    createTime: '2024-09-01 08:00:00',
    updateTime: '2024-11-15 10:30:00'
  },
  {
    classId: 'class_002',
    className: '计算机科学与技术2022级2班',
    majorId: 'major_001',
    majorName: '计算机科学与技术',
    studentCount: 42,
    scheduleInfo: {
      classTime: '周一 3-4节',
      classroom: '教学楼A-102',
      weekday: '周一',
      timeSlot: '10:00-11:40'
    },
    teacherInfo: {
      teacherId: 'teacher_001',
      teacherName: '张教授',
      role: '主讲教师'
    },
    publishStatus: 'published',
    publishTime: '2024-11-10 09:00:00',
    entranceYear: '2022',
    classStatus: 'active',
    createTime: '2024-09-01 08:00:00',
    updateTime: '2024-11-15 10:30:00'
  },
  {
    classId: 'class_003',
    className: '软件工程2022级1班',
    majorId: 'major_002',
    majorName: '软件工程',
    studentCount: 38,
    scheduleInfo: {
      classTime: '周二 1-2节',
      classroom: '教学楼B-201',
      weekday: '周二',
      timeSlot: '08:00-09:40'
    },
    teacherInfo: {
      teacherId: 'teacher_002',
      teacherName: '李副教授',
      role: '助教'
    },
    publishStatus: 'not_published',
    entranceYear: '2022',
    classStatus: 'active',
    createTime: '2024-09-01 08:00:00',
    updateTime: '2024-11-15 10:30:00'
  },
  {
    classId: 'class_004',
    className: '软件工程2022级2班',
    majorId: 'major_002',
    majorName: '软件工程',
    studentCount: 40,
    scheduleInfo: {
      classTime: '周二 3-4节',
      classroom: '教学楼B-202',
      weekday: '周二',
      timeSlot: '10:00-11:40'
    },
    teacherInfo: {
      teacherId: 'teacher_002',
      teacherName: '李副教授',
      role: '助教'
    },
    publishStatus: 'published',
    publishTime: '2024-11-12 14:00:00',
    entranceYear: '2022',
    classStatus: 'active',
    createTime: '2024-09-01 08:00:00',
    updateTime: '2024-11-15 10:30:00'
  },
  {
    classId: 'class_005',
    className: '网络工程2022级1班',
    majorId: 'major_003',
    majorName: '网络工程',
    studentCount: 35,
    scheduleInfo: {
      classTime: '周三 1-2节',
      classroom: '教学楼C-301',
      weekday: '周三',
      timeSlot: '08:00-09:40'
    },
    teacherInfo: {
      teacherId: 'teacher_003',
      teacherName: '王讲师',
      role: '主讲教师'
    },
    publishStatus: 'ended',
    publishTime: '2024-10-15 10:00:00',
    entranceYear: '2022',
    classStatus: 'active',
    createTime: '2024-09-01 08:00:00',
    updateTime: '2024-11-15 10:30:00'
  },
  {
    classId: 'class_006',
    className: '计算机科学与技术2023级1班',
    majorId: 'major_001',
    majorName: '计算机科学与技术',
    studentCount: 48,
    scheduleInfo: {
      classTime: '周四 1-2节',
      classroom: '教学楼A-103',
      weekday: '周四',
      timeSlot: '08:00-09:40'
    },
    teacherInfo: {
      teacherId: 'teacher_001',
      teacherName: '张教授',
      role: '主讲教师'
    },
    publishStatus: 'not_published',
    entranceYear: '2023',
    classStatus: 'active',
    createTime: '2024-09-01 08:00:00',
    updateTime: '2024-11-15 10:30:00'
  }
];

// 兼容性数据（临时保留）
export const courseList = [
  {
    courseId: '1',
    courseName: 'Java程序设计',
    courseCode: 'RB7001152',
    courseCredit: 4,
    assessmentMethods: [
      { methodId: '1', methodName: '作业', weight: 30, isFinalExam: false },
      { methodId: '2', methodName: '阶段性测验', weight: 20, isFinalExam: false },
      { methodId: '3', methodName: '期末考试', weight: 50, isFinalExam: true }
    ],
    courseObjectives: [
      {
        objectiveId: 'obj1',
        number: '课程目标1',
        objectiveName: '掌握Java基础语法',
        description: '掌握JAVA程序设计语法，能够按照要求编写程序，运行出正确的结果。',
        weight: 40,
        graduationRequirement: '毕业要求3：设计/开发解决方案'
      },
      {
        objectiveId: 'obj2',
        number: '课程目标2',
        objectiveName: '理解面向对象思想',
        description: '理解面向对象程序设计思想，完成软件需求的抽象分析，设计合理的软件类图。',
        weight: 60,
        graduationRequirement: '毕业要求4：研究'
      }
    ]
  }
]; 