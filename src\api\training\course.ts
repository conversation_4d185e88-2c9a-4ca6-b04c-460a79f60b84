import request from '@/utils/request';
import {courseList, homeInfo} from '@/api/mock/courseMockData';
import { PoVO } from '@/api/training/po';


// 数据接口定义
const BASE_URL = '/tp/course'; // Ignored /api/

export interface CourseCacheInfo {
  courseId: string;
  courseName: string;
  courseCode: string;
  planId: string;
  majorId?: string; // 可选，专业ID
  courseLeader?: string; // 可选，课程负责人ID
}

export interface AssessmentMethod {
  id: string;
  name: string;
  weight: Number; // 考核方式的权重
  //typeName: String; // 考核方式的名称
  isCustom: boolean, // 从后端加载的数据默认为非自定义
  //typeId: String; // 考核方式的序列号
}

// 课程考核计划相关接口(CourseAssessmentPlan页面)
export interface CourseObjectiveVO {
  objectiveId: number;
  number: number; // 课程目标序号
  objectiveName: string;
  expectedScore: number; // 预期达成度
  description?: string; // 课程目标描述
  assessmentMethods?: AssessmentMethod[]; // 考核方式列表
  po: PoVO; // 对应的毕业要求
}



/**
 * 对应后端：Objectiveweight
 * 课程目标权重分配-考核配置内部数据结构
 */
export interface CourseObjectiveWeight {
  objectiveId: string; // 课程目标ID
  objectiveName: string; // 课程目标名称
  expectedScore: number; // 预期达成度
  description?: string; // 课程目标描述
  weight: number; // 权重分配

}

// 考核配置数据结构：对应后端结构：AssessmentConfigVO
export interface CourseAssessmentConfig {
  methodId: string; // 考核环节ID
  methodName: string; // 考核环节名称
  examWeight: number; // 权重
  description?: string; // 考核环节描述
  objectiveList?: CourseObjectiveWeight[]; // 当前考核方式下的不同课程目标对应的权重（占比）配置
  isFinalExam?: boolean; // 是否为期末考试
//  weightsTable?: Record<string, Record<string,number> | number>; // 每个环节不同课程目标的权重
}

/**
 * 课程考核计划详情：对应后端接口CourseAssessmentDetailVO（CourseAssessmentDataDTO）
 */
export interface CourseAssessmentDetailVO {
  courseId: number;
  courseName: string;
  courseObjectiveList: CourseObjectiveVO[];
  assessmentMethods: AssessmentMethod[]; // 考核方式列表
  assessmentWeight: CourseAssessmentConfig[]; // 权重分配
  assessmentProportions: CourseAssessmentConfig[]; // 占比分配
  finalExamWeights: CourseAssessmentConfig[]; // 期末考试权重分配
}


export interface BookDetail {
  isbn?: string;
  name: string;
  author: string;
  publisher: string;
  version?: string;
  publishDate: string;
  type?: 'MAIN' | 'REFERENCE';
  description?: string;
  price?: number;
  coverUrl?: string;
  applicableScope?: string;
}

// 详细课程信息接口 - 用于课程详情对话框
export interface CourseDetailInfo {
  courseId: number;
  courseName: string;
  courseCode: string;
  courseLeader: number;
  courseCredit: number;
  courseCore: boolean;
  courseExam: boolean;
  courseHoursTotal: number;
  courseHoursTheory: number;
  courseHoursExperiment: number;
  courseHoursOther: number;
  courseHoursExtracurricular: number;
  courseSemester: string;
  courseType1: string; // 本专业课程类型（专业基础课、个性化发展课等）
  courseType2: string; // 专业认证课程类型
  courseType3: string; // 国标课程类别
  courseNature: string; // 课程性质（必修、选修、限选等）
  courseVersion: number; // 所属培养方案的版本
  planId?: number; // 所属培养方案ID（可选）
  // 解析后的数据（前端使用）
  courseObjectives?: CourseObjectiveVO[];
  assessmentMethodList?: AssessmentMethod[];
  courseBookVO?: {
    mainBook?: BookDetail; // 主教材
    referenceBooks?: BookDetail[]; // 参考书目列表
  };
}

export interface CoursesByLeaderAndMajorResponse {
  courses: CourseDetailInfo[];
  totalCount: number;
}

/**
 * 获取课程负责人管理的课程列表
 * @param majorId 查询参数，可包含专业ID
 * @returns Promise<CoursesByLeaderAndMajorResponse>
 */
export function getCoursesByLeaderAndMajor(majorId: number) : Promise<CoursesByLeaderAndMajorResponse> {
  return request({
    url: `${BASE_URL}/findByLeaderAndMajor/${majorId}`,
    method: 'get',
  });
}

// /**
//  * 查询专业的课程列表（只包含基本信息，不包括课程考核详情详细信息）
//  * @param majorId 查询参数，可包含专业ID
//  * @returns Promise<CourseDetailInfo>
//  */
// export function findCourseBaseInfoList(params: any) : Promise<CourseDetailInfo[]> {
//   return request({
//     url: `${BASE_URL}/base-info/list`,
//     method: 'post',
//     params: params
//   });
// }

/**
 * /base-info/{courseId}
 * courseInfo的基本信息查询接口
 * @param courseId 课程ID
 * @returns Promise<CourseDetailInfo>
 * 这个接口只返回课程的基本信息，不包括课程考核详情
 * 主要用于课程列表的基本信息展示
 */
export function getCourseBaseInfo(courseId: number) : Promise<any> {
  return request({
    url: `${BASE_URL}/base-info/${courseId}`,
    method: 'get',
  });
}

/**
 * 分页查询课程列表
 * @param params 查询参数
 * @returns Promise<any>
 */
export function getCourseList(params: any): Promise<any> {
  return request({
    url: `${BASE_URL}/list`,
    method: 'post',
    data: params
  });
}

export function getCourseStatistics(majorId: number, planId: number): Promise<any> {
  return request({
    url: `${BASE_URL}/statistics/${majorId}/${planId}`,
    method: 'get'
  });
}

export function getInstructorStats(majorId: number, planId: number): Promise<any> {
  return request({
    url: `${BASE_URL}/instructor-stats/${majorId}/${planId}`,
    method: 'get'
  });
}

export function getCourseListByLeaderAndPlan(params: any): Promise<any> {
  return request({
    url: `${BASE_URL}/findByLeaderAndPlan`,
    method: 'post',
    data: params
  });
}

/**
 * 添加课程
 * @param data 课程数据
 * @returns Promise<any>
 */
export function addCourse(data: any): Promise<any> {
  return request({
    url: `${BASE_URL}/add`,
    method: 'post',
    data
  });
}

/**
 * 更新课程
 * @param data 课程数据
 * @returns Promise<any>
 */
export function updateCourse(data: any): Promise<any> {
  return request({
    url: `${BASE_URL}/mod`,
    method: 'put',
    data
  });
}

export function updateCourseLeader(data: any) {
  return request({
    url: `${BASE_URL}/leader`,
    method: 'put',
    data
  });
}

/**
 * 删除课程（软删除）
 * @param courseId 课程ID
 * @returns Promise<any>
 */
export function deleteCourse(courseId: number | string): Promise<any> {
  return request({
    url: `${BASE_URL}/del?courseId=${courseId}`,
    method: 'delete'
  });
}


/**
 * 更新课程目标 - POST请求（查询参数+请求体）
 * @param courseId 课程ID
 * @param target 课程目标数据（JSON格式）
 * @returns Promise<any>
 */
export function updateCourseTarget(courseId: number, coList: CourseAssessmentDetailVO): Promise<any> {
 // console.log('更新课程目标:', { courseId, target });
  // 确保 courseId 是数字类型
  if (typeof courseId !== 'number') {
    courseId = Number(courseId);
    if (isNaN(courseId)) {
      throw new Error('Invalid courseId: ' + courseId);
    }
  }
  return request<CourseAssessmentDetailVO>({
    url: `${BASE_URL}/updateTarget/${courseId}`,
    method: 'post',
    data: coList // 直接传递对象
  });
}

export function updateCourseAssessmentMethod(courseId: number, assessmentMethod: string): Promise<any> {
  // 确保 courseId 是数字类型
  if (typeof courseId !== 'number') {
    courseId = Number(courseId);
    if (isNaN(courseId)) {
      throw new Error('Invalid courseId: ' + courseId);
    }
  }
  return request({
    url: `${BASE_URL}/updateAssessmentMethod/${courseId}`,
    method: 'post',
    data: {
      assessmentMethod: assessmentMethod
    }
  });
}

/**
 * （暂未使用）
 * 获取课程详情，不做任何处理，直接返回API数据
 * 这个接口主要用于课程列表的详情查看，不需要解析JSON字段
 * @param courseId 课程ID
 * @returns Promise<any>
 */
export function getCourseDetailById(courseId: number | string): Promise<any> {
  return request({
    url: `${BASE_URL}/${courseId}`,
    method: 'get'
  });
}
/**CourseTarget已使用，CourseDetailsDialog也调用这个
 * 获取课程详细信息 - 用于课程详情对话框，CourseTarget也调用这个
 * @param courseId 课程ID
 */
export async function getCourseDetailInfo(courseId: number | string) {
  try {
    const res = await request({
      url: `${BASE_URL}/${courseId}`,
      method: 'get'
    });

    if (res.code === 200) {
      const courseData = res.data;
      return courseData;
    }
    throw new Error(res?.message || '获取课程详细信息失败');
  } catch (error) {
    console.error('获取课程详细信息失败:', error);
    return {
      courseId: courseId,
      //空数据
      courseName: '',
      courseCode: '',
      courseLeader: '',
      courseCredit: 0,
      courseCore: false,
      courseExam: false,
      courseHoursTotal: 0,
      courseHoursTheory: 0,
      courseHoursExperiment: 0,
      courseHoursOther: 0,
      courseHoursExtracurricular: 0,
      courseSemester: '',
      courseType1: '',
      courseType2: '',
      courseType3: '',
      courseNature: '',
      courseVersion: 0,
      courseObjectives: [],
      assessmentMethodList: [],
      courseBookVO: {
        mainBook: {
          isbn: '',
          name: '', 
          author: '', 
          publisher: '', 
          version: '', 
          publishDate: '', 
          type: 'MAIN', 
          description: '', 
          price: 0, 
          coverUrl: '', 
          applicableScope: ''
        },
        referenceBooks: []
      }
    };
  }
}
/**
 * 获取课程目标列表
 * @param courseId 课程ID
 * @returns Promise<any>（List<CourseObjectiveVO>，只含有课程目标的基本信息，没有课程的考核）
 */
export async function getCourseTargetList(courseId: number): Promise<CourseObjectiveVO[]> {
  try {
    const res = await request({
      url: `${BASE_URL}/objectives/${courseId}`,
      method: 'get'
    }); // 返回类型为 List<CourseObjectiveVO>
    if (res.code === 200) {
      return res.data;
    }
    throw new Error(res?.message || '获取课程目标列表失败');
  } catch (error) {
    console.error('获取课程目标列表失败:', error);
    throw error;
  }
}


// 获取课程考核计划详情
export async function getCourseAssessmentDetail(courseId: number): Promise<CourseAssessmentDetailVO> {
  try {
    const res = await request({
      url: `${BASE_URL}/assessment-detail/${courseId}`,
      method: 'get'
    }) as ApiResponse<CourseAssessmentDetailVO>;

    if (res.code === 200) {
      return res.data;
    }
    throw new Error(res?.message || '获取课程考核计划详情失败');
  } catch (error) {
    console.error('获取课程考核计划详情失败:', error);
    throw error;
  }
}

// 保存课程考核方式
export async function saveCourseAssessmentMethod(courseId: string | number, assessmentMethodJson: string): Promise<any> {
  try {
    // 直接发送JSON字符串，不添加外层包装
    const res = await request({
      url: `${BASE_URL}/assessment-method/${courseId}`,
      method: 'put',
      data: assessmentMethodJson,
      headers: {
        'Content-Type': 'application/json'
      }
    }) as ApiResponse<any>;

    if (res.code === 200) {
      return res.data;
    }
    throw new Error(res?.message || '保存考核方式失败');
  } catch (error) {
    console.error('保存考核方式失败:', error);
    throw error;
  }
}

// 保存考核权重分配
export async function saveCourseAssessmentWeight(courseId: string | number, assessmentWeightJson: string): Promise<any> {
  try {
    // 直接发送JSON字符串，不添加外层包装
    const res = await request({
      url: `${BASE_URL}/assessment-weight/${courseId}`,
      method: 'put',
      data: assessmentWeightJson,
      headers: {
        'Content-Type': 'application/json'
      }
    }) as ApiResponse<any>;

    if (res.code === 200) {
      return res.data;
    }
    throw new Error(res?.message || '保存考核权重分配失败');
  } catch (error) {
    console.error('保存考核权重分配失败:', error);
    throw error;
  }
}

// 获取课程考核占比配置
export async function getCourseAssessmentProportion(courseId: number): Promise<any> {
  try {
    const res = await request({
      url: `${BASE_URL}/assessment-proportion/${courseId}`,
      method: 'get'
    }) as ApiResponse<any>;

    if (res.code === 200) {
      return res.data;
    }
    throw new Error(res?.message || '获取课程考核占比配置失败');
  } catch (error) {
    console.error('获取课程考核占比配置失败:', error);
    throw error;
  }
}

// 保存考核占比分配
export async function saveCourseAssessmentProportion(courseId: string | number, assessmentProportionJson: string): Promise<any> {
  try {
    // 直接发送JSON字符串，不添加外层包装
    const res = await request({
      url: `${BASE_URL}/assessment-proportion/${courseId}`,
      method: 'put',
      data: assessmentProportionJson,
      headers: {
        'Content-Type': 'application/json'
      }
    }) as ApiResponse<any>;

    if (res.code === 200) {
      return res.data;
    }
    throw new Error(res?.message || '保存考核占比分配失败');
  } catch (error) {
    console.error('保存考核占比分配失败:', error);
    throw error;
  }
};

// 保存考核配置详情
export async function saveCourseAssessmentConfig(courseId: number, assessmentConfig: any): Promise<any> {
  try {
    // 直接发送JSON字符串，不添加外层包装
    const res = await request({
      url: `${BASE_URL}/assessment-config/${courseId}`,
      method: 'put',
      data: assessmentConfig
    }) as ApiResponse<any>;

    if (res.code === 200) {
      return res.data;
    }
    throw new Error(res?.message || '保存考核配置失败');
  } catch (error) {
    console.error('保存考核配置失败:', error);
    throw error;
  }
};

//======================张炎峰===============================

/**
 * 获取教师学期课程统计
 * @param year 学年，可为空，后端默认当前年份
 * @param termType 学期类型（0: 春季, 1: 秋季），可为空，后端默认根据当前月份推断
 * @returns Promise<any>
 */
export function getTeacherSemesterCourses(year?: number, termType?: number): Promise<any> {
  const yearParam = year !== undefined ? year : '';
  const termTypeParam = termType !== undefined ? termType : '';

  return request({
    url: `${BASE_URL}/teacher/semester/static/${yearParam}/${termTypeParam}`,
    method: 'get'
  });
}

/**
 * 获取课程授课历史统计
 * @returns Promise<any>
 */
export function getTeachingHistoryCourses(): Promise<any> {
  return request({
    url: `${BASE_URL}/teacher/history/static`,
    method: 'get',
  });
}

/**
 * 获取课程任务基本信息
 * @param courseId 课程ID
 * @param taskId 教学任务ID
 * @returns Promise<any>
 */
export function getCourseTaskInfo(courseId: string | number, taskId: string | number): Promise<any> {
  return request({
    url: `${BASE_URL}/info/${courseId}/${taskId}`,
    method: 'get'
  });
}

// 获取课程详情
export async function getCourseDetail(courseId: string,teacherId:string): Promise<CourseDetailItem> {
  try {
    const res = await request({
      url: '/courseLeader/courses/detail',
      method: 'get',
      params: { courseId, teacherId }
    }) as ApiResponse;

    if (res.code === 200) {
      return res.data;
    }
    throw new Error(res?.message || '获取课程详情失败');
  } catch (error) {
    console.error('获取课程详情失败:', error);
    throw error;
  }
}

//课程目标管理
export interface courseObjectiveItem {
  courseGoalId: string
  courseGoalTitle: string
  goalDescription: string
  collapsed: boolean
}

//查看课程目标
export async function getCourseObjective(courseId: string): Promise<courseObjectiveItem> {
  try {
    const res = await request({
      url: '/courseLeader/courses/courseObjective',
      method: 'get',
      params: { courseId }
    }) as ApiResponse;

    if (res.code === 200) {
      return res.data;
    }
    throw new Error(res?.message || '获取课程目标失败');
  } catch (error) {
    console.error('获取课程目标失败:', error);
    throw error;
  }
}

//增加课程目标
export const postCourseObjective = async (data: any) => {
  console.log('提交的数据?', JSON.stringify(data, null, 2));
  try {
    const response = await request({
      url: '/courseLeader/courses/courseObjectiveSubmit',
      method: 'post',
      data
    }) as ApiResponse<any>;

    if (response && response.code === 200) {
      return response;
    }
    throw new Error(response?.message || '表单提交失败');
  } catch (error) {
    console.error('表单提交失败:', error);
    throw error;
  }
};

// 添加教师
export const addTeacher = async (data: any, courseId: number) => {
  console.log('提交的数据:', JSON.stringify(data, null, 2));
  try {
    const response = await request({
      url: '/courseLeader/courses/addTeacher',
      method: 'post',
      data,
      params: { courseId }
    }) as ApiResponse<any>;

    if (response && response.code === 200) {
      return response;
    }

    throw new Error(response?.message || '表单提交失败');
  } catch (error) {
    console.error('表单提交失败:', error);
    throw error;
  }
}





/**
 * 获取课程任务统计信息
 * @param courseId 课程ID
 * @param taskId 教学任务ID
 * @returns Promise<any>
 */
export function getCourseTaskStatistics(courseId: string | number, taskId: string | number): Promise<any> {
  return request({
    url: `${BASE_URL}/task/statistics/${courseId}/${taskId}`,
    method: 'get'
  });
}

/**
 * 获取课程任务班级信息
 * @param courseId 课程ID
 * @param taskId 教学任务ID
 * @returns Promise<any>
 */
export function getCourseTaskClasses(courseId: string | number, taskId: string | number): Promise<any> {
  return request({
    url: `${BASE_URL}/classes/${courseId}/${taskId}`,
    method: 'get'
  });
}

/**
 * 获取课程任务教师团队信息
 * @param courseId 课程ID
 * @param taskId 教学任务ID
 * @returns Promise<any>
 */
export function getCourseTaskTeachers(courseId: string | number, taskId: string | number): Promise<any> {
  return request({
    url: `${BASE_URL}/team/${courseId}/${taskId}`,
    method: 'get'
  });
}


interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
}
//-----------------------------------------------------------------------------------------
// 历史数据，全部写完后会删除
// Mock数据暂时保留，后续替换为真实API
// export interface CourseItem {
//   courseId: number;
//   courseName: string;
//   courseCode: string;
//   semesterName: string;
//   status: string;
//   credits: number;
//   totalHours: number;
//   courseNature: string;
//   courseUnit: string;
//   isTeaching: boolean;
//   teachers: Array<{
//     id: string;
//     name: string;
//     teacherNumber: string;
//     isResponsible: boolean;
//   }>;
//   isResponsible: boolean;
// }
// export async function getCoursesAll(teacherId: string): Promise<CourseItem[]> {
//   try {
//     const res = await request({
//       url: '/courseLeader/courses/list',
//       method: 'get',
//       params: { teacherId }
//     }) as ApiResponse;

//     if (res.code === 200) {
//       return res.data;
//     }
//     throw new Error(res?.message || '获取课程信息失败');
//   } catch (error) {
//     console.error('获取所有课程列表失败:', error);
//     throw error;
//   }
// }

// //首页接口
// export interface ProfessorItem {
//   name: string;
//   title: string;
//   department: string;
//   avatar: string;
//   courseCount: number;
//   studentCount: number;
//   years: number;
//   introduction: string;
//   courseList: string[];
//   currentSemester: string;
//   courses: CourseTimelineItem[];
// }

// export interface CourseTimelineItem {
//   id: number;
//   name: string;
//   image: string;
//   date: string;
//   status: string;
//   reach: number;
// }

// //首页信息
// export async function getHomeInfo(): Promise<ProfessorItem[]> {
//   try {
//     const res = await request({
//       url: '/courseLeader/courses/home',
//       method: 'get'
//     }) as ApiResponse;

//     if (res.code === 200) {
//       return res.data;
//     }
//     throw new Error(res?.message || '获取首页信息失败');
//   } catch (e) {
//     console.error('获取首页信息失败:', e);
//     return homeInfo;
//   }
// }


// //题目类型
// export interface questionTypeItem {
//   id: number;
//   questionType:string,
//   count: number
// }

// // 获取题目类型
// export async function getQuestionType(id: string): Promise<questionTypeItem> {
//   try {
//     const res = await request({
//       url: '/courseLeader/courses/questionType',
//       method: 'get',
//       params: { id }
//     }) as ApiResponse<questionTypeItem>;

//     if (res.code === 200) {
//       return res.data;
//     }
//     throw new Error(res?.message || '获取题目类型失败');
//   } catch (error) {
//     console.error('获取题目类型失败:', error);
//     throw error;
//   }
// }


// //配置课程信息
// export const postCourseConfig = async (data: any) => {
//   try {
//     const response = await request({
//       url: '/courseLeader/course/courseConfig/submit',
//       method: 'post',
//       data
//     }) as ApiResponse<any>;

//     if (response && response.code === 200) {
//       return response;
//     }
//     throw new Error(response?.message || '表单提交失败');
//   } catch (error) {
//     console.error('表单提交失败:', error);
//     throw error;
//   }
// };


//   interface classData{
//     name: string;       // 班级名称
//     value: number;      // 课程达成度
//     teacher: string;    // 班主任姓名
//   }
//   export interface degreeByClassItem {
//     year: string;         // 年份
//     classes: classData[]; // 该年度所有班级数据
//   }
//   export async function getDegreeByClass(courseId: string): Promise<degreeByClassItem[]> {
//     try {
//       const res = await request({
//         url: '/courseLeader/courses/degree',
//         method: 'get',
//         params: { courseId }
//       }) as ApiResponse;

//       if (res.code === 200) {
//         return res.data;
//       }
//       throw new Error(res?.message || '获取课程达成度失败');
//     } catch (error) {
//       console.error('获取课程达成度失败:', error);
//       throw error;
//     }
//   }

// export interface degreeClassInfo {
//     id: number,
//     name: string,
//     class: string,
//     code: string,
//     studentCount: number,
//     semester: string,
//     status: number,
//     goals: []
// }
// export async function getDegreeClassInfo(courseId: string): Promise<degreeClassInfo[]> {
//     try {
//         const res = await request({
//           url: '/courseLeader/courses/degree/information',
//           method: 'get',
//           params: { courseId }
//         }) as ApiResponse;

//         if (res.code === 200) {
//           return res.data;
//         }
//         throw new Error(res?.message || '获取课程基本信息失败');
//     } catch (error) {
//         console.error('获取课程基本信息失败:', error);
//         throw error;
//     }
// }

// interface classGoal{
//     number: string;       // 学生学号
//     value: number;      // 课程达成度
// }
// export interface degreeByClassGoal {
//     classGoal: string;         // 课程目标
//     classes: classGoal[]; // 该课程目标所有的学生数据
// }
// export async function getDegreeClassSpecific(courseId: string): Promise<degreeByClassGoal[]> {
//     try {
//         const res = await request({
//           url: '/courseLeader/courses/degree/specific',
//           method: 'get',
//           params: { courseId }
//         }) as ApiResponse;

//         if (res.code === 200) {
//           return res.data;
//         }
//         throw new Error(res?.message || '获取每个学生课程目标情况失败');
//     } catch (error) {
//         console.error('获取每个学生课程目标情况失败:', error);
//         throw error;
//     }
// }



// export interface questionListItem{
//   id: number,
//   typeId: number,
//   questionTitle: string,
//   score: number,
//   creator: string,
//   createTime: string
// }

// // 获取题目列表
// export async function getQuestionList(id: string): Promise<questionListItem> {
//   try {
//     const res = await request({
//       url: '/courseLeader/courses/questionLists',
//       method: 'get',
//       params: { id }
//     }) as ApiResponse<questionListItem>;

//     if (res.code === 200) {
//       return res.data;
//     }
//     throw new Error(res?.message || '获取题目列表失败');
//   } catch (error) {
//     console.error('获取题目列表失败:', error);
//     throw error;
//   }
// }

// //课程详情--->时间轴
//   export interface CourseDataItem {
//     id: number;
//     name: string;
//     image: string;
//     date: string; // 学期信息
//     status: string;
//     reach: number; // 达成度
//   }
// export interface CourseInfo {
//   courseCode: string;
//   courseName: string;
//   teachers: string[];
//   credit: number;
//   totalHours: number;
//   courseDesc: string;
//   semester: string;
//   courseNature: string; //
//   courseUnit: string; //
// }

// export interface CourseDetailItem {
//   courseData: CourseDataItem[];
//   newestTerm: string;
//   courseInfo: CourseInfo;
// }


