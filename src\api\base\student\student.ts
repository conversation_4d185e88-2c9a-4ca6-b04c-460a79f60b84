import request from '@/utils/request';
import {
  StudentInfo,
  StudentQueryParams,
  StudentListResult,
  StudentDetailResult,
  StudentOperationResult,
  StudentFormData,
  ImportResult
} from '@/api/model/student/studentModel';

/**
 * 分页查询学生列表
 * @param params 查询参数
 * @returns 学生列表分页结果
 */
export async function getStudentList(params: StudentQueryParams): Promise<StudentListResult> {
  try {
    const res = await request({
      url: '/base/student/list',
      method: 'GET',
      params
    });
    return res;
  } catch (error) {
    console.error('获取学生列表失败:', error);
    throw error;
  }
}

/**
 * 根据ID查询学生详情
 * @param id 学生ID
 * @returns 学生详情信息
 */
export async function getStudentDetail(id: string | number): Promise<StudentDetailResult> {
  try {
    const res = await request({
      url: `/base/student/${id}`,
      method: 'GET'
    });
    return res;
  } catch (error) {
    console.error('获取学生详情失败:', error);
    throw error;
  }
}

/**
 * 根据ID查询学生详细信息（包含更多字段）
 * @param id 学生ID
 * @returns 学生详细信息
 */
export async function getStudentDetailById(id: string | number): Promise<StudentDetailResult> {
  try {
    const res = await request({
      url: '/base/student/detail',
      method: 'GET',
      params: { id }
    });
    return res;
  } catch (error) {
    console.error('获取学生详细信息失败:', error);
    throw error;
  }
}

/**
 * 新增学生
 * @param data 学生信息
 * @returns 操作结果
 */
export async function addStudent(data: StudentFormData): Promise<StudentOperationResult> {
  try {
    const res = await request({
      url: '/base/student',
      method: 'POST',
      data
    });
    return res;
  } catch (error) {
    console.error('新增学生失败:', error);
    throw error;
  }
}

/**
 * 更新学生信息
 * @param data 学生信息
 * @returns 操作结果
 */
export async function updateStudent(data: StudentFormData): Promise<StudentOperationResult> {
  try {
    const res = await request({
      url: '/base/student',
      method: 'PUT',
      data
    });
    return res;
  } catch (error) {
    console.error('更新学生失败:', error);
    throw error;
  }
}

/**
 * 删除学生
 * @param id 学生ID
 * @returns 操作结果
 */
export async function deleteStudent(id: string | number): Promise<StudentOperationResult> {
  try {
    const res = await request({
      url: `/base/student/${id}`,
      method: 'DELETE'
    });
    return res;
  } catch (error) {
    console.error('删除学生失败:', error);
    throw error;
  }
}

/**
 * 停用学生
 * @param id 学生ID
 * @returns 操作结果
 */
export async function stopStudentUsing(id: string | number): Promise<StudentOperationResult> {
  try {
    const res = await request({
      url: `/base/student/using/${id}`,
      method: 'DELETE'
    });
    return res;
  } catch (error) {
    console.error('停用学生失败:', error);
    throw error;
  }
}

/**
 * 导入学生数据
 * @param file 文件对象
 * @returns 导入结果
 */
export async function importStudents(file: File): Promise<ImportResult> {
  try {
    const formData = new FormData();
    formData.append('file', file);
    
    const res = await request({
      url: '/base/student/import',
      method: 'POST',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data',
      }
    });
    return res;
  } catch (error) {
    console.error('导入学生数据失败:', error);
    throw error;
  }
}

/**
 * 导出学生数据
 * @param params 查询参数
 * @returns 文件流
 */
export async function exportStudents(params: StudentQueryParams): Promise<Blob> {
  try {
    const res = await request({
      url: '/base/student/export',
      method: 'GET',
      params,
      responseType: 'blob'
    });
    return res;
  } catch (error) {
    console.error('导出学生数据失败:', error);
    throw error;
  }
}

/**
 * 获取学生树结构数据
 * @returns 树结构数据
 */
export async function getStudentTree(): Promise<any> {
  try {
    const res = await request({
      url: '/base/student/tree',
      method: 'GET'
    });
    return res;
  } catch (error) {
    console.error('获取学生树结构失败:', error);
    throw error;
  }
} 