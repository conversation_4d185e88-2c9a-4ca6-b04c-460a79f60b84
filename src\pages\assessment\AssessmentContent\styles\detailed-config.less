// 新增：直接录入配置弹窗样式
.direct-config-container {
    padding: 16px 0;
    
    .config-header {
      margin-bottom: 24px;
  
      .description-card {
        display: flex;
        align-items: flex-start;
        gap: 12px;
        padding: 16px;
        background: linear-gradient(135deg, var(--td-brand-color-1) 0%, var(--td-brand-color-2) 100%);
        border: 1px solid var(--td-brand-color-3);
        border-radius: 8px;
        border-left: 4px solid var(--td-brand-color);
  
        .description-content {
          flex: 1;
  
          .config-description {
            margin: 0;
            color: var(--td-text-color-primary);
            font-size: 14px;
            line-height: 1.6;
          }
        }
      }
    }
  
    .mode-description {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 16px;
      padding: 12px;
      background: var(--td-bg-color-page);
      border-radius: 6px;
      font-size: 14px;
      color: var(--td-text-color-secondary);
  
      .t-icon {
        color: var(--td-brand-color);
      }
    }
  
    // 表格表头样式 - 所有表头居中对齐
    :deep(.t-table__header) {
      th {
        text-align: center !important;
      }
    }
  
    // 表格表尾样式
    :deep(.t-table__footer) {
      .direct-mode-footer {
        background: var(--td-bg-color-page) !important;
        border-top: 2px solid var(--td-brand-color) !important;
  
        td {
          background: var(--td-bg-color-page) !important;
          font-weight: 600 !important;
          text-align: center !important;
          color: var(--td-text-color-primary) !important;
          padding: 12px 16px !important;
  
          // 占比列的状态颜色
          &:nth-child(3) {
            &.percentage-valid {
              color: var(--td-success-color) !important;
              background: var(--td-success-color-1) !important;
            }
            
            &.percentage-invalid {
              color: var(--td-error-color) !important;
              background: var(--td-error-color-1) !important;
            }
          }
  
          // 说明列的状态颜色
          &:nth-child(4) {
            &.description-valid {
              color: var(--td-success-color) !important;
              background: var(--td-success-color-1) !important;
            }
            
            &.description-invalid {
              color: var(--td-error-color) !important;
              background: var(--td-error-color-1) !important;
            }
          }
        }
      }
    }
  
    .validation-message {
      display: flex;
      align-items: center;
      gap: 6px;
      margin-top: 12px;
      padding: 12px 16px;
      color: var(--td-error-color);
      font-size: 13px;
      background: var(--td-error-color-1);
      border: 1px solid var(--td-error-color-3);
      border-radius: 6px;
  
      .t-icon {
        font-size: 14px;
      }
    }
  
    .invalid-percentage {
      color: var(--td-warning-color);
    }
  }
  
  // 新增：详细录入配置全屏弹窗内容样式
  .detailed-config-content {
    .config-header {
      margin-bottom: 24px;
  
      .description-card {
        display: flex;
        align-items: flex-start;
        gap: 12px;
        padding: 16px;
        background: linear-gradient(135deg, var(--td-brand-color-1) 0%, var(--td-brand-color-2) 100%);
        border: 1px solid var(--td-brand-color-3);
        border-radius: 8px;
        border-left: 4px solid var(--td-brand-color);
  
        .description-content {
          flex: 1;
  
          .config-description {
            margin: 0;
            color: var(--td-text-color-primary);
            font-size: 14px;
            line-height: 1.6;
          }
        }
      }
    }
  
    .mode-description {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 24px;
      padding: 12px;
      background: var(--td-bg-color-page);
      border-radius: 6px;
      font-size: 14px;
      color: var(--td-text-color-secondary);
  
      .t-icon {
        color: var(--td-brand-color);
      }
    }
  
    > * + * {
      margin-top: 32px;
    }
    
    // 课程目标概览样式
    .objective-overview {
      h5 {
        margin: 0 0 20px 0;
        font-size: 18px;
        font-weight: 600;
        color: var(--td-text-color-primary);
        border-bottom: 2px solid var(--td-brand-color);
        padding-bottom: 10px;
      }
  
      .objectives-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
        gap: 20px;
  
        .objective-card {
          padding: 24px;
          border: 1px solid var(--td-border-level-1-color);
          border-radius: 12px;
          background: var(--td-bg-color-container);
          transition: all 0.3s ease;
  
          &:hover {
            border-color: var(--td-brand-color);
            box-shadow: 0 6px 16px rgba(0, 123, 255, 0.15);
            transform: translateY(-2px);
          }
  
          .objective-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
  
            .objective-number {
              font-size: 18px;
              font-weight: 600;
              color: var(--td-brand-color);
            }
  
            .objective-weight {
              font-size: 14px;
              color: var(--td-text-color-secondary);
              padding: 6px 12px;
              background: var(--td-brand-color-light);
              border-radius: 6px;
              border: 1px solid var(--td-brand-color-3);
            }
          }
  
          .objective-name {
            font-size: 16px;
            font-weight: 500;
            color: var(--td-text-color-primary);
            margin-bottom: 12px;
          }
  
          .objective-description {
            font-size: 14px;
            color: var(--td-text-color-secondary);
            line-height: 1.6;
            margin-bottom: 16px;
          }
  
          .graduation-requirement {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 13px;
  
            .label {
              color: var(--td-text-color-secondary);
              font-weight: 500;
            }
  
            .value {
              color: var(--td-success-color);
              font-weight: 500;
              padding: 4px 10px;
              background: var(--td-success-color-1);
              border-radius: 6px;
              border: 1px solid var(--td-success-color-3);
            }
          }
        }
      }
    }
  
    // 题目明细表格样式
    .question-detail-section {
      .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
  
        h5 {
          margin: 0;
          font-size: 18px;
          font-weight: 600;
          color: var(--td-text-color-primary);
          border-bottom: 2px solid var(--td-brand-color);
          padding-bottom: 10px;
        }
      }
  
      .question-table-container {
        max-height: 500px;
        overflow: auto;
        border-radius: 8px;
        border: 1px solid var(--td-border-level-1-color);
        
        .question-detail-table {
          width: 100%;
          border-collapse: collapse;
          overflow: hidden;
  
          thead {
            background: var(--td-bg-color-page);
            position: sticky;
            top: 0;
            z-index: 10;
  
            th {
              padding: 16px 12px;
              text-align: center;
              font-weight: 600;
              color: var(--td-text-color-primary);
              border: 1px solid var(--td-border-level-1-color);
              font-size: 14px;
            }
          }
  
          tbody {
            tr {
                            td {
                  padding: 12px;
                  border: 1px solid var(--td-border-level-1-color);
                  vertical-align: middle;
                  font-size: 13px;
  
                  &.question-number-cell {
                    text-align: center;
                    font-weight: 600;
                    color: var(--td-brand-color);
                    background: var(--td-brand-color-1);
                    min-width: 80px;
                    border-left: 3px solid var(--td-brand-color);
                    
                    .question-number {
                      font-size: 18px;
                      font-weight: 700;
                      color: var(--td-brand-color);
                    }
                  }
  
                &.sub-number-cell {
                  text-align: center;
                  font-weight: 500;
                  color: var(--td-text-color-primary);
                  min-width: 80px;
                }
  
                &.question-type-cell {
                  min-width: 160px;
                  
                  .question-type-content {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    
                    .delete-question-btn {
                      flex-shrink: 0;
                      width: 32px;
                      height: 32px;
                      border-radius: 3px; // 与TDesign组件圆角保持一致
                      background: var(--td-error-color-1);
                      border-color: var(--td-error-color-3);
                      color: var(--td-error-color);
                      transition: all 0.2s ease;
                      
                      &:hover {
                        background: var(--td-error-color);
                        border-color: var(--td-error-color);
                        color: white;
                        transform: translateY(-1px);
                        box-shadow: 0 2px 8px rgba(220, 38, 38, 0.3);
                      }
                      
                      .t-icon {
                        font-size: 14px;
                      }
                    }
                  }
                }
  
                &.question-content-cell {
                  min-width: 250px;
                }
  
                &.answer-cell {
                  min-width: 200px;
                }
  
                &.score-cell {
                  text-align: center;
                  min-width: 100px;
                }
  
                &.objective-cell {
                  min-width: 140px;
                }
  
                &.action-cell {
                  text-align: center;
                  min-width: 120px;
                  padding: 8px !important;
                  
                  .action-buttons {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    gap: 8px;
                    
                    .action-btn {
                      width: 28px;
                      height: 28px;
                      border-radius: 50%;
                      transition: all 0.2s ease;
                      position: relative;
                      z-index: 10;
                      
                      &:hover {
                        transform: translateY(-1px);
                        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
                      }
                      
                      .t-icon {
                        font-size: 14px;
                      }
                      
                      &.add-btn {
                        background: var(--td-brand-color-1);
                        border-color: var(--td-brand-color);
                        color: var(--td-brand-color);
                        
                        &:hover {
                          background: var(--td-brand-color);
                          color: white;
                          border-color: var(--td-brand-color);
                        }
                      }
                      
                      &.remove-btn {
                        background: var(--td-warning-color-1);
                        border-color: var(--td-warning-color);
                        color: var(--td-warning-color);
                        
                        &:hover:not(.t-is-disabled) {
                          background: var(--td-warning-color);
                          color: white;
                          border-color: var(--td-warning-color);
                        }
                      }
                      
  
                    }
                  }
                }
              }
            }
          }
        }
  
        .empty-questions {
          text-align: center;
          padding: 80px 20px;
          color: var(--td-text-color-secondary);
  
          .t-icon {
            color: var(--td-text-color-placeholder);
            margin-bottom: 20px;
          }
  
          p {
            margin: 0;
            font-size: 16px;
          }
        }
      }
      
      .table-bottom-actions {
        display: flex;
        justify-content: center;
        padding: 20px;
        border-top: 1px solid var(--td-border-level-1-color);
        background: var(--td-bg-color-container);
        margin-top: 16px;
        border-radius: 0 0 8px 8px;
        
        .t-button {
          width: 100%;
          transition: all 0.3s ease;
          font-size: 14px;
          padding: 8px 16px;
          
          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.25);
          }
        }
      }
    }
  
    // 汇总表格样式
    .objective-summary {
      .summary-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 20px;
        gap: 20px;
        
        h5 {
          margin: 0;
          font-size: 18px;
          font-weight: 600;
          color: var(--td-text-color-primary);
          border-bottom: 2px solid var(--td-brand-color);
          padding-bottom: 10px;
          flex-shrink: 0;
        }
        
        .summary-tips-inline {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 8px 14px;
          border-radius: 6px;
          font-size: 13px;
          line-height: 1.4;
          white-space: nowrap;
          flex-shrink: 0;
          
          .t-icon {
            font-size: 14px;
            flex-shrink: 0;
          }
          
          &.tip-info {
            background: var(--td-brand-color-1);
            border: 1px solid var(--td-brand-color-3);
            color: var(--td-brand-color-8);
            
            .t-icon {
              color: var(--td-brand-color);
            }
          }
          
          &.tip-warning {
            background: var(--td-warning-color-1);
            border: 1px solid var(--td-warning-color-3);
            color: var(--td-warning-color-8);
            
            .t-icon {
              color: var(--td-warning-color);
            }
          }
          
          &.tip-success {
            background: var(--td-success-color-1);
            border: 1px solid var(--td-success-color-3);
            color: var(--td-success-color-8);
            
            .t-icon {
              color: var(--td-success-color);
            }
          }
        }
        
        // 中等屏幕下缩小间距
        @media (max-width: 1600px) {
          gap: 16px;
          
          .summary-tips-inline {
            font-size: 12px;
            padding: 6px 12px;
            
            .t-icon {
              font-size: 12px;
            }
          }
        }
        
        // 小屏幕下的响应式处理
        @media (max-width: 1200px) {
          flex-direction: column;
          align-items: flex-start;
          gap: 12px;
          
          .summary-tips-inline {
            align-self: stretch;
            white-space: normal;
            font-size: 14px;
            padding: 10px 16px;
            
            .t-icon {
              font-size: 16px;
            }
          }
        }
      }
  
      .summary-table-container {
        max-height: 400px;
        overflow: auto;
        border-radius: 8px;
        border: 1px solid var(--td-border-level-1-color);
      
        .summary-table {
          width: 100%;
          border-collapse: collapse;
  
          thead {
            background: var(--td-bg-color-page);
            position: sticky;
            top: 0;
            z-index: 10;
  
            th {
              padding: 16px 12px;
              text-align: center;
              font-weight: 600;
              color: var(--td-text-color-primary);
              border: 1px solid var(--td-border-level-1-color);
              font-size: 14px;
  
              &.objective-header {
                background: var(--td-brand-color-1);
                color: var(--td-brand-color);
              }
  
              &.question-type-header {
                background: var(--td-success-color-1);
                color: var(--td-success-color);
              }
  
              &.total-header {
                background: var(--td-warning-color-1);
                color: var(--td-warning-color);
              }
  
              &.sub-header {
                font-size: 13px;
                background: var(--td-bg-color-container);
              }
            }
          }
  
          tbody {
            tr {
              &:nth-child(even) {
                background: var(--td-bg-color-container-hover);
              }
  
              &.summary-row {
                background: var(--td-bg-color-page);
                border-top: 2px solid var(--td-brand-color);
  
                td {
                  font-weight: 600;
                  color: var(--td-text-color-primary);
                  font-size: 14px;
                  
                  &.summary-label {
                    background: var(--td-brand-color-1);
                    color: var(--td-brand-color);
                  }
  
                  &.summary-total {
                    background: var(--td-warning-color-1);
                    color: var(--td-warning-color);
                    font-size: 16px;
                  }
                }
              }
  
              td {
                padding: 12px;
                text-align: center;
                border: 1px solid var(--td-border-level-1-color);
                font-size: 13px;
  
                &.objective-cell {
                  text-align: left;
                  font-weight: 500;
                  color: var(--td-text-color-primary);
                }
  
                &.count-cell {
                  color: var(--td-text-color-secondary);
                }
  
                &.sub-numbers-cell {
                  font-size: 12px;
                  color: var(--td-text-color-secondary);
                  line-height: 1.4;
                  max-width: 120px;
                  word-wrap: break-word;
                }
  
                &.score-cell {
                  font-weight: 500;
                  color: var(--td-text-color-primary);
                }
  
                &.total-cell {
                  font-weight: 600;
                  color: var(--td-brand-color);
                  background: var(--td-brand-color-1);
                }
              }
            }
          }
        }
  
        .empty-summary {
          text-align: center;
          padding: 60px 20px;
          color: var(--td-text-color-secondary);
          font-size: 16px;
        }
      }
  
      .validation-message {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-top: 16px;
        padding: 16px 20px;
        color: var(--td-error-color);
        font-size: 14px;
        background: var(--td-error-color-1);
        border: 1px solid var(--td-error-color-3);
        border-radius: 8px;
  
        .t-icon {
          font-size: 16px;
        }
      }
    }
  }
  