<template>
  <div
    class="teaching-task-card"
    :class="{ 'selected': selected, 'disabled': disabled }"
    @click="handleCardClick"
  >
    <!-- 选择状态指示器 -->
    <div class="selection-indicator">
      <t-checkbox
        :checked="selected"
        :disabled="disabled"
        @change="handleSelectionChange"
        @click.stop
      />
    </div>

    <!-- 卡片内容 -->
    <div class="card-content">
      <!-- 任务基本信息 -->
      <div class="task-header">
        <div class="task-title">
          <h4>{{ taskData.taskName }}</h4>
          <t-tag theme="primary" size="small">任务{{ taskData.taskNumber }}</t-tag>
        </div>
        <div class="task-meta">
          <span class="meta-item">
            <t-icon name="time" />
            {{ taskData.teachWeek }}周 / {{ taskData.weekHours }}学时
          </span>
        </div>
      </div>

      <!-- 教师信息 -->
      <div class="teacher-section">
        <div class="section-title">
          <t-icon name="user" />
          <span>授课教师</span>
        </div>
        <div class="teacher-list">
          <div 
            v-for="teacher in taskData.teachers" 
            :key="teacher.teacherId"
            class="teacher-item"
          >
            <t-tag 
              :theme="teacher.role === 1 ? 'primary' : 'default'"
              size="small"
              class="teacher-tag"
            >
              {{ teacher.teacherName }}
            </t-tag>
            <span class="teacher-role">{{ teacher.roleName }}</span>
          </div>
        </div>
      </div>

      <!-- 班级信息 -->
      <div class="class-section">
        <div class="section-title">
          <t-icon name="user-group" />
          <span>授课班级</span>
          <t-tag theme="success" size="small">{{ taskData.classCount }}个班级</t-tag>
        </div>
        <div class="class-list">
          <div 
            v-for="classInfo in taskData.classes" 
            :key="classInfo.classId"
            class="class-item"
          >
            <span class="class-name">{{ classInfo.className }}</span>
            <span class="student-count">{{ classInfo.studentNumber }}人</span>
          </div>
        </div>
      </div>

      <!-- 统计信息 -->
      <div class="stats-section">
        <div class="stat-item">
          <span class="stat-label">总学生数</span>
          <span class="stat-value">{{ taskData.studentCount }}</span>
        </div>
      </div>
    </div>

    <!-- 选中状态遮罩 -->
    <div v-if="selected" class="selected-overlay">
      <t-icon name="check-circle-filled" class="check-icon" />
    </div>
  </div>
</template>

<script setup lang="ts">
import type { TaskWorkDetailVO } from '@/api/teaching/task'

// Props 定义
interface Props {
  taskData: TaskWorkDetailVO
  selected: boolean
  disabled?: boolean
}

const props = defineProps<Props>()

// Emits 定义
const emit = defineEmits<{
  'toggle-selection': [taskId: number, selected: boolean]
}>()

// 事件处理
const handleCardClick = () => {
  if (props.disabled) return
  emit('toggle-selection', props.taskData.taskId, !props.selected)
}

const handleSelectionChange = (checked: boolean) => {
  if (props.disabled) return
  emit('toggle-selection', props.taskData.taskId, checked)
}
</script>

<style lang="less" scoped>
.teaching-task-card {
  position: relative;
  background: var(--td-bg-color-container);
  border: 1px solid var(--td-border-level-1-color);
  border-radius: var(--td-radius-large);
  padding: var(--td-comp-paddingTB-xl) var(--td-comp-paddingLR-xl);
  cursor: pointer;
  transition: all var(--td-transition);
  overflow: hidden;
  min-height: 180px;

  &:hover {
    border-color: var(--td-brand-color);
    box-shadow: var(--td-shadow-2);
    transform: translateY(-1px);
  }

  &.selected {
    border-color: var(--td-brand-color);
    background: var(--td-brand-color-1);
    box-shadow: var(--td-shadow-3);

    .card-content {
      position: relative;
      z-index: 2;
    }
  }

  &.disabled {
    cursor: not-allowed;
    opacity: 0.6;
    background: var(--td-bg-color-container-hover);

    &:hover {
      border-color: var(--td-border-level-1-color);
      box-shadow: none;
      transform: none;
    }

    .card-content {
      pointer-events: none;
    }
  }
}

.selection-indicator {
  position: absolute;
  top: var(--td-comp-paddingTB-l);
  right: var(--td-comp-paddingLR-l);
  z-index: 3;

  :deep(.t-checkbox) {
    transform: scale(1.1);
  }
}

.card-content {
  .task-header {
    margin-bottom: var(--td-comp-margin-l);

    .task-title {
      display: flex;
      align-items: center;
      gap: var(--td-comp-margin-m);
      margin-bottom: var(--td-comp-margin-m);

      h4 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: var(--td-text-color-primary);
        flex: 1;
      }

      :deep(.t-tag) {
        font-size: var(--td-font-size-body-small);
        font-weight: 500;
      }
    }

    .task-meta {
      .meta-item {
        display: flex;
        align-items: center;
        gap: var(--td-comp-margin-s);
        font-size: var(--td-font-size-body-medium);
        color: var(--td-text-color-secondary);
        font-weight: 500;

        :deep(.t-icon) {
          font-size: 14px;
        }
      }
    }
  }
}

.teacher-section,
.class-section {
  margin-bottom: var(--td-comp-margin-l);

  .section-title {
    display: flex;
    align-items: center;
    gap: var(--td-comp-margin-s);
    margin-bottom: var(--td-comp-margin-m);
    font-size: var(--td-font-size-body-medium);
    font-weight: 500;
    color: var(--td-text-color-secondary);

    :deep(.t-icon) {
      font-size: 14px;
    }
  }
}

.teacher-list {
  .teacher-item {
    display: flex;
    align-items: center;
    gap: var(--td-comp-margin-xs);
    margin-bottom: var(--td-comp-margin-xs);

    .teacher-tag {
      flex-shrink: 0;
    }

    .teacher-role {
      font-size: var(--td-font-size-body-extra-small);
      color: var(--td-text-color-placeholder);
    }
  }
}

.class-list {
  display: flex;
  flex-direction: column;
  gap: var(--td-comp-margin-xs);

  .class-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--td-comp-paddingTB-xs) var(--td-comp-paddingLR-s);
    background: var(--td-bg-color-container-hover);
    border-radius: var(--td-radius-small);
    font-size: var(--td-font-size-body-small);
    border: 1px solid transparent;
    transition: all var(--td-transition);

    &:hover {
      background: var(--td-bg-color-container-active);
      border-color: var(--td-border-level-1-color);
    }

    .class-name {
      font-weight: var(--td-font-weight-medium);
      color: var(--td-text-color-primary);
    }

    .student-count {
      color: var(--td-text-color-secondary);
    }
  }
}

.stats-section {
  padding-top: var(--td-comp-paddingTB-s);
  border-top: 1px solid var(--td-border-level-1-color);
  margin-top: var(--td-comp-margin-s);

  .stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .stat-label {
      font-size: var(--td-font-size-body-small);
      color: var(--td-text-color-secondary);
    }

    .stat-value {
      font-size: var(--td-font-size-body-medium);
      font-weight: var(--td-font-weight-semi-bold);
      color: var(--td-brand-color);
    }
  }
}

.selected-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--td-brand-color-1);
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: none;
  border-radius: var(--td-radius-large);

  .check-icon {
    position: absolute;
    bottom: var(--td-comp-paddingTB-m);
    right: var(--td-comp-paddingLR-m);
    font-size: var(--td-font-size-title-large);
    color: var(--td-brand-color);
  }
}
</style>
