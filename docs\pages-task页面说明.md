# Task模块 Vue页面说明

## 全局目录结构

```
/task/
├── worklist/                  # 教学任务管理
│   ├── WorklistManagement.vue # 教学任务管理
│   └── TeacherTask.vue        # 教师任务管理
└── score/                     # 成绩管理
    ├── ScoreManagement.vue    # 成绩管理
    └── TeacherScoreManage.vue # 教师成绩管理
```

## 模块概述

Task模块（教学任务模块）负责管理教学过程中的任务分配、执行和成绩统计等功能，是连接培养方案与具体教学实施的重要桥梁。

### 数据库表对应关系

- `task_worklist` - 教学任务信息表
- `task_score` - 教学任务对应学生成绩表

## 功能分类

### 1. 教学任务管理 (worklist/)
负责教学任务的创建、分配、跟踪和管理。

### 2. 成绩管理 (score/)
管理教学任务相关的学生成绩和统计分析。

## 详细页面说明

### Worklist目录 - 教学任务管理

#### /task/worklist/WorklistManagement.vue
- **功能**: 教学任务管理
- **描述**: 管理学期教学任务的分配、执行和监控
- **主要功能**:
  - 教学任务列表展示
  - 任务创建和分配
  - 任务进度跟踪
  - 教师工作量统计
  - 任务执行情况监控

#### /task/worklist/TeacherTask.vue
- **功能**: 教师任务管理
- **描述**: 教师端的任务管理功能，包括任务查看、执行和状态更新
- **主要功能**:
  - 个人任务列表查看
  - 任务详情和要求
  - 任务执行状态更新
  - 任务完成度统计
  - 任务相关文档管理

### Score目录 - 成绩管理

#### /task/score/ScoreManagement.vue
- **功能**: 成绩管理
- **描述**: 管理教学任务相关的学生成绩录入、统计和分析
- **主要功能**:
  - 成绩录入和维护
  - 成绩统计分析
  - 不及格学生跟踪
  - 成绩分布分析
  - 达成度计算

#### /task/score/TeacherScoreManage.vue
- **功能**: 教师成绩管理
- **描述**: 教师端的成绩管理功能，支持成绩录入、修改和分析
- **主要功能**:
  - 班级成绩录入
  - 成绩批量导入
  - 成绩修改和审核
  - 个人教学数据分析
  - 成绩报表生成

## 页面功能详细说明

### 教学任务管理功能特点
1. **任务分配**: 支持按学期、课程、班级进行任务分配
2. **进度监控**: 实时跟踪教学任务的执行进度
3. **资源管理**: 管理教学所需的教师、教室等资源
4. **工作量统计**: 自动计算教师的教学工作量
5. **冲突检测**: 检测时间、资源冲突并提供解决方案

### 成绩管理功能特点
1. **多维统计**: 支持按课程、班级、教师等维度统计
2. **趋势分析**: 分析成绩变化趋势和规律
3. **预警机制**: 对异常成绩和不及格情况进行预警
4. **报表生成**: 自动生成各类成绩统计报表
5. **数据导入**: 支持从Excel等格式批量导入成绩

### 教师端功能特点
1. **个性化界面**: 针对教师角色优化的用户界面
2. **便捷操作**: 简化的操作流程，提高工作效率
3. **数据分析**: 提供教师个人的教学数据分析
4. **移动支持**: 支持移动端访问和操作
5. **消息通知**: 任务提醒和成绩录入通知

## 与其他模块的关系

### 与Training模块的关系
- 基于Training模块的课程体系创建教学任务
- 教学任务的执行验证培养方案的可行性

### 与Assessment模块的关系
- 为Assessment模块提供教学任务上下文
- 接收Assessment模块的考核成绩数据

### 与Base模块的关系
- 使用Base模块的教师、学生、班级等基础数据
- 按Base模块的组织结构进行任务分配和统计

## 目录结构规范

### 文件命名规范
- 所有Vue文件使用PascalCase命名法
- 文件名应体现页面功能
- 组件名称应与文件名保持一致

### 功能分类体系
1. **任务管理**: 教学任务的创建、分配和跟踪
2. **成绩管理**: 成绩录入、统计和分析

## 注意事项

1. **时间管理**: 教学任务涉及学期、周次等时间概念，需要准确处理
2. **资源冲突**: 需要检测和避免教师、教室等资源的冲突
3. **数据一致性**: 与其他模块的数据保持一致性
4. **权限控制**: 不同角色对教学任务的操作权限不同
5. **性能优化**: 大量任务和成绩数据的处理需要注意性能优化 