import { request } from '@/utils/request';


export function getPlanList(params: any) {
  return request({
    url: '/tp/plan/list',
    method: 'get',
    params: params
  });
}


export function addPlan(data: any) {
  return request({
    url: '/tp/plan',
    method: 'post',
    data
  });
}

export function getPlan(id: number) {
  return request({
    url: `/tp/plan/${id}`,
    method: 'get'
  });
}

export function getPlanDetail(id: number) {
  return request({
    url: `/tp/plan/detail/${id}`,
    method: 'get'
  });
}

export function updatePlan(id:number,data:any){
  return request({
    url:`/tp/plan/${id}`,
    method:'put',
    data
  });
}

export function deleteByplanId(id:number ,data:any) {

  return request( {
    url:`/tp/plan/${id}`,
    method:'delete',
  })
}

export function getPlanSemesters(id: number) {
  return request({
    url: `/tp/plan/${id}/semesters`,
    method: 'get'
  });
}

export function getClassesBySemester(majorId: string, planId: string): Promise<any> {
  return request({
    url: `/tp/plan/${planId}/major/${majorId}/classes`,
    method: 'get'
  });
}

export function getTasks(majorId: string, planId: string, params): Promise<any> {
  return request({
    url: `/tp/plan/${planId}/major/${majorId}/tasks`,
    method: 'get',
    params
  });
}



export function getTeachers(majorId: string, planId: string): Promise<any> {
  return request({
    url: `/tp/plan/${planId}/major/${majorId}/teachers`,
    method: 'get'
  });
}

export function getTaskClasses(majorId: string, planId: string): Promise<any> {
  return request({
    url: `/tp/plan/${planId}/major/${majorId}/task-classes`,
    method: 'get'
  });
}

// 创建教学任务
export function createTask(data: any): Promise<any> {
  return request({
    url: '/teaching/task/add',
    method: 'post',
    data
  });
}

// 更新教学任务
export function updateTask(data: any): Promise<any> {
  return request({
    url: '/teaching/task/update',
    method: 'put',
    data
  });
}

// 删除教学任务
export function deleteTask(id: string): Promise<any> {
  return request({
    url: `/teaching/task/delete/${id}`,
    method: 'delete'
  });
}

// 获取教学任务详情
export function getTaskDetail(id: string): Promise<any> {
  return request({
    url: `/teaching/task/detail/${id}`,
    method: 'get'
  });
}

// 获取课程选项（按学期）
export function getSemesterCourses(majorId: string, planId: string, semesterId: string): Promise<any> {
  return request({
    url: `/tp/plan/${planId}/major/${majorId}/semester/${semesterId}/course`,
    method: 'get'
  });
}
