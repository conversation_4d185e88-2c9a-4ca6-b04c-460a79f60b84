<template>
  <div class="course-leader-dashboard">
    <!-- 背景装饰 - 已移除动画白色框元素 -->
    <!-- 专业选择弹窗 -->
    <MajorSelector
      v-model:visible="showMajorSelector"
      :current-major-id="currentMajor?.id"
      :major-list="majorList"
      @select="handleMajorSelect"
    />
    <!-- 头部信息 -->
    <div class="dashboard-header">
      <div class="header-content">
        <div class="breadcrumb-section">
          <!-- <t-breadcrumb>
            <t-breadcrumb-item>课程负责人工作台</t-breadcrumb-item>
            <t-breadcrumb-item>仪表盘</t-breadcrumb-item>
          </t-breadcrumb> -->
        </div>
        <div class="title-info">
         <div>
           <div class="title-container">
             <h1 class="dashboard-title">课程负责人工作台</h1>
             <t-tag theme="primary" size="small" class="role-tag">
               课程负责人
             </t-tag>
           </div>
           <p class="dashboard-subtitle">课程管理数据概览与统计分析</p>
         </div>
          <div class="title-actions">
            <t-button theme="primary" variant="outline" size="small" @click="handleReselectMajor">
              <template #icon>
                <t-icon name="swap" />
              </template>
              重新选择专业
            </t-button>
          </div>
        </div>

      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="dashboard-content">
      <!-- 课程统计模块 -->
      <div class="statistics-section">
        <div class="section-header">
          <div class="header-left">
            <h2 class="section-title">
              <t-icon name="chart-line" class="title-icon" />
              课程统计
            </h2>
            <!-- <p class="section-subtitle">课程管理数据总览</p> -->
          </div>
        </div>

        <div class="stats-grid">
          <div class="stat-card" v-for="(stat, index) in statsData" :key="stat.key" :style="{ '--delay': `${index * 0.1}s` }">
            <div class="stat-card-inner">
              <div class="stat-icon-wrapper" :class="stat.iconClass">
                <t-icon :name="stat.icon" size="28px" />
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ stat.value }}</div>
                <div class="stat-label">{{ stat.label }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 我管理的课程模块 -->
      <div class="managed-courses-section">
        <div class="section-header">
          <div class="header-left">
            <h2 class="section-title">
              <t-icon name="book" class="title-icon" />
              我管理的课程
            </h2>
          </div>
          <div class="header-right">
            <div class="filter-controls">
              <t-select
                v-model="selectedPlanVersions"
                placeholder="请选择培养方案版本"
                multiple
                clearable
                style="width: 220px;"
                :options="planVersionOptions"
                @change="handlePlanVersionChange"
              />
              <t-select
                v-model="selectedSemesters"
                placeholder="请选择上课学期"
                multiple
                clearable
                style="width: 200px;"
                :options="courseSemesterOptions"
                @change="handleCourseSemesterChange"
              />
              <t-button
                v-if="selectedPlanVersions.length > 0 || selectedSemesters.length > 0"
                theme="default"
                variant="outline"
                size="small"
                @click="clearAllFilters"
              >
                <template #icon>
                  <t-icon name="close" />
                </template>
                清除筛选
              </t-button>
            </div>
          </div>
        </div>

        <div class="courses-grid">
          <div class="course-card" v-for="(course, index) in managedCourses" :key="course.courseId" :style="{ '--delay': `${(index + 5) * 0.1}s` }">
            <div class="course-card-inner">
              <!-- 课程状态标签 -->
              <div class="course-status" :class="course.statusClass">
                {{ course.status }}
              </div>

              <!-- 课程基本信息 -->
              <div class="course-header">
                <h3 class="course-name">
                  {{ course.courseName }}
                  <span class="course-code-inline">{{ course.courseCode }}</span>
                </h3>
              </div>

              <!-- 课程统计信息 -->
              <div class="course-stats">
                <div class="stats-row">
                  <div class="stat-item">
                    <div class="stat-icon semester-icon">
                      <t-icon name="calendar" size="16px" />
                    </div>
                    <div class="stat-info">
                      <div class="stat-label-with-icon">
                        <t-icon name="time" size="12px" class="inline-icon" />
                        开课学期
                      </div>
                      <div class="stat-value">{{ course.teachingSemester }}</div>
                    </div>
                  </div>
                  <div class="stat-item">
                    <div class="stat-icon version-icon">
                      <t-icon name="file-1" size="16px" />
                    </div>
                    <div class="stat-info">
                      <div class="stat-label-with-icon">
                        <t-icon name="bookmark" size="12px" class="inline-icon" />
                        培养方案版本
                      </div>
                      <div class="stat-value">{{ course.planVersion }}</div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 操作按钮 -->
              <div class="course-actions">
                <t-button theme="primary" variant="base" size="small" @click="viewCourseDetail(course.courseId)">
                  <template #icon>
                    <t-icon name="view-module" size="14px" />
                  </template>
                  课程详情
                </t-button>
                <t-button theme="warning" variant="base" size="small" @click="manageCourse(course.courseId)">
                  <template #icon>
                    <t-icon name="setting" size="14px" />
                  </template>
                  课程管理
                </t-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 课程详情对话框 -->
    <CourseDetailsDialog
      v-model:visible="showCourseDetailsDialog"
      :course-id="selectedCourseId"
      :course-data="selectedCourseData"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { CourseDetailInfo, getCoursesByLeaderAndMajor } from '@/api/training/course';
import CourseDetailsDialog from '../training/course/components/CourseDetailsDialog.vue';
import MajorSelector from "@/pages/dashboard/components/MajorSelector.vue";
import { storage } from '@/utils/storage';
import { CourseCacheInfo } from '@/types/course';
import { getMajorSelectorByCourseLeader } from '@/api/base/major'
import { getDictLabelByTypeTitle } from '@/utils/dictUtil'
// 路由相关
const route = useRoute()
const router = useRouter();
// 类型定义
interface CurrentMajor {
  id?: number | string;
  name?: string;
  type?: string;
}
// 当前专业信息
const currentMajor = ref<CurrentMajor>({})
// 专业选择弹窗状态
const showMajorSelector = ref(false)
// 专业列表
const majorList = ref<MajorSelectorVO[]>([])

// 加载专业列表方法
const loadMajorList = async () => {
  try {
    const { data } = await getMajorSelectorByCourseLeader()
    majorList.value = data || []
  } catch (e) {
    majorList.value = []
  }
}

// 设置当前专业并可选跳转
const setCurrentMajor = async (major: any, needRoute = false) => {
  // 获取专业类型中文名
  const typeLabel = await getDictLabelByTypeTitle('专业类型', major.discipline || major.type || '')
  currentMajor.value = {
    id: major.majorId || major.id,
    name: major.majorName || major.name,
    type: typeLabel || major.discipline || major.type || ''
  }
  if (needRoute) {
    await router.replace({
      name: 'DashboardCourseLeader',
      params: { majorId: currentMajor.value.id },
      query: {
        majorName: currentMajor.value.name,
        type: currentMajor.value.type
      }
    })
  }
  await loadData()
}

// 专业选择处理
const handleMajorSelect = async (major: MajorSelectorVO) => {
  await setCurrentMajor(major, true)
  showMajorSelector.value = false
  console.log('选择专业:', major)
}
// 重新选择专业
const handleReselectMajor = () => {
  showMajorSelector.value = true
}
// 数据接口定义
interface CourseLeaderStatistics {
  totalCourses: number;
  totalVersions: number;
  requiredCount: number;
  electiveCount: number;
}

// 接口定义，确保从API返回的数据类型一致
interface CoursesByLeaderAndMajorResponse {
  code: number;
  message: string;
  data: CourseDetailInfo[];
}

interface CourseManagementCard {
  courseId: number;
  courseName: string;
  courseCode: string;
  teachingSemester: string; // 替换 offeringCount: number
  planVersion: string; // 替换 currentStudents: number
  courseNature?: string; // 课程性质：必修/选修
  semesterType: string;
  status: string;
  statusClass: string;
}

// 响应式数据
const loading = ref(false);
const showCourseDetailsDialog = ref(false);
const selectedCourseId = ref<number | string>('');
const selectedCourseData = ref<DisplayCourseInfo | null>(null);

const statistics = computed(() => {
  const courses = managedCourses.value || [];
  const totalCourses = courses.length;
  // 使用courseVersion作为培养方案版本计数
  const versionSet = new Set(courses.map(c => c.courseVersion));
  const totalVersions = versionSet.size;

  // 根据courseNature统计必修课和选修课数量
  const requiredCount = courses.filter(course => {
    if (typeof course.courseNature === 'number') return course.courseNature === 1;
    if (typeof course.courseNature === 'string') return course.courseNature === '必修';
    return false;
  }).length;

  const electiveCount = courses.filter(course => {
    if (typeof course.courseNature === 'number') return course.courseNature === 2;
    if (typeof course.courseNature === 'string') return course.courseNature === '选修';
    return false;
  }).length;

  return {
    totalCourses,
    totalVersions,
    requiredCount,
    electiveCount
  };
});

// 模拟课程数据
// 使用自定义接口类型，因为我们在loadData中会添加一些额外字段
interface DisplayCourseInfo extends CourseDetailInfo {
  teachingSemester?: string;
  planVersion?: string;
  status?: string;
  statusClass?: string;
  semesterType?: string;
}

// 原始课程数据（用于筛选）
const allCourses = ref<DisplayCourseInfo[]>([]);
// 筛选后的课程数据（用于显示）
const managedCourses = ref<DisplayCourseInfo[]>([]);

// 筛选条件
const selectedPlanVersions = ref<string[]>([]);
const selectedSemesters = ref<string[]>([]);

// 培养方案版本选项
const planVersionOptions = ref<{ label: string; value: string }[]>([]);

// 课程上课学期选项（1-8学期）
const courseSemesterOptions = ref([
  { label: '第1学期', value: '1' },
  { label: '第2学期', value: '2' },
  { label: '第3学期', value: '3' },
  { label: '第4学期', value: '4' },
  { label: '第5学期', value: '5' },
  { label: '第6学期', value: '6' },
  { label: '第7学期', value: '7' },
  { label: '第8学期', value: '8' }
]);

// 统计数据配置
const statsData = computed(() => [
  {
    key: 'totalCourses',
    label: '管理课程总数',
    value: statistics.value.totalCourses,
    icon: 'book',
    iconClass: 'icon-blue'
  },
  {
    key: 'totalMajors',
    label: '培养方案版本数',
    value: statistics.value.totalVersions,
    icon: 'chart-bar',
    iconClass: 'icon-green'
  },
  {
    key: 'totalStudents',
    label: '必修课数量',
    value: statistics.value.requiredCount,
    icon: 'usergroup',
    iconClass: 'icon-orange'
  },
  {
    key: 'totalCourseTypes',
    label: '选修课数量',
    value: statistics.value.electiveCount,
    icon: 'layers',
    iconClass: 'icon-purple'
  }
]);


  // 数据加载方法 - 从后端API获取真实数据
const loadData = async () => {
  try {
    loading.value = true;
    // TODO: 从用户信息或路由参数中获取真实的专业ID
    const response = await getCoursesByLeaderAndMajor(currentMajor.value.id as number);


    // 数据需要处理，状态转换设置，status的值：courseVersion版本；
    // 根据courseVersion(年份)与当前年份进行比较，设置学期是春季学期还是秋季学期
    // 检查response的结构，适配API返回的数据格式
    const courseData = response.data;
    console.log('获取课程数据:', courseData);

    const currentYear = new Date().getFullYear();

    // 处理课程数据
    const processedCourses = courseData.map((item: any) => {
      let course = item;//.courseBaseInfoVO;
      const courseYear = course.courseVersion;

      const courseSemester = typeof course.courseSemester === 'string'
        ? parseInt(course.courseSemester)
        : (course.courseSemester || 1);

      const teachingSemester = '第' + courseSemester + '学期';
      const planVersion = courseYear + '版';
      const semesterType = courseSemester % 2 === 0 ? '春季学期' : '秋季学期';

      //如果当前月份是1~8月是春季学期，如果是9~2月是秋季学期，statusType的值看课程是不是在当前学期：进行中、未开始
      const currentMonth = new Date().getMonth() + 1; // 月份从0开始，所以加1
      let statusType = '未开始';

      if (currentMonth >= 1 && currentMonth <= 8) {
        // 使用Math.ceil向上取整，确保courseYear + Math.ceil(courseSemester/2)表示课程真实开课年份
        statusType = (courseYear + Math.ceil(courseSemester/2) <= currentYear) && courseSemester % 2 === 0 ? '进行中' : '未开始';
      } else {
        statusType = (courseYear + Math.ceil(courseSemester/2) <= currentYear) && courseSemester % 2 === 1 ? '进行中' : '未开始';
      }

      // 设置状态类名
      const statusClass = statusType === '进行中' ? 'status-active' : (statusType === '未开始' ? 'status-finished' : 'status-upcoming');

      // 处理课程性质
      const courseNatureValue = course.courseNature;
      const courseNatureText = typeof courseNatureValue === 'number'
        ? (courseNatureValue === 1 ? '必修' : '选修')
        : (courseNatureValue || '未知');
      // 将课程的基本信息缓存到localStorage
      const cacheInfo: CourseCacheInfo = {
        ...course, // 保留原始数据
        courseId: String(course.courseId),
        courseName: course.courseName,
        courseCode: course.courseCode,
        planId: String(course.planId),
        majorId: String(course.majorId),
        courseLeader: String(course.courseLeader)
      };
      storage.set('courseCacheInfo', cacheInfo);

      // 返回处理后的课程数据
      return {
        ...course, // 保留原始数据
        courseId: course.courseId,
        courseName: course.courseName,
        courseCode: course.courseCode,
        teachingSemester,
        planVersion,
        courseNature: courseNatureText,
        semesterType,
        status: statusType,
        statusClass
      };
    });

    // 存储原始数据用于筛选
    allCourses.value = processedCourses;

    // 生成培养方案版本选项
    const versionSet = new Set<string>();
    processedCourses.forEach((course: any) => {
      if (course.courseVersion) {
        versionSet.add(course.courseVersion.toString());
      }
    });

    planVersionOptions.value = Array.from(versionSet)
      .map(version => ({
        label: `${version}版`,
        value: version
      }))
      .sort((a, b) => {
        // 按版本号降序排列（数字比较）
        const numA = parseInt(a.value) || 0;
        const numB = parseInt(b.value) || 0;
        return numB - numA;
      });

    console.log('生成的培养方案版本选项:', planVersionOptions.value);

    // 初始显示所有课程
    managedCourses.value = [...allCourses.value];

  } catch (error) {
    console.error('获取课程数据失败:', error);
    // 保留空数组作为错误时的回退
    allCourses.value = [];
    managedCourses.value = [];
    planVersionOptions.value = [];
  } finally {
    loading.value = false;
  }
};

// 筛选事件处理方法
const handlePlanVersionChange = (value: any) => {
  console.log('培养方案版本筛选:', value);
  selectedPlanVersions.value = Array.isArray(value) ? value : [];
  applyFilters();
};

const handleCourseSemesterChange = (value: any) => {
  console.log('课程学期筛选:', value);
  selectedSemesters.value = Array.isArray(value) ? value : [];
  applyFilters();
};

// 应用筛选逻辑
const applyFilters = () => {
  console.log('应用筛选条件:', {
    planVersions: selectedPlanVersions.value,
    semesters: selectedSemesters.value
  });

  let filteredCourses = [...allCourses.value];

  // 1. 培养方案版本筛选
  if (selectedPlanVersions.value.length > 0) {
    filteredCourses = filteredCourses.filter(course => {
      const courseVersion = course.courseVersion?.toString() || '';
      return selectedPlanVersions.value.includes(courseVersion);
    });
    console.log('按培养方案版本筛选后的课程数量:', filteredCourses.length);
  }

  // 2. 课程上课学期筛选
  if (selectedSemesters.value.length > 0) {
    filteredCourses = filteredCourses.filter(course => {
      const courseSemester = course.courseSemester?.toString() || '';
      return selectedSemesters.value.includes(courseSemester);
    });
    console.log('按课程学期筛选后的课程数量:', filteredCourses.length);
  }

  // 更新显示的课程列表
  managedCourses.value = filteredCourses;

  console.log('最终筛选结果:', {
    原始课程数量: allCourses.value.length,
    筛选后课程数量: managedCourses.value.length,
    筛选条件: {
      培养方案版本: selectedPlanVersions.value,
      课程学期: selectedSemesters.value
    }
  });
};

// 清除所有筛选条件
const clearAllFilters = () => {
  selectedPlanVersions.value = [];
  selectedSemesters.value = [];
  managedCourses.value = [...allCourses.value];
  console.log('已清除所有筛选条件，显示全部课程');
};

const viewCourseDetail = (courseId: number) => {
  // 查找选中的课程对象
  const selectedCourse = managedCourses.value.find(course => course.courseId === courseId);

  // 保存课程ID和完整课程数据
  selectedCourseId.value = courseId;
  selectedCourseData.value = selectedCourse || null;

  // 显示详情对话框
  showCourseDetailsDialog.value = true;
};

const manageCourse = (courseId: number) => {
  // 查找选中的课程对象
  const selectedCourse = managedCourses.value.find(course => course.courseId === courseId);
  if (selectedCourse) {
    // 构建缓存对象
    const cacheInfo: CourseCacheInfo = {
      courseId: String(selectedCourse.courseId),
      courseName: selectedCourse.courseName,
      courseCode: String(selectedCourse.courseCode)
    };
    storage.set('lastCourseDetail', cacheInfo);
  }
  router.push({
    path: `/course/leader/objective/${courseId}`,
    query: { courseId }
  });
};

// 生命周期
onMounted(async () => {
  // 先加载专业列表
  await loadMajorList()

  // 1. URL有专业信息
  const majorId = route.params.majorId as string
  const majorName = route.query.majorName as string
  const type = route.query.type as string

  if (majorId && majorName && type) {
    await setCurrentMajor({ id: majorId, name: majorName, type }, false)
    return
  }

  // 2. 只有一个专业，自动选择
  if (majorList.value.length === 1) {
    await setCurrentMajor(majorList.value[0], true)
    return
  }

  // 3. 其它情况弹窗
  showMajorSelector.value = true
})
</script>

<style lang="less" scoped>
// 完全复用教师工作台的样式风格
.course-leader-dashboard {
  min-height: 100%;
  background: linear-gradient(135deg, var(--td-brand-color) 0%, var(--td-brand-color-8) 100%);
  position: relative;
  overflow: hidden;

  // 移除动画背景装饰元素

  .dashboard-header {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding: 24px;
    position: relative;
    z-index: 2;

    .header-content {
      margin: 0 auto;


      .breadcrumb-section {
        margin-bottom: 16px;

        :deep(.t-breadcrumb) {
          .t-breadcrumb__item {
            color: var(--td-text-color-secondary);

            &:last-child {
              color: var(--td-text-color-anti);
              font-weight: 500;
            }
          }

          .t-breadcrumb__separator {
            color: var(--td-text-color-placeholder);
          }
        }
      }

      .title-info {
        display: flex;
        justify-content: space-between;
        align-items: flex-end;
        .title-container {
          position: relative;
          display: inline-block;

          .dashboard-title {
            font-size: 32px;
            font-weight: 700;
            color: var(--td-text-color-anti);
            margin: 0;
            text-shadow: 0 2px 10px var(--td-shadow-1);
            padding-right: 60px;
          }

          .role-tag {
            position: absolute;
            top: -4px;
            right: 0;
            backdrop-filter: blur(10px);
            font-size: 10px;

            :deep(.t-tag__text) {
              color: var(--td-brand-color);
              font-weight: 600;
              font-size: 10px;
            }
          }
        }
        .title-actions {
          flex-shrink: 0;

          :deep(.t-button) {
            background: rgba(255, 255, 255, 0.1);
            border-color: rgba(255, 255, 255, 0.3);
            color: var(--td-text-color-anti);

            &:hover {
              background: rgba(255, 255, 255, 0.2);
              border-color: rgba(255, 255, 255, 0.5);
            }
          }
        }
        .dashboard-subtitle {
          font-size: 16px;
          color: rgba(255, 255, 255, 0.85);
          margin: 0;
          line-height: 1.5;
        }
      }
    }
  }

  .dashboard-content {
    margin: 0 auto;
    width: 100%;
    padding: 32px 24px;

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-end;
      margin-bottom: 32px;

      .header-left {
        flex: 1;

        .section-title {
          font-size: 24px;
          font-weight: 700;
          color: var(--td-text-color-anti);
          margin-bottom: 8px;
          display: flex;
          align-items: center;
          gap: 12px;

          .title-icon {
            color: var(--td-text-color-anti);
            filter: drop-shadow(0 2px 4px var(--td-shadow-1));
          }
        }

        .section-subtitle {
          font-size: 16px;
          color: rgba(255, 255, 255, 0.85);
          margin: 0;
        }
      }

      .header-right {
        flex-shrink: 0;

        .filter-controls {
          display: flex;
          gap: 16px;
          align-items: center;
          flex-wrap: wrap;
        }

        :deep(.t-select) {
          .t-input {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);

            .t-input__inner {
              color: var(--td-text-color-anti);

              &::placeholder {
                color: var(--td-text-color-placeholder);
              }
            }

            .t-input__suffix {
              color: var(--td-text-color-secondary);
            }
          }

          &:hover .t-input {
            border-color: rgba(255, 255, 255, 0.5);
          }
        }

        :deep(.t-button) {
          background: rgba(255, 255, 255, 0.1);
          border-color: rgba(255, 255, 255, 0.3);
          color: var(--td-text-color-anti);

          &:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.5);
          }
        }
      }
    }

    // 总体统计样式
    .statistics-section {
      margin-bottom: 48px;

      .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
        gap: 20px;

        .stat-card {
          opacity: 0;
          transform: translateY(30px);
          animation: slideInUp 0.6s ease-out var(--delay) both;

          .stat-card-inner {
            background: white;
            border: 1px solid rgba(0, 0, 0, 0.1);
            border-radius: 16px;
            padding: 20px;
            display: flex;
            align-items: center;
            gap: 16px;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;

            &:hover {
              transform: translateY(-4px);
              box-shadow: 0 12px 32px var(--td-shadow-3);
              background: white;
              border-color: rgba(0, 0, 0, 0.2);
            }

            .stat-icon-wrapper {
              width: 48px;
              height: 48px;
              border-radius: 12px;
              display: flex;
              align-items: center;
              justify-content: center;
              flex-shrink: 0;

              &.icon-blue {
                background: var(--td-brand-color);
              }

              &.icon-green {
                background: var(--td-success-color);
              }

              &.icon-orange {
                background: var(--td-warning-color);
              }

              &.icon-purple {
                background: var(--td-brand-color-6);
              }

              :deep(.t-icon) {
                color: var(--td-text-color-anti);
                filter: drop-shadow(0 2px 4px var(--td-shadow-1));
              }
            }

            .stat-content {
              flex: 1;

              .stat-number {
                font-size: 24px;
                font-weight: 700;
                color: var(--td-text-color-primary);
                line-height: 1;
                margin-bottom: 4px;
              }

              .stat-label {
                font-size: 14px;
                color: var(--td-text-color-secondary);
                margin-bottom: 4px;
              }
            }
          }
        }
      }
    }

    // 当前学期课程样式
    .managed-courses-section {
      margin-bottom: 48px;

      .courses-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
        gap: 24px;

        .course-card {
          opacity: 0;
          transform: translateY(30px);
          animation: slideInUp 0.6s ease-out var(--delay) both;

          .course-card-inner {
            background: white;
            border: 1px solid rgba(0, 0, 0, 0.08);
            border-radius: 16px;
            padding: 28px;
            position: relative;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            height: 100%;
            display: flex;
            flex-direction: column;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

            &:hover {
              background: white;
              border-color: var(--td-brand-color-3);
              transform: translateY(-4px);
              box-shadow: 0 12px 40px var(--td-brand-color-2);
            }
          }

          .course-status {
            position: absolute;
            top: 20px;
            right: 20px;
            padding: 6px 14px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 600;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

            &.status-active {
              background: var(--td-success-color);
              color: var(--td-text-color-anti);
            }

            &.status-finished {
              background: var(--td-brand-color);
              color: var(--td-text-color-anti);
            }
          }

          .course-header {
            margin-bottom: 20px;

            .course-name {
              font-size: 20px;
              font-weight: 600;
              color: var(--td-text-color-primary);
              margin: 0 0 12px 0;
              padding-right: 80px;
              line-height: 1.3;
              display: flex;
              align-items: center;
              gap: 12px;
              flex-wrap: wrap;

              .course-code-inline {
                font-size: 14px;
                font-weight: 500;
                color: var(--td-text-color-secondary);
                background: var(--td-brand-color-1);
                padding: 4px 10px;
                border-radius: 6px;
                border: 1px solid var(--td-brand-color-3);
                white-space: nowrap;
              }
            }

            .course-meta {
              display: flex;
              align-items: center;
              gap: 12px;
              flex-wrap: wrap;

              .course-code {
                display: inline-block;
                font-size: 13px;
                color: var(--td-text-color-secondary);
                background: var(--td-brand-color-1);
                padding: 6px 12px;
                border-radius: 8px;
                font-weight: 500;
                border: 1px solid var(--td-brand-color-3);
              }

              .semester-info {
                display: flex;
                justify-content: flex-start;

                .semester-type-block {
                  display: inline-block;
                  background: var(--td-brand-color-1);
                  color: var(--td-brand-color);
                  padding: 6px 12px;
                  border-radius: 16px;
                  font-size: 12px;
                  font-weight: 600;
                  border: 1px solid var(--td-brand-color-3);
                  transition: all 0.3s ease;

                  &:hover {
                    background: var(--td-brand-color-2);
                    border-color: var(--td-brand-color-4);
                  }
                }
              }
            }
          }

          .course-stats {
            flex: 1;
            margin-bottom: 24px;

            .stats-row {
              display: flex;
              gap: 16px;

              .stat-item {
                display: flex;
                align-items: center;
                gap: 12px;
                background: var(--td-success-color-1);
                padding: 14px 18px;
                border-radius: 12px;
                flex: 1;
                border: 1px solid var(--td-success-color-3);
                transition: all 0.3s ease;
                position: relative;
                overflow: hidden;

                &:hover {
                  background: var(--td-success-color-2);
                  transform: translateY(-2px);
                  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
                }

                &:first-child {
                  background: var(--td-brand-color-1);
                  border-color: var(--td-brand-color-3);

                  &:hover {
                    background: var(--td-brand-color-2);
                  }

                  .stat-icon.semester-icon :deep(.t-icon) {
                    color: var(--td-brand-color);
                  }
                }

                &:last-child {
                  .stat-icon.version-icon :deep(.t-icon) {
                    color: var(--td-success-color);
                  }
                }

                .stat-icon {
                  width: 36px;
                  height: 36px;
                  border-radius: 8px;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  background: rgba(255, 255, 255, 0.8);
                  flex-shrink: 0;

                  :deep(.t-icon) {
                    font-size: 16px;
                    font-weight: 600;
                  }
                }

                .stat-info {
                  flex: 1;

                  .stat-label-with-icon {
                    display: flex;
                    align-items: center;
                    gap: 6px;
                    margin-bottom: 6px;
                    font-size: 12px;
                    color: var(--td-text-color-secondary);
                    font-weight: 500;

                    .inline-icon {
                      font-size: 12px;
                      opacity: 0.8;
                    }
                  }

                  .stat-value {
                    font-size: 16px;
                    font-weight: 700;
                    color: var(--td-text-color-primary);
                    line-height: 1.2;
                  }
                }
              }
            }
          }

          .course-actions {
            display: flex;
            gap: 12px;

            :deep(.t-button) {
              flex: 1;
              font-size: 13px;
              font-weight: 500;
              padding: 8px 16px;
              border-radius: 8px;
              transition: all 0.3s ease;

              &.t-button--theme-primary {
                background: var(--td-brand-color);
                border-color: var(--td-brand-color);
                color: white;

                &:hover {
                  background: var(--td-brand-color-7);
                  border-color: var(--td-brand-color-7);
                  transform: translateY(-1px);
                  box-shadow: 0 4px 12px var(--td-brand-color-3);
                }
              }

              &.t-button--theme-warning {
                background: var(--td-warning-color);
                border-color: var(--td-warning-color);
                color: white;

                &:hover {
                  background: var(--td-warning-color-7);
                  border-color: var(--td-warning-color-7);
                  transform: translateY(-1px);
                  box-shadow: 0 4px 12px var(--td-warning-color-3);
                }
              }

              &.t-button--theme-default {
                background: var(--td-bg-color-container);
                border: 1px solid var(--td-border-level-1-color);
                color: var(--td-text-color-primary);

                &:hover {
                  background: var(--td-brand-color-1);
                  border-color: var(--td-brand-color-3);
                  color: var(--td-brand-color);
                  transform: translateY(-1px);
                  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                }
              }
            }
          }
        }
      }
    }
  }
}

// 完全复制教师工作台的动画效果
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 移除背景浮动动画

// 响应式设计 - 采用教师工作台的响应式布局
@media (max-width: 1200px) {
  .course-leader-dashboard {
    .dashboard-content {
      padding: 24px 16px;

      .statistics-section .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 16px;
      }

      .managed-courses-section .courses-grid {
        grid-template-columns: repeat(auto-fit, minmax(340px, 1fr));
        gap: 20px;
      }
    }
  }
}

@media (max-width: 768px) {
  .course-leader-dashboard {
    .dashboard-header {
      padding: 16px;

      .header-content {
        .title-info .title-container {
          .dashboard-title {
            font-size: 24px;
            padding-right: 0;
          }

          .role-tag {
            position: static;
            margin-top: 8px;
          }
        }
      }
    }

    .dashboard-content {
      padding: 20px 16px;

      .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
        margin-bottom: 24px;

        .header-right {
          width: 100%;

          .filter-controls {
            flex-direction: column;
            gap: 12px;
            width: 100%;
          }

          :deep(.t-select) {
            width: 100% !important;
          }
        }
      }

      .statistics-section {
        margin-bottom: 32px;

        .stats-grid {
          grid-template-columns: 1fr;
          gap: 12px;
        }
      }

      .managed-courses-section {
        .courses-grid {
          grid-template-columns: 1fr;
          gap: 16px;

          .course-card {
            .course-card-inner {
              .course-header {
                .course-name {
                  font-size: 18px;
                  flex-direction: column;
                  align-items: flex-start;
                  gap: 8px;

                  .course-code-inline {
                    font-size: 13px;
                    align-self: flex-start;
                  }
                }
              }

              .course-stats {
                .stats-row {
                  flex-direction: column;
                  gap: 12px;

                  .stat-item {
                    padding: 12px 16px;

                    .stat-icon {
                      width: 32px;
                      height: 32px;
                    }

                    .stat-info {
                      .stat-label-with-icon {
                        font-size: 11px;
                      }

                      .stat-value {
                        font-size: 15px;
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>
