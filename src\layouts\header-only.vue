<template>
  <base-layout
    layout-mode="headerOnly"
    :show-sidebar="false"
  >
    <!-- Header组件 -->
    <template #header>
      <header-only-header />
    </template>
    
    <!-- 内容区域 -->
    <template #content>
      <router-view />
    </template>
    
    <!-- Footer组件 -->
    <template #footer>
      <header-only-footer />
    </template>
    
    <!-- 设置面板 -->
    <template #setting>
      <setting-panel />
    </template>
  </base-layout>
</template>

<script setup lang="ts">
import BaseLayout from './components/BaseLayout.vue';
import HeaderOnlyHeader from './components/HeaderOnlyHeader.vue';
import HeaderOnlyFooter from './components/HeaderOnlyFooter.vue';
import SettingPanel from './setting.vue';
</script>

 