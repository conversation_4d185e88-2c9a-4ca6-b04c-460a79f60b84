<template>
  <div class="file-viewer">
    <div class="file-viewer-header">
      <div class="file-info">
        <t-icon :name="getFileIcon(fileType)" class="file-icon" />
        <span class="file-name" :title="fileName">{{ fileName }}</span>
      </div>
      <t-space>
        <t-button v-if="fileType === 'docx' || fileType === 'doc'" theme="primary" variant="text" @click="toggleDocViewer">
          {{ useDocViewer ? '关闭Office预览' : '使用Office预览' }}
        </t-button>
        <t-button theme="default" variant="text" @click="openInNewTab">
          <template #icon><t-icon name="browse" /></template>
          新窗口打开
        </t-button>
        <t-button theme="default" variant="text" @click="downloadFile" :loading="isDownloading">
          <template #icon><t-icon name="download" /></template>
          下载
        </t-button>
        <t-button theme="default" variant="text" @click="toggleFullscreen">
          <template #icon><t-icon :name="isFullscreen ? 'minimize' : 'fullscreen'" /></template>
          {{ isFullscreen ? '退出全屏' : '全屏' }}
        </t-button>
      </t-space>
    </div>
    
    <div :class="['file-viewer-content', { 'fullscreen': isFullscreen }]">
      <!-- PDF查看器 -->
      <iframe 
        v-if="fileType === 'pdf'" 
        :src="fileUrl"
        class="pdf-viewer"
        frameborder="0"
      ></iframe>
      
      <!-- Office文档查看器 -->
      <div 
        v-else-if="(fileType === 'docx' || fileType === 'doc') && useDocViewer" 
        class="doc-viewer-container"
      >
        <t-loading :loading="docViewerLoading" overlay content="正在加载文档，请稍候..." :size="'large'">
          <iframe 
            ref="docViewerIframe"
            :src="officePrevierUrl"
            class="doc-viewer"
            frameborder="0"
            @load="handleDocViewerLoad"
            @error="handleDocViewerError"
          ></iframe>
        </t-loading>
      </div>
      
      <!-- Word文档预览模拟 -->
      <div v-else-if="fileType === 'docx' || fileType === 'doc'" class="doc-preview-simulation">
        <div class="doc-header">
          <div class="doc-title">{{ fileName }}</div>
          <div class="ribbon">
            <div class="ribbon-tab active">开始</div>
            <div class="ribbon-tab">插入</div>
            <div class="ribbon-tab">页面布局</div>
            <div class="ribbon-tab">引用</div>
            <div class="ribbon-tab">视图</div>
          </div>
        </div>
        <div class="doc-content">
          <div class="doc-page">
            <div class="doc-paragraph doc-title-text">{{ getDocTitle() }}</div>
            <div class="doc-paragraph">
              本文档旨在提供<strong>{{ getDocType() }}</strong>相关的重要信息。作为教学资料的一部分，本文档将帮助教师和学生更好地理解课程内容和要求。
            </div>
            <div class="doc-paragraph doc-heading">一、课程目标</div>
            <div class="doc-paragraph">
              1. 掌握{{ getCourseName() }}的基本概念和理论
            </div>
            <div class="doc-paragraph">
              2. 了解{{ getCourseName() }}在实际应用中的重要性
            </div>
            <div class="doc-paragraph">
              3. 培养学生的实践能力和创新思维
            </div>
            <div class="doc-paragraph doc-heading">二、教学内容</div>
            <div class="doc-paragraph">
              本课程分为理论教学和实践教学两部分，具体内容如下：
            </div>
            <div class="doc-paragraph">
              <strong>理论部分：</strong>基础知识、核心概念、前沿技术
            </div>
            <div class="doc-paragraph">
              <strong>实践部分：</strong>课堂练习、实验项目、课程设计
            </div>
            <div class="doc-paragraph doc-heading">三、教学安排</div>
            <div class="doc-paragraph">
              总学时：48学时（理论32学时，实践16学时）
            </div>
            <div class="doc-paragraph">
              教学进度：每周2学时，共24周
            </div>
            <div class="doc-paragraph doc-heading">四、考核方式</div>
            <div class="doc-paragraph">
              考核采用多元化评价方式，包括：
            </div>
            <div class="doc-paragraph">
              - 平时成绩（30%）：出勤、课堂表现、作业
            </div>
            <div class="doc-paragraph">
              - 实验成绩（30%）：实验报告、项目完成情况
            </div>
            <div class="doc-paragraph">
              - 期末考试（40%）：闭卷笔试
            </div>
          </div>
        </div>
      </div>
      
      <!-- 其他文件类型 -->
      <div v-else class="unsupported-file">
        <t-empty
          :image="fileType === 'docx' || fileType === 'doc' 
            ? 'https://tdesign.gtimg.com/starter/empty-data.png' 
            : 'https://tdesign.gtimg.com/starter/empty-file.png'"
          :description="fileType === 'docx' || fileType === 'doc' 
            ? '点击上方「使用Office预览」可查看Word文档' 
            : '暂不支持该类型文件直接预览'"
        >
          <template #actions>
            <t-space>
              <t-button theme="primary" @click="fileType === 'docx' || fileType === 'doc' ? toggleDocViewer() : downloadFile()">
                {{ fileType === 'docx' || fileType === 'doc' ? '使用Office预览' : '下载文件' }}
              </t-button>
            </t-space>
          </template>
        </t-empty>
      </div>
    </div>
    
    <div v-if="isDownloading" class="download-progress">
      <t-progress :percentage="downloadProgress" theme="line" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, PropType, onMounted } from 'vue';
import { 
  MessagePlugin, 
  Progress as TProgress, 
  Space as TSpace, 
  Button as TButton, 
  Icon as TIcon, 
  Empty as TEmpty,
  Loading as TLoading,
} from 'tdesign-vue-next';

const props = defineProps({
  fileUrl: {
    type: String,
    required: true
  },
  fileName: {
    type: String,
    default: '未命名文件'
  },
  fileType: {
    type: String,
    default: 'other',
    validator: (value: string) => ['pdf', 'doc', 'docx', 'image', 'other'].includes(value)
  }
});

const isFullscreen = ref(false);
const useDocViewer = ref(false);
const isDownloading = ref(false);
const downloadProgress = ref(0);
const docViewerLoading = ref(false);
const docViewerIframe = ref(null);
const docViewerLoadError = ref(false);

// 获取文档标题
const getDocTitle = () => {
  return props.fileName || '教学文档';
};

// 获取文档类型
const getDocType = () => {
  if (props.fileName.includes('教学计划')) return '教学计划';
  if (props.fileName.includes('课程大纲')) return '课程大纲';
  if (props.fileName.includes('考核方案')) return '考核方案';
  if (props.fileName.includes('实验')) return '实验指导';
  return '教学材料';
};

// 获取课程名称
const getCourseName = () => {
  if (props.fileName.includes('数据结构')) return '数据结构';
  if (props.fileName.includes('计算机网络')) return '计算机网络';
  if (props.fileName.includes('操作系统')) return '操作系统';
  if (props.fileName.includes('编译原理')) return '编译原理';
  return '本课程';
};

// 根据文件类型获取图标
const getFileIcon = (fileType: string) => {
  switch (fileType) {
    case 'pdf':
      return 'file-pdf';
    case 'doc':
    case 'docx':
      return 'file-doc';
    case 'image':
      return 'file-image';
    default:
      return 'file';
  }
};

// 切换全屏状态
const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value;
};

// 切换Office文档查看器
const toggleDocViewer = () => {
  useDocViewer.value = !useDocViewer.value;
  if (useDocViewer.value) {
    docViewerLoading.value = true;
    docViewerLoadError.value = false;
    MessagePlugin.info('正在加载Office文档，请稍候...');
  }
};

// 处理文档查看器加载完成
const handleDocViewerLoad = () => {
  docViewerLoading.value = false;
  if (!docViewerLoadError.value) {
    MessagePlugin.success('文档加载成功');
  }
};

// 处理文档查看器加载错误
const handleDocViewerError = () => {
  docViewerLoading.value = false;
  docViewerLoadError.value = true;
  MessagePlugin.error('文档加载失败，请检查网络连接或文件格式');
  
  // 自动切换回内置预览模式
  useDocViewer.value = false;
};

// 获取Office预览URL
const officePrevierUrl = computed(() => {
  if (!props.fileUrl) return '';
  
  // 构建完整URL
  const origin = window.location.origin;
  const fullUrl = props.fileUrl.startsWith('http') 
    ? props.fileUrl 
    : `${origin}${props.fileUrl}`;
  
  // 使用Office Online Viewer
  return `https://view.officeapps.live.com/op/view.aspx?src=${encodeURIComponent(fullUrl)}`;
});

// 在新窗口打开
const openInNewTab = () => {
  if (props.fileType === 'doc' || props.fileType === 'docx') {
    // 对于Word文档，使用Office Online Viewer
    window.open(officePrevierUrl.value, '_blank');
  } else {
    // 直接打开文件
    window.open(props.fileUrl, '_blank');
  }
};

// 下载文件
const downloadFile = async () => {
  if (!props.fileUrl) {
    MessagePlugin.error('文件路径不存在');
    return;
  }
  
  try {
    isDownloading.value = true;
    downloadProgress.value = 0;
    
    const updateProgress = () => {
      if (downloadProgress.value < 95) {
        downloadProgress.value += Math.floor(Math.random() * 10) + 1;
        setTimeout(updateProgress, 200);
      }
    };
    
    updateProgress();
    
    // 创建下载链接
    const link = document.createElement('a');
    link.href = props.fileUrl;
    link.download = props.fileName || 'download-file';
    link.target = '_blank';
    
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    // 完成下载
    setTimeout(() => {
      downloadProgress.value = 100;
      setTimeout(() => {
        isDownloading.value = false;
      }, 500);
    }, 1000);
    
    MessagePlugin.success('文件下载已开始');
  } catch (error) {
    console.error('下载文件失败:', error);
    MessagePlugin.error('下载文件失败');
    isDownloading.value = false;
  }
};

onMounted(() => {
  // 初始化
  if (props.fileType === 'doc' || props.fileType === 'docx') {
    // 自动使用内置预览
    // useDocViewer.value = true;
  }
});
</script>

<style lang="less" scoped>
.file-viewer {
  width: 100%;
  border: 1px solid #e7e7e7;
  border-radius: 4px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  
  .file-viewer-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background-color: #f9f9f9;
    border-bottom: 1px solid #e7e7e7;
    
    .file-info {
      display: flex;
      align-items: center;
      overflow: hidden;
      
      .file-icon {
        margin-right: 8px;
        font-size: 20px;
      }
      
      .file-name {
        font-size: 14px;
        font-weight: 500;
        color: #333;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        max-width: 300px;
      }
    }
  }
  
  .file-viewer-content {
    height: 500px;
    position: relative;
    background-color: #f5f5f5;
    transition: all 0.3s ease;
    
    &.fullscreen {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: 10000;
      height: 100vh;
      width: 100vw;
      background-color: rgba(0, 0, 0, 0.9);
      padding: 20px;
      border-radius: 0;
      
      .doc-preview-simulation {
        max-width: 1000px;
        margin: 0 auto;
      }
    }
    
    .pdf-viewer, .doc-viewer {
      width: 100%;
      height: 100%;
      border: none;
      background-color: white;
    }
    
    .doc-viewer-container {
      width: 100%;
      height: 100%;
    }
    
    .unsupported-file {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100%;
      width: 100%;
    }
    
    .doc-preview-simulation {
      width: 100%;
      height: 100%;
      background-color: #f0f0f0;
      display: flex;
      flex-direction: column;
      overflow: hidden;
      
      .doc-header {
        background-color: #2b579a;
        padding: 8px 16px;
        
        .doc-title {
          color: white;
          font-size: 14px;
          font-weight: 500;
          margin-bottom: 8px;
        }
        
        .ribbon {
          display: flex;
          background-color: #f3f2f1;
          border-top-left-radius: 4px;
          border-top-right-radius: 4px;
          
          .ribbon-tab {
            padding: 6px 12px;
            font-size: 12px;
            color: #333;
            cursor: pointer;
            
            &.active {
              background-color: white;
              border-top: 2px solid #2b579a;
              padding-top: 4px;
              font-weight: 500;
            }
            
            &:hover:not(.active) {
              background-color: #f9f9f9;
            }
          }
        }
      }
      
      .doc-content {
        flex: 1;
        padding: 20px;
        overflow: auto;
        background-color: #5a5a5a;
        
        .doc-page {
          width: 100%;
          max-width: 800px;
          min-height: 1000px;
          margin: 0 auto;
          background-color: white;
          padding: 50px;
          box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
          
          .doc-paragraph {
            margin-bottom: 12px;
            line-height: 1.6;
            text-align: justify;
          }
          
          .doc-title-text {
            font-size: 20px;
            font-weight: bold;
            text-align: center;
            margin-bottom: 24px;
          }
          
          .doc-heading {
            font-size: 16px;
            font-weight: bold;
            margin-top: 20px;
            margin-bottom: 16px;
          }
        }
      }
    }
  }
  
  .download-progress {
    padding: 8px 16px;
    background-color: #f9f9f9;
    border-top: 1px solid #e7e7e7;
  }
}
</style>