import {
  teacherExamMockData,
  courseAchievementMockData,
  getTeacherInfoById,
  teacherCourseMockData,
  teacherHomeworkMockData,
  teacherInfoMockData
} from '@/api/mock/teacherMockData';
import request from '@/utils/request';

// API响应类型定义
interface ApiResponse<T = any> {
  code: string | number;
  message: string;
  data: T;
}

//接口类型
export interface TeacherInfo{
    id:number;
    name:string;
    number:string;
    gender:string;
    title:string;
    college:string;
    courseCount:number;
    studentCount:number;
    email:string;
    phone:string;
    courseList:string[];
}

//获取教师数据
export async function getTeacherInfo(id:number):Promise<TeacherInfo>{
    try{
        const res = await request({
          url: `/teacher/${id}/info`,
          method: 'get'
        }) as ApiResponse;
        if(res && res.code===200){
            return res.data;
        }
        return getTeacherInfoById(id);
    }catch(error){
        console.error('获取教师数据失败:', error);
        throw error;
    }
}

export interface TeacherCourse{
    id:number;
    name:string;
    image:string;
    status:string;
    reach:number;
    semester:string;//学期
    assessment_method_id:number;//考核方式id


}

//获取课程数据
export async function getCourseInfo(id:number):Promise<TeacherCourse[]>{
  try{
    const res = await request({
      url: `/teacher/${id}/courses`,
      method: 'get'
    }) as ApiResponse;
    if(res && res.code===200){
      return res.data;
    }
    return teacherCourseMockData;
  }catch(error){
    console.error('获取课程数据失败:', error);
    throw error;
  }
}
export interface TeacherHomework{
    id:number;
    title:string;
    classes:string;
    classId:string;
    startTime:string;
    endTime:string;
    status:string;
    submissionCount:number;
    viewCount:number;
    commentCount:number;
    type:string;
}
//获取作业数据
export async function getHomeworkInfo(course_id: number, class_id: string | number): Promise<TeacherHomework[]> {
  try {
    // 如果班级ID为'all'，获取所有班级作业
    if (typeof class_id === 'string' && class_id === 'all') {
      const res = await request({
        url: `/courses/${course_id}/tasks/list`,
        method: 'get'
      }) as ApiResponse;
      if (res && res.code === "200") {
        return res.data;
      }
      return teacherHomeworkMockData;
    }
    
    // 否则获取指定班级的作业
    const res = await request({
      url: `/courses/${course_id}/classes/${class_id}/tasks/list`,
      method: 'get'
    }) as ApiResponse;
    if (res && res.code === "200") {
      return res.data;
    }
    
    // 使用模拟数据时，如果为'all'返回所有作业，否则根据classId过滤
    if (typeof class_id === 'string' && class_id === 'all') {
      return teacherHomeworkMockData;
    } else {
      return teacherHomeworkMockData.filter(item => 
        item.classId.split(',').includes(String(class_id))
      );
    }
  } catch (error) {
    console.error('获取作业数据失败:', error);
    throw error;
  }
}
//获取试卷信息
export interface TeacherExam{
  id:number;
  title:string;
  classes:string;
  classId:string;
  startTime:string;
  endTime:string;
  status:string;
  submissionCount:number;
  viewCount:number;
  commentCount:number;
  type:string;
}
export async function getExamInfo(course_id: number, class_id: string | number): Promise<TeacherExam[]> {
  try {
    // 如果班级ID为'all'，获取所有班级试卷
    if (typeof class_id === 'string' && class_id === 'all') {
      const res = await request({
        url: `/courses/${course_id}/exams/list`,
        method: 'get'
      }) as ApiResponse;
      if (res && res.code === "200") {
        return res.data;
      }
      return teacherExamMockData;
    }
    
    // 否则获取指定班级的试卷
    const res = await request({
      url: `/courses/${course_id}/classes/${class_id}/exams/list`,
      method: 'get'
    }) as ApiResponse;
    if (res && res.code === "200") {
      return res.data;
    }
    
    // 使用模拟数据时，如果为'all'返回所有试卷，否则根据classId过滤
    if (typeof class_id === 'string' && class_id === 'all') {
      return teacherExamMockData;
    } else {
      return teacherExamMockData.filter(item => 
        item.classId.split(',').includes(String(class_id))
      );
    }
  } catch (error) {
    console.error('获取试卷数据失败:', error);
    throw error;
  }
}

