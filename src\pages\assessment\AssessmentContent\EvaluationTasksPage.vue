<template>
  <div class="evaluation-tasks-page">
    <EvaluationTasksTab
      :course-id="courseId"
      :task-id="taskId"
      :course-name="courseName"
      :year="year"
      :term="term"
      :assessment-id="assessmentId"
      :is-return="isReturn"
    />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useRoute } from 'vue-router';
import EvaluationTasksTab from './components/EvaluationTasksTab.vue';

// 获取路由参数
const route = useRoute();

// 从路由参数中获取数据
const courseId = computed(() => route.params.courseId as string);
const taskId = computed(() => route.params.taskId as string);
const courseName = computed(() => route.query.courseName as string || '');
const year = computed(() => route.query.year as string || '');
const term = computed(() => route.query.term as string || '');
const assessmentId = computed(() => route.query.assessmentId as string || '');
const isReturn = computed(() => route.query.isReturn === 'true');

console.log('EvaluationTasksPage 路由参数:', {
  courseId: courseId.value,
  taskId: taskId.value,
  courseName: courseName.value,
  year: year.value,
  term: term.value,
  assessmentId: assessmentId.value,
  isReturn: isReturn.value
});
</script>

<style lang="less" scoped>
.evaluation-tasks-page {
  width: 100%;
  height: 100%;
  padding: var(--td-comp-paddingTB-xl) var(--td-comp-paddingLR-xl);
  background-color: var(--td-bg-color-page);
  min-height: 100vh;
}
</style>
