<template>
  <t-dialog
    v-model:visible="dialogVisible"
    header="课程详情"
    :width="800"
    :footer="false"
    class="course-details-dialog"
    :destroy-on-close="true"
    :close-on-overlay-click="true"
    @close="handleClose"
  >
    <t-loading :loading="loading" text="正在加载课程详情...">
      <div v-if="courseDetail" class="course-detail-container">
        <!-- 基本信息 -->
        <div class="detail-section">
          <h3 class="section-title">
            <t-icon name="info-circle" class="section-icon" />
            基本信息
          </h3>
          <t-descriptions :column="2" bordered>
            <t-descriptions-item label="课程名称">{{ courseDetail.courseName }}</t-descriptions-item>
            <t-descriptions-item label="课程编号">{{ courseDetail.courseCode }}</t-descriptions-item>
            <t-descriptions-item label="开课学期">第{{ courseDetail.courseSemester }}学期</t-descriptions-item>
            <t-descriptions-item label="培养方案版本">{{ courseDetail.courseVersion }}版</t-descriptions-item>
            <t-descriptions-item label="学分">{{ courseDetail.courseCredit }}</t-descriptions-item>
            <t-descriptions-item label="是否核心课程">
              <t-tag :theme="courseDetail.courseCore ? 'success' : 'default'" size="small">
                {{ courseDetail.courseCore ? '是' : '否' }}
              </t-tag>
            </t-descriptions-item>
          </t-descriptions>
        </div>

        <!-- 学时信息 -->
        <div class="detail-section">
          <h3 class="section-title">
            <t-icon name="time" class="section-icon" />
            学时信息
          </h3>
          <t-descriptions :column="2" bordered>
            <t-descriptions-item label="总学时">{{ courseDetail.courseHoursTotal }}</t-descriptions-item>
            <t-descriptions-item label="理论教学时">{{ courseDetail.courseHoursTheory }}</t-descriptions-item>
            <t-descriptions-item label="实验学时">{{ courseDetail.courseHoursExperiment }}</t-descriptions-item>
            <t-descriptions-item label="其他学时">{{ courseDetail.courseHoursOther }}</t-descriptions-item>
            <t-descriptions-item label="课外学时" :span="2">{{ courseDetail.courseHoursExtracurricular }}</t-descriptions-item>
          </t-descriptions>
        </div>

        <!-- 课程分类 -->
        <div class="detail-section">
          <h3 class="section-title">
            <t-icon name="layers" class="section-icon" />
            课程分类
          </h3>
          <t-descriptions :column="1" bordered>
            <t-descriptions-item label="课程性质">
              <t-tag :theme="getCourseNatureTheme(courseDetail.courseNature)" size="small">
                {{ courseDetail.courseNature }}
              </t-tag>
            </t-descriptions-item>
            <t-descriptions-item label="本专业课程类型">{{ courseDetail.courseType1 || '-' }}</t-descriptions-item>
            <t-descriptions-item label="专业认证课程类型">{{ courseDetail.courseType2 || '-' }}</t-descriptions-item>
            <t-descriptions-item label="国标课程类别">{{ courseDetail.courseType3 || '-' }}</t-descriptions-item>
            <t-descriptions-item label="是否考试课">
              <t-tag :theme="courseDetail.courseExam ? 'warning' : 'default'" size="small">
                {{ courseDetail.courseExam ? '是' : '否' }}
              </t-tag>
            </t-descriptions-item>
          </t-descriptions>
        </div>

        <!-- 课程目标 -->
        <div class="detail-section" v-if="courseDetail.parsedCourseTarget">
          <h3 class="section-title">
            <t-icon name="target" class="section-icon" />
            课程目标
          </h3>
          <div class="course-targets">
            <div 
              v-for="(target, index) in courseDetail.parsedCourseTarget.targetList" 
              :key="index"
              class="target-item"
            >
              <div class="target-header">
                <span class="target-number">目标 {{ index + 1 }}</span>
                <t-tag size="small" theme="primary">{{ target.GraduateTargetId }}</t-tag>
              </div>
              <div class="target-content">{{ target.targetTitle }}</div>
            </div>
          </div>
        </div>

        <!-- 考核方式 -->
        <div class="detail-section" v-if="courseDetail.parsedAssessmentMethod">
          <h3 class="section-title">
            <t-icon name="check-circle" class="section-icon" />
            考核方式
          </h3>
          <div class="assessment-methods">
            <t-tag 
              v-for="method in courseDetail.parsedAssessmentMethod" 
              :key="method.typeId"
              class="assessment-tag"
              theme="primary"
              variant="outline"
            >
              {{ method.typeName }}
            </t-tag>
          </div>
        </div>
      </div>
      
      <!-- 错误状态 -->
      <div v-else-if="error" class="error-container">
        <div class="custom-error-result">
          <t-icon name="error-circle" class="error-icon" />
          <h3 class="error-title">加载失败</h3>
          <p class="error-description">{{ error }}</p>
          <div class="error-actions">
            <t-button theme="primary" @click="loadCourseDetail">重试</t-button>
          </div>
        </div>
      </div>
    </t-loading>
  </t-dialog>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue';
import { getCourseBaseInfo, type CourseDetailInfo } from '@/api/training/course';
import { MessagePlugin } from 'tdesign-vue-next';

interface Props {
  visible: boolean;
  courseId?: number | string;
  courseData?: CourseDetailInfo; // 添加一个可选的courseData属性，用于接收完整的课程信息
}

const props = defineProps<Props>();
const emit = defineEmits<{
  'update:visible': [value: boolean];
}>();

// 响应式数据
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

const loading = ref(false);
const courseDetail = ref<CourseDetailInfo | null>(null);
const error = ref<string>('');

// 方法
const handleClose = () => {
  dialogVisible.value = false;
  courseDetail.value = null;
  error.value = '';
};

const getCourseNatureTheme = (nature: string) => {
  switch (nature) {
    case '必修': return 'success';
    case '选修': return 'primary';
    case '限选': return 'warning';
    default: return 'default';
  }
};

const loadCourseDetail = async () => {
  // 如果有传入完整的课程数据，直接使用
  if (props.courseData) {
    courseDetail.value = props.courseData;
    loading.value = false;
    return;
  }
  
  // 否则，通过API获取数据
  if (!props.courseId) {
    error.value = '课程ID不能为空';
    return;
  }

  try {
    loading.value = true;
    error.value = '';
    courseDetail.value = null; // 清空之前的数据

    const data = await getCourseDetailInfo(props.courseId);
    courseDetail.value = data;

    // 如果没有解析到课程目标或考核方式，给出提示
    if (!data.parsedCourseTarget && data.courseTarget) {
      console.warn('课程目标数据格式可能有误:', data.courseTarget);
    }
    if (!data.parsedAssessmentMethod && data.assessmentMethod) {
      console.warn('考核方式数据格式可能有误:', data.assessmentMethod);
    }

  } catch (err: any) {
    console.error('获取课程详情失败:', err);
    error.value = err.message || '获取课程详情失败，请稍后重试';
    MessagePlugin.error(error.value);
    courseDetail.value = null;
  } finally {
    loading.value = false;
  }
};

// 监听对话框显示状态、课程ID和课程数据的变化
watch(
  [() => props.visible, () => props.courseId, () => props.courseData], 
  ([visible, courseId, courseData]) => {
    if (visible && (courseId || courseData)) {
      loadCourseDetail();
    }
  }
);
</script>

<style lang="less" scoped>
.course-details-dialog {
  :deep(.t-dialog__body) {
    padding: 24px;
    max-height: 70vh;
    overflow-y: auto;
  }
}

.course-detail-container {
  .detail-section {
    margin-bottom: 32px;
    background: var(--td-bg-color-container);
    border-radius: 8px;
    padding: 20px;
    border: 1px solid var(--td-border-level-1-color);
    transition: all 0.2s ease;

    &:hover {
      border-color: var(--td-brand-color-3);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    }

    &:last-child {
      margin-bottom: 0;
    }

    .section-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 16px;
      font-weight: 600;
      color: var(--td-text-color-primary);
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 2px solid var(--td-brand-color-1);

      .section-icon {
        color: var(--td-brand-color);
        font-size: 18px;
      }
    }
  }
  
  .course-targets {
    .target-item {
      background: var(--td-bg-color-page);
      border: 1px solid var(--td-border-level-2-color);
      border-radius: 8px;
      padding: 16px;
      margin-bottom: 12px;
      transition: all 0.2s ease;

      &:hover {
        border-color: var(--td-brand-color-4);
        background: var(--td-brand-color-1);
      }

      &:last-child {
        margin-bottom: 0;
      }

      .target-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 8px;

        .target-number {
          font-weight: 600;
          color: var(--td-text-color-primary);
          font-size: 14px;
        }
      }

      .target-content {
        color: var(--td-text-color-secondary);
        line-height: 1.6;
        font-size: 14px;
      }
    }
  }
  
  .assessment-methods {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    
    .assessment-tag {
      margin: 0;
    }
  }
}

.error-container {
  padding: 40px 20px;
  text-align: center;
  
  .custom-error-result {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    
    .error-icon {
      font-size: 48px;
      color: var(--td-error-color);
      margin-bottom: 16px;
    }
    
    .error-title {
      font-size: 18px;
      font-weight: 600;
      color: var(--td-text-color-primary);
      margin-bottom: 8px;
    }
    
    .error-description {
      color: var(--td-text-color-secondary);
      margin-bottom: 24px;
    }
    
    .error-actions {
      margin-top: 8px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .course-details-dialog {
    :deep(.t-dialog) {
      width: 95% !important;
      margin: 20px auto;
    }
    
    :deep(.t-dialog__body) {
      padding: 16px;
      max-height: 80vh;
    }
  }
  
  .course-detail-container {
    .detail-section {
      margin-bottom: 24px;
      
      .section-title {
        font-size: 14px;
      }
    }
    
    .course-targets .target-item {
      padding: 12px;
    }
  }
}
</style>
