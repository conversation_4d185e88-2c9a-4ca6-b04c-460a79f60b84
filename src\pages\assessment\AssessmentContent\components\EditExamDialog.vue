<template>
  <t-dialog
    v-model:visible="visible"
    :header="false"
    :footer="false"
    :close-btn="false"
    :mode="isFullscreen ? 'full-screen' : 'modal'"
    :on-close="handleClose"
    width="min(1200px, 90vw)"
    class="edit-exam-dialog"
  >
    <!-- 自定义头部 -->
    <div v-if="examInfo" class="flex items-center justify-between p-4 bg-blue-50 border-b border-blue-200">
      <div class="flex items-center gap-3">
        <t-icon name="edit" class="text-blue-600" size="20px" />
        <h2 class="text-lg font-semibold text-gray-800">考核配置</h2>
        <t-tag theme="primary" variant="outline" size="small">{{ examInfo.courseName }}</t-tag>
      </div>
      <div class="flex items-center gap-2">
        <t-button
          theme="default"
          variant="text"
          size="small"
          @click="toggleFullscreen"
        >
          <template #icon>
            <t-icon :name="isFullscreen ? 'fullscreen-exit' : 'fullscreen'" />
          </template>
        </t-button>
        <t-button
          theme="default"
          variant="text"
          size="small"
          @click="handleClose"
        >
          <template #icon>
            <t-icon name="close" />
          </template>
        </t-button>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="p-6 max-h-[80vh] overflow-y-auto">
      <!-- 考核配置 -->
      <div>
        <div class="mb-6">
          <h3 class="text-lg font-medium text-gray-800 mb-2">考核配置模式</h3>
          <p class="text-sm text-gray-600">选择期末考核的配置方式，不同配置方式将影响教师的成绩录入选项</p>
        </div>

        <div class="space-y-4">
          <div
            v-for="mode in configModes"
            :key="mode.value"
            class="border rounded-lg p-4 cursor-pointer transition-all duration-200 hover:shadow-md"
            :class="selectedConfigMode === mode.value ? 'border-blue-500 bg-blue-50' : 'border-gray-200'"
            @click="selectedConfigMode = mode.value"
          >
            <div class="flex items-start gap-3">
              <t-radio
                :value="mode.value"
                :checked="selectedConfigMode === mode.value"
                @change="selectedConfigMode = mode.value"
              />
              <div class="flex-1">
                <h4 class="font-medium text-gray-800">{{ mode.label }}</h4>
                <p class="text-sm text-gray-600 mt-1">{{ mode.description }}</p>
                <div class="mt-2">
                  <t-tag
                    v-for="tag in mode.tags"
                    :key="tag"
                    :theme="selectedConfigMode === mode.value ? 'primary' : 'default'"
                    variant="outline"
                    size="small"
                    class="mr-2"
                  >
                    {{ tag }}
                  </t-tag>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 自定义脚部 -->
    <div class="flex justify-end p-4 border-t border-gray-200">
      <t-button theme="default" variant="outline" @click="handleClose">取消</t-button>
      <t-button theme="primary" class="ml-3" @click="handleConfirm">确认设置</t-button>
    </div>
  </t-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';

interface ConfigMode {
  value: number;
  label: string;
  description: string;
  tags: string[];
}

interface ExamInfo {
  courseName: string;
  semester: string;
  examType: string;
}

const props = defineProps<{
  visible: boolean;
  examInfo: ExamInfo | null;
  scoreType?: number; // 初始配置模式，0: 直接录入, 1: 详细录入
}>();

const emit = defineEmits<{
  'update:visible': [value: boolean];
  confirm: [config: { configMode: number }];
}>();

const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
});

const isFullscreen = ref(false);
const selectedConfigMode = ref<number | null>(null);

const configModes: ConfigMode[] = [
  {
    value: 0,
    label: '直接录入',
    description: '教师直接录入最终的总成绩，适用于已有线下成绩记录或计算方式简单的场景。',
    tags: ['快速', '便捷', '总分录入']
  },
  {
    value: 1,
    label: '详细录入',
    description: '教师需设置多个考核子项（如出勤、作业、期中、期末），并为每个子项录入成绩，系统将自动计算总成绩。',
    tags: ['灵活', '过程化', '自动计算']
  }
];

watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      selectedConfigMode.value = props.scoreType || 0;
    }
  }
);

const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value;
};

const handleClose = () => {
  visible.value = false;
};

const handleConfirm = () => {
  if (selectedConfigMode.value !== null) {
    emit('confirm', { configMode: selectedConfigMode.value });
    handleClose();
  }
};
</script>

<style scoped>
.edit-exam-dialog :deep(.t-dialog__body) {
  padding: 0;
}
</style>
