<template>
  <div class="enhanced-training-objectives">
    <!-- 顶部标题和操作按钮 -->
    <div class="header-section">
      <div class="title-container">
        <div class="title-wrapper">
          <h1 class="page-title">{{ pageTitle }}</h1>
        </div>
      </div>
    </div>

    <!-- Plan 和 Standard 信息展示 -->
    <t-card title="培养计划与标准信息" style="margin-bottom: 24px;" bordered>
      <t-row :gutter="[16, 24]">
        <t-col :span="12">
          <div style="font-size: 16px; font-weight: 600; margin-bottom: 12px;">培养计划信息</div>
          <t-descriptions v-if="plan" :column="1" size="small" bordered>
            <t-descriptions-item label="计划名称">{{ plan.planName }}</t-descriptions-item>
            <t-descriptions-item label="计划版本">{{ plan.planVersion }}</t-descriptions-item>
            <t-descriptions-item label="创建时间">{{ plan.createTime }}</t-descriptions-item>
          </t-descriptions>
          <t-empty v-else description="暂无培养计划信息" />
        </t-col>
        <t-col :span="12">
          <div style="font-size: 16px; font-weight: 600; margin-bottom: 12px;">关联标准信息</div>
          <t-descriptions v-if="standard" :column="1" size="small" bordered>
            <t-descriptions-item label="标准名称">{{ standard.standardName }}</t-descriptions-item>
            <t-descriptions-item label="标准版本">{{ standard.standardVersion }}</t-descriptions-item>
            <t-descriptions-item label="学科类型">{{ enumData?.map?.disciplineType?.[standard.disciplineType] || standard.disciplineType }}</t-descriptions-item>
            <t-descriptions-item label="发布日期">{{ standard.releaseDate }}</t-descriptions-item>
          </t-descriptions>
          <t-empty v-else description="暂无关联标准信息" />
        </t-col>
      </t-row>
    </t-card>

    <!-- 操作工具栏 -->
    <div class="toolbar">
      <t-input
        v-model="searchText"
        placeholder="搜索培养目标..."
        clearable
        class="search-input"
      >
        <template #prefix-icon>
          <search-icon />
        </template>
      </t-input>
    </div>

    <!-- 专业概况编辑/展示 -->
    <div class="section-container">
      <div class="section-header">
        <h2 class="section-title">
          <bookmark-icon class="title-icon" />
          目标
        </h2>
        <div class="section-stats">
          <t-tag theme="default" size="small" shape="round">
            <template #icon><file-icon /></template>
            字数: {}
          </t-tag>
        </div>
      </div>
    </div>

    <!-- 培养目标网格 -->
    <div class="section-container">
      <div class="section-header">
        <h2 class="section-title">
          <tag-icon class="title-icon" />
          培养目标
        </h2>
        <div class="section-stats">
          <t-tag theme="default" size="small" shape="round">
            <template #icon><list-icon /></template>
            总数: {{ eoList.length }}
          </t-tag>
        </div>
      </div>

      <t-loading :loading="loading" style="width: 100%">
        <div v-if="eoList.length > 0" class="targets-grid">
          <div
            v-for="(target, index) in eoList"
            :key="index"
            class="target-card"
          >
            <div class="card-header">
              <div class="target-number">
                <t-tag shape="round" theme="primary" size="medium">
                  <template #icon><tag-icon size="16px" /></template>
                  {{ target.eoTitle }}
                </t-tag>
              </div>
              <div class="card-actions">
                <t-button
                  size="small"
                  variant="text"
                  theme="primary"
                  @click="editTarget(index)"
                  class="action-btn"
                >
                  <template #icon><edit-icon /></template>
                  编辑
                </t-button>
                <t-button
                  size="small"
                  variant="text"
                  theme="danger"
                  @click="confirmDeleteTarget(index)"
                  class="action-btn"
                >
                  <template #icon><delete-icon /></template>
                  删除
                </t-button>
              </div>
            </div>
            <div class="card-content">
              <div class="target-content">
                {{ target.eoDescription || '暂无目标内容，点击编辑按钮添加内容' }}
              </div>
            </div>
            <div class="card-footer">
              <div class="target-meta">
                <span class="update-time">最后更新: {{ target.modifyTime || target.createTime }}</span>

              </div>
            </div>
          </div>

          <!-- 添加新目标卡片 -->
          <div class="target-card add-card" @click="addNewTarget">
            <div class="add-card-content">
              <div class="add-icon">
                <add-icon size="48px" />
              </div>
              <div class="add-text">添加新目标</div>
            </div>
          </div>
        </div>
        <div v-else class="empty-state">
          <t-empty description="暂无培养目标">
            <template #actions>
              <t-button theme="primary" @click="addNewTarget" >
                <template #icon><add-icon /></template>
                添加一个目标
              </t-button>
            </template>
          </t-empty>
        </div>
      </t-loading>
    </div>


    <!-- 删除确认对话框 -->
    <t-dialog
      v-model:visible="deleteDialogVisible"
      header="确认删除"
      :on-confirm="handleDeleteConfirm"
      :on-cancel="() => deleteDialogVisible = false"
      :confirm-btn="{
        content: '确认删除',
        theme: 'danger'
      }"
      :cancel-btn="{ content: '取消', variant: 'outline' }"
    >
      <p>确定要删除培养目标"{{ targetToDelete?.eoTitle || '目标' + (deleteTargetIndex + 1) }}"吗？此操作不可撤销！</p>
    </t-dialog>



    <!-- EO编辑弹窗 -->
    <t-dialog
      v-model:visible="eoDialogVisible"
      :header="eoDialogTitle"
      :footer="false"
      width="480px"
    >
      <t-alert theme="info" style="margin-bottom: 16px;">
        当前培养计划：{{ plan?.planName || '未知计划' }}
      </t-alert>
      <t-form :data="eoForm" :rules="eoFormRules" @submit="handleEoFormSubmit">
        <t-form-item label="目标标题" name="eoTitle">
          <t-input v-model="eoForm.eoTitle" placeholder="请输入培养目标标题" />
        </t-form-item>
        <t-form-item label="目标描述" name="eoDescription">
          <t-textarea v-model="eoForm.eoDescription" placeholder="请输入培养目标描述" :autosize="{ minRows: 3, maxRows: 8 }" />
        </t-form-item>
        <t-form-item>
          <t-space>
            <t-button theme="primary" type="submit">确认</t-button>
            <t-button theme="default" variant="base" @click="eoDialogVisible = false">取消</t-button>
          </t-space>
        </t-form-item>
      </t-form>
    </t-dialog>
  </div>
</template>

<script setup lang="ts">
import {ref, computed, onMounted, watch} from 'vue'
import TMessage, { MessagePlugin } from 'tdesign-vue-next'
import { useRoute } from 'vue-router'
import { getPlan } from '@/api/training/plan'
import {
  EditIcon,
  DownloadIcon,
  UploadIcon,
  AddIcon,
  DeleteIcon,
  BookmarkIcon,
  FileIcon,
  ListIcon,
  TimeIcon,
  SearchIcon,
  TagIcon
} from 'tdesign-icons-vue-next'
import { getEoList, addEo, deleteEo, updateEo } from "@/api/training/eo";
import {
  getGraduationStandardDetail
} from '@/api/base/standard'
import { getEnum } from '@/api/system/enum'

const message = MessagePlugin

// 定义EoVO接口，对应后端EoVO.java
interface EoVO {
  id?: number;
  eoTitle: string;
  eoDescription: string;
  status: number;
  createTime?: string;
  modifyTime?: string;
}

// 路由参数获取
const route = useRoute()
const id = ref(Number(route.query.id))
const plan = ref<any>(null)
const standard = ref<any>(null)
const enumData = ref<any>(null)

const eoList = ref<EoVO[]>([]); // 存储所有培养目标对象

// 状态管理
const loading = ref(true)
const deleteDialogVisible = ref(false)

// 删除相关状态
const targetToDelete = ref<EoVO | null>(null)
const deleteTargetIndex = ref(-1)

// 搜索功能
const searchText = ref('')

// 页面标题
const pageTitle = ref('培养目标')

// 新增EO表单相关
const eoForm = ref<EoVO>({
  eoTitle: '',
  eoDescription: '',
  status: 0
})

const eoFormRules = {
  eoTitle: [
    { required: true, message: '请输入目标标题', trigger: 'blur' as const },
    { min: 2, max: 100, message: '标题长度应在2-100字符之间', trigger: 'blur' as const }
  ],
  eoDescription: [
    { required: true, message: '请输入目标描述', trigger: 'blur' as const },
    { min: 10, max: 1000, message: '描述长度应在10-1000字符之间', trigger: 'blur' as const }
  ]
}

const eoDialogTitle = ref('新增培养目标')
const eoDialogVisible = ref(false)

// 响应式列数
const columns = computed(() => {
  const width = window.innerWidth
  if (width < 768) return 1
  if (width < 1024) return 2
  return 3
})

// 初始化加载数据
onMounted(async () => {
  if (!id.value) {
    MessagePlugin.warning('未提供培养方案ID，无法加载培养目标数据。');
    return
  }

  try {
    loading.value = true
    // 获取培养计划信息
    const planRes = await getPlan(id.value)
    plan.value = planRes.data

    // 获取关联的标准信息
    if (plan.value.standardId) {
      const standardRes = await getGraduationStandardDetail(plan.value.standardId)
      standard.value = standardRes.data
    }

    // 获取培养目标数据
    await loadEoList()

    // 获取枚举数据
    const enumRes = await getEnum()
    enumData.value = enumRes.data

  } catch (error) {
    message.error('数据加载失败')
  } finally {
    loading.value = false
  }
})


// 添加新目标
const addNewTarget = async () => {
  eoDialogTitle.value = '新增培养目标'
  eoForm.value = {
    eoTitle: '',
    eoDescription: '',
    status: 0,
    id: null
  }
  eoDialogVisible.value = true
}

// 删除目标
const deleteTarget = async (index: number) => {
  try {
    const target = eoList.value[index]
    if (!target?.id) {
      message.error('无效的目标ID')
      return
    }

    await deleteEo(target.id)
    message.success('删除成功')

    // 重新加载数据
    await loadEoList()

  } catch (error) {
    message.error('删除失败')
  }
};


// 编辑目标
const editTarget = (index: number) => {
  const target = eoList.value[index]
  eoDialogTitle.value = '编辑培养目标'
  eoForm.value = {
    ...target,
    status: target.status
  }
  eoDialogVisible.value = true
}

// 统一的数据加载函数
const loadEoList = async () => {
  try {
    const goalRes = await getEoList(id.value)
    eoList.value = goalRes.data;
  } catch (error) {
    message.error('数据加载失败')
  }
}

// 确认删除目标
const confirmDeleteTarget = (index: number) => {
  const target = eoList.value[index]
  if (!target) return

  targetToDelete.value = target
  deleteTargetIndex.value = index
  deleteDialogVisible.value = true
}

// 处理删除确认
const handleDeleteConfirm = async () => {
  if (deleteTargetIndex.value >= 0) {
    await deleteTarget(deleteTargetIndex.value)
    deleteDialogVisible.value = false
    targetToDelete.value = null
    deleteTargetIndex.value = -1
  }
}


// 新增EO表单提交
const handleEoFormSubmit = async (context: any) => {
  if (context.validateResult === true) {
    try {
      const submitData = {
        ...eoForm.value,
        planId: id.value
      }

      if (eoForm.value.id) {
        await updateEo(submitData)
        message.success('编辑成功')
      } else {
        await addEo(submitData)
        message.success('新增成功')
      }

      // 重新加载数据
      await loadEoList()

      // 关闭弹窗
      eoDialogVisible.value = false
    } catch (error) {
      message.error(eoForm.value.id ? '编辑失败' : '新增失败')
    }
  }
}

</script>

<style lang="less" scoped>
.enhanced-training-objectives {
  --header-padding: 24px;
  --card-spacing: 16px;
  --section-spacing: 24px;
  --item-spacing: 12px;
  --border-radius: 8px;
  --transition-duration: 0.2s;

  padding: var(--header-padding);
  max-width: 1200px;
  margin: 0 auto;
  color: var(--td-text-color-primary);
  font-family: var(--td-font-family);
  line-height: 1.5;
  background-color: var(--td-bg-color-page);
  min-height: 100vh;

  .header-section {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    gap: var(--card-spacing);
    margin-bottom: var(--section-spacing);
    padding: 16px 20px;
    padding-bottom: var(--card-spacing);
    border-bottom: 1px solid var(--td-component-stroke);
    background-color: var(--td-bg-color-container);
    border-radius: var(--border-radius);
    box-shadow: var(--td-shadow-1);

    .title-container {
      flex: 1;
      min-width: 300px;
      display: flex;
      flex-direction: column;
      gap: var(--item-spacing);
    }

    .title-wrapper {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      gap: var(--item-spacing);
      margin-bottom: var(--item-spacing);

      .page-title {
        margin: 0;
        font-size: 28px;
        font-weight: 600;
        line-height: 1.3;
        outline: none;
        min-width: 200px;
        flex: 1;
        color: var(--td-text-color-primary);
      }

      .version-selector {
        width: 140px;
        flex-shrink: 0;
      }
    }

    .page-subtitle {
      margin: 0;
      font-size: 14px;
      color: var(--td-text-color-secondary);
    }

    .action-buttons {
      display: flex;
      gap: var(--item-spacing);
      align-items: center;
      flex-wrap: wrap;
    }

    .action-btn {
      display: flex;
      align-items: center;
      gap: 6px;
      padding: 8px 16px;
      min-width: 120px;
    }

    .add-btn {
      background-color: var(--td-brand-color);
      color: white;
      padding: 8px 16px;
      margin-left: 8px;

      &:hover {
        opacity: 0.9;
      }

      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }
    }
  }

  /* 统计卡片样式 */
  .stats-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--card-spacing);
    margin-bottom: var(--section-spacing);

    .stat-card {
      border-radius: var(--border-radius);
      transition: transform var(--transition-duration), box-shadow var(--transition-duration);
      will-change: transform;

      &:hover {
        transform: translateY(-4px);
        box-shadow: var(--td-shadow-3);
      }

      .stat-content {
        display: flex;
        align-items: center;
        padding: 16px;

        .stat-icon {
          width: 48px;
          height: 48px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 16px;
          font-size: 24px;
          flex-shrink: 0;
          transition: all var(--transition-duration);

          &.total-icon {
            background: rgba(var(--td-brand-color-rgb), 0.1);
            color: var(--td-brand-color);
          }

          &.sub-icon {
            background: rgba(var(--td-success-color-rgb), 0.1);
            color: var(--td-success-color);
          }

          &.recent-icon {
            background: rgba(var(--td-purple-color-rgb), 0.1);
            color: var(--td-purple-color);
          }
        }

        .stat-info {
          .stat-value {
            font-size: 24px;
            font-weight: 600;
            line-height: 1.2;
            margin-bottom: 4px;
          }

          .stat-label {
            font-size: 14px;
            color: var(--td-text-color-secondary);
          }
        }
      }
    }
  }

  /* 工具栏样式 */
  .toolbar {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
    gap: var(--card-spacing);
    margin-bottom: var(--section-spacing);

    .search-input {
      width: 280px;
      min-width: 100%;

      @media (min-width: 480px) {
        min-width: auto;
      }
    }
  }

  .section-container {
    margin-bottom: 28px;
    background-color: var(--td-bg-color-container);
    border-radius: var(--border-radius);
    padding: 20px;
    box-shadow: var(--td-shadow-1);

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      flex-wrap: wrap;
      gap: var(--item-spacing);
    }

    .section-title {
      display: flex;
      align-items: center;
      font-size: 20px;
      font-weight: 500;
      margin: 0;
      color: var(--td-text-color-primary);

      .title-icon {
        margin-right: 10px;
        color: var(--td-brand-color);
      }
    }

    .section-stats {
      display: flex;
      gap: var(--item-spacing);
      align-items: center;
    }

    .clear-btn {
      color: var(--td-error-color);
      border-color: var(--td-error-color);

      &:hover {
        background-color: var(--td-error-color-1);
      }
    }
  }

  .info-card {
    border-radius: var(--border-radius);
    transition: all var(--transition-duration);
    padding: 20px;
    margin-bottom: 0;
    border: 1px solid var(--td-component-stroke);

    .info-content {
      line-height: 1.8;
      color: var(--td-text-color-secondary);
      font-size: 15px;
      white-space: pre-wrap;
      padding: 8px;
    }

    .edit-textarea {
      width: 100%;
      min-height: 120px;
      font-size: 15px;
      line-height: 1.8;
      padding: 8px;
    }
  }

  .targets-grid {
    display: grid;
    grid-template-columns: repeat(v-bind(columns), 1fr);
    gap: 20px;
    margin-top: 16px;

    .target-card {
      background-color: var(--td-bg-color-container);
      border-radius: var(--border-radius);
      padding: 20px;
      box-shadow: var(--td-shadow-1);
      transition: all var(--transition-duration) ease;
      border: 1px solid var(--td-component-stroke);
      min-height: 220px;
      display: flex;
      flex-direction: column;
      position: relative;
      overflow: hidden;

      &:hover {
        box-shadow: var(--td-shadow-2);
        transform: translateY(-2px);
      }

      &.editing-card {
        border: 2px solid var(--td-brand-color);
        background-color: var(--td-brand-color-light);
      }

      &.add-card {
        background: linear-gradient(135deg, rgba(var(--td-brand-color-rgb), 0.05) 0%, rgba(var(--td-brand-color-rgb), 0.1) 100%);
        border: 2px dashed var(--td-brand-color);
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all var(--transition-duration) ease;

        &:hover {
          background: linear-gradient(135deg, rgba(var(--td-brand-color-rgb), 0.1) 0%, rgba(var(--td-brand-color-rgb), 0.15) 100%);
          border-color: var(--td-brand-color);
          transform: translateY(-4px);
          box-shadow: 0 8px 24px rgba(var(--td-brand-color-rgb), 0.2);
        }

        .add-card-content {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          text-align: center;
          color: var(--td-brand-color);

          .add-icon {
            margin-bottom: 12px;
            opacity: 0.8;
            transition: all var(--transition-duration) ease;
          }

          .add-text {
            font-size: 16px;
            font-weight: 500;
            opacity: 0.9;
            transition: all var(--transition-duration) ease;
          }
        }

        &:hover .add-card-content {
          .add-icon {
            opacity: 1;
            transform: scale(1.1);
          }

          .add-text {
            opacity: 1;
          }
        }
      }

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
        flex-wrap: wrap;
        gap: 8px;

        .target-number {
          font-weight: 600;
        }

        .card-actions {
          display: flex;
          gap: 4px;

          .action-btn {
            color: var(--td-text-color-secondary);

            &:hover {
              color: var(--td-brand-color);
            }

            &:disabled {
              opacity: 0.5;
              cursor: not-allowed;
            }
          }

          .delete-btn {
            color: var(--td-error-color);

            &:hover {
              background-color: var(--td-error-color-1);
            }
          }
        }
      }

      .card-content {
        flex: 1;
        display: flex;
        flex-direction: column;

        .target-content {
          line-height: 1.7;
          color: var(--td-text-color-secondary);
          font-size: 15px;
          flex: 1;
          white-space: pre-wrap;
          padding: 8px;
        }

        .edit-textarea {
          width: 100%;
          flex: 1;
          min-height: 140px;
          font-size: 15px;
          line-height: 1.7;
          resize: none;
          padding: 8px;
        }
      }

      .card-footer {
        margin-top: var(--item-spacing);
        padding-top: 8px;
        border-top: 1px dashed var(--td-component-stroke);

        .target-meta {
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-size: 12px;
          color: var(--td-text-color-placeholder);

          .update-time {
            font-style: italic;
          }

          .word-count {
            font-weight: 500;
          }
        }
      }
    }
  }

  .empty-state {
    padding: 40px 0;
    text-align: center;
    background-color: var(--td-bg-color-container);
    border-radius: var(--border-radius);
    margin-top: 16px;
  }

  .import-dialog-content {
    .import-textarea {
      font-family: monospace;
      font-size: 14px;
    }

    .import-actions {
      margin-top: var(--item-spacing);
      display: flex;
      justify-content: flex-end;
    }
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .enhanced-training-objectives {
    padding: 16px;

    .header-section {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
      padding: 16px;

      .title-container {
        width: 100%;

        .title-wrapper {
          flex-direction: column;
          gap: var(--item-spacing);

          .page-title,
          .version-selector {
            width: 100%;
          }
        }
      }

      .action-buttons {
        width: 100%;
        flex-wrap: wrap;
        justify-content: flex-end;
      }
    }

    .stats-cards {
      grid-template-columns: 1fr;
    }

    .section-container {
      padding: 16px;
    }

    .section-header {
      flex-direction: column;
      align-items: flex-start !important;
    }

    .section-stats {
      width: 100%;
      justify-content: space-between;
      margin-top: var(--item-spacing);
    }

    .toolbar {
      flex-direction: column;
      align-items: stretch;

      .search-input {
        width: 100%;
      }
    }

    .targets-grid {
      grid-template-columns: 1fr !important;
      gap: 16px;
    }

    .target-card {
      padding: 16px !important;
      min-height: 200px !important;
    }
  }
}

@media (max-width: 480px) {
  .enhanced-training-objectives {
    padding: 16px;
  }
}
</style>
