<template>
    <div class="assignment-creator">
        <!-- 顶部设置区域 -->
        <t-card :title="pageTitle" class="settings-card">
            <t-form :data="assignmentSettings" label-width="100px">
                <t-row :gutter="[16, 16]">
                    <t-col :span="8">
                        <t-form-item :label="`${type}名称`">
                            <t-input v-model="assignmentSettings.title" :placeholder="`请输入${type}名称`" />
                        </t-form-item>
                    </t-col>
                    <t-col :span="8">
                        <t-form-item label="班级">
                            <t-select v-model="assignmentSettings.classes" placeholder="请选择班级" multiple>
                                <t-option v-for="option in classOptions" :key="option.value" :value="option.value"
                                    :label="option.label" />
                            </t-select>
                        </t-form-item>
                    </t-col>
                    <t-col :span="8">
                        <t-form-item :label="`${type}类型`">
                            <t-radio-group v-model="assignmentSettings.type">
                                <t-radio value="regular">普通{{ type }}</t-radio>
                                <t-radio value="exam">答题卡{{ type }}</t-radio>
                            </t-radio-group>
                        </t-form-item>
                    </t-col>
                </t-row>

                <t-row :gutter="[16, 16]">
                    <t-col :span="8">
                        <t-form-item label="评分机制">
                            <t-radio-group v-model="assignmentSettings.scoringSystem">
                                <t-radio value="percentage">百分制（学校统配百分制）</t-radio>
                                <t-radio value="custom">自定义（自行设置题目分值）</t-radio>
                            </t-radio-group>
                        </t-form-item>
                    </t-col>
                    <t-col :span="8">
                        <t-form-item label="题型设置">
                            <t-radio-group v-model="assignmentSettings.questionDisplay">
                                <t-radio value="grouped">按题型归类</t-radio>
                                <t-radio value="ungrouped">不按题型归类</t-radio>
                            </t-radio-group>
                        </t-form-item>
                    </t-col>
                    <t-col :span="8">
                        <t-form-item :label="`开始时间`">
                            <t-date-picker v-model="assignmentSettings.startTime" mode="date" enableTimePicker />
                        </t-form-item>
                    </t-col>
                </t-row>

                <t-row :gutter="[16, 16]">
                    <t-col :span="8">
                        <t-form-item label="结束时间">
                            <t-date-picker v-model="assignmentSettings.endTime" mode="date" enableTimePicker />
                        </t-form-item>
                    </t-col>
                    <t-col :span="8">
                        <t-form-item label="总分">
                            <t-input-number v-model="assignmentSettings.totalScore" :min="0" :max="100" />
                        </t-form-item>
                    </t-col>
                </t-row>
            </t-form>
        </t-card>

        <!-- 下方三区域布局 -->
        <div class="content-area">
            <!-- 左侧题目列表 -->
            <div class="question-list">
                <t-card title="题目列表" class="list-card">
                    <div v-if="questions.length === 0" class="empty-list">
                        <t-empty description="暂无题目，请添加题目" />
                    </div>
                    <template v-else>
                        <div v-for="(group, groupName) in groupedQuestions" :key="groupName" class="question-group">
                            <div class="group-title">{{ getGroupTitle(groupName) }}</div>
                            <div v-for="(question, index) in group" :key="question.id" class="question-item"
                                :class="{ 'active': currentQuestion && currentQuestion.id === question.id }"
                                @click="selectQuestion(question)">
                                <span class="question-number">{{ getQuestionNumber(groupName, index) }}</span>
                                <span class="question-title">{{ question.title || '未命名题目' }}</span>
                                <div class="question-actions">
                                    <t-button theme="default" variant="text" size="small"
                                        @click.stop="deleteQuestion(question)">
                                        <template #icon><t-icon name="delete" /></template>
                                    </t-button>
                                </div>
                            </div>
                        </div>
                    </template>
                </t-card>
            </div>

            <!-- 右侧内容区 -->
            <div class="question-editor">
                <!-- 题型选择 -->
                <t-card title="添加题目" class="type-selector-card">
                    <div class="question-types">
                        <t-button v-for="type in questionTypes" :key="type.value"
                            :variant="currentQuestionType === type.value ? 'base' : 'outline'"
                            style="margin-right: 8px; margin-bottom: 8px;" @click="selectQuestionType(type.value)">
                            {{ type.label }}
                        </t-button>
                    </div>
                    <!-- 题目编辑器 -->
                    <t-card :title="getEditorTitle()" class="editor-card">
                        <template v-if="!currentQuestion">
                            <t-empty description="请选择或创建一个题目" />
                        </template>
                        <template v-else>
                            <!-- 题目内容编辑 -->
                            <div class="question-content-editor">
                                <div class="editor-label">题目内容：</div>
                                <t-textarea v-model="currentQuestion.content" placeholder="请输入题目内容"
                                    :autosize="{ minRows: 4, maxRows: 8 }" />
                            </div>

                            <!-- 选项编辑（单选/多选题） -->
                            <div v-if="['single', 'multiple'].includes(currentQuestion.type)" class="options-editor">
                                <div v-for="(option, index) in currentQuestion.options" :key="index"
                                    class="option-item">
                                    <div class="option-label">{{ String.fromCharCode(65 + index) }}</div>
                                    <t-textarea v-model="option.content" placeholder="请输入选项内容"
                                        :autosize="{ minRows: 2, maxRows: 4 }" />
                                    <t-checkbox v-if="currentQuestion.type === 'multiple'" v-model="option.isCorrect"
                                        class="correct-checkbox" />
                                    <t-radio v-else :value="option.isCorrect" :name="`correct-${currentQuestion.id}`"
                                        @change="setCorrectOption(index)" class="correct-radio" />
                                    <t-button theme="danger" variant="text" size="small" @click="removeOption(index)"
                                        class="remove-option">
                                        <template #icon><t-icon name="close" /></template>
                                    </t-button>
                                </div>

                                <t-button theme="primary" variant="dashed" block @click="addOption"
                                    class="add-option-btn">
                                    <template #icon><t-icon name="add" /></template>
                                    添加选项
                                </t-button>
                            </div>

                            <!-- 判断题 -->
                            <div v-if="currentQuestion.type === 'truefalse'" class="truefalse-editor">
                                <t-radio-group v-model="currentQuestion.answer">
                                    <t-radio value="true">正确</t-radio>
                                    <t-radio value="false">错误</t-radio>
                                </t-radio-group>
                            </div>

                            <!-- 填空题 -->
                            <div v-if="currentQuestion.type === 'fillblank'" class="fillblank-editor">
                                <div class="editor-label">答案：</div>
                                <div v-for="(blank, index) in currentQuestion.blanks" :key="index" class="blank-item">
                                    <t-input-adornment :prepend="`空格 ${index + 1}`">
                                        <t-input v-model="blank.answer" placeholder="请输入答案" />
                                    </t-input-adornment>
                                    <t-button v-if="currentQuestion.blanks.length > 1" theme="danger" variant="text"
                                        size="small" @click="removeBlank(index)" class="remove-blank">
                                        <template #icon><t-icon name="close" /></template>
                                    </t-button>
                                </div>

                                <t-button theme="primary" variant="dashed" block @click="addBlank"
                                    class="add-blank-btn">
                                    <template #icon><t-icon name="add" /></template>
                                    添加空格
                                </t-button>
                            </div>

                            <!-- 简答题 -->
                            <div v-if="currentQuestion.type === 'essay'" class="essay-editor">
                                <div class="editor-label">参考答案：</div>
                                <t-textarea v-model="currentQuestion.referenceAnswer" placeholder="请输入参考答案"
                                    :autosize="{ minRows: 4, maxRows: 8 }" />
                            </div>

                            <!-- 分值设置 -->
                            <div class="score-setting">
                                <t-input-adornment prepend="分值">
                                    <t-input-number v-model="currentQuestion.score" :min="0" :max="100" />
                                </t-input-adornment>
                            </div>

                            <!-- 保存按钮 -->
                            <div class="action-buttons">
                                <t-button theme="primary" @click="saveQuestion">保存题目</t-button>
                                <t-button theme="default" @click="cancelEdit">取消</t-button>
                            </div>
                        </template>
                    </t-card>
                </t-card>


            </div>
        </div>

        <!-- 底部操作栏 -->
        <div class="bottom-actions">
            <t-button theme="primary" @click="saveAssignment">{{ saveButtonText }}</t-button>
            <t-button theme="default" @click="previewAssignment">预览</t-button>
            <t-button theme="danger" variant="outline" @click="cancelAssignment">取消</t-button>
        </div>
    </div>
</template>

<!--对于简答题，完整的数据流程是：-->
<!--1 点击"简答题"按钮，调用 selectQuestionType('essay')，创建一个新的简答题对象，并存储在 currentQuestion.value 中-->
<!--2 用户在界面上编辑题目内容、参考答案和分值，直接修改 currentQuestion.value 的属性-->
<!--3 点击"保存题目"按钮，调用 saveQuestion()，将当前题目添加到 questions.value 数组中-->
<!--4 点击"保存作业"按钮，调用 saveAssignment()，将整个作业（包括所有题目）保存到服务器-->

<script setup lang="ts">
import { ref, computed, reactive, onMounted, watch } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import { useRouter, useRoute } from 'vue-router';

const router = useRouter();
const route = useRoute();

const taskType = ref(route.query.type?.toString() || 'homework');
const isExam = computed(() => taskType.value === 'exam');

// 修改页面标题
const pageTitle = computed(() => isExam.value ? '新建考试' : '新建作业');
const type = computed(() => isExam.value ? '考试' : '作业');

// 班级选项
const classOptions = [
    { label: 'R8软工1班241', value: '241' },
    { label: 'R8软工1班242', value: '242' },
    { label: 'R8软工1班243', value: '243' },
    { label: 'R8软工1班244', value: '244' },
];

// 题型选项类型定义
type QuestionType = 'single' | 'multiple' | 'truefalse' | 'fillblank' | 'essay';

// 题型选项
const questionTypes = [
    { label: '单选题', value: 'single' as QuestionType },
    { label: '多选题', value: 'multiple' as QuestionType },
    { label: '判断题', value: 'truefalse' as QuestionType },
    { label: '填空题', value: 'fillblank' as QuestionType },
    { label: '简答题', value: 'essay' as QuestionType },
];

// 作业设置
const assignmentSettings = ref({
    title: '',
    classes: [],
    type: taskType.value,
    scoringSystem: 'percentage',
    questionDisplay: 'grouped',
    startTime: '',
    endTime: '',
    totalScore: 100,
});

// 题目列表
const questions = ref<any[]>([]);
const currentQuestion = ref<any>(null);
const currentQuestionType = ref('single');

// 根据题型分组题目
const groupedQuestions = computed(() => {
    const grouped: Record<QuestionType, any[]> = {
        single: [],
        multiple: [],
        truefalse: [],
        fillblank: [],
        essay: []
    };

    questions.value.forEach(question => {
        const qType = question.type as QuestionType;
        if (grouped[qType]) {
            grouped[qType].push(question);
        }
    });

    return grouped;
});

// 获取题型标题
const getGroupTitle = (type: string): string => {
    const typeMap: { [key: string]: string } = {
        single: '单选题',
        multiple: '多选题',
        truefalse: '判断题',
        fillblank: '填空题',
        essay: '简答题'
    };
    return typeMap[type] || '未知题型';
};

// 获取题目编号
const getQuestionNumber = (type: string, index: number): string => {
    const typePrefix: { [key: string]: string } = {
        single: 'S',
        multiple: 'M',
        truefalse: 'T',
        fillblank: 'F',
        essay: 'E'
    };
    return `${typePrefix[type] || '?'}${index + 1}`;
};

// 获取编辑器标题
const getEditorTitle = (): string => {
    if (!currentQuestion.value) return '题目编辑';

    const typeMap: { [key: string]: string } = {
        single: '单选题',
        multiple: '多选题',
        truefalse: '判断题',
        fillblank: '填空题',
        essay: '简答题'
    };

    return `${typeMap[currentQuestion.value.type] || '未知题型'}编辑`;
};

// 初始化填空题
const initFillBlankQuestion = (question: any): void => {
    // 确保初始化时至少有两个空格
    if (!question.blanks || !Array.isArray(question.blanks) || question.blanks.length === 0) {
        question.blanks = [
            { answer: '' },
            { answer: '' }
        ];
    }
}

// 选择题型
const selectQuestionType = (type: QuestionType): void => {
    currentQuestionType.value = type;

    // 创建新题目
    const newQuestion = {
        id: Date.now().toString(),
        type,
        title: '',
        content: '',
        score: 5,
        options: [],
        blanks: [],
        referenceAnswer: '',
        answer: '',
        subjectId: '', // 添加科目ID字段，可以为空
        needManualGrading: type === 'essay' || type === 'coding', // 简答题和编程题需要手动批阅
        autoGradingRule: type === 'single' || type === 'multiple' || type === 'true_false' ? 'exact_match' : 'keyword_match'
    };

    // 根据题型初始化默认选项
    if (['single', 'multiple'].includes(type)) {
        for (let i = 0; i < 4; i++) {
            newQuestion.options.push({
                content: '',
                isCorrect: false
            });
        }
    } else if (type === 'truefalse') {
        newQuestion.answer = 'true';
    } else if (type === 'fillblank') {
        // 填空题默认添加两个空格
        newQuestion.blanks = [
            { answer: '' },
            { answer: '' }
        ];
    }

    currentQuestion.value = newQuestion;
};

// 选择题目
const selectQuestion = (question: any): void => {
    currentQuestion.value = JSON.parse(JSON.stringify(question));
    currentQuestionType.value = question.type;
};

// 删除题目
const deleteQuestion = (question: any): void => {
    if (confirm('确定要删除该题目吗？')) {
        questions.value = questions.value.filter(q => q.id !== question.id);
        if (currentQuestion.value && currentQuestion.value.id === question.id) {
            currentQuestion.value = null;
        }
    }
};

// 添加选项
const addOption = (): void => {
    if (!currentQuestion.value) return;
    currentQuestion.value.options.push({ content: '', isCorrect: false });
};

// 移除选项
const removeOption = (index: number): void => {
    if (!currentQuestion.value || currentQuestion.value.options.length <= 2) {
        MessagePlugin.warning('至少需要保留两个选项');
        return;
    }
    currentQuestion.value.options.splice(index, 1);
};

// 设置正确答案 (单选题)
const setCorrectOption = (index: number): void => {
    if (!currentQuestion.value) return;

    currentQuestion.value.options.forEach((option: any, i: number) => {
        option.isCorrect = i === index;
    });
};

// 添加填空
const addBlank = (): void => {
    if (!currentQuestion.value) return;
    currentQuestion.value.blanks.push({ answer: '' });
};

// 移除填空
const removeBlank = (index: number): void => {
    if (!currentQuestion.value || currentQuestion.value.blanks.length <= 1) {
        MessagePlugin.warning('至少需要保留一个空格');
        return;
    }
    currentQuestion.value.blanks.splice(index, 1);
};

// 保存题目
const saveQuestion = (): void => {
    if (!currentQuestion.value) return;

    // 验证题目内容
    if (!currentQuestion.value.content.trim()) {
        MessagePlugin.warning('请输入题目内容');
        return;
    }

    // 根据题型验证
    if (['single', 'multiple'].includes(currentQuestion.value.type)) {
        // 验证选项
        const emptyOption = currentQuestion.value.options.find((option: any) => !option.content.trim());
        if (emptyOption) {
            MessagePlugin.warning('请填写所有选项内容');
            return;
        }

        // 验证正确答案
        if (currentQuestion.value.type === 'multiple') {
            const hasCorrect = currentQuestion.value.options.some((option: any) => option.isCorrect);
            if (!hasCorrect) {
                MessagePlugin.warning('请至少选择一个正确答案');
                return;
            }
        } else {
            const hasCorrect = currentQuestion.value.options.some((option: any) => option.isCorrect);
            if (!hasCorrect) {
                MessagePlugin.warning('请选择一个正确答案');
                return;
            }
        }
    } else if (currentQuestion.value.type === 'fillblank') {
        // 验证填空答案
        const emptyBlank = currentQuestion.value.blanks.find((blank: any) => !blank.answer.trim());
        if (emptyBlank) {
            MessagePlugin.warning('请填写所有空格的答案');
            return;
        }
    } else if (currentQuestion.value.type === 'essay') {
        // 验证参考答案
        if (!currentQuestion.value.referenceAnswer.trim()) {
            MessagePlugin.warning('请填写参考答案');
            return;
        }
    }

    // 设置题目标题（取内容前20个字符）
    const contentText = currentQuestion.value.content.replace(/<[^>]+>/g, '');
    currentQuestion.value.title = contentText.length > 20
        ? contentText.substring(0, 20) + '...'
        : contentText;

    // 查找是否已存在该题目
    const existingIndex = questions.value.findIndex(q => q.id === currentQuestion.value.id);
    if (existingIndex >= 0) {
        // 更新现有题目
        questions.value[existingIndex] = JSON.parse(JSON.stringify(currentQuestion.value));
    } else {
        // 添加新题目
        questions.value.push(JSON.parse(JSON.stringify(currentQuestion.value)));
    }

    MessagePlugin.success('题目保存成功');
    currentQuestion.value = null;
};

// 取消编辑
const cancelEdit = (): void => {
    currentQuestion.value = null;
};

// 保存作业
const saveAssignment = (): void => {
    // 验证作业设置
    if (!assignmentSettings.value.title.trim()) {
        MessagePlugin.warning(`请输入${type.value}名称`);
        return;
    }

    if (assignmentSettings.value.classes.length === 0) {
        MessagePlugin.warning('请选择班级');
        return;
    }

    if (!assignmentSettings.value.startTime || !assignmentSettings.value.endTime) {
        MessagePlugin.warning(`请设置${type.value}时间`);
        return;
    }

    if (questions.value.length === 0) {
        MessagePlugin.warning('请添加至少一道题目');
        return;
    }

    // 构建作业数据
    const assignmentData = {
        ...assignmentSettings.value,
        type: taskType.value,
        questions: questions.value,
        createdAt: new Date().toISOString(),
    };

    // 这里应该调用API保存作业
    console.log('保存作业数据:', assignmentData);

    MessagePlugin.success(`${type.value}保存成功`);
    if (isExam.value) {
        router.push('/teachers/exam');
    } else {
        router.push('/teachers/task');
    }
};

// 预览作业
const previewAssignment = (): void => {
    // 实现预览逻辑
    MessagePlugin.info('预览功能开发中');
};

// 取消创建作业
const cancelAssignment = (): void => {
    if (confirm(`确定要取消创建${type.value}吗？所有未保存的内容将丢失。`)) {
        if (isExam.value) {
            router.push('/teachers/exam');
        } else {
            router.push('/teachers/task');
        }
    }
};

// 修改保存按钮文字
const saveButtonText = computed(() => isExam.value ? '保存考试' : '保存作业');

// 添加导入逻辑
const fromBank = ref(route.query.fromBank === 'true')

// 当从题库导入时，获取选中的题目
onMounted(() => {
    if (fromBank.value) {
        try {
            const selectedQuestions = JSON.parse(localStorage.getItem('selectedBankQuestions') || '[]')
            if (selectedQuestions.length > 0) {
                // 将题库中的题目添加到当前作业/考试中
                questions.value = [...questions.value, ...selectedQuestions]
                MessagePlugin.success(`已导入${selectedQuestions.length}道题目`)
                // 清除localStorage中的数据
                localStorage.removeItem('selectedBankQuestions')
            }
        } catch (error) {
            console.error('解析题库题目失败', error)
        }
    }
})
</script>

<style scoped>
.assignment-creator {
    padding: 20px;
}

.settings-card {
    margin-bottom: 20px;
}

.content-area {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.question-list {
    width: 30%;
}

.question-editor {
    width: 70%;
}

.list-card,
.type-selector-card,
.editor-card {
    height: 100%;
}

.empty-list {
    padding: 40px 0;
    text-align: center;
}

.question-group {
    margin-bottom: 16px;
}

.group-title {
    font-weight: bold;
    margin-bottom: 8px;
    padding: 4px 8px;
    background-color: #f5f5f5;
    border-radius: 4px;
}

.question-item {
    display: flex;
    align-items: center;
    padding: 8px;
    border-radius: 4px;
    cursor: pointer;
    margin-bottom: 4px;
    border: 1px solid transparent;
}

.question-item:hover {
    background-color: #f5f5f5;
}

.question-item.active {
    background-color: #e6f7ff;
    border-color: #1890ff;
}

.question-number {
    margin-right: 8px;
    font-weight: bold;
    min-width: 24px;
}

.question-title {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.question-actions {
    opacity: 0;
    transition: opacity 0.2s;
}

.question-item:hover .question-actions {
    opacity: 1;
}

.question-types {
    display: flex;
    flex-wrap: wrap;
}

.question-content-editor,
.options-editor,
.truefalse-editor,
.fillblank-editor,
.essay-editor {
    margin-bottom: 20px;
}

.editor-label {
    margin-bottom: 8px;
    font-weight: bold;
}

.option-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 12px;
    gap: 8px;
}

.option-label {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f0f0f0;
    border-radius: 50%;
    margin-top: 8px;
}

.correct-checkbox,
.correct-radio {
    margin-top: 8px;
    margin-left: 8px;
}

.remove-option,
.remove-blank {
    margin-top: 8px;
}

.add-option-btn,
.add-blank-btn {
    margin-top: 8px;
}

.blank-item {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    gap: 8px;
}

.score-setting {
    margin-bottom: 20px;
    max-width: 200px;
}

.action-buttons {
    display: flex;
    gap: 12px;
    margin-top: 20px;
}

.bottom-actions {
    display: flex;
    gap: 12px;
    justify-content: center;
    margin-top: 20px;
    padding: 20px 0;
    border-top: 1px solid #f0f0f0;
}
</style>
