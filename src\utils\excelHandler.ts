import * as XLSX from 'xlsx';
import { MessagePlugin } from 'tdesign-vue-next';
import { StudentScore } from '@/store/modules/indicator';

/**
 * 读取Excel文件并转换为JSON数据
 * @param file 上传的文件对象
 * @returns Promise包含解析后的数据和文件名
 */
export const readExcelFile = async (file: File): Promise<{ data: any[], fileName: string }> => {
  return new Promise((resolve, reject) => {
    try {
      const fileName = file.name;
      const reader = new FileReader();
      
      reader.onload = (e) => {
        try {
          const data = new Uint8Array(e.target?.result as ArrayBuffer);
          const workbook = XLSX.read(data, { type: 'array' });
          
          // 获取第一个工作表
          const firstSheetName = workbook.SheetNames[0];
          const worksheet = workbook.Sheets[firstSheetName];
          
          // 转换为JSON
          const jsonData = XLSX.utils.sheet_to_json(worksheet, {
            defval: '', // 空白单元格设为空字符串
          });
          
          console.log(`成功解析文件 ${fileName}，数据行数: ${jsonData.length}`);
          resolve({ data: jsonData, fileName });
        } catch (error: any) {
          console.error('Excel文件解析失败:', error);
          reject(new Error(`Excel文件解析失败: ${error.message}`));
        }
      };
      
      reader.onerror = () => {
        reject(new Error('文件读取失败'));
      };
      
      reader.readAsArrayBuffer(file);
    } catch (error: any) {
      reject(new Error(`文件处理失败: ${error.message}`));
    }
  });
};

/**
 * 处理成绩数据，将导入的原始数据转换为标准成绩格式
 * @param data 从Excel解析的原始数据
 * @param fileName 文件名，用于日志
 * @returns 处理后的学生成绩数据数组
 */
export const processScoreData = (data: any[], fileName: string): StudentScore[] | null => {
  try {
    if (!data || data.length === 0) {
      MessagePlugin.error('导入文件为空或格式不正确');
      return null;
    }
    
    // 检查必要字段
    const firstItem = data[0];
    if (!firstItem || !firstItem['学号'] || !(firstItem['成绩'] !== undefined || firstItem['分数'] !== undefined)) {
      MessagePlugin.error('导入文件格式不正确，请确保包含"学号"和"成绩/分数"列');
      return null;
    }
    
    // 处理成绩数据
    const scores: StudentScore[] = data.map(item => {
      // 尝试获取成绩，可能是"成绩"或"分数"列
      const scoreValue = 
        item['成绩'] !== undefined ? Number(item['成绩']) : 
        item['分数'] !== undefined ? Number(item['分数']) : 0;
      
      return {
        studentId: String(item['学号']),
        studentName: String(item['姓名'] || ''),
        score: isNaN(scoreValue) ? 0 : scoreValue,
        importDate: new Date().toISOString()
      };
    }).filter(score => score.studentId && !isNaN(score.score));
    
    if (scores.length === 0) {
      MessagePlugin.error('未能找到有效的成绩数据');
      return null;
    }
    
    console.log(`成功处理 ${fileName} 中的成绩数据，共 ${scores.length} 条记录`);
    return scores;
  } catch (error: any) {
    console.error('成绩数据处理失败:', error);
    MessagePlugin.error(`成绩数据处理失败: ${error.message}`);
    return null;
  }
};

/**
 * 读取文件为ArrayBuffer
 * @param file 文件对象
 * @returns Promise包含ArrayBuffer
 */
export const readFileAsArrayBuffer = (file: File): Promise<ArrayBuffer> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (e) => resolve(e.target?.result as ArrayBuffer);
    reader.onerror = () => reject(new Error('文件读取失败'));
    reader.readAsArrayBuffer(file);
  });
}; 