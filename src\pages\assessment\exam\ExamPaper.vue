<template>
  <t-layout>
    <t-content>
      <t-card title="试卷管理" :bordered="false">
        <template #actions>
          <t-space>
            <t-button @click="showImportDialog = true">
              <template #icon>
                <t-icon name="upload"/>
              </template>
              导入试卷
            </t-button>
            <t-button @click="handleCreateNew" theme="primary">
              <template #icon>
                <t-icon name="add"/>
              </template>
              创建试卷
            </t-button>
          </t-space>
        </template>

        <!-- 试卷列表 -->
        <t-table
          :data="filteredPapers"
          :columns="paperColumns"
          row-key="id"
          :pagination="pagination"
          @page-change="handlePageChange"
          hover
        >
          <template #operation="{ row }">
            <t-link theme="primary" @click="viewPaper(row)">查看</t-link>
            <t-link theme="primary" @click="editPaper(row)" style="margin-left: 10px;">编辑</t-link>
            <t-link theme="danger" @click="confirmDelete(row)" style="margin-left: 10px;">删除</t-link>
          </template>

          <template #empty>
            <div class="empty-container">
              <t-icon name="file" size="24px"/>
              <p>暂无试卷数据</p>
            </div>
          </template>
        </t-table>
      </t-card>
    </t-content>

    <!-- 导入试卷对话框 -->
    <t-dialog
      v-model:visible="showImportDialog"
      header="导入试卷"
      width="600px"
      :confirm-btn="{ content: '确定', theme: 'primary' }"
      @confirm="handleImportConfirm"
      @close="resetImport"
    >
      <t-upload
        v-model="importFiles"
        action="/api/paper/import"
        :before-upload="beforeUpload"
        accept=".doc,.docx,.pdf"
        multiple
        theme="file"
        auto-upload
        :format-response="formatImportResponse"
      >
        <template #trigger>
          <t-button variant="outline">
            <template #icon>
              <t-icon name="upload"/>
            </template>
            选择文件
          </t-button>
        </template>
        <template #tip> 支持扩展名：.doc .docx .pdf，单个文件不超过10MB</template>
      </t-upload>
    </t-dialog>

    <!-- 创建/编辑试卷对话框 -->
    <t-dialog
      v-model:visible="showCreateDialog"
      :header="currentPaper.id ? '编辑试卷' : '创建试卷'"
      width="1000px"
      :confirm-btn="{
    content: currentPaper.id ? '保存' : '创建',
    loading: submitLoading,
    theme: 'primary',
  }"
      :cancel-btn="{ content: '取消', variant: 'outline' }"
      @confirm="submitPaperForm"
      @close="handleDialogClose"
    >
      <t-form :data="currentPaper" :rules="formRules" label-width="80px" ref="formRef">
        <t-form-item label="试卷名称" name="name" required>
          <t-input v-model="currentPaper.name" placeholder="请输入试卷名称" clearable/>
        </t-form-item>
        <t-form-item label="试卷描述" name="description">
          <t-textarea v-model="currentPaper.description" placeholder="请输入试卷描述"/>
        </t-form-item>

        <t-divider>添加试题</t-divider>

        <div class="question-transfer-container">
          <div class="transfer-filter">
            <t-input
              v-model="questionFilter"
              placeholder="搜索题目名称"
              clearable
              style="width: 300px; margin-right: 16px;"
            >
              <template #prefix-icon>
                <t-icon name="search"/>
              </template>
            </t-input>
            <t-select
              v-model="objectiveFilter"
              placeholder="按课程目标筛选"
              clearable
              style="width: 200px;"
              :options="objectiveOptions"
            />
          </div>

          <t-transfer
            v-model="selectedQuestionValues"
            :data="filteredTransferQuestions"
            :search="false"
            :checked-keys="checkedKeys"
            @checked-change="handleCheckedChange"
            @change="handleTransferChange"
            class="enhanced-transfer"
          >
            <template #title="{ type }">
              <div class="transfer-title">
                {{ type === 'source' ? '题库' : '' }}
                <t-tag v-if="type === 'target'" theme="primary" variant="light">
                  共 {{ selectedQuestions.length }} 题 {{ totalScore }} 分
                </t-tag>
              </div>
            </template>
            <template #empty="{ type }">
              <div class="transfer-empty">
                <t-icon :name="type === 'source' ? 'file' : 'folder'"/>
                <span>{{ type === 'source' ? '暂无题目数据' : '请从左侧选择题目' }}</span>
              </div>
            </template>
            <template #footer="{ type }">
              <div class="transfer-footer" v-if="type === 'source'">
                <t-button size="small" variant="text" @click="expandAll">
                  <template #icon>
                    <t-icon name="caret-down"/>
                  </template>
                  展开全部
                </t-button>
                <t-button size="small" variant="text" @click="collapseAll">
                  <template #icon>
                    <t-icon name="caret-up"/>
                  </template>
                  收起全部
                </t-button>
              </div>
            </template>
          </t-transfer>

          <div class="question-preview">
            <t-card title="已选题目预览" size="small" :bordered="false">
              <t-table
                :data="selectedQuestions"
                :columns="questionColumns"
                row-key="value"
                size="small"
                :pagination="{ defaultPageSize: 5, total: selectedQuestions.length }"
              >
                <template #operation="{ row }">
                  <t-space>
                    <t-popconfirm content="确定要移除该题目吗？" @confirm="removeQuestion(row.value)">
                      <t-button variant="text" theme="danger">
                        <t-icon name="delete"/>
                        移除
                      </t-button>
                    </t-popconfirm>
                    <t-button variant="text" @click="editQuestionScore(row)">
                      <t-icon name="edit"/>
                      分值
                    </t-button>
                  </t-space>
                </template>
                <template #empty>
                  <div class="empty-hint">
                    <t-icon name="file" size="16px"/>
                    <span>请从左侧题库选择题目</span>
                  </div>
                </template>
              </t-table>
            </t-card>
          </div>
        </div>
      </t-form>
    </t-dialog>

    <!-- 修改分值对话框 -->
    <t-dialog
      v-model:visible="showScoreDialog"
      header="修改题目分值"
      width="400px"
      :confirm-btn="{ content: '确定', theme: 'primary' }"
      @confirm="confirmScoreChange"
    >
      <t-form :data="currentQuestion" label-width="80px">
        <t-form-item label="题目内容">
          <div style="padding: 8px 0">{{ currentQuestion.label }}</div>
        </t-form-item>
        <t-form-item label="分值" name="score" required>
          <t-input-number v-model="currentQuestion.score" :min="1" :max="100"/>
        </t-form-item>
      </t-form>
    </t-dialog>
  </t-layout>
</template>

<script setup>
import {ref, computed, onMounted} from 'vue'
import {MessagePlugin} from 'tdesign-vue-next'
import {getPaperList, getQuestionList} from "@/api/assessment/question.ts";

const objectives = ref([
  { value: 'obj1', label: '掌握基础知识' },
  { value: 'obj2', label: '理解核心概念' },
  { value: 'obj3', label: '应用分析能力' },
  { value: 'obj4', label: '综合创新能力' },
])

const objectiveOptions = computed(() => [
  { label: '全部目标', value: '' },
  ...objectives.value.map(obj => ({ label: obj.label, value: obj.value }))
])


// 修改题目数据，增加objective字段
// const questions = ref([
//   {
//     value: '1',
//     label: '数学',
//     children: [
//       {
//         value: '1-1',
//         label: '选择题',
//         children: [
//           { value: 'q1', label: '1. 下列哪个是二次函数？', type: 'choice', score: 10, objectives: ['obj1', 'obj2'] },
//           { value: 'q2', label: '2. 2x + 3 = 7的解是？', type: 'choice', score: 5, objectives: ['obj1'] },
//         ]
//       },
//       {
//         value: '1-2',
//         label: '填空题',
//         children: [
//           { value: 'q3', label: '3. 圆的面积公式是____。', type: 'fill', score: 8, objectives: ['obj1', 'obj3'] },
//         ]
//       },
//       {
//         value: '1-3',
//         label: '解答题',
//         children: [
//           { value: 'q4', label: '4. 证明勾股定理。', type: 'answer', score: 20, objectives: ['obj3', 'obj4'] },
//         ]
//       },
//     ],
//   },
//   {
//     value: '2',
//     label: '语文',
//     children: [
//       {
//         value: '2-1',
//         label: '选择题',
//         children: [
//           { value: 'q5', label: '1. "红楼梦"的作者是？', type: 'choice', score: 5, objectives: ['obj1'] },
//         ]
//       },
//       {
//         value: '2-2',
//         label: '阅读理解',
//         children: [
//           { value: 'q6', label: '2. 阅读下面文章并回答问题。', type: 'answer', score: 15, objectives: ['obj2', 'obj3'] },
//         ]
//       },
//     ],
//   },
// ])

const questions=ref([])
const fetchQuestionListData = async () => {
  try {
    loading.value = true
    const response = await getQuestionList()
    questions.value = response.list
  } catch (error) {
    console.error('获取题库题目失败:', error)
    MessagePlugin.error('获取题库题目失败')
  } finally {
    loading.value = false
  }
}


// 新增筛选条件
const questionFilter = ref('')
const objectiveFilter = ref('')

// 修改后的筛选逻辑
const filteredTransferQuestions = computed(() => {
  let filtered = flattenQuestions(questions.value)

  // 按题目名称筛选
  if (questionFilter.value) {
    const filterText = questionFilter.value.toLowerCase()
    filtered = filtered.filter(q => q.label.toLowerCase().includes(filterText))
  }

  // 按课程目标筛选
  if (objectiveFilter.value) {
    filtered = filtered.filter(q =>
      q.objectives && q.objectives.includes(objectiveFilter.value)
    )
  }

  return filtered
})

// 试卷列表相关
const papers = ref([])
const loading = ref(false)

const fetchPaperListData = async () => {
  try {
    loading.value = true
    const response = await getPaperList()
    papers.value = response.list
  } catch (error) {
    console.error('获取试卷列表失败:', error)
    MessagePlugin.error('获取试卷列表失败')
  } finally {
    loading.value = false
  }
}

const paperColumns = [
  {colKey: 'name', title: '试卷名称', width: 200},
  {colKey: 'description', title: '试卷描述', ellipsis: true},
  {colKey: 'createTime', title: '创建时间', width: 180},
  {colKey: 'operation', title: '操作', width: 180, fixed: 'right'},
]

const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
})

// 导入试卷相关
const showImportDialog = ref(false)
const importFiles = ref([])

// 创建/编辑试卷相关
const showCreateDialog = ref(false)
const submitLoading = ref(false)
const formRef = ref(null)
const currentPaper = ref(createEmptyPaper())

const formRules = {
  name: [{required: true, message: '请输入试卷名称', trigger: 'blur'}],
}

// 穿梭框相关
const selectedQuestionValues = ref([])
const checkedKeys = ref({source: [], target: []})
const transferQuestions = computed(() => {
  return flattenQuestions(questions.value)
})

const selectedQuestions = computed(() => {
  return transferQuestions.value
    .filter(q => selectedQuestionValues.value.includes(q.value))
    .map(q => ({...q, label: `${q.label} (${q.score}分)`}))
})

// 修改分值相关
const showScoreDialog = ref(false)
const currentQuestion = ref({})

const questionColumns = [
  {colKey: 'label', title: '题目', width: 300, ellipsis: true},
  {
    colKey: 'type',
    title: '类型',
    width: 100,
    cell: (h, {row}) => {
      const typeMap = {choice: '选择题', fill: '填空题', answer: '解答题'}
      return typeMap[row.type] || row.type
    }
  },
  {colKey: 'score', title: '分值', width: 80},
  {colKey: 'operation', title: '操作', width: 150},
]

// 计算属性
const filteredPapers = computed(() => {
  return papers.value
})

const totalScore = computed(() => {
  return selectedQuestions.value.reduce((sum, q) => sum + (q.score || 0), 0)
})

// 方法
function createEmptyPaper() {
  return {
    id: null,
    name: '',
    description: '',
    questions: [],
  }
}

function flattenQuestions(nodes) {
  if (!nodes) return []

  let result = []
  for (const node of nodes) {
    if (node.children) {
      result = result.concat(flattenQuestions(node.children))
    } else {
      result.push(node)
    }
  }
  return result
}

function fetchPapers() {
  pagination.value.total = papers.value.length
}

function handlePageChange(pageInfo) {
  pagination.value.current = pageInfo.current
  pagination.value.pageSize = pageInfo.pageSize
  fetchPapers()
}

function viewPaper(paper) {
  console.log(paper.id)
  MessagePlugin.info(`查看试卷: ${paper.name}`)
}

function handleCreateNew() {
  fetchQuestionListData()
  currentPaper.value = createEmptyPaper()
  selectedQuestionValues.value = []
  checkedKeys.value = {source: [], target: []}
  showCreateDialog.value = true
}

function editPaper(paper) {
  currentPaper.value = {...paper}
  selectedQuestionValues.value = paper.questions.map(q => q.value)
  showCreateDialog.value = true
}

function confirmDelete(paper) {
  MessagePlugin.confirm({
    content: `确定要删除试卷【${paper.name}】吗？`,
    onConfirm: () => deletePaper(paper),
  })
}

function deletePaper(paper) {
  papers.value = papers.value.filter(p => p.id !== paper.id)
  MessagePlugin.success('删除成功')
}

function beforeUpload(file) {
  const maxSize = 10 * 1024 * 1024 // 10MB
  if (file.size > maxSize) {
    MessagePlugin.error('文件大小不能超过10MB')
    return false
  }
  return true
}

function formatImportResponse(res) {
  if (res.code === 0) {
    MessagePlugin.success('导入成功')
    fetchPaperListData()
    return {status: 'success'}
  } else {
    MessagePlugin.error(res.message || '导入失败')
    return {status: 'fail'}
  }
}

function resetImport() {
  importFiles.value = []
}

function handleImportConfirm() {
  showImportDialog.value = false
}

async function submitPaperForm() {
  const result = await formRef.value.validate()
  if (result !== true) return

  submitLoading.value = true

  try {
    const paperData = {
      ...currentPaper.value,
      questions: selectedQuestions.value.map(q => ({
        value: q.value,
        label: q.label,
        type: q.type,
        score: q.score
      })),
      createTime: new Date().toLocaleString(),
    }

    if (currentPaper.value.id) {
      // 更新
      const index = papers.value.findIndex(p => p.id === currentPaper.value.id)
      if (index !== -1) {
        papers.value.splice(index, 1, paperData)
      }
      MessagePlugin.success('更新成功')
    } else {
      // 新增
      paperData.id = papers.value.length ? Math.max(...papers.value.map(p => p.id)) + 1 : 1
      papers.value.unshift(paperData)
      MessagePlugin.success('创建成功')
    }

    showCreateDialog.value = false
  } catch (error) {
    MessagePlugin.error('操作失败')
  } finally {
    submitLoading.value = false
  }
}

function resetPaperForm() {
  formRef.value?.reset()
  currentPaper.value = createEmptyPaper()
  selectedQuestionValues.value = []
}

function removeQuestion(value) {
  selectedQuestionValues.value = selectedQuestionValues.value.filter(v => v !== value)
}

function editQuestionScore(row) {
  currentQuestion.value = {...row}
  showScoreDialog.value = true
}

function confirmScoreChange() {
  const question = transferQuestions.value.find(q => q.value === currentQuestion.value.value)
  if (question) {
    question.score = currentQuestion.value.score
    MessagePlugin.success('分值修改成功')
  }
  showScoreDialog.value = false
}

function handleCheckedChange({checked, sourceChecked, targetChecked, type}) {
  checkedKeys.value = {source: sourceChecked, target: targetChecked}
}

function handleTransferChange({movedKeys, type}) {
  if (type === 'target') {
    // 移动到右侧时，自动选中新添加的项
    checkedKeys.value.target = [...checkedKeys.value.target, ...movedKeys]
  }
}

function expandAll() {
  const allKeys = questions.value.map(item => item.value)
  checkedKeys.value.source = [...new Set([...checkedKeys.value.source, ...allKeys])]
}

function collapseAll() {
  checkedKeys.value.source = []
}

// 初始化
onMounted(() => {
  fetchPaperListData()
})
</script>

<style scoped>
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  color: var(--td-text-color-secondary);
}

.empty-container p {
  margin-top: 8px;
}

.question-transfer-container {
  display: flex;
  flex-direction: column;
  height: 500px;
}

.transfer-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 6px 1px;
  font-weight: bold;
  text-align: right;
  flex-grow: 1;
}

.transfer-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100px;
  color: var(--td-text-color-secondary);
}

.transfer-empty .t-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.transfer-footer {
  display: flex;
  justify-content: flex-end;
  padding: 8px;
  border-top: 1px solid var(--td-border-level-1-color);
}

.question-preview {
  margin-top: 16px;
  border-top: 1px solid var(--td-border-level-1-color);
  padding-top: 16px;
}

.empty-hint {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px 0;
  color: var(--td-text-color-secondary);
}

.question-transfer-container {
  display: flex;
  flex-direction: column;
  height: 600px;
}

.transfer-filter {
  display: flex;
  margin-bottom: 16px;
  align-items: center;
}

.enhanced-transfer {
  flex: 1;
  min-height: 400px;
}

.enhanced-transfer :deep(.t-transfer__list) {
  height: 100%;
}

.enhanced-transfer :deep(.t-transfer__list-source),
.enhanced-transfer :deep(.t-transfer__list-target) {
  height: calc(100% - 40px);
}

.enhanced-transfer :deep(.t-transfer__list-content) {
  height: 100%;
  overflow-y: auto;
}

.transfer-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: var(--td-text-color-secondary);
}

.transfer-footer {
  display: flex;
  justify-content: flex-end;
  padding: 12px;
  border-top: 1px solid var(--td-border-level-1-color);
  background-color: var(--td-bg-color-container);
}

.question-preview {
  margin-top: 20px;
  border-top: 1px solid var(--td-border-level-1-color);
  padding-top: 20px;
}

.empty-hint {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24px 0;
  color: var(--td-text-color-secondary);
}
</style>
