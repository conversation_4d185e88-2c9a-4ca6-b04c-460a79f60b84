<template>  <t-dialog
    v-model:visible="dialogVisible"
    :header="props.isEditMode ? '编辑课程目标' : '添加课程目标'"
    :width="isFullscreen ? '100%' : '1100px'"
    :top="isFullscreen ? '0px' : '50px'"
    :confirm-btn="{ content: '保存', theme: 'primary' }"
    :cancel-btn="{ content: '取消', theme: 'default', variant: 'outline' }"
    @confirm="confirmDialog"
    @cancel="cancelDialog"
    class="objective-dialog"
    :class="{'fullscreen-dialog': isFullscreen}"
  >
    <template #header-right>
      <t-icon 
        :name="isFullscreen ? 'browse-off' : 'browse'" 
        size="20px" 
        style="cursor: pointer; margin-right: 8px;" 
        @click="toggleFullscreen"
      />
    </template>
    <div class="add-objective-dialog-content">
      <div class="objective-form-container">
        <!-- 左侧表单 -->
        <div class="objective-form">
          <t-form ref="formRef" :data="formData" label-align="top" :rules="formRules">
            <t-form-item label="关联指标点" name="indicatorId">
                <div class="indicator-display-card">
                  <div class="indicator-meta" v-if="props.selectedIndicator">
                    <t-tag theme="primary" variant="light" size="small">
                     毕业要求 {{ props.selectedIndicator.standardNumber }}.{{ props.selectedIndicator.poNumber }}
                    </t-tag>
                  <!-- </div>
                  <div class="indicator-title"> -->
                    {{ props.selectedIndicator?.poTitle || '未选择指标点' }}
                  </div>
                  <div class="indicator-description" v-if="props.selectedIndicator?.poDescription">
                    {{ props.selectedIndicator.poDescription }}
                  </div>
                  
                </div>
            </t-form-item>
            <t-form-item label="目标位置" name="positionType" required>
              <t-radio-group v-model="positionType">
                <t-radio-button value="start">放在最前面</t-radio-button>
                <t-radio-button value="before">放在选中目标之前</t-radio-button>
                <t-radio-button value="after">放在选中目标之后</t-radio-button>
                <t-radio-button value="end">放在最后面</t-radio-button>
              </t-radio-group>
              
              <template #tips>
                <p>
                  <t-icon name="info-circle-filled" style="color: var(--td-brand-color); margin-right: 4px;"/>
                  {{ positionType === 'start' ? '新目标将插入到列表最前面' : 
                     positionType === 'end' ? '新目标将插入到列表最后面' : 
                     (selectedPositionObjectiveId ? `新目标将插入到选中目标${positionType === 'before' ? '之前' : '之后'}` : '请在右侧列表选择一个目标') }}
                </p>
              </template>
            </t-form-item>
            <t-form-item label="目标标题" name="title" required>
              <t-input 
                v-model="formData.title" 
                placeholder="请输入课程目标标题"
              />
            </t-form-item>
            <t-form-item label="目标描述" name="description">
              <t-textarea
                v-model="formData.description"
                placeholder="请输入课程目标描述"
                :autosize="{ minRows: 3, maxRows: 6 }"
              />
            </t-form-item>
            <t-form-item label="达成度期望值" name="weight" required>
              <t-input-number
                v-model="formData.weight"
                :min="1"
                :max="100"
                placeholder="请输入达成度期望值（1-100）"
                suffix="%"
                :disabled="props.isEditMode"
              />
            </t-form-item>
          </t-form>
        </div>
        
        <!-- 右侧课程目标列表 -->
        <div class="objective-list">
          <div class="objective-list-header">
            <h4>当前课程目标列表</h4>
            <p class="help-text">
              <t-icon name="info-circle" style="margin-right: 4px;"/>
              选择一个目标来确定新目标的位置
            </p>
          </div>
          
          <div class="objective-cards-container">
            <div
              v-if="props.existingObjectives.length === 0"
              class="empty-objectives"
            >
              <t-icon name="file" size="36px" />
              <div>暂无课程目标</div>
            </div>
            
            <div
              v-else
              class="objectives-container"
            >
              <!-- 空状态 -->
              <div v-if="sortedObjectives.length === 0" class="empty-objectives">
                <t-icon name="file" size="36px" />
                <div>暂无课程目标</div>
              </div>
              
              <div
                v-for="objective in sortedObjectives"
                :key="objective.objectiveId"
                :class="[
                  'objective-card',
                  {
                    'selected': selectedPositionObjectiveId === objective.objectiveId
                  }
                ]"
                @click="selectObjective(objective)"
              >
                <div class="objective-card-header">
                  <div class="objective-info">
                    <span class="objective-number">{{ objective.number }}</span>
                    <div class="objective-indicator">
                      <span class="indicator-code">{{ objective.po?.standardNumber || '' }}-{{ objective.po?.poNumber || '' }}</span>
                    </div>
                  </div>
                  <div class="objective-controls">
                    <!-- 上下移动按钮 -->
                    <div class="move-buttons">
                      <t-button
                        size="small"
                        variant="text"
                        :disabled="isFirstObjective(objective)"
                        @click.stop="moveObjectiveUp(objective)"
                        title="向上移动"
                      >
                        <t-icon name="chevron-up" size="14px" />
                      </t-button>
                      <t-button
                        size="small"
                        variant="text"
                        :disabled="isLastObjective(objective)"
                        @click.stop="moveObjectiveDown(objective)"
                        title="向下移动"
                      >
                        <t-icon name="chevron-down" size="14px" />
                      </t-button>
                    </div>
                  </div>
                </div>
                <div class="objective-title">{{ objective.objectiveName }}</div>
                <div class="objective-description" v-if="objective.description">
                  {{ objective.description }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </t-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import { type CoursePoVO } from '@/api/training/po-matrix';
import {type CourseObjectiveVO} from '@/api/training/course';

// 接口定义


interface CourseObjectiveForm {
  number: number;
  title: string;
  description: string;
  weight: number;
  poId: number;
  poTitle: string;
  poDescription?: string;
  poNumber?: number;
}


// Props 定义
interface Props {
  visible: boolean;
  selectedIndicator: CoursePoVO | null;
  existingObjectives?: CourseObjectiveVO[];
  // 编辑模式相关props
  isEditMode?: boolean;
  editingObjective?: CourseObjectiveVO | null;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  selectedIndicator: null,
  existingObjectives: () => [],
  isEditMode: false,
  editingObjective: null
});

// Emits 定义
interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'confirm', objective: CourseObjectiveVO, positionInfo: { type: string, position: number }): void;
  (e: 'cancel'): void;
  (e: 'reorderObjectives', data: { sourceId: number, targetId: number, position: 'before' | 'after' }): void;
}

const emit = defineEmits<Emits>();

// 响应式数据
const formRef = ref();
const positionType = ref('end'); // 默认放在最后
const selectedPositionObjectiveId = ref<number | null>(null);
const dialogVisible = computed({
  get: () => props.visible,
  set: (value: boolean) => emit('update:visible', value)
});

// 获取经过排序的课程目标列表
const sortedObjectives = computed<CourseObjectiveVO[]>(() => {
  if (!props.existingObjectives) return [];
  
  // 过滤掉当前正在编辑的目标（如果在编辑模式）
  const filteredObjectives = props.isEditMode && props.editingObjective 
    ? props.existingObjectives.filter(obj => obj.objectiveId !== props.editingObjective.objectiveId)
    : [...props.existingObjectives];
  
  // 按照编号排序
  return [...filteredObjectives].sort((a, b) => a.number - b.number);
});



// 表单数据
const formData = ref<CourseObjectiveForm>({
  number: 0, // 编号将自动计算，此字段仅作为数据结构占位
  title: '',
  description: '',
  weight: 50,
  poId: 0,
  poTitle: ''
});

// 表单验证规则
const formRules = {
  title: [{ required: true, message: '请输入课程目标标题' }],
  weight: [
    { required: true, message: '请输入权重' },
    { min: 1, max: 100, message: '权重范围为1-100' }
  ]
};

// 移动按钮相关函数

// 移动按钮相关函数

// 选择目标作为插入位置
const selectObjective = (objective: CourseObjectiveVO): void => {
  selectedPositionObjectiveId.value = objective.objectiveId;

  // 自动设置为"放在之后"，除非已经选择了"放在之前"
  if (positionType.value !== 'before') {
    positionType.value = 'after';
  }
};

// 检查是否为第一个目标
const isFirstObjective = (objective: CourseObjectiveVO): boolean => {
  const sortedList = sortedObjectives.value;
  return sortedList.length > 0 && sortedList[0].objectiveId === objective.objectiveId;
};

// 检查是否为最后一个目标
const isLastObjective = (objective: CourseObjectiveVO): boolean => {
  const sortedList = sortedObjectives.value;
  return sortedList.length > 0 && sortedList[sortedList.length - 1].objectiveId === objective.objectiveId;
};

// 向上移动目标
const moveObjectiveUp = (objective: CourseObjectiveVO): void => {
  const currentIndex = sortedObjectives.value.findIndex(obj => obj.objectiveId === objective.objectiveId);
  if (currentIndex > 0) {
    const targetObjective = sortedObjectives.value[currentIndex - 1];

    // 发送重排序事件：将当前目标移动到前一个目标之前
    emit('reorderObjectives', {
      sourceId: objective.objectiveId,
      targetId: targetObjective.objectiveId,
      position: 'before'
    });

    // 更新选中状态
    selectedPositionObjectiveId.value = objective.objectiveId;
    positionType.value = 'before';
  }
};

// 向下移动目标
const moveObjectiveDown = (objective: CourseObjectiveVO): void => {
  const currentIndex = sortedObjectives.value.findIndex(obj => obj.objectiveId === objective.objectiveId);
  if (currentIndex < sortedObjectives.value.length - 1) {
    const targetObjective = sortedObjectives.value[currentIndex + 1];

    // 发送重排序事件：将当前目标移动到后一个目标之后
    emit('reorderObjectives', {
      sourceId: objective.objectiveId,
      targetId: targetObjective.objectiveId,
      position: 'after'
    });

    // 更新选中状态
    selectedPositionObjectiveId.value = objective.objectiveId;
    positionType.value = 'after';
  }
};

// 根据选择的位置计算插入位置信息
const calculatePositionInfo = (): { type: string; position: number } => {
  if (!props.existingObjectives || props.existingObjectives.length === 0 || positionType.value === 'start') {
    // 如果没有目标，或者选择放在最前面
    return { type: 'start', position: 0 };
  }

  // 如果选择放在最后面
  if (positionType.value === 'end') {
    return { type: 'end', position: props.existingObjectives.length };
  }

  // 如果选择放在选中目标之前或之后
  if ((positionType.value === 'before' || positionType.value === 'after') && selectedPositionObjectiveId.value) {
    const targetIndex = props.existingObjectives.findIndex(
      obj => obj.objectiveId === selectedPositionObjectiveId.value
    );
    
    if (targetIndex !== -1) {
      if (positionType.value === 'before') {
        // 放在目标之前
        return { type: 'before', position: targetIndex };
      } else {
        // 放在目标之后
        return { type: 'after', position: targetIndex + 1 };
      }
    }
  }

  // 默认放在最后
  return { type: 'end', position: props.existingObjectives.length };
};

// 重置表单
const resetForm = () => {
  formData.value = {
    number: 0, // 编号将自动计算
    title: '',
    description: '',
    weight: 50,
    poId: 0,
    poTitle: ''
  };
  
  // 重置插入位置选择
  positionType.value = 'end';
  selectedPositionObjectiveId.value = null;
  
  // 清除表单验证状态
  if (formRef.value) {
    formRef.value.clearValidate();
  }
};

// 确认对话框
const confirmDialog = async () => {
  // 表单验证
  if (!formData.value.title.trim()) {
    MessagePlugin.warning('请输入课程目标标题');
    return;
  }

  // 编辑模式下不验证权重（因为权重字段被禁用）
  if (!props.isEditMode && (!formData.value.weight || formData.value.weight < 1 || formData.value.weight > 100)) {
    MessagePlugin.warning('权重范围为1-100');
    return;
  }
  
  // 验证是否选择了插入位置
  if (!positionType.value) {
    MessagePlugin.warning('请选择插入位置');
    return;
  }
  
  // 如果选择了"放在指定目标之前/后"，验证是否选择了具体目标
  if ((positionType.value === 'after' || positionType.value === 'before') && 
      !selectedPositionObjectiveId.value && 
      props.existingObjectives.length > 0) {
    MessagePlugin.warning('请选择目标位置');
    return;
  }

  try {
    // 计算插入位置信息
    const positionInfo = calculatePositionInfo();
    
    // 创建课程目标对象
    let objectiveToEmit: CourseObjectiveVO;
    
    if (props.isEditMode && props.editingObjective) {
      // 编辑模式：更新title、description等信息
      objectiveToEmit = {
        ...props.editingObjective,
        objectiveName: formData.value.title,
        description: formData.value.description || '暂无描述',
        // 编号将由父组件根据positionInfo重新计算
        number: props.editingObjective.number
      };
    } else {
      // 添加模式：创建新的课程目标对象
      objectiveToEmit = {
        objectiveId: Date.now(),
        // 临时设置编号为0，实际编号将由父组件根据positionInfo重新计算
        number: 0,
        objectiveName: formData.value.title,
        description: formData.value.description || '暂无描述',
        expectedScore: formData.value.weight,
        // 关联的指标点信息
        po: {
          id: props.selectedIndicator.poId,
          // CoursePoVO 中的 po 字段包含了完整的 PoVO 信息
          title: props.selectedIndicator.poTitle,
          poTitle: props.selectedIndicator.poTitle,
          poDescription: props.selectedIndicator.poDescription || '',
          poNumber: props.selectedIndicator.poNumber || 0,
          planId: props.selectedIndicator.planId || 0,
          requirementId: props.selectedIndicator.standardId || 0,
          isRequirement: false,
          parentId: 0,
          // 补充缺失的属性
          standardId: props.selectedIndicator.standardId || 0,
          standardNumber: props.selectedIndicator.standardNumber || 0,
          status: props.selectedIndicator.status || 1 // 默认状态为1（启用）
        },
        assessmentMethods: []
      };
    }

    // 发送确认事件，将位置信息作为单独参数传递
    emit('confirm', objectiveToEmit, positionInfo);
    
    // 重置表单
    resetForm();
  } catch (error) {
    console.error('创建课程目标失败:', error);
    MessagePlugin.error('创建课程目标失败');
  }
};

// 取消对话框
const cancelDialog = () => {
  resetForm();
  emit('cancel');
};

// 全屏控制
const isFullscreen = ref(false);
const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value;
};

// 监听全屏状态变化调整内容高度
watch(() => isFullscreen.value, (isFullscreen) => {
  if (isFullscreen) {
    // 全屏模式下增加内容区域高度
    setTimeout(() => {
      const cardsContainer = document.querySelector('.objective-cards-container') as HTMLElement;
      if (cardsContainer) {
        cardsContainer.style.maxHeight = 'calc(80vh - 100px)';
      }
    }, 100);
  } else {
    // 恢复默认高度
    const cardsContainer = document.querySelector('.objective-cards-container') as HTMLElement;
    if (cardsContainer) {
      cardsContainer.style.maxHeight = '400px';
    }
  }
});

// 监听课程目标数据变化，强制刷新视图
watch(() => props.existingObjectives, (newVal) => {
  console.log('课程目标数据已更新，重新排序:', 
    newVal?.map(obj => `${obj.number}(${obj.objectiveName})`).join(', ')
  );
  // 强制更新sortedObjectives计算属性
  nextTick(() => {
    const container = document.querySelector('.objective-cards-container');
    if (container) {
      // 先滚动到顶部再滚动回来，强制刷新DOM
      const scrollTop = container.scrollTop;
      container.scrollTop = 0;
      setTimeout(() => {
        container.scrollTop = scrollTop;
      }, 10);
    }
  });
}, { deep: true });
</script>

<style scoped>
.objective-dialog.fullscreen-dialog {
  height: 100vh;
}

.add-objective-dialog-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.objective-form-container {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.objective-form {
  flex: 1;
  padding-right: 20px;
  border-right: 1px solid #eaeaea;
}

.objective-list {
  flex: 1;
  padding-left: 20px;
}

.objective-list-header {
  margin-bottom: 16px;
}

.objective-cards-container {
  max-height: 400px;
  overflow-y: auto;
}

.objective-card {
  background-color: #f9f9f9;
  border: 1px solid #eaeaea;
  border-radius: 4px;
  padding: 12px;
  margin-bottom: 12px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.objective-card.selected {
  background-color: var(--td-brand-color-light);
  border-color: var(--td-brand-color);
}



.objective-info {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.objective-number {
  font-weight: bold;
  margin-right: 8px;
}

.objective-indicator {
  display: flex;
  align-items: center;
  background-color: var(--td-brand-color-light);
  color: var(--td-brand-color);
  border-radius: 4px;
  padding: 2px 6px;
  font-size: 12px;
}

.indicator-code {
  font-size: 12px;
  font-weight: 500;
}

.objective-controls {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: auto;
}

.move-buttons {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.move-buttons .t-button {
  padding: 2px 4px;
  min-width: 24px;
  height: 20px;
  border-radius: 2px;
}

.move-buttons .t-button:hover:not(:disabled) {
  background-color: var(--td-brand-color-light);
  color: var(--td-brand-color);
}

.move-buttons .t-button:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}



.empty-objectives {
  text-align: center;
  color: var(--td-text-color-secondary);
  padding: 60px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12px;
  border: 2px dashed #eaeaea;
  border-radius: 6px;
  margin: 10px 0;
  transition: all 0.3s;
}


</style>