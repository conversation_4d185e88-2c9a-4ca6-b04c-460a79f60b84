import request from '@/utils/request';
import {StudentGradeListResult} from "@/api/model/student/studentGradeModel";


//获取成绩数据
export async function getGradeList(cid:string): Promise<StudentGradeListResult> {
  try {
    const res = await request({
      url: 'api/student/grade/list',
      method: 'get',
      params: { cid }
    });
    return res;
  } catch (error) {
    console.error('获取成绩数据失败:', error);
    throw error;
  }
}
