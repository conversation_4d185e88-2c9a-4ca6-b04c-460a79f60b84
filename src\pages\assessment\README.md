# 教学任务管理组件使用指南

## 组件概述

本模块提供了完整的教学任务管理功能，包括教学任务列表展示、多选操作、批量管理等功能。

## 组件结构

```
src/pages/assessment/
├── AssessmentContent/components/
│   ├── TeachingTaskCard.vue          # 教学任务卡片组件
│   ├── TeachingTaskList.vue          # 教学任务列表组件
│   └── AssessmentPublishingDialog.vue # 考核发布对话框
├── TeachingTaskManagement.vue        # 教学任务管理页面
└── README.md                         # 使用指南
```

## 核心组件

### 1. TeachingTaskCard.vue
教学任务卡片组件，用于展示单个教学任务的详细信息。

**Props:**
- `taskData: TaskWorkDetailVO` - 教学任务数据
- `selected: boolean` - 是否选中状态

**Events:**
- `toggle-selection(taskId: number, selected: boolean)` - 选择状态切换

**特性:**
- ✅ 符合 TDesign 设计规范
- ✅ 支持选中状态视觉反馈
- ✅ 响应式设计
- ✅ 悬停交互效果

### 2. TeachingTaskList.vue
教学任务列表组件，提供完整的列表管理功能。

**Props:**
- `courseId: number` - 课程ID（必需）
- `taskYear?: number` - 学年（默认当前年份）
- `taskTerm?: number` - 学期（默认1）
- `courseName?: string` - 课程名称

**Events:**
- `task-selected(taskIds: number[])` - 任务选择事件
- `task-deleted(taskIds: number[])` - 任务删除事件

**功能特性:**
- ✅ 网格布局（响应式，最多5列）
- ✅ 全选/取消全选
- ✅ 批量操作（删除、编辑等）
- ✅ 分页支持
- ✅ 加载状态和空状态处理
- ✅ 错误处理和用户提示

### 3. TeachingTaskManagement.vue
完整的教学任务管理页面，包含筛选和操作功能。

**功能特性:**
- ✅ 课程筛选
- ✅ 学年学期筛选
- ✅ 选中任务信息面板
- ✅ 批量操作（导出、编辑）
- ✅ 响应式设计

## 使用方法

### 基础使用

```vue
<template>
  <TeachingTaskList
    :course-id="1001"
    :task-year="2024"
    :task-term="1"
    course-name="高等数学A"
    @task-selected="handleTaskSelected"
    @task-deleted="handleTaskDeleted"
  />
</template>

<script setup>
import TeachingTaskList from '@/pages/assessment/AssessmentContent/components/TeachingTaskList.vue'

const handleTaskSelected = (taskIds) => {
  console.log('选中的任务:', taskIds)
}

const handleTaskDeleted = (deletedIds) => {
  console.log('删除的任务:', deletedIds)
}
</script>
```

### 高级使用

```vue
<template>
  <div>
    <!-- 筛选区域 -->
    <div class="filter-section">
      <t-select v-model="courseId" :options="courseOptions" />
      <t-select v-model="taskYear" :options="yearOptions" />
      <t-select v-model="taskTerm" :options="termOptions" />
    </div>
    
    <!-- 任务列表 -->
    <TeachingTaskList
      ref="taskListRef"
      :course-id="courseId"
      :task-year="taskYear"
      :task-term="taskTerm"
      @task-selected="handleTaskSelected"
    />
    
    <!-- 操作按钮 -->
    <div class="actions">
      <t-button @click="refreshList">刷新</t-button>
      <t-button @click="clearSelection">清空选择</t-button>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import TeachingTaskList from '@/pages/assessment/AssessmentContent/components/TeachingTaskList.vue'

const taskListRef = ref()
const courseId = ref(1001)
const taskYear = ref(2024)
const taskTerm = ref(1)

const refreshList = () => {
  taskListRef.value?.refresh()
}

const clearSelection = () => {
  taskListRef.value?.clearSelection()
}

const handleTaskSelected = (taskIds) => {
  console.log('当前选中:', taskIds)
}
</script>
```

## API 接口

### 数据获取
使用 `src/api/teaching/task.ts` 中的 `getTaskWorkList` 接口：

```typescript
interface TaskWorkDetailVO {
  academicYear: string      // 学年
  semester: string         // 学期
  taskId: number          // 任务ID
  taskName: string        // 任务名称
  taskNumber: number      // 任务编号
  teachWeek: number       // 教学周数
  weekHours: number       // 周学时
  classes: TaskClassVO[]  // 班级列表
  teachers: TaskTeacherVO[] // 教师列表
  studentCount: number    // 学生总数
  classCount: number      // 班级总数
}
```

### 请求参数
```typescript
{
  courseId: number,        // 课程ID
  taskYear?: number,       // 学年
  taskTerm?: number,       // 学期
  current?: number,        // 当前页
  pageSize?: number        // 页面大小
}
```

## 样式定制

### TDesign 设计变量
组件使用了完整的 TDesign 设计变量系统：

```less
// 间距
--td-comp-margin-xs/s/m/l/xl/xxl
--td-comp-paddingTB-s/m/l/xl
--td-comp-paddingLR-s/m/l/xl

// 颜色
--td-brand-color
--td-success-color / --td-warning-color
--td-text-color-primary/secondary/placeholder
--td-bg-color-container/page

// 字体
--td-font-size-title-large/medium/small
--td-font-size-body-medium/small
--td-font-weight-bold/semi-bold/medium

// 圆角和阴影
--td-radius-large/medium/small
--td-shadow-2/3
```

### 响应式断点
```less
@media (min-width: 1920px) { /* 5列 */ }
@media (max-width: 1600px) { /* 4列 */ }
@media (max-width: 1200px) { /* 3列 */ }
@media (max-width: 900px)  { /* 2列 */ }
@media (max-width: 600px)  { /* 1列 */ }
```

## 最佳实践

### 1. 性能优化
- 使用分页加载大量数据
- 合理设置页面大小（推荐20-50）
- 避免频繁的API调用

### 2. 用户体验
- 提供清晰的加载状态
- 友好的错误提示信息
- 合理的空状态设计

### 3. 数据管理
- 及时清理选中状态
- 处理数据更新后的状态同步
- 合理的缓存策略

### 4. 错误处理
- 网络异常处理
- 权限验证
- 数据验证

## 扩展功能

### 自定义操作
可以通过监听事件来实现自定义操作：

```vue
<TeachingTaskList
  @task-selected="handleCustomAction"
  @task-deleted="handleAfterDelete"
/>
```

### 自定义样式
通过 CSS 变量覆盖默认样式：

```less
.teaching-task-list {
  --td-brand-color: #your-color;
  --td-radius-large: 12px;
}
```

## 注意事项

1. **必需参数**: `courseId` 是必需的，确保传入有效的课程ID
2. **权限控制**: 根据用户权限控制批量操作的可见性
3. **数据同步**: 删除操作后需要同步更新相关状态
4. **移动端适配**: 在小屏幕设备上会自动调整为单列布局
5. **浏览器兼容**: 使用了 CSS Grid，需要现代浏览器支持

## 更新日志

- **v1.0.0**: 初始版本，包含基础的列表展示和多选功能
- 支持响应式网格布局
- 集成 TDesign 设计规范
- 完整的错误处理和用户反馈
