<template>
  <div class="course-detail-container">
    <!-- 顶部导航区域 -->
    <div class="page-header">
      <t-button theme="default" @click="goBack">
        <template #icon><t-icon name="arrow-left" /></template>
        返回课程列表
      </t-button>
      <h2>课程体系管理</h2>
    </div>

    <!-- 课程体系管理内容区域 -->
    <t-card class="main-card">
      <template #title>
        <div class="card-header">
          <t-icon name="book" class="title-icon" />
          <span>课程体系总览</span>
        </div>
      </template>
      
      <t-tabs v-model="activeTab">
        <t-tab-panel value="curriculum" label="课程培养方案">
          <div class="tab-content">
            <!-- 课程培养方案内容 -->
            <div class="filter-bar">
              <t-select
                v-model="selectedVersion"
                placeholder="选择培养方案版本"
                clearable
                style="width: 200px"
              >
                <t-option v-for="version in versionOptions" :key="version.value" :value="version.value" :label="version.label" />
              </t-select>
              
              <t-select
                v-model="selectedMajor"
                placeholder="选择专业"
                clearable
                style="width: 200px"
              >
                <t-option v-for="major in majorOptions" :key="major.value" :value="major.value" :label="major.label" />
              </t-select>
              
              <t-button theme="primary" @click="handleSearch">
                <template #icon><t-icon name="search" /></template>
                查询
              </t-button>
            </div>
            
            <!-- 课程列表 -->
            <t-table
              :data="courseList"
              :columns="curriculumColumns"
              :bordered="true"
              :loading="loading"
              :pagination="pagination"
              @page-change="onPageChange"
              stripe
              hover
            >
              <template #courseType="{ row }">
                <t-tag theme="primary" variant="light" v-if="row.courseType === 1">必修</t-tag>
                <t-tag theme="warning" variant="light" v-else-if="row.courseType === 2">选修</t-tag>
                <t-tag theme="success" variant="light" v-else>通识</t-tag>
              </template>
              
              <template #status="{ row }">
                <t-tag theme="success" variant="light" v-if="row.status === 'active'">启用</t-tag>
                <t-tag theme="danger" variant="light" v-else>禁用</t-tag>
              </template>
              
              <template #op="slotProps">
                <t-space>
                  <t-button theme="primary" size="small" variant="text" @click="viewCourseDetail(slotProps.row)">
                    <template #icon><t-icon name="browse" /></template>
                    查看
                  </t-button>
                  <t-button theme="warning" size="small" variant="text" @click="editCourse(slotProps.row)">
                    <template #icon><t-icon name="edit" /></template>
                    编辑
                  </t-button>
                </t-space>
              </template>
            </t-table>
          </div>
        </t-tab-panel>
        
        <t-tab-panel value="structure" label="课程体系结构">
          <div class="tab-content">
            <!-- 课程体系结构内容 -->
            <div class="filter-bar">
              <t-select
                v-model="selectedStructureVersion"
                placeholder="选择培养方案版本"
                clearable
                style="width: 200px"
              >
                <t-option v-for="version in versionOptions" :key="version.value" :value="version.value" :label="version.label" />
              </t-select>
              
              <t-button theme="primary" @click="handleStructureSearch">
                <template #icon><t-icon name="search" /></template>
                查询
              </t-button>
            </div>
            
            <!-- 课程体系结构展示 -->
            <div class="structure-container">
              <div class="structure-chart" ref="structureChartRef"></div>
            </div>
          </div>
        </t-tab-panel>
        
        <t-tab-panel value="statistics" label="课程统计分析">
          <div class="tab-content">
            <!-- 课程统计分析内容 -->
            <div class="statistics-grid">
              <t-card title="课程类型分布" bordered class="stat-card">
                <div class="chart-container" ref="typeChartRef"></div>
              </t-card>
              
              <t-card title="学期课程数量" bordered class="stat-card">
                <div class="chart-container" ref="termChartRef"></div>
              </t-card>
              
              <t-card title="课程学分分布" bordered class="stat-card">
                <div class="chart-container" ref="creditChartRef"></div>
              </t-card>
              
              <t-card title="毕业要求支撑度" bordered class="stat-card">
                <div class="chart-container" ref="supportChartRef"></div>
              </t-card>
            </div>
          </div>
        </t-tab-panel>
      </t-tabs>
    </t-card>
    
    <!-- 课程详情对话框 -->
    <t-dialog
      v-model:visible="courseDetailVisible"
      header="课程详细信息"
      :width="700"
      :footer="false"
    >
      <template v-if="currentCourse">
        <t-descriptions bordered>
          <t-descriptions-item label="课程名称">{{ currentCourse.courseName }}</t-descriptions-item>
          <t-descriptions-item label="课程代码">{{ currentCourse.courseCode }}</t-descriptions-item>
          <t-descriptions-item label="开设学期">{{ currentCourse.semester }}</t-descriptions-item>
          <t-descriptions-item label="学分">{{ currentCourse.credit }}</t-descriptions-item>
          <t-descriptions-item label="课程类型">
            <t-tag theme="primary" variant="light" v-if="currentCourse.courseType === 1">必修</t-tag>
            <t-tag theme="warning" variant="light" v-else-if="currentCourse.courseType === 2">选修</t-tag>
            <t-tag theme="success" variant="light" v-else>通识</t-tag>
          </t-descriptions-item>
          <t-descriptions-item label="课程状态">
            <t-tag theme="success" variant="light" v-if="currentCourse.status === 'active'">启用</t-tag>
            <t-tag theme="danger" variant="light" v-else>禁用</t-tag>
          </t-descriptions-item>
        </t-descriptions>
        
        <div class="detail-section">
          <h3>课程描述</h3>
          <p>{{ currentCourse.description || '暂无课程描述' }}</p>
        </div>
        
        <div class="detail-section">
          <h3>教学目标</h3>
          <p>{{ currentCourse.objective || '暂无教学目标' }}</p>
        </div>
        
        <div class="detail-section">
          <h3>先修课程</h3>
          <t-tag v-for="(prerequisite, index) in currentCourse.prerequisites" 
            :key="index" theme="default" variant="light" style="margin-right: 8px; margin-bottom: 8px;">
            {{ prerequisite }}
          </t-tag>
          <p v-if="!currentCourse.prerequisites || currentCourse.prerequisites.length === 0">暂无先修课程</p>
        </div>
      </template>
    </t-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import * as echarts from 'echarts';

const router = useRouter();

// 页面状态
const activeTab = ref('curriculum');
const loading = ref(false);
const courseDetailVisible = ref(false);
const currentCourse = ref(null);

// 筛选条件
const selectedVersion = ref('');
const selectedMajor = ref('');
const selectedStructureVersion = ref('');

// 筛选选项
const versionOptions = [
  { label: '2024版', value: '2024' },
  { label: '2023版', value: '2023' },
  { label: '2022版', value: '2022' },
];

const majorOptions = [
  { label: '计算机科学与技术', value: 'CS' },
  { label: '软件工程', value: 'SE' },
  { label: '网络工程', value: 'NE' },
  { label: '信息安全', value: 'IS' },
];

// 表格分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  pageSizeOptions: [5, 10, 20, 50],
});

// 表格列配置
const curriculumColumns = [
  { colKey: 'courseCode', title: '课程代码', width: 120 },
  { colKey: 'courseName', title: '课程名称', width: 180 },
  { colKey: 'credit', title: '学分', width: 80 },
  { colKey: 'courseType', title: '课程类型', width: 100, cell: 'courseType' },
  { colKey: 'semester', title: '开设学期', width: 120 },
  { colKey: 'department', title: '所属院系', width: 160 },
  { colKey: 'status', title: '状态', width: 80, cell: 'status' },
  { colKey: 'op', title: '操作', width: 120, fixed: 'right', cell: 'op' },
];

// 课程列表数据
const courseList = ref([
  {
    courseCode: 'CS1001',
    courseName: '程序设计基础',
    credit: 4,
    courseType: 1,
    semester: '第1学期',
    department: '计算机科学与技术学院',
    status: 'active',
    description: '本课程介绍计算机程序设计的基础知识，包括基本数据类型、控制结构、函数、数组等内容。',
    objective: '掌握程序设计的基本方法和技能，能够编写简单的程序解决实际问题。',
    prerequisites: [],
  },
  {
    courseCode: 'CS1002',
    courseName: '计算机组成原理',
    credit: 3,
    courseType: 1,
    semester: '第2学期',
    department: '计算机科学与技术学院',
    status: 'active',
    description: '本课程介绍计算机硬件系统的基本组成和工作原理，包括CPU、内存、I/O系统等内容。',
    objective: '理解计算机系统的基本组成和工作原理，掌握计算机硬件系统的设计方法。',
    prerequisites: ['程序设计基础'],
  },
  {
    courseCode: 'CS2001',
    courseName: '数据结构',
    credit: 4,
    courseType: 1,
    semester: '第3学期',
    department: '计算机科学与技术学院',
    status: 'active',
    description: '本课程介绍常用的数据结构和算法，包括线性表、栈、队列、树、图等内容。',
    objective: '掌握常用数据结构的实现方法和应用技巧，能够分析算法的时间和空间复杂度。',
    prerequisites: ['程序设计基础'],
  },
  {
    courseCode: 'CS2002',
    courseName: '操作系统',
    credit: 3,
    courseType: 1,
    semester: '第4学期',
    department: '计算机科学与技术学院',
    status: 'active',
    description: '本课程介绍操作系统的基本概念、原理和实现方法，包括进程管理、内存管理、文件系统等内容。',
    objective: '理解操作系统的基本原理和实现机制，掌握操作系统的设计方法和技术。',
    prerequisites: ['计算机组成原理', '数据结构'],
  },
  {
    courseCode: 'CS3001',
    courseName: '计算机网络',
    credit: 3,
    courseType: 1,
    semester: '第5学期',
    department: '计算机科学与技术学院',
    status: 'active',
    description: '本课程介绍计算机网络的基本概念、体系结构和协议，包括物理层、数据链路层、网络层、传输层和应用层等内容。',
    objective: '掌握计算机网络的基本原理和协议，能够设计和实现简单的网络应用。',
    prerequisites: ['操作系统'],
  },
  {
    courseCode: 'SE2001',
    courseName: '软件工程',
    credit: 3,
    courseType: 2,
    semester: '第5学期',
    department: '软件学院',
    status: 'active',
    description: '本课程介绍软件开发的基本方法和技术，包括需求分析、系统设计、编码实现、测试和维护等内容。',
    objective: '掌握软件开发的基本方法和技术，能够参与软件项目的开发和管理。',
    prerequisites: ['程序设计基础', '数据结构'],
  },
]);

// 图表引用
const structureChartRef = ref(null);
const typeChartRef = ref(null);
const termChartRef = ref(null);
const creditChartRef = ref(null);
const supportChartRef = ref(null);

// 方法定义
const goBack = () => {
  router.push('/training/plan');
};

const handleSearch = () => {
  loading.value = true;
  console.log('查询条件：', { version: selectedVersion.value, major: selectedMajor.value });
  
  // 模拟API请求
  setTimeout(() => {
    loading.value = false;
    // 更新表格数据
    pagination.total = courseList.value.length;
  }, 500);
};

const handleStructureSearch = () => {
  console.log('查询课程体系结构：', { version: selectedStructureVersion.value });
  renderStructureChart();
};

const onPageChange = (curr) => {
  pagination.current = curr;
};

const viewCourseDetail = (course) => {
  currentCourse.value = course;
  courseDetailVisible.value = true;
};

const editCourse = (course) => {
  console.log('编辑课程：', course);
  // 跳转到课程编辑页面或显示编辑对话框
};

// 渲染课程体系结构图表
const renderStructureChart = () => {
  if (!structureChartRef.value) return;
  
  const chart = echarts.init(structureChartRef.value);
  
  // 课程体系结构数据
  const data = {
    name: '计算机科学与技术',
    children: [
      {
        name: '公共基础课程',
        children: [
          { name: '高等数学', value: 5 },
          { name: '线性代数', value: 3 },
          { name: '大学物理', value: 4 },
          { name: '大学英语', value: 4 },
        ]
      },
      {
        name: '专业基础课程',
        children: [
          { name: '程序设计基础', value: 4 },
          { name: '计算机组成原理', value: 3 },
          { name: '数据结构', value: 4 },
          { name: '操作系统', value: 3 },
        ]
      },
      {
        name: '专业核心课程',
        children: [
          { name: '计算机网络', value: 3 },
          { name: '编译原理', value: 3 },
          { name: '数据库系统', value: 3 },
          { name: '软件工程', value: 3 },
        ]
      },
      {
        name: '专业选修课程',
        children: [
          { name: '人工智能', value: 2 },
          { name: '机器学习', value: 3 },
          { name: '云计算', value: 2 },
          { name: '信息安全', value: 2 },
        ]
      },
      {
        name: '实践课程',
        children: [
          { name: '课程设计', value: 2 },
          { name: '毕业实习', value: 4 },
          { name: '毕业设计', value: 8 },
        ]
      }
    ]
  };
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c} 学分'
    },
    series: [
      {
        type: 'tree',
        data: [data],
        left: '2%',
        right: '2%',
        top: '8%',
        bottom: '20%',
        symbol: 'emptyCircle',
        orient: 'vertical',
        expandAndCollapse: true,
        label: {
          position: 'top',
          rotate: 0,
          verticalAlign: 'middle',
          align: 'right',
          fontSize: 12
        },
        leaves: {
          label: {
            position: 'right',
            verticalAlign: 'middle',
            align: 'left'
          }
        },
        emphasis: {
          focus: 'descendant'
        },
        initialTreeDepth: 1
      }
    ]
  };
  
  chart.setOption(option);
  
  // 响应窗口大小变化
  window.addEventListener('resize', () => {
    chart.resize();
  });
};

// 渲染统计图表
const renderStatCharts = () => {
  // 课程类型分布饼图
  if (typeChartRef.value) {
    const typeChart = echarts.init(typeChartRef.value);
    typeChart.setOption({
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'horizontal',
        bottom: 0,
        data: ['必修课', '选修课', '通识课']
      },
      series: [
        {
          name: '课程类型',
          type: 'pie',
          radius: '50%',
          center: ['50%', '40%'],
          avoidLabelOverlap: false,
          label: {
            show: true,
            formatter: '{b}: {c} 门'
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          },
          data: [
            { value: 25, name: '必修课' },
            { value: 15, name: '选修课' },
            { value: 10, name: '通识课' }
          ]
        }
      ]
    });
    
    // 响应窗口大小变化
    window.addEventListener('resize', () => {
      typeChart.resize();
    });
  }
  
  // 学期课程数量柱状图
  if (termChartRef.value) {
    const termChart = echarts.init(termChartRef.value);
    termChart.setOption({
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: ['第1学期', '第2学期', '第3学期', '第4学期', '第5学期', '第6学期', '第7学期', '第8学期']
      },
      yAxis: {
        type: 'value',
        name: '课程数量'
      },
      series: [
        {
          name: '课程数量',
          type: 'bar',
          data: [6, 7, 8, 7, 6, 5, 4, 2]
        }
      ]
    });
    
    // 响应窗口大小变化
    window.addEventListener('resize', () => {
      termChart.resize();
    });
  }
  
  // 课程学分分布饼图
  if (creditChartRef.value) {
    const creditChart = echarts.init(creditChartRef.value);
    creditChart.setOption({
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'horizontal',
        bottom: 0,
        data: ['1学分', '2学分', '3学分', '4学分', '5学分及以上']
      },
      series: [
        {
          name: '学分分布',
          type: 'pie',
          radius: '50%',
          center: ['50%', '40%'],
          avoidLabelOverlap: false,
          label: {
            show: true,
            formatter: '{b}: {c} 门'
          },
          data: [
            { value: 5, name: '1学分' },
            { value: 12, name: '2学分' },
            { value: 18, name: '3学分' },
            { value: 10, name: '4学分' },
            { value: 5, name: '5学分及以上' }
          ]
        }
      ]
    });
    
    // 响应窗口大小变化
    window.addEventListener('resize', () => {
      creditChart.resize();
    });
  }
  
  // 毕业要求支撑度雷达图
  if (supportChartRef.value) {
    const supportChart = echarts.init(supportChartRef.value);
    supportChart.setOption({
      tooltip: {
        trigger: 'item'
      },
      radar: {
        indicator: [
          { name: '工程知识', max: 100 },
          { name: '问题分析', max: 100 },
          { name: '设计/开发解决方案', max: 100 },
          { name: '研究', max: 100 },
          { name: '使用现代工具', max: 100 },
          { name: '工程与社会', max: 100 },
          { name: '环境和可持续发展', max: 100 },
          { name: '职业规范', max: 100 },
          { name: '个人和团队', max: 100 },
          { name: '沟通', max: 100 },
          { name: '项目管理', max: 100 },
          { name: '终身学习', max: 100 }
        ]
      },
      series: [
        {
          type: 'radar',
          data: [
            {
              value: [85, 90, 80, 75, 85, 60, 55, 65, 70, 75, 80, 85],
              name: '支撑度'
            }
          ]
        }
      ]
    });
    
    // 响应窗口大小变化
    window.addEventListener('resize', () => {
      supportChart.resize();
    });
  }
};

onMounted(() => {
  handleSearch();
  
  // 页面加载后延迟一点时间再渲染图表，确保DOM已经准备好
  setTimeout(() => {
    renderStructureChart();
    renderStatCharts();
  }, 200);
});
</script>

<style lang="less" scoped>
.course-detail-container {
  padding: 20px;
  
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    
    h2 {
      margin: 0;
    }
  }
  
  .main-card {
    margin-bottom: 24px;
    
    .card-header {
      display: flex;
      align-items: center;
      
      .title-icon {
        margin-right: 8px;
        color: var(--td-brand-color);
      }
    }
    
    .tab-content {
      padding: 16px 0;
      
      .filter-bar {
        display: flex;
        gap: 16px;
        margin-bottom: 16px;
      }
    }
  }
  
  .structure-container {
    height: 500px;
    border: 1px solid #e7e7e7;
    border-radius: 3px;
    padding: 16px;
    
    .structure-chart {
      width: 100%;
      height: 100%;
    }
  }
  
  .statistics-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
    
    .stat-card {
      .chart-container {
        height: 300px;
      }
    }
  }
  
  .detail-section {
    margin-top: 16px;
    
    h3 {
      margin-bottom: 8px;
      font-size: 16px;
      font-weight: 500;
    }
  }
}
</style>
