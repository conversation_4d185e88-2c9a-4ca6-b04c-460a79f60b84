<template>
  <div class="pie-chart-container">
    <div ref="chartRef" class="chart" :id="chartId" style="width: 100%; height: 400px;"></div>
  </div>
</template>
<script setup lang="ts">
import { ref, watch, onMounted, onBeforeUnmount } from 'vue';
import * as echarts from 'echarts';
interface PieChartData {
  name: string;
  value: number;
}
const props = defineProps<{ chartId?: string; title?: string; data: PieChartData[] }>();
const chartRef = ref<HTMLDivElement | null>(null);
let chartInstance: echarts.ECharts | null = null;
const renderChart = () => {
  if (!chartRef.value || !props.data) return;
  if (!chartInstance) chartInstance = echarts.init(chartRef.value);
  const option = {
    title: { text: props.title || '饼图', left: 'center' },
    tooltip: { trigger: 'item' },
    legend: { orient: 'vertical', left: 'left' },
    series: [{ type: 'pie', radius: '60%', data: props.data, emphasis: { itemStyle: { shadowBlur: 10, shadowOffsetX: 0, shadowColor: 'rgba(0, 0, 0, 0.5)' } } }],
    grid: { left: '10%', right: '10%', top: '20%', bottom: '15%' }
  };
  chartInstance.setOption(option);
  chartInstance.resize();
};
watch(() => props.data, renderChart, { deep: true });
onMounted(() => { renderChart(); window.addEventListener('resize', handleResize); });
onBeforeUnmount(() => { chartInstance?.dispose(); window.removeEventListener('resize', handleResize); });
function handleResize() { chartInstance?.resize(); }
</script>
<style scoped>
.pie-chart-container { width: 100%; }
.chart { width: 100%; height: 400px; }
</style>
