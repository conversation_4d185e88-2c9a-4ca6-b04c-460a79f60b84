<template>
  <div class="task-list-demo">
    <div class="demo-header">
      <h1>教学任务列表组件演示</h1>
      <p>展示 TeachingTaskList 组件的各种功能和用法</p>
    </div>

    <!-- 基础用法演示 -->
    <div class="demo-section">
      <h2>基础用法</h2>
      <div class="demo-content">
        <div class="demo-controls">
          <t-space>
            <t-select
              v-model="demoConfig.courseId"
              placeholder="选择课程"
              :options="courseOptions"
              style="width: 200px"
            />
            <t-select
              v-model="demoConfig.taskYear"
              placeholder="选择学年"
              :options="yearOptions"
              style="width: 150px"
            />
            <t-select
              v-model="demoConfig.taskTerm"
              placeholder="选择学期"
              :options="termOptions"
              style="width: 120px"
            />
            <t-button theme="primary" @click="refreshDemo">
              <template #icon><t-icon name="refresh" /></template>
              刷新
            </t-button>
          </t-space>
        </div>

        <div class="demo-result">
          <TeachingTaskList
            v-if="demoConfig.courseId"
            ref="demoTaskListRef"
            :course-id="demoConfig.courseId"
            :task-year="demoConfig.taskYear"
            :task-term="demoConfig.taskTerm"
            :course-name="selectedCourseName"
            @task-selected="handleDemoTaskSelected"
            @task-deleted="handleDemoTaskDeleted"
          />
          
          <div v-else class="no-course-tip">
            <t-alert theme="info" message="请选择课程以查看教学任务列表" />
          </div>
        </div>
      </div>
    </div>

    <!-- 事件监听演示 -->
    <div class="demo-section">
      <h2>事件监听</h2>
      <div class="event-log">
        <div class="log-header">
          <span>事件日志</span>
          <t-button size="small" variant="text" @click="clearEventLog">清空</t-button>
        </div>
        <div class="log-content">
          <div v-if="eventLog.length === 0" class="no-events">
            暂无事件记录
          </div>
          <div
            v-for="(event, index) in eventLog"
            :key="index"
            class="event-item"
          >
            <span class="event-time">{{ event.time }}</span>
            <span class="event-type" :class="event.type">{{ event.type }}</span>
            <span class="event-message">{{ event.message }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- API 方法演示 -->
    <div class="demo-section">
      <h2>API 方法</h2>
      <div class="api-demo">
        <t-space>
          <t-button @click="callRefresh">
            调用 refresh()
          </t-button>
          <t-button @click="callClearSelection">
            调用 clearSelection()
          </t-button>
          <t-button @click="callGetSelectedTasks">
            调用 getSelectedTasks()
          </t-button>
        </t-space>
      </div>
    </div>

    <!-- 状态信息 -->
    <div class="demo-section">
      <h2>当前状态</h2>
      <div class="status-info">
        <t-descriptions :data="statusData" layout="vertical" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive } from 'vue'
import { MessagePlugin } from 'tdesign-vue-next'
import TeachingTaskList from '../AssessmentContent/components/TeachingTaskList.vue'

// 响应式数据
const demoTaskListRef = ref()
const selectedTaskIds = ref<number[]>([])
const eventLog = ref<Array<{
  time: string
  type: string
  message: string
}>>([])

// 演示配置
const demoConfig = reactive({
  courseId: 1001,
  taskYear: 2024,
  taskTerm: 1
})

// 选项数据
const courseOptions = [
  { label: '高等数学A', value: 1001 },
  { label: '线性代数', value: 1002 },
  { label: '概率论与数理统计', value: 1003 },
  { label: '数据结构与算法', value: 1004 },
  { label: '计算机网络', value: 1005 }
]

const yearOptions = [
  { label: '2022-2023学年', value: 2022 },
  { label: '2023-2024学年', value: 2023 },
  { label: '2024-2025学年', value: 2024 },
  { label: '2025-2026学年', value: 2025 }
]

const termOptions = [
  { label: '春季学期', value: 1 },
  { label: '秋季学期', value: 2 }
]

// 计算属性
const selectedCourseName = computed(() => {
  const course = courseOptions.find(c => c.value === demoConfig.courseId)
  return course?.label || '未知课程'
})

const statusData = computed(() => [
  { label: '选中课程', value: selectedCourseName.value },
  { label: '学年学期', value: `${demoConfig.taskYear}-${demoConfig.taskYear + 1}学年 ${demoConfig.taskTerm === 1 ? '春季' : '秋季'}学期` },
  { label: '选中任务数', value: selectedTaskIds.value.length },
  { label: '选中任务ID', value: selectedTaskIds.value.join(', ') || '无' }
])

// 方法定义
const addEventLog = (type: string, message: string) => {
  const now = new Date()
  const time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`
  
  eventLog.value.unshift({
    time,
    type,
    message
  })
  
  // 保持最多50条记录
  if (eventLog.value.length > 50) {
    eventLog.value = eventLog.value.slice(0, 50)
  }
}

const handleDemoTaskSelected = (taskIds: number[]) => {
  selectedTaskIds.value = taskIds
  addEventLog('task-selected', `选中了 ${taskIds.length} 个任务: [${taskIds.join(', ')}]`)
}

const handleDemoTaskDeleted = (deletedIds: number[]) => {
  addEventLog('task-deleted', `删除了 ${deletedIds.length} 个任务: [${deletedIds.join(', ')}]`)
}

const refreshDemo = () => {
  selectedTaskIds.value = []
  demoTaskListRef.value?.refresh()
  addEventLog('refresh', '手动刷新了任务列表')
}

const clearEventLog = () => {
  eventLog.value = []
}

const callRefresh = () => {
  demoTaskListRef.value?.refresh()
  addEventLog('api-call', '调用了 refresh() 方法')
  MessagePlugin.success('已调用 refresh() 方法')
}

const callClearSelection = () => {
  demoTaskListRef.value?.clearSelection()
  addEventLog('api-call', '调用了 clearSelection() 方法')
  MessagePlugin.success('已调用 clearSelection() 方法')
}

const callGetSelectedTasks = () => {
  const selected = demoTaskListRef.value?.getSelectedTasks() || []
  addEventLog('api-call', `调用了 getSelectedTasks() 方法，返回: [${selected.join(', ')}]`)
  MessagePlugin.success(`getSelectedTasks() 返回: [${selected.join(', ')}]`)
}
</script>

<style lang="less" scoped>
.task-list-demo {
  padding: var(--td-comp-paddingTB-xl) var(--td-comp-paddingLR-xl);
  background: var(--td-bg-color-page);
  min-height: 100vh;
}

.demo-header {
  text-align: center;
  margin-bottom: var(--td-comp-margin-xxl);
  
  h1 {
    margin: 0 0 var(--td-comp-margin-s) 0;
    font-size: var(--td-font-size-title-large);
    font-weight: var(--td-font-weight-bold);
    color: var(--td-text-color-primary);
  }
  
  p {
    margin: 0;
    font-size: var(--td-font-size-body-medium);
    color: var(--td-text-color-secondary);
  }
}

.demo-section {
  margin-bottom: var(--td-comp-margin-xxl);
  
  h2 {
    margin: 0 0 var(--td-comp-margin-l) 0;
    font-size: var(--td-font-size-title-medium);
    font-weight: var(--td-font-weight-semi-bold);
    color: var(--td-text-color-primary);
    border-bottom: 2px solid var(--td-brand-color);
    padding-bottom: var(--td-comp-paddingTB-s);
  }
}

.demo-content {
  .demo-controls {
    margin-bottom: var(--td-comp-margin-l);
    padding: var(--td-comp-paddingTB-l) var(--td-comp-paddingLR-l);
    background: var(--td-bg-color-container);
    border-radius: var(--td-radius-medium);
    border: 1px solid var(--td-border-level-1-color);
  }
  
  .no-course-tip {
    padding: var(--td-comp-paddingTB-l);
  }
}

.event-log {
  background: var(--td-bg-color-container);
  border-radius: var(--td-radius-medium);
  border: 1px solid var(--td-border-level-1-color);
  
  .log-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--td-comp-paddingTB-m) var(--td-comp-paddingLR-l);
    border-bottom: 1px solid var(--td-border-level-1-color);
    font-weight: var(--td-font-weight-medium);
    color: var(--td-text-color-primary);
  }
  
  .log-content {
    max-height: 300px;
    overflow-y: auto;
    
    .no-events {
      padding: var(--td-comp-paddingTB-l);
      text-align: center;
      color: var(--td-text-color-placeholder);
    }
    
    .event-item {
      display: flex;
      align-items: center;
      gap: var(--td-comp-margin-m);
      padding: var(--td-comp-paddingTB-s) var(--td-comp-paddingLR-l);
      border-bottom: 1px solid var(--td-border-level-1-color);
      
      &:last-child {
        border-bottom: none;
      }
      
      .event-time {
        font-family: monospace;
        font-size: var(--td-font-size-body-small);
        color: var(--td-text-color-placeholder);
        min-width: 60px;
      }
      
      .event-type {
        padding: 2px 8px;
        border-radius: var(--td-radius-small);
        font-size: var(--td-font-size-body-small);
        font-weight: var(--td-font-weight-medium);
        min-width: 100px;
        text-align: center;
        
        &.task-selected {
          background: var(--td-success-color-1);
          color: var(--td-success-color);
        }
        
        &.task-deleted {
          background: var(--td-error-color-1);
          color: var(--td-error-color);
        }
        
        &.refresh {
          background: var(--td-brand-color-1);
          color: var(--td-brand-color);
        }
        
        &.api-call {
          background: var(--td-warning-color-1);
          color: var(--td-warning-color);
        }
      }
      
      .event-message {
        flex: 1;
        font-size: var(--td-font-size-body-small);
        color: var(--td-text-color-secondary);
      }
    }
  }
}

.api-demo {
  padding: var(--td-comp-paddingTB-l) var(--td-comp-paddingLR-l);
  background: var(--td-bg-color-container);
  border-radius: var(--td-radius-medium);
  border: 1px solid var(--td-border-level-1-color);
}

.status-info {
  padding: var(--td-comp-paddingTB-l) var(--td-comp-paddingLR-l);
  background: var(--td-bg-color-container);
  border-radius: var(--td-radius-medium);
  border: 1px solid var(--td-border-level-1-color);
}
</style>
