// 学校管理员系统模拟数据

// 学院数据
export const collegeMockData = [
  {
    id: 'SCH_202401_0001',
    name: '计算机科学与技术学院',
    dean: '张教授',
    creator: 'admin',
    createTime: '2024-01-01 10:00:00',
    status: '启用',
    updater: 'admin',
    updateTime: '2024-01-01 10:00:00',
    majors: 5,
    classes: 20,
  },
  {
    id: 'SCH_202401_0002',
    name: '软件工程学院',
    dean: '李教授',
    creator: 'admin',
    createTime: '2024-01-02 10:00:00',
    status: '启用',
    updater: 'admin',
    updateTime: '2024-01-02 10:00:00',
    majors: 4,
    classes: 16,
  },
  {
    id: 'SCH_202401_0003',
    name: '人工智能学院',
    dean: '王教授',
    creator: 'admin',
    createTime: '2024-01-03 10:00:00',
    status: '停用',
    updater: 'admin',
    updateTime: '2024-01-03 10:00:00',
    majors: 3,
    classes: 12,
  },
  {
    id: 'SCH_202401_0004',
    name: '电子信息学院',
    dean: '刘教授',
    creator: 'admin',
    createTime: '2024-01-04 10:00:00',
    status: '启用',
    updater: 'admin',
    updateTime: '2024-01-04 10:00:00',
    majors: 6,
    classes: 24,
  },
  {
    id: 'SCH_202401_0005',
    name: '数学与统计学院',
    dean: '陈教授',
    creator: 'admin',
    createTime: '2024-01-05 10:00:00',
    status: '启用',
    updater: 'admin',
    updateTime: '2024-01-05 10:00:00',
    majors: 4,
    classes: 15,
  },
  {
    id: 'SCH_202401_0006',
    name: '物理学院',
    dean: '赵教授',
    creator: 'admin',
    createTime: '2024-01-06 10:00:00',
    status: '启用',
    updater: 'admin',
    updateTime: '2024-01-06 10:00:00',
    majors: 3,
    classes: 10,
  },
  {
    id: 'SCH_202401_0007',
    name: '化学学院',
    dean: '钱教授',
    creator: 'admin',
    createTime: '2024-01-07 10:00:00',
    status: '停用',
    updater: 'admin',
    updateTime: '2024-01-07 10:00:00',
    majors: 2,
    classes: 8,
  },
  {
    id: 'SCH_202401_0008',
    name: '生物学院',
    dean: '孙教授',
    creator: 'admin',
    createTime: '2024-01-08 10:00:00',
    status: '启用',
    updater: 'admin',
    updateTime: '2024-01-08 10:00:00',
    majors: 4,
    classes: 16,
  },
  {
    id: 'SCH_202401_0009',
    name: '外国语学院',
    dean: '周教授',
    creator: 'admin',
    createTime: '2024-01-09 10:00:00',
    status: '启用',
    updater: 'admin',
    updateTime: '2024-01-09 10:00:00',
    majors: 5,
    classes: 20,
  },
  {
    id: 'SCH_202401_0010',
    name: '经济管理学院',
    dean: '吴教授',
    creator: 'admin',
    createTime: '2024-01-10 10:00:00',
    status: '启用',
    updater: 'admin',
    updateTime: '2024-01-10 10:00:00',
    majors: 6,
    classes: 25,
  },
  {
    id: 'SCH_202401_0011',
    name: '医学院',
    dean: '郑教授',
    creator: 'admin',
    createTime: '2024-01-11 10:00:00',
    status: '启用',
    updater: 'admin',
    updateTime: '2024-01-11 10:00:00',
    majors: 7,
    classes: 30,
  },
  {
    id: 'SCH_202401_0012',
    name: '土木工程学院',
    dean: '王副教授',
    creator: 'admin',
    createTime: '2024-01-12 10:00:00',
    status: '停用',
    updater: 'admin',
    updateTime: '2024-01-12 10:00:00',
    majors: 4,
    classes: 16,
  },
];

// 专业数据
export const majorMockData = [
  { id: 'major_001', collegeId: 'SCH_202401_0001', name: '计算机科学与技术', director: '张主任', courses: 25, students: 120, creator: 'admin', createTime: '2024-01-01 10:00:00', status: '启用', updater: 'admin', updateTime: '2024-01-01 10:00:00' },
  { id: 'major_002', collegeId: 'SCH_202401_0001', name: '网络工程', director: '李主任', courses: 22, students: 110, creator: 'admin', createTime: '2024-01-01 10:30:00', status: '启用', updater: 'admin', updateTime: '2024-01-01 10:30:00' },
  { id: 'major_003', collegeId: 'SCH_202401_0001', name: '信息安全', director: '王主任', courses: 20, students: 90, creator: 'admin', createTime: '2024-01-01 11:00:00', status: '启用', updater: 'admin', updateTime: '2024-01-01 11:00:00' },
  { id: 'major_004', collegeId: 'SCH_202401_0001', name: '数据科学与大数据技术', director: '刘主任', courses: 23, students: 100, creator: 'admin', createTime: '2024-01-01 11:30:00', status: '启用', updater: 'admin', updateTime: '2024-01-01 11:30:00' },
  { id: 'major_005', collegeId: 'SCH_202401_0001', name: '物联网工程', director: '陈主任', courses: 21, students: 95, creator: 'admin', createTime: '2024-01-01 12:00:00', status: '启用', updater: 'admin', updateTime: '2024-01-01 12:00:00' },
  
  { id: 'major_006', collegeId: 'SCH_202401_0002', name: '软件工程', director: '赵主任', courses: 24, students: 130, creator: 'admin', createTime: '2024-01-02 10:00:00', status: '启用', updater: 'admin', updateTime: '2024-01-02 10:00:00' },
  { id: 'major_007', collegeId: 'SCH_202401_0002', name: '数字媒体技术', director: '钱主任', courses: 23, students: 115, creator: 'admin', createTime: '2024-01-02 10:30:00', status: '启用', updater: 'admin', updateTime: '2024-01-02 10:30:00' },
  { id: 'major_008', collegeId: 'SCH_202401_0002', name: '智能科学与技术', director: '孙主任', courses: 22, students: 105, creator: 'admin', createTime: '2024-01-02 11:00:00', status: '停用', updater: 'admin', updateTime: '2024-01-02 11:00:00' },
  { id: 'major_009', collegeId: 'SCH_202401_0002', name: '网络空间安全', director: '周主任', courses: 25, students: 120, creator: 'admin', createTime: '2024-01-02 11:30:00', status: '启用', updater: 'admin', updateTime: '2024-01-02 11:30:00' },
  
  { id: 'major_010', collegeId: 'SCH_202401_0003', name: '人工智能', director: '吴主任', courses: 26, students: 140, creator: 'admin', createTime: '2024-01-03 10:00:00', status: '启用', updater: 'admin', updateTime: '2024-01-03 10:00:00' },
  { id: 'major_011', collegeId: 'SCH_202401_0003', name: '机器学习', director: '郑主任', courses: 24, students: 130, creator: 'admin', createTime: '2024-01-03 10:30:00', status: '启用', updater: 'admin', updateTime: '2024-01-03 10:30:00' },
  { id: 'major_012', collegeId: 'SCH_202401_0003', name: '深度学习', director: '冯主任', courses: 22, students: 110, creator: 'admin', createTime: '2024-01-03 11:00:00', status: '启用', updater: 'admin', updateTime: '2024-01-03 11:00:00' },
];

// 学生数据生成函数
export function generateStudentMockData(count = 200) {
  const students = [];
  const status = ['在读', '休学', '毕业', '退学'];
  const genders = ['男', '女'];
  
  for (let i = 0; i < count; i++) {
    const collegeIndex = Math.floor(Math.random() * collegeMockData.length);
    const college = collegeMockData[collegeIndex];
    
    // 获取该学院的专业
    const filteredMajors = majorMockData.filter(m => m.collegeId === college.id);
    if (filteredMajors.length === 0) continue;
    
    const major = filteredMajors[Math.floor(Math.random() * filteredMajors.length)];
    const enrollmentYear = 2020 + Math.floor(Math.random() * 5);
    const classNum = Math.floor(Math.random() * 5) + 1;
    
    students.push({
      id: `student_${i.toString().padStart(5, '0')}`,
      name: `学生${i + 1}`,
      gender: genders[Math.floor(Math.random() * genders.length)],
      studentId: `${enrollmentYear}${Math.floor(Math.random() * 10000).toString().padStart(4, '0')}`,
      college: college.name,
      collegeId: college.id,
      major: major.name,
      majorId: major.id,
      className: `${enrollmentYear}级${major.name}${classNum}班`,
      enrollmentYear: enrollmentYear.toString(),
      status: status[Math.floor(Math.random() * status.length)],
      creator: 'admin',
      createTime: new Date(Date.now() - Math.random() * 31536000000).toLocaleString(),
      updater: 'admin',
      updateTime: new Date(Date.now() - Math.random() * 15768000000).toLocaleString(),
    });
  }
  
  return students;
}

// 教师数据生成函数
export function generateTeacherMockData(count = 100) {
  const teachers = [];
  const titles = ['教授', '副教授', '讲师', '助教'];
  
  for (let i = 0; i < count; i++) {
    const collegeIndex = Math.floor(Math.random() * collegeMockData.length);
    const college = collegeMockData[collegeIndex];
    const title = titles[Math.floor(Math.random() * titles.length)];
    const gender = Math.floor(Math.random() * 2); // 0或1
    
    teachers.push({
      teacher_id: `teacher_${i.toString().padStart(5, '0')}`,
      teacher_name: `教师${i + 1}`,
      teacher_number: `T${(2000 + Math.floor(Math.random() * 100)).toString().padStart(4, '0')}`,
      gender: gender,
      teacher_title: title,
      academy_id: college.id,
      academy_name: college.name,
      phone: `1${Math.floor(Math.random() * 9 + 1)}${Math.floor(Math.random() * 10000000000).toString().padStart(10, '0')}`.substring(0, 11),
      creator: 'admin',
      createTime: new Date(Date.now() - Math.random() * 31536000000).toLocaleString(),
      updater: 'admin',
      updateTime: new Date(Date.now() - Math.random() * 15768000000).toLocaleString(),
    });
  }
  
  return teachers;
}

// 操作日志数据
export const logMockData = [
  { id: 'log_00001', operator: 'admin', operateType: '登录', operateTime: '2024-03-01 08:00:00', operateContent: '用户登录系统', operateResult: '成功', ip: '***********', location: '北京' },
  { id: 'log_00002', operator: 'admin', operateType: '查询', operateTime: '2024-03-01 08:05:10', operateContent: '查询学院列表', operateResult: '成功', ip: '***********', location: '北京' },
  { id: 'log_00003', operator: 'admin', operateType: '添加', operateTime: '2024-03-01 09:15:22', operateContent: '添加学院信息', operateResult: '成功', ip: '***********', location: '北京' },
  { id: 'log_00004', operator: 'admin', operateType: '修改', operateTime: '2024-03-01 10:30:15', operateContent: '修改学院信息', operateResult: '成功', ip: '***********', location: '北京' },
  { id: 'log_00005', operator: 'admin', operateType: '删除', operateTime: '2024-03-01 11:42:33', operateContent: '删除学院信息', operateResult: '成功', ip: '***********', location: '北京' },
  { id: 'log_00006', operator: 'teacher1', operateType: '登录', operateTime: '2024-03-01 13:10:45', operateContent: '用户登录系统', operateResult: '成功', ip: '***********', location: '上海' },
  { id: 'log_00007', operator: 'teacher1', operateType: '查询', operateTime: '2024-03-01 13:15:20', operateContent: '查询学生信息', operateResult: '成功', ip: '***********', location: '上海' },
  { id: 'log_00008', operator: 'teacher1', operateType: '添加', operateTime: '2024-03-01 14:20:11', operateContent: '添加学生成绩', operateResult: '失败', ip: '***********', location: '上海' },
  { id: 'log_00009', operator: 'teacher1', operateType: '添加', operateTime: '2024-03-01 14:22:05', operateContent: '添加学生成绩', operateResult: '成功', ip: '***********', location: '上海' },
  { id: 'log_00010', operator: 'teacher1', operateType: '导出', operateTime: '2024-03-01 15:30:45', operateContent: '导出学生成绩', operateResult: '成功', ip: '***********', location: '上海' },
  { id: 'log_00011', operator: 'student1', operateType: '登录', operateTime: '2024-03-01 16:00:20', operateContent: '用户登录系统', operateResult: '成功', ip: '***********', location: '广州' },
  { id: 'log_00012', operator: 'student1', operateType: '查询', operateTime: '2024-03-01 16:05:11', operateContent: '查询个人成绩', operateResult: '成功', ip: '***********', location: '广州' },
  { id: 'log_00013', operator: 'student1', operateType: '修改', operateTime: '2024-03-01 16:10:35', operateContent: '修改个人信息', operateResult: '成功', ip: '***********', location: '广州' },
  { id: 'log_00014', operator: 'admin', operateType: '登录', operateTime: '2024-03-02 08:15:00', operateContent: '用户登录系统', operateResult: '成功', ip: '***********', location: '北京' },
  { id: 'log_00015', operator: 'admin', operateType: '查询', operateTime: '2024-03-02 08:20:10', operateContent: '查询系统日志', operateResult: '成功', ip: '***********', location: '北京' },
  { id: 'log_00016', operator: 'admin', operateType: '备份', operateTime: '2024-03-02 09:00:00', operateContent: '系统数据备份', operateResult: '成功', ip: '***********', location: '北京' },
  { id: 'log_00017', operator: 'admin', operateType: '登出', operateTime: '2024-03-02 12:00:30', operateContent: '用户登出系统', operateResult: '成功', ip: '***********', location: '北京' },
];

// 系统监控数据
export const systemMonitorMockData = {
  // CPU使用率时间序列数据(过去24小时，每小时一个点)
  cpuUsageSeries: Array.from({ length: 24 }, (_, i) => {
    const baseValue = 40;
    const hourOfDay = new Date().getHours() - (23 - i);
    const hourAdjusted = hourOfDay < 0 ? hourOfDay + 24 : hourOfDay;
    
    // 模拟工作时间CPU使用率更高
    let value = baseValue;
    if (hourAdjusted >= 9 && hourAdjusted <= 18) {
      value += 20 + Math.floor(Math.random() * 15);
    } else {
      value += Math.floor(Math.random() * 15);
    }
    
    return {
      time: `${hourAdjusted}:00`,
      value: value
    };
  }),
  
  // 内存使用率时间序列数据
  memoryUsageSeries: Array.from({ length: 24 }, (_, i) => {
    const baseValue = 50;
    const hourOfDay = new Date().getHours() - (23 - i);
    const hourAdjusted = hourOfDay < 0 ? hourOfDay + 24 : hourOfDay;
    
    // 模拟工作时间内存使用率更高
    let value = baseValue;
    if (hourAdjusted >= 9 && hourAdjusted <= 18) {
      value += 15 + Math.floor(Math.random() * 15);
    } else {
      value += Math.floor(Math.random() * 15);
    }
    
    return {
      time: `${hourAdjusted}:00`,
      value: value
    };
  }),
  
  // 当前服务器状态
  serverStatus: {
    cpu: {
      usage: 58,
      cores: 8,
      speed: '3.4 GHz',
      model: 'Intel Xeon E5-2670',
      loadAverage: [2.51, 2.24, 2.10]
    },
    memory: {
      total: 16384,
      used: 9728,
      free: 6656,
      usage: 59.4,
      buffers: 2048,
      cached: 3072
    },
    disk: [
      { mount: '/', total: 512000, used: 215040, free: 296960, usage: 42.0 },
      { mount: '/data', total: 1024000, used: 358400, free: 665600, usage: 35.0 },
      { mount: '/backup', total: 2048000, used: 491520, free: 1556480, usage: 24.0 }
    ],
    network: {
      received: 1258000,
      sent: 943000,
      connections: 43,
      interfaces: [
        { name: 'eth0', ip: '***********00', mac: '00:1A:2B:3C:4D:5E', status: 'up' }
      ]
    }
  },
  
  // JVM信息
  jvmInfo: {
    version: '1.8.0_292',
    vendor: 'Oracle Corporation',
    uptime: 432000,
    memoryUsage: {
      heap: {
        max: 4096,
        committed: 3072,
        used: 2048,
        usage: 50.0
      },
      nonHeap: {
        max: 1024,
        committed: 768,
        used: 512,
        usage: 50.0
      }
    },
    threads: {
      active: 45,
      peak: 62,
      daemon: 28
    },
    gc: {
      minor: {
        count: 328,
        time: 12.5
      },
      major: {
        count: 12,
        time: 8.3
      }
    }
  },
  
  // API调用统计
  apiStats: {
    total: 1543200,
    success: 1523000,
    error: 20200,
    avgResponseTime: 235,
    maxResponseTime: 1520,
    minResponseTime: 12,
    topApis: [
      { url: '/api/student/list', count: 245000, avgResponseTime: 180 },
      { url: '/api/college/list', count: 164500, avgResponseTime: 120 },
      { url: '/api/teacher/list', count: 152300, avgResponseTime: 200 },
      { url: '/api/course/list', count: 134700, avgResponseTime: 150 },
      { url: '/api/grade/list', count: 124000, avgResponseTime: 350 }
    ]
  }
};

// 数据备份记录
export const backupMockData = [
  {
    id: 'BK_202401_0001',
    fileName: 'backup_20240101_120000.sql',
    fileSize: '256.5 MB',
    backupTime: '2024-01-01 12:00:00',
    backupUser: 'admin',
    status: 'success',
    remark: '系统自动备份',
  },
  {
    id: 'BK_202401_0002',
    fileName: 'backup_20240102_120000.sql',
    fileSize: '258.2 MB',
    backupTime: '2024-01-02 12:00:00',
    backupUser: 'admin',
    status: 'success',
    remark: '系统自动备份',
  },
  {
    id: 'BK_202401_0003',
    fileName: 'backup_20240103_120000.sql',
    fileSize: '259.8 MB',
    backupTime: '2024-01-03 12:00:00',
    backupUser: 'admin',
    status: 'success',
    remark: '系统自动备份',
  },
  {
    id: 'BK_202401_0004',
    fileName: 'backup_20240104_120000.sql',
    fileSize: '261.3 MB',
    backupTime: '2024-01-04 12:00:00',
    backupUser: 'admin',
    status: 'success',
    remark: '系统自动备份',
  },
  {
    id: 'BK_202401_0005',
    fileName: 'backup_20240105_120000.sql',
    fileSize: '262.7 MB',
    backupTime: '2024-01-05 12:00:00',
    backupUser: 'admin',
    status: 'success',
    remark: '系统自动备份',
  },
  {
    id: 'BK_202401_0006',
    fileName: 'backup_20240110_150000.sql',
    fileSize: '265.4 MB',
    backupTime: '2024-01-10 15:00:00',
    backupUser: 'admin',
    status: 'success',
    remark: '手动备份',
  },
  {
    id: 'BK_202401_0007',
    fileName: 'backup_20240115_120000.sql',
    fileSize: '268.1 MB',
    backupTime: '2024-01-15 12:00:00',
    backupUser: 'system',
    status: 'success',
    remark: '系统自动备份',
  },
  {
    id: 'BK_202401_0008',
    fileName: 'backup_20240120_120000.sql',
    fileSize: '271.3 MB',
    backupTime: '2024-01-20 12:00:00',
    backupUser: 'system',
    status: 'success',
    remark: '系统自动备份',
  },
  {
    id: 'BK_202401_0009',
    fileName: 'backup_20240125_163025.sql',
    fileSize: '272.8 MB',
    backupTime: '2024-01-25 16:30:25',
    backupUser: 'admin',
    status: 'success',
    remark: '手动备份',
  },
  {
    id: 'BK_202401_0010',
    fileName: 'backup_20240128_120000.sql',
    fileSize: '274.5 MB',
    backupTime: '2024-01-28 12:00:00',
    backupUser: 'system',
    status: 'failed',
    remark: '备份过程中断',
  },
  {
    id: 'BK_202402_0001',
    fileName: 'backup_20240201_120000.sql',
    fileSize: '275.2 MB',
    backupTime: '2024-02-01 12:00:00',
    backupUser: 'system',
    status: 'success',
    remark: '系统自动备份',
  },
  {
    id: 'BK_202402_0002',
    fileName: 'backup_20240205_120000.sql',
    fileSize: '278.7 MB',
    backupTime: '2024-02-05 12:00:00',
    backupUser: 'system',
    status: 'success',
    remark: '系统自动备份',
  },
  {
    id: 'BK_202402_0003',
    fileName: 'backup_20240210_120000.sql',
    fileSize: '280.1 MB',
    backupTime: '2024-02-10 12:00:00',
    backupUser: 'system',
    status: 'success',
    remark: '系统自动备份',
  }
];

// 通知数据
export const noticeMockData = [
  {
    id: 'notice_001',
    title: '系统更新通知',
    content: '系统将于今晚22:00-23:00进行例行维护，请各位用户提前做好准备。',
    type: '系统通知',
    status: '已发布',
    createTime: '2024-03-25 10:00:00',
    isRead: false,
  },
  {
    id: 'notice_002',
    title: '教师绩效考核开始',
    content: '2024年教师绩效考核即将开始，请各位教师在4月30日前完成相关资料提交。',
    type: '重要通知',
    status: '已发布',
    createTime: '2024-03-24 14:30:00',
    isRead: true,
  },
  {
    id: 'notice_003',
    title: '学生信息核对',
    content: '请各位辅导员组织学生在本周内完成学生信息核对工作，确保学生信息准确无误。',
    type: '工作通知',
    status: '已发布',
    createTime: '2024-03-23 09:15:00',
    isRead: false,
  },
  {
    id: 'notice_004',
    title: '教务系统功能更新',
    content: '教务系统新增成绩分析功能，可按院系、专业、课程等多维度进行成绩统计分析。',
    type: '系统通知',
    status: '已发布',
    createTime: '2024-03-22 16:45:00',
    isRead: false,
  },
  {
    id: 'notice_005',
    title: '教师资格认证',
    content: '2024年上半年教师资格认证工作即将开始，请有需要的教师关注相关通知。',
    type: '工作通知',
    status: '已发布',
    createTime: '2024-03-21 11:20:00',
    isRead: true,
  },
]; 