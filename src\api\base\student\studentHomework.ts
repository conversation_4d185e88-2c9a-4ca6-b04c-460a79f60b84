import {StudentInformationListResult, StudentPaperListResult} from "@/api/model/student/studentPaperModel";
import {
  StudentQuestionAnswerResult,
  StudentQuestionListResult,
  StudentQuestionModifyResult,
  StudentRepliedListResult
} from "@/api/model/student/studentQuestionl";
import request from "@/utils/request";

//获取作业数据
export async function getList(cid:string): Promise<StudentPaperListResult> {
  try {
    const res = await request({
      url: 'api/student/homework/list',
      method: 'get',
      params: { cid }
    });
    return res;
  } catch (error) {
    console.error('获取作业数据失败:', error);
    throw error;
  }
}

//获取作业未开始数据
export async function getNoStartList(pid:string): Promise<StudentInformationListResult> {
  try {
    const res = await request({
      url: 'api/student/homework/noStart/list',
      method: 'get',
      params: { pid }
    });
    return res;
  } catch (error) {
    console.error('获取作业未开始数据失败:', error);
    throw error;
  }
}

//获取作业题目数据
export async function getWriteList(pid:string): Promise<StudentQuestionListResult> {
  try {
    const res = await request({
      url: 'api/student/homework/write/list',
      method: 'get',
      params: { pid }
    });
    return res;
  } catch (error) {
    console.error('获取作业题目数据失败:', error);
    throw error;
  }
}



//获取作业未批阅数据
export async function getCorrectList(pid:string): Promise<StudentInformationListResult> {
  try {
    const res = await request({
      url: 'api/student/homework/correct/list',
      method: 'get',
      params: { pid }
    });
    return res;
  } catch (error) {
    console.error('获取作业未批阅数据失败:', error);
    throw error;
  }
}


//获取作业结束后数据
export async function getEndList(pid:string): Promise<StudentInformationListResult> {
  try {
    const res = await request({
      url: 'api/student/homework/end/list',
      method: 'get',
      params: { pid }
    });
    return res;
  } catch (error) {
    console.error('获取作业结束后数据失败:', error);
    throw error;
  }
}


//获取作业修改题目和回答数据
export async function getModifyList(pid:string): Promise<StudentQuestionModifyResult> {
  try {
    const res = await request({
      url: 'api/student/homework/modify/list',
      method: 'get',
      params: { pid }
    });
    console.log(res)
    return res;
  } catch (error) {
    console.error('获取作业修改题目和回答失败:', error);
    throw error;
  }
}

//提交
export async function putHomeWorkList(data:StudentRepliedListResult): Promise<string> {
  try {
    const res = await request({
      url: 'api/student/homeWorkReplied/list',
      method: 'post',
      data
    });
    return res;
  } catch (error) {
    console.error('提交问卷数据失败:', error);
    throw error;
  }
}

//答案页面数据
export async function getAnswerList(pid:string): Promise<StudentQuestionAnswerResult> {
  try {
    const res = await request({
      url: 'api/student/homeWorkAnswer/list',
      method: 'get',
      params: { pid }
    });
    return res;
  } catch (error) {
    console.error('提交问卷数据失败:', error);
    throw error;
  }
}
