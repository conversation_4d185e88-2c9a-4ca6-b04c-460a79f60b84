/* eslint-disable simple-import-sort/imports */
import { createApp } from 'vue';
import TDesign from 'tdesign-vue-next';

import App from './App.vue';
import {setupRouter} from './router';
import { store } from './store';
import i18n from './locales';
import directive from '@/utils/directive';
import gp from "@/utils/gp";
import dictPlugin from '@/plugins/dictPlugin';

import 'tdesign-vue-next/es/style/index.css';
import '@/style/index.less';
import './main.css'

const app = createApp(App);

// 注册TDesign组件
app.use(TDesign);
app.use(store);
app.use(i18n);
app.use(directive);
app.use(gp);
app.use(dictPlugin, {
  autoInit: true,
  initTimeout: 30000,
  retryCount: 3,
  retryInterval: 2000
});
setupRouter(app)

app.mount('#app');
