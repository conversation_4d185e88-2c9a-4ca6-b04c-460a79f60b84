import { MockMethod } from 'vite-plugin-mock';
import { ApiResponse } from '@/utils/requests/system';

// Mock数据
const knowledgeGraphData = {
    nodes: [
      {
        id: 'center',
        name: '学风知识图谱',
        nodeType: 'center',
        createTime: '2024-03-20 10:00:00',
        updateTime: '2024-03-20 10:00:00',
        creator: 'admin',
        updater: 'admin'
      },
      {
        id: '1',
        name: '办学方向与本科地位',
        description: '学校办学方向和本科教育地位的评估指标',
        nodeType: 'indicator',
        parentId: 'center',
        createTime: '2024-03-20 10:00:00',
        updateTime: '2024-03-20 10:00:00',
        creator: 'admin',
        updater: 'admin'
      },
      {
        id: '1.1',
        name: '党的领导',
        description: '党的全面领导、社会主义办学方向、立德树人根本任务落实情况',
        nodeType: 'secondaryIndicator',
        parentId: '1',
        createTime: '2024-03-20 10:00:00',
        updateTime: '2024-03-20 10:00:00',
        creator: 'admin',
        updater: 'admin'
      }
    ],
    links: [
      {
        id: 'link1',
        source: 'center',
        target: '1',
        description: '一级指标连接'
      },
      {
        id: 'link2',
        source: '1',
        target: '1.1',
        description: '二级指标连接'
      }
    ]
  };

// Mock接口
export default [
  // 获取知识图谱数据
  {
    url: '/api/system/knowledge-graph/data',
    method: 'get',
    response: (): ApiResponse<typeof knowledgeGraphData> => ({
      code: 200,
      message: 'ok',
      data: knowledgeGraphData
    })
  },

  // 添加节点
  {
    url: '/api/system/knowledge-graph/node',
    method: 'post',
    response: ({ body }: { body: any }): ApiResponse<any> => {
      const newNode = {
        ...body,
        id: `node_${Date.now()}`,
        createTime: new Date().toISOString(),
        updateTime: new Date().toISOString(),
        creator: 'admin',
        updater: 'admin'
      };
      knowledgeGraphData.nodes.push(newNode);
      return {
        code: 200,
        message: 'ok',
        data: newNode
      };
    }
  },

  // 更新节点
  {
    url: '/api/system/knowledge-graph/node/:id',
    method: 'put',
    response: ({ body, params }: { body: any; params: { id: string } }): ApiResponse<any> => {
      const index = knowledgeGraphData.nodes.findIndex(node => node.id === params.id);
      if (index > -1) {
        knowledgeGraphData.nodes[index] = {
          ...knowledgeGraphData.nodes[index],
          ...body,
          updateTime: new Date().toISOString(),
          updater: 'admin'
        };
        return {
          code: 200,
          message: 'ok',
          data: knowledgeGraphData.nodes[index]
        };
      }
      return {
        code: 404,
        message: '节点不存在',
        data: null
      };
    }
  },

  // 删除节点
  {
    url: '/api/system/knowledge-graph/node/:id',
    method: 'delete',
    response: ({ params }: { params: { id: string } }): ApiResponse<void> => {
      const index = knowledgeGraphData.nodes.findIndex(node => node.id === params.id);
      if (index > -1) {
        knowledgeGraphData.nodes.splice(index, 1);
        // 同时删除相关的连接线
        knowledgeGraphData.links = knowledgeGraphData.links.filter(
          link => link.source !== params.id && link.target !== params.id
        );
        return {
          code: 200,
          message: 'ok',
          data: null
        };
      }
      return {
        code: 404,
        message: '节点不存在',
        data: null
      };
    }
  },

  // 添加连接线
  {
    url: '/api/system/knowledge-graph/link',
    method: 'post',
    response: ({ body }: { body: any }): ApiResponse<any> => {
      const newLink = {
        ...body,
        id: `link_${Date.now()}`
      };
      knowledgeGraphData.links.push(newLink);
      return {
        code: 200,
        message: 'ok',
        data: newLink
      };
    }
  },

  // 更新连接线
  {
    url: '/api/system/knowledge-graph/link/:id',
    method: 'put',
    response: ({ body, params }: { body: any; params: { id: string } }): ApiResponse<any> => {
      const index = knowledgeGraphData.links.findIndex(link => link.id === params.id);
      if (index > -1) {
        knowledgeGraphData.links[index] = {
          ...knowledgeGraphData.links[index],
          ...body
        };
        return {
          code: 200,
          message: 'ok',
          data: knowledgeGraphData.links[index]
        };
      }
      return {
        code: 404,
        message: '连接线不存在',
        data: null
      };
    }
  },

  // 删除连接线
  {
    url: '/api/system/knowledge-graph/link/:id',
    method: 'delete',
    response: ({ params }: { params: { id: string } }): ApiResponse<void> => {
      const index = knowledgeGraphData.links.findIndex(link => link.id === params.id);
      if (index > -1) {
        knowledgeGraphData.links.splice(index, 1);
        return {
          code: 200,
          message: 'ok',
          data: null
        };
      }
      return {
        code: 404,
        message: '连接线不存在',
        data: null
      };
    }
  }
] as MockMethod[]; 