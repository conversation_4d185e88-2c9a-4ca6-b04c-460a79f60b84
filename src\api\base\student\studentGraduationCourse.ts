import request from '@/utils/request';
import {StudentGraduationCourseListResult} from "@/api/model/student/studentGraduationCourseModel";


//获取课程类型数据
export async function getCourseList(gid:string): Promise<StudentGraduationCourseListResult> {
    try {
        const res = await request({
            url: 'api/student/GraduationCourse/list',
            method: 'get',
            params: { gid }
        });
        return res;
    } catch (error) {
        console.error('获取课程类型数据失败:', error);
        throw error;
    }
}
