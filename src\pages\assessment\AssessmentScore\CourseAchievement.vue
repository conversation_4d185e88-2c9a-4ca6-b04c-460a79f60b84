<template>
  <div class="course-achievement">
    <!-- 第一行：课程基本信息 -->
    <t-card class="mb-4">
      <div class="flex items-center mb-2">
        <div class="course-info">
          <span class="info-item"><strong>课程名称：</strong>{{ courseBase.name }}</span>
          <span class="info-item"><strong>课程编号：</strong>{{ courseBase.code }}</span>
          <span class="info-item"><strong>学分：</strong>{{ courseBase.credit }}</span>
          <span class="info-item"><strong>负责人：</strong>{{ courseBase.leader }}</span>
        </div>
      </div>
    </t-card>

    <!-- 第二行：课程目标-毕业要求卡片列表 -->
    <t-row :gutter="[16, 16]" class="mb-4">
      <t-col v-for="item in objectivePoList" :key="item.id" :span="6">
        <t-card>
          <div><strong>课程目标：</strong>{{ item.objectiveName }}</div>
          <div><strong>毕业要求：</strong>{{ item.poTitle }}</div>
        </t-card>
      </t-col>
    </t-row>

    <!-- 第三行：课程历史版本时间轴 -->
    <t-card class="mb-4">
      <CourseTimeline
        :courses="courseHistoryList"
        @open-achievement="handleOpenAchievement"
      />
    </t-card>

    <!-- 对话框：课程目标达成度详情 -->
    <t-dialog v-model:visible="achievementDialogVisible" width="900px" :footer="false">
      <ObjectiveAchivemen :course-id="dialogCourseId" />
    </t-dialog>

    <!-- 第四行：图表区 -->
    <t-row :gutter="[16, 16]" class="mb-4">
      <t-col :span="6">
        <RadarChart
          :title="'所有课程目标达成情况'"
          :data="radarObjectiveData"
        />
      </t-col>
      <t-col :span="6">
        <RadarChart
          :title="'所有毕业要求达成情况'"
          :data="radarPoData"
        />
      </t-col>
      <t-col :span="6">
        <BarChart
          :title="'课程目标达成结果'"
          :data="barObjectiveData"
        />
      </t-col>
      <t-col :span="6">
        <BarChart
          :title="'毕业要求达成结果'"
          :data="barPoData"
        />
      </t-col>
    </t-row>

    <!-- 第五行：趋势折线图 -->
    <t-card>
      <LineChart
        :title="'课程目标达成度趋势'"
        :data="lineObjectiveTrendData"
      />
    </t-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import { useRoute } from 'vue-router';
import ObjectiveAchivemen from '@/pages/assessment/AssessmentScore/components/ObjectiveAchivemen.vue';
import CourseTimeline from '@/pages/assessment/AssessmentScore/components/CourseTimeline.vue';
import RadarChart from '@/pages/assessment/AssessmentScore/components/RadarChart.vue';
import BarChart from '@/pages/assessment/AssessmentScore/components/BarChart.vue';
import LineChart from '@/pages/assessment/AssessmentScore/components/LineChart.vue';

// 数据定义
const route = useRoute();
const courseId = ref(Number(route.params.courseId) || 0);
const loading = ref(false);

// 课程基本信息
const courseBase = ref({
  name: '示例课程',
  code: 'COURSE001',
  credit: 3,
  leader: '张老师'
});

// 课程目标-毕业要求卡片列表
const objectivePoList = ref([
  { id: 1, objectiveName: '目标1', poTitle: '毕业要求A' },
  { id: 2, objectiveName: '目标2', poTitle: '毕业要求B' },
  { id: 3, objectiveName: '目标3', poTitle: '毕业要求C' }
]);

// 课程历史版本列表（时间轴）
const courseHistoryList = ref([
  { id: 101, version: '2021春', year: 2021, semester: '春', courseId: 101 },
  { id: 102, version: '2022秋', year: 2022, semester: '秋', courseId: 102 },
  { id: 103, version: '2023春', year: 2023, semester: '春', courseId: 103 }
]);

// 对话框控制
const achievementDialogVisible = ref(false);
const dialogCourseId = ref(0);
const handleOpenAchievement = (course) => {
  dialogCourseId.value = course.courseId;
  achievementDialogVisible.value = true;
};

// 图表数据（示例结构，后续可对接接口）
const radarObjectiveData = ref({
  indicators: [
    { name: '目标1', max: 100 },
    { name: '目标2', max: 100 },
    { name: '目标3', max: 100 }
  ],
  data: [80, 90, 85]
});

const radarPoData = ref({
  indicators: [
    { name: '要求A', max: 100 },
    { name: '要求B', max: 100 },
    { name: '要求C', max: 100 }
  ],
  data: [85, 88, 92]
});

const barObjectiveData = ref([
  { label: '目标1', value: 80 },
  { label: '目标2', value: 90 },
  { label: '目标3', value: 85 }
]);

const barPoData = ref([
  { label: '要求A', value: 85 },
  { label: '要求B', value: 88 },
  { label: '要求C', value: 92 }
]);

const lineObjectiveTrendData = ref([
  { label: '2021春', value: 85 },
  { label: '2022秋', value: 88 },
  { label: '2023春', value: 92 }
]);

onMounted(() => {
  // TODO: 可在此处请求接口，填充 courseBase、objectivePoList、courseHistoryList、图表数据等
});
</script>

<style scoped>
.course-achievement {
  padding: 16px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.chart-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.chart {
  width: 100%;
  height: 500px;
  margin-bottom: 20px;
}

.chart-info {
  width: 100%;
  padding: 16px;
  background-color: #f0f0f0;
  border-radius: 4px;
}

.mb-4 {
  margin-bottom: 16px;
}

.ml-4 {
  margin-left: 16px;
}

.w-40 {
  width: 10rem;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}
</style>