<template>
  <div class="course-achievement">
    <!-- 第一行：课程基本信息 -->
    <t-card class="course-info-card mb-6" :bordered="false">
      <template #header>
        <div class="card-header">
          <div class="header-title">
            <t-icon name="book" class="header-icon" />
            课程基本信息
          </div>
        </div>
      </template>
      <div class="course-info-content">
        <t-row :gutter="[24, 16]">
          <t-col :span="6">
            <div class="info-item">
              <div class="info-label">课程名称</div>
              <div class="info-value">{{ courseBase.name }}</div>
            </div>
          </t-col>
          <t-col :span="6">
            <div class="info-item">
              <div class="info-label">课程编号</div>
              <div class="info-value">{{ courseBase.code }}</div>
            </div>
          </t-col>
          <t-col :span="6">
            <div class="info-item">
              <div class="info-label">课程学分</div>
              <div class="info-value">{{ courseBase.credit }}</div>
            </div>
          </t-col>
          <t-col :span="6">
            <div class="info-item">
              <div class="info-label">课程负责人</div>
              <div class="info-value">{{ courseBase.leader }}</div>
            </div>
          </t-col>
        </t-row>
        <t-row :gutter="[24, 16]" class="mt-4">
          <t-col :span="6">
            <div class="info-item">
              <div class="info-label">课程性质</div>
              <div class="info-value">{{ courseBase.nature || '必修' }}</div>
            </div>
          </t-col>
          <t-col :span="6">
            <div class="info-item">
              <div class="info-label">开课学期</div>
              <div class="info-value">{{ courseBase.semester || '第1学期' }}</div>
            </div>
          </t-col>
          <t-col :span="6">
            <div class="info-item">
              <div class="info-label">总学时</div>
              <div class="info-value">{{ courseBase.totalHours || '48' }}</div>
            </div>
          </t-col>
          <t-col :span="6">
            <div class="info-item">
              <div class="info-label">课程状态</div>
              <div class="info-value">
                <t-tag theme="success" variant="light">{{ courseBase.status || '正常开课' }}</t-tag>
              </div>
            </div>
          </t-col>
        </t-row>
      </div>
    </t-card>

    <!-- 第二行：课程目标-毕业要求卡片列表 -->
    <t-card class="objectives-mapping-card mb-6" :bordered="false">
      <template #header>
        <div class="card-header">
          <div class="header-title">
            <t-icon name="target" class="header-icon" />
            课程目标与毕业要求映射关系
          </div>
          <div class="header-info">
            共 {{ objectivePoList.length }} 个课程目标
          </div>
        </div>
      </template>
      <div class="objectives-grid">
        <div
          v-for="(item, index) in objectivePoList"
          :key="item.id"
          class="objective-mapping-item"
          :class="`objective-${index + 1}`"
        >
          <div class="objective-card">
            <div class="objective-header">
              <div class="objective-number">CO{{ index + 1 }}</div>
              <div class="objective-title">{{ item.objectiveName }}</div>
            </div>
            <div class="mapping-arrow">
              <t-icon name="arrow-right" />
            </div>
            <div class="po-section">
              <div class="po-label">支撑毕业要求</div>
              <div class="po-title">{{ item.poTitle }}</div>
            </div>
          </div>
        </div>
      </div>
    </t-card>

    <!-- 第三行：课程历史版本时间轴 -->
    <t-card class="timeline-section-card mb-6" :bordered="false">
      <CourseTimeline
        :courses="courseHistoryList"
        @open-achievement="handleOpenAchievement"
      />
    </t-card>

    <!-- 对话框：课程目标达成度详情 -->
    <t-dialog v-model:visible="achievementDialogVisible" width="900px" :footer="false">
      <ObjectiveAchivemen :course-id="dialogCourseId" />
    </t-dialog>

    <!-- 第四行：数据可视化分析区域 -->
    <div class="visualization-section">
      <!-- 第一组：雷达图分析 -->
      <t-card class="chart-group-card mb-6" :bordered="false">
        <template #header>
          <div class="card-header">
            <div class="header-title">
              <t-icon name="chart-radar" class="header-icon" />
              达成度雷达分析
            </div>
            <div class="header-info">多维度达成度对比分析</div>
          </div>
        </template>
        <t-row :gutter="[24, 24]">
          <t-col :span="12">
            <div class="chart-wrapper">
              <RadarChart
                :title="'课程目标达成情况'"
                :data="radarObjectiveData"
              />
            </div>
          </t-col>
          <t-col :span="12">
            <div class="chart-wrapper">
              <RadarChart
                :title="'毕业要求达成情况'"
                :data="radarPoData"
              />
            </div>
          </t-col>
        </t-row>
      </t-card>

      <!-- 第二组：柱状图分析 -->
      <t-card class="chart-group-card mb-6" :bordered="false">
        <template #header>
          <div class="card-header">
            <div class="header-title">
              <t-icon name="chart-bar" class="header-icon" />
              达成度结果统计
            </div>
            <div class="header-info">各项指标达成度数值统计</div>
          </div>
        </template>
        <t-row :gutter="[24, 24]">
          <t-col :span="12">
            <div class="chart-wrapper">
              <BarChart
                :title="'课程目标达成结果'"
                :data="barObjectiveData"
              />
            </div>
          </t-col>
          <t-col :span="12">
            <div class="chart-wrapper">
              <BarChart
                :title="'毕业要求达成结果'"
                :data="barPoData"
              />
            </div>
          </t-col>
        </t-row>
      </t-card>

      <!-- 第三组：趋势分析 -->
      <t-card class="chart-group-card" :bordered="false">
        <template #header>
          <div class="card-header">
            <div class="header-title">
              <t-icon name="chart-line" class="header-icon" />
              达成度趋势分析
            </div>
            <div class="header-info">历年课程目标达成度变化趋势</div>
          </div>
        </template>
        <div class="chart-wrapper trend-chart">
          <LineChart
            :title="'课程目标达成度趋势'"
            :data="lineObjectiveTrendData"
          />
        </div>
      </t-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import ObjectiveAchivemen from '@/pages/assessment/AssessmentScore/components/ObjectiveAchivemen.vue';
import CourseTimeline from '@/pages/assessment/AssessmentScore/components/CourseTimeline.vue';
import RadarChart from '@/pages/assessment/AssessmentScore/components/RadarChart.vue';
import BarChart from '@/pages/assessment/AssessmentScore/components/BarChart.vue';
import LineChart from '@/pages/assessment/AssessmentScore/components/LineChart.vue';

// 数据定义

// 课程基本信息
const courseBase = ref({
  name: '示例课程',
  code: 'COURSE001',
  credit: 3,
  leader: '张老师',
  nature: '必修',
  semester: '第1学期',
  totalHours: '48',
  status: '正常开课'
});

// 课程目标-毕业要求卡片列表
const objectivePoList = ref([
  { id: 1, objectiveName: '掌握软件工程基本理论和方法', poTitle: '工程知识' },
  { id: 2, objectiveName: '具备软件需求分析和设计能力', poTitle: '问题分析' },
  { id: 3, objectiveName: '能够设计和开发软件系统', poTitle: '设计/开发解决方案' },
  { id: 4, objectiveName: '具备软件测试和质量保证能力', poTitle: '研究' },
  { id: 5, objectiveName: '掌握现代软件开发工具和技术', poTitle: '使用现代工具' }
]);

// 课程历史版本列表（时间轴）
const courseHistoryList = ref([
  { id: 101, version: '2021春', year: 2021, semester: '春', courseId: 101 },
  { id: 102, version: '2022秋', year: 2022, semester: '秋', courseId: 102 },
  { id: 103, version: '2023春', year: 2023, semester: '春', courseId: 103 }
]);

// 对话框控制
const achievementDialogVisible = ref(false);
const dialogCourseId = ref(0);
const handleOpenAchievement = (course: any) => {
  dialogCourseId.value = course.courseId;
  achievementDialogVisible.value = true;
};

// 图表数据（示例结构，后续可对接接口）
const radarObjectiveData = ref({
  indicators: [
    { name: '课程目标1', max: 100 },
    { name: '课程目标2', max: 100 },
    { name: '课程目标3', max: 100 },
    { name: '课程目标4', max: 100 },
    { name: '课程目标5', max: 100 }
  ],
  data: [85, 92, 88, 93, 85] // 最新学期的达成度数据
});

const radarPoData = ref({
  indicators: [
    { name: '工程知识', max: 100 },
    { name: '问题分析', max: 100 },
    { name: '设计开发', max: 100 },
    { name: '研究能力', max: 100 },
    { name: '工程工具', max: 100 },
    { name: '工程伦理', max: 100 }
  ],
  data: [92, 88, 93, 87, 91, 95] // 最新学期的达成度数据
});

const barObjectiveData = ref([
  { label: '课程目标1', value: 85, target: 80 },
  { label: '课程目标2', value: 92, target: 85 },
  { label: '课程目标3', value: 88, target: 82 },
  { label: '课程目标4', value: 93, target: 88 },
  { label: '课程目标5', value: 85, target: 80 }
]);

const barPoData = ref([
  { label: '工程知识', value: 92, target: 85 },
  { label: '问题分析', value: 88, target: 82 },
  { label: '设计开发', value: 93, target: 88 },
  { label: '研究能力', value: 87, target: 80 },
  { label: '工程工具', value: 91, target: 85 },
  { label: '工程伦理', value: 95, target: 90 }
]);

const lineObjectiveTrendData = ref([
  { label: '2019春', value: 78 },
  { label: '2019秋', value: 80 },
  { label: '2020春', value: 82 },
  { label: '2020秋', value: 84 },
  { label: '2021春', value: 81 },
  { label: '2021秋', value: 85 },
  { label: '2022春', value: 88 },
  { label: '2022秋', value: 87 },
  { label: '2023春', value: 90 }
]);

onMounted(() => {
  // TODO: 可在此处请求接口，填充 courseBase、objectivePoList、courseHistoryList、图表数据等
  console.log('CourseAchievement页面已加载，包含以下功能：');
  console.log('1. 课程基本信息展示');
  console.log('2. 课程目标与毕业要求映射关系');
  console.log('3. 课程历史版本时间轴（水平布局）');
  console.log('4. 数据可视化分析（雷达图、柱状图、趋势图）');
  console.log('5. 响应式设计和现代化UI');
});
</script>

<style scoped>
.course-achievement {
  padding: 24px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  min-height: 100vh;
}

/* 卡片通用样式 */
.course-info-card,
.objectives-mapping-card,
.timeline-section-card,
.chart-group-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.course-info-card:hover,
.objectives-mapping-card:hover,
.timeline-section-card:hover,
.chart-group-card:hover {
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

/* 卡片头部样式 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.header-icon {
  font-size: 20px;
  color: #3b82f6;
}

.header-info {
  color: #6b7280;
  font-size: 14px;
}

/* 课程基本信息样式 */
.course-info-content {
  padding: 8px 0;
}

.info-item {
  padding: 16px 20px;
  background: #f8fafc;
  border-radius: 12px;
  border-left: 4px solid #3b82f6;
  transition: all 0.2s ease;
}

.info-item:hover {
  background: #eff6ff;
  transform: translateX(4px);
}

.info-label {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
  margin-bottom: 4px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.info-value {
  font-size: 16px;
  color: #1f2937;
  font-weight: 600;
}

/* 课程目标映射样式 */
.objectives-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  padding: 8px 0;
}

.objective-mapping-item {
  position: relative;
}

.objective-card {
  display: flex;
  align-items: center;
  padding: 20px;
  background: linear-gradient(135deg, #f8fafc 0%, #eff6ff 100%);
  border-radius: 12px;
  border: 2px solid #e5e7eb;
  transition: all 0.3s ease;
}

.objective-card:hover {
  border-color: #3b82f6;
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(59, 130, 246, 0.15);
}

.objective-header {
  flex: 1;
}

.objective-number {
  display: inline-block;
  padding: 6px 12px;
  background: #3b82f6;
  color: white;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  margin-bottom: 8px;
}

.objective-title {
  font-size: 14px;
  color: #1f2937;
  font-weight: 500;
  line-height: 1.4;
}

.mapping-arrow {
  margin: 0 16px;
  color: #9ca3af;
  font-size: 18px;
}

.po-section {
  flex: 1;
  text-align: right;
}

.po-label {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 4px;
}

.po-title {
  font-size: 14px;
  color: #1f2937;
  font-weight: 600;
  padding: 8px 12px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

/* 可视化分析区域样式 */
.visualization-section {
  margin-top: 8px;
}

.chart-wrapper {
  background: white;
  border-radius: 12px;
  padding: 20px;
  border: 1px solid #f3f4f6;
  transition: all 0.2s ease;
}

.chart-wrapper:hover {
  border-color: #e5e7eb;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.trend-chart {
  min-height: 400px;
}

/* 间距工具类 */
.mb-6 {
  margin-bottom: 24px;
}

.mt-4 {
  margin-top: 16px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .objectives-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 16px;
  }
}

@media (max-width: 768px) {
  .course-achievement {
    padding: 16px;
  }

  .header-title {
    font-size: 16px;
  }

  .header-icon {
    font-size: 18px;
  }

  .objectives-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .objective-card {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .mapping-arrow {
    transform: rotate(90deg);
    margin: 8px 0;
  }

  .po-section {
    text-align: center;
  }

  .chart-wrapper {
    padding: 16px;
  }
}

@media (max-width: 480px) {
  .course-achievement {
    padding: 12px;
  }

  .info-item {
    padding: 12px 16px;
  }

  .info-value {
    font-size: 14px;
  }

  .objective-card {
    padding: 16px;
  }
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.course-info-card,
.objectives-mapping-card,
.timeline-section-card,
.chart-group-card {
  animation: fadeInUp 0.6s ease-out;
}

.objectives-mapping-card {
  animation-delay: 0.1s;
}

.timeline-section-card {
  animation-delay: 0.2s;
}

.chart-group-card:nth-child(1) {
  animation-delay: 0.3s;
}

.chart-group-card:nth-child(2) {
  animation-delay: 0.4s;
}

.chart-group-card:nth-child(3) {
  animation-delay: 0.5s;
}
</style>