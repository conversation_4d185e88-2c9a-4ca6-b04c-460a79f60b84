.assessment-content-publish {
  display: flex;
  flex-direction: column;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;
    flex-shrink: 0;

          .header-content {
        .page-title {
          margin: 0;
          font-size: 24px;
          font-weight: 600;
          color: var(--td-text-color-primary);

          .course-info {
            color: var(--td-text-color-secondary);
            font-weight: 400;
            font-size: 18px;

            .course-name {
              color: var(--td-brand-color);
              font-weight: 500;
            }

            .course-code {
              color: var(--td-text-color-placeholder);
              font-weight: 400;
            }
          }
        }
      }

    .header-actions {
      display: flex;
      gap: 12px;
    }
  }

  .assessment-sections-overview {
    margin-bottom: 24px;

    .section-header {
      margin-bottom: 16px;

      h3 {
        margin: 0 0 8px 0;
        font-size: 18px;
        font-weight: 600;
        color: var(--td-text-color-primary);
      }

      .section-subtitle {
        margin: 0;
        font-size: 14px;
        color: var(--td-text-color-secondary);
      }
    }

    .empty-state {
      text-align: center;
      padding: 60px 20px;

      .t-icon {
        color: var(--td-text-color-placeholder);
        margin-bottom: 16px;
      }

      p {
        color: var(--td-text-color-secondary);
        margin-bottom: 20px;
      }
    }

    .section-cards {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 16px;

      .section-card {
        border: 1px solid var(--td-border-level-1-color);
        border-radius: 8px;
        padding: 16px;
        cursor: pointer;
        transition: all 0.3s ease;
        background: var(--td-bg-color-container);

        &:hover {
          border-color: var(--td-brand-color);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        &.active {
          border-color: var(--td-brand-color);
          background: var(--td-brand-color-light);
          box-shadow: 0 2px 12px rgba(0, 123, 255, 0.2);
        }

        .card-header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: 12px;

          .section-info {
            flex: 1;

            .section-name {
              margin: 0;
              font-size: 16px;
              font-weight: 600;
              color: var(--td-text-color-primary);
            }
          }

          .section-weight {
            text-align: right;

            .weight-value {
              display: block;
              font-size: 20px;
              font-weight: 700;
              color: var(--td-brand-color);
            }

            .weight-label {
              font-size: 12px;
              color: var(--td-text-color-secondary);
            }
          }
        }

        .card-content {
          .section-description {
            font-size: 14px;
            color: var(--td-text-color-secondary);
            line-height: 1.5;
            margin-bottom: 16px;
          }

                      .objective-distribution {
              h5 {
                margin: 0 0 8px 0;
                font-size: 14px;
                font-weight: 600;
                color: var(--td-text-color-primary);
              }

                              .objective-tags {
                  display: flex;
                  flex-wrap: wrap;
                  gap: 8px;
                  margin-top: 4px;

                  .objective-tag {
                    cursor: help;
                    transition: all 0.2s ease;
                    font-weight: 500;
                    border-radius: 16px;
                    border-width: 1.5px !important;

                    .percentage-separator {
                      margin: 0 4px;
                      opacity: 0.6;
                      font-weight: 400;
                    }

                    .percentage {
                      font-weight: 600;
                    }

                    &:hover {
                      transform: translateY(-1px);
                      box-shadow: 0 3px 8px rgba(0, 123, 255, 0.25);
                      border-color: var(--td-brand-color);
                    }

                    &.selected {
                      background: var(--td-brand-color-light);
                      border-color: var(--td-brand-color);
                      color: var(--td-brand-color);

                      .percentage-separator {
                        color: var(--td-brand-color);
                        opacity: 0.8;
                      }

                      .percentage {
                        color: var(--td-brand-color);
                      }
                    }
                  }
                }
            }
        }
      }
    }
  }

  .assessment-content-management {
    .content-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      h3 {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        color: var(--td-text-color-primary);
      }

      .header-actions {
        display: flex;
        gap: 12px;
      }
    }

    .content-tabs {
      margin-bottom: 16px;
    }

    .content-table-container {
      .empty-content {
        text-align: center;
        padding: 60px 20px;

        .t-icon {
          color: var(--td-text-color-placeholder);
          margin-bottom: 16px;
        }

        p {
          color: var(--td-text-color-secondary);
          margin-bottom: 20px;
        }
      }
    }
  }

  .assessment-form-container {
    padding: 16px 0;

    :deep(.t-form-item) {
      margin-bottom: 16px;
    }

    :deep(.t-form-item__label) {
      font-weight: 500;
      color: var(--td-text-color-primary);
    }
  }

  // 表格内容样式
  .source-info {
    .creator-name {
      font-size: 12px;
      color: var(--td-text-color-secondary);
      margin-top: 4px;
    }
  }

  .time-info {
    font-size: 12px;
    line-height: 1.4;

    .publish-time,
    .create-time {
      display: flex;
      align-items: center;
      margin-bottom: 2px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .time-label {
      color: var(--td-text-color-secondary);
      width: 28px;
      flex-shrink: 0;
    }

    .time-value {
      color: var(--td-text-color-primary);
      font-weight: 500;
      cursor: help;
      text-decoration: underline;
      text-decoration-style: dotted;
      text-underline-offset: 2px;

      &:hover {
        color: var(--td-brand-color);
      }
    }
  }

  // 弹窗样式
  .content-form-container {
    padding: 16px 0;

    :deep(.t-form-item) {
      margin-bottom: 16px;
    }

    :deep(.t-form-item__label) {
      font-weight: 500;
      color: var(--td-text-color-primary);
    }
  }

  .config-form-container {
    max-height: calc(100vh - 220px);
    overflow-y: auto;

    .config-header {
      margin-bottom: 24px;

      .description-card {
        display: flex;
        align-items: flex-start;
        gap: 12px;
        padding: 16px;
        background: linear-gradient(135deg, var(--td-brand-color-1) 0%, var(--td-brand-color-2) 100%);
        border: 1px solid var(--td-brand-color-3);
        border-radius: 8px;
        border-left: 4px solid var(--td-brand-color);

        .description-content {
          flex: 1;

          .config-description {
            margin: 0;
            color: var(--td-text-color-primary);
            font-size: 14px;
            line-height: 1.6;
          }
        }
      }
    }

    .mode-description {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 16px;
      padding: 12px;
      background: var(--td-bg-color-page);
      border-radius: 6px;
      font-size: 14px;
      color: var(--td-text-color-secondary);

      .t-icon {
        color: var(--td-brand-color);
      }
    }

    .direct-mode-config {
      margin-bottom: 24px;

      // 表格表头样式 - 所有表头居中对齐
      :deep(.t-table__header) {
        th {
          text-align: center !important;
        }
      }

      // 表格表尾样式
      :deep(.t-table__footer) {
        .direct-mode-footer {
          background: var(--td-bg-color-page) !important;
          border-top: 2px solid var(--td-brand-color) !important;

          td {
            background: var(--td-bg-color-page) !important;
            font-weight: 600 !important;
            text-align: center !important;
            color: var(--td-text-color-primary) !important;
            padding: 12px 16px !important;
          }
        }
      }

      .validation-message {
        display: flex;
        align-items: center;
        gap: 6px;
        margin-top: 12px;
        padding: 12px 16px;
        color: var(--td-error-color);
        font-size: 13px;
        background: var(--td-error-color-1);
        border: 1px solid var(--td-error-color-3);
        border-radius: 6px;

        .t-icon {
          font-size: 14px;
        }
      }

      .invalid-percentage {
        color: var(--td-warning-color);
      }
    }

    .detailed-mode-config {
      > * + * {
        margin-top: 24px;
      }

      // 课程目标概览样式
      .objective-overview {

        h5 {
          margin: 0 0 16px 0;
          font-size: 16px;
          font-weight: 600;
          color: var(--td-text-color-primary);
          border-bottom: 2px solid var(--td-brand-color);
          padding-bottom: 8px;
        }

        .objectives-grid {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
          gap: 16px;

          .objective-card {
            padding: 20px;
            border: 1px solid var(--td-border-level-1-color);
            border-radius: 8px;
            background: var(--td-bg-color-container);
            transition: all 0.3s ease;

            &:hover {
              border-color: var(--td-brand-color);
              box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
            }

            .objective-header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 12px;

              .objective-number {
                font-size: 16px;
                font-weight: 600;
                color: var(--td-brand-color);
              }

              .objective-weight {
                font-size: 14px;
                color: var(--td-text-color-secondary);
                padding: 4px 8px;
                background: var(--td-brand-color-light);
                border-radius: 4px;
                border: 1px solid var(--td-brand-color-3);
              }
            }

            .objective-name {
              font-size: 15px;
              font-weight: 500;
              color: var(--td-text-color-primary);
              margin-bottom: 8px;
            }

            .objective-description {
              font-size: 14px;
              color: var(--td-text-color-secondary);
              line-height: 1.6;
              margin-bottom: 12px;
            }

            .graduation-requirement {
              display: flex;
              align-items: center;
              gap: 8px;
              font-size: 13px;

              .label {
                color: var(--td-text-color-secondary);
                font-weight: 500;
              }

              .value {
                color: var(--td-success-color);
                font-weight: 500;
                padding: 2px 8px;
                background: var(--td-success-color-1);
                border-radius: 4px;
                border: 1px solid var(--td-success-color-3);
              }
            }
          }
        }
      }

      // 题目明细表格样式
      .question-detail-section {
        margin-bottom: 32px;

                          .section-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 16px;

          h5 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            color: var(--td-text-color-primary);
            border-bottom: 2px solid var(--td-brand-color);
            padding-bottom: 8px;
          }
        }

        .question-table-container {
          max-height: 400px;
          overflow: auto;

          .question-detail-table {
            width: 100%;
            border-collapse: collapse;
            border: 1px solid var(--td-border-level-1-color);
            border-radius: 6px;
            overflow: hidden;

            thead {
              background: var(--td-bg-color-page);

              th {
                padding: 12px 8px;
                text-align: center;
                font-weight: 600;
                color: var(--td-text-color-primary);
                border: 1px solid var(--td-border-level-1-color);
                font-size: 14px;
              }
            }

                          tbody {
                tr {
                                    td {
                    padding: 12px 8px;
                    border: 1px solid var(--td-border-level-1-color);
                    vertical-align: middle;
                    font-size: 13px;

                    &.question-number-cell {
                      text-align: center;
                      font-weight: 600;
                      color: var(--td-brand-color);
                      background: var(--td-brand-color-1);
                      border-left: 3px solid var(--td-brand-color);

                      .question-number {
                        font-size: 18px;
                        font-weight: 700;
                        color: var(--td-brand-color);
                      }
                    }

                  &.sub-number-cell {
                    text-align: center;
                    font-weight: 500;
                    color: var(--td-text-color-primary);
                    min-width: 80px;
                  }

                  &.question-type-cell {
                    min-width: 140px;

                    .question-type-content {
                      display: flex;
                      align-items: center;
                      gap: 8px;

                      .delete-question-btn {
                        flex-shrink: 0;
                        width: 32px;
                        height: 32px;
                        border-radius: 3px; // 与TDesign组件圆角保持一致
                        background: var(--td-error-color-1);
                        border-color: var(--td-error-color-3);
                        color: var(--td-error-color);
                        transition: all 0.2s ease;

                        &:hover {
                          background: var(--td-error-color);
                          border-color: var(--td-error-color);
                          color: white;
                          transform: translateY(-1px);
                          box-shadow: 0 2px 8px rgba(220, 38, 38, 0.3);
                        }

                        .t-icon {
                          font-size: 14px;
                        }
                      }
                    }
                  }

                  &.question-content-cell {
                    min-width: 200px;
                  }

                  &.answer-cell {
                    min-width: 150px;
                  }

                  &.score-cell {
                    text-align: center;
                    min-width: 80px;
                  }

                  &.objective-cell {
                    min-width: 120px;
                  }

                  &.action-cell {
                    text-align: center;
                    min-width: 120px;
                    padding: 8px !important;

                    .action-buttons {
                      display: flex;
                      justify-content: center;
                      align-items: center;
                      gap: 8px;

                      .action-btn {
                        width: 28px;
                        height: 28px;
                        border-radius: 50%;
                        transition: all 0.2s ease;
                        position: relative;
                        z-index: 10;

                        &:hover {
                          transform: translateY(-1px);
                          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
                        }

                        .t-icon {
                          font-size: 14px;
                        }

                        &.add-btn {
                          background: var(--td-brand-color-1);
                          border-color: var(--td-brand-color);
                          color: var(--td-brand-color);

                          &:hover {
                            background: var(--td-brand-color);
                            color: white;
                            border-color: var(--td-brand-color);
                          }
                        }

                        &.remove-btn {
                          background: var(--td-warning-color-1);
                          border-color: var(--td-warning-color);
                          color: var(--td-warning-color);

                          &:hover:not(.t-is-disabled) {
                            background: var(--td-warning-color);
                            color: white;
                            border-color: var(--td-warning-color);
                          }
                        }


                      }
                    }
                  }
                }
              }
            }
          }

          .empty-questions {
            text-align: center;
            padding: 60px 20px;
            color: var(--td-text-color-secondary);

            .t-icon {
              color: var(--td-text-color-placeholder);
              margin-bottom: 16px;
            }

            p {
              margin: 0;
              font-size: 14px;
            }
          }
        }

        .table-bottom-actions {
          display: flex;
          justify-content: center;
          padding: 16px;
          border-top: 1px solid var(--td-border-level-1-color);
          background: var(--td-bg-color-container);
          margin-top: 12px;
          border-radius: 0 0 6px 6px;

          .t-button {
            width: 100%;
            transition: all 0.3s ease;

            &:hover {
              transform: translateY(-1px);
              box-shadow: 0 4px 12px rgba(0, 123, 255, 0.25);
            }
          }
        }
      }

      // 汇总表格样式
      .objective-summary {
        margin-bottom: 24px;

        .summary-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 16px;
          gap: 16px;

          h5 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            color: var(--td-text-color-primary);
            border-bottom: 2px solid var(--td-brand-color);
            padding-bottom: 8px;
            flex-shrink: 0;
          }

          .summary-tips-inline {
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 6px 10px;
            border-radius: 4px;
            font-size: 12px;
            line-height: 1.3;
            white-space: nowrap;
            flex-shrink: 0;

            .t-icon {
              font-size: 12px;
              flex-shrink: 0;
            }

            &.tip-info {
              background: var(--td-brand-color-1);
              border: 1px solid var(--td-brand-color-3);
              color: var(--td-brand-color-8);

              .t-icon {
                color: var(--td-brand-color);
              }
            }

            &.tip-warning {
              background: var(--td-warning-color-1);
              border: 1px solid var(--td-warning-color-3);
              color: var(--td-warning-color-8);

              .t-icon {
                color: var(--td-warning-color);
              }
            }

            &.tip-success {
              background: var(--td-success-color-1);
              border: 1px solid var(--td-success-color-3);
              color: var(--td-success-color-8);

              .t-icon {
                color: var(--td-success-color);
              }
            }
          }

          // 中等屏幕下缩小间距
          @media (max-width: 1400px) {
            gap: 12px;

            .summary-tips-inline {
              font-size: 11px;
              padding: 5px 8px;

              .t-icon {
                font-size: 11px;
              }
            }
          }

          // 小屏幕下的响应式处理
          @media (max-width: 1200px) {
            flex-direction: column;
            align-items: flex-start;
            gap: 8px;

            .summary-tips-inline {
              align-self: stretch;
              white-space: normal;
              font-size: 13px;
              padding: 8px 12px;

              .t-icon {
                font-size: 14px;
              }
            }
          }
        }

                  .summary-table-container {
            max-height: 300px;
            overflow: auto;

          .summary-table {
            width: 100%;
            border-collapse: collapse;
            border: 1px solid var(--td-border-level-1-color);
            border-radius: 6px;
            overflow: hidden;

            thead {
              background: var(--td-bg-color-page);

              th {
                padding: 12px 8px;
                text-align: center;
                font-weight: 600;
                color: var(--td-text-color-primary);
                border: 1px solid var(--td-border-level-1-color);
                font-size: 14px;

                &.objective-header {
                  background: var(--td-brand-color-1);
                  color: var(--td-brand-color);
                }

                &.question-type-header {
                  background: var(--td-success-color-1);
                  color: var(--td-success-color);
                }

                &.total-header {
                  background: var(--td-warning-color-1);
                  color: var(--td-warning-color);
                }

                &.sub-header {
                  font-size: 13px;
                  background: var(--td-bg-color-container);
                }
              }
            }

            tbody {
              tr {
                &:nth-child(even) {
                  background: var(--td-bg-color-container-hover);
                }

                &.summary-row {
                  background: var(--td-bg-color-page);
                  border-top: 2px solid var(--td-brand-color);

                  td {
                    font-weight: 600;
                    color: var(--td-text-color-primary);

                    &.summary-label {
                      background: var(--td-brand-color-1);
                      color: var(--td-brand-color);
                    }

                    &.summary-total {
                      background: var(--td-warning-color-1);
                      color: var(--td-warning-color);
                      font-size: 16px;
                    }
                  }
                }

                td {
                  padding: 10px 8px;
                  text-align: center;
                  border: 1px solid var(--td-border-level-1-color);
                  font-size: 13px;

                  &.objective-cell {
                    text-align: left;
                    font-weight: 500;
                    color: var(--td-text-color-primary);
                  }

                                &.count-cell {
                color: var(--td-text-color-secondary);
              }

              &.sub-numbers-cell {
                font-size: 12px;
                color: var(--td-text-color-secondary);
                line-height: 1.4;
                max-width: 120px;
                word-wrap: break-word;
              }

              &.score-cell {
                font-weight: 500;
                color: var(--td-text-color-primary);
              }

                  &.total-cell {
                    font-weight: 600;
                    color: var(--td-brand-color);
                    background: var(--td-brand-color-1);
                  }
                }
              }
            }
          }

          .empty-summary {
            text-align: center;
            padding: 40px 20px;
            color: var(--td-text-color-secondary);
            font-size: 14px;
          }
        }

                  .validation-message {
            display: flex;
            align-items: center;
            gap: 6px;
            margin-top: 12px;
            padding: 12px 16px;
            color: var(--td-error-color);
            font-size: 13px;
            background: var(--td-error-color-1);
            border: 1px solid var(--td-error-color-3);
            border-radius: 6px;

          .t-icon {
            font-size: 14px;
          }
        }
      }
    }
  }
}

// 新增：直接录入配置弹窗样式
.direct-config-container {
  padding: 16px 0;

  .config-header {
    margin-bottom: 24px;

    .description-card {
      display: flex;
      align-items: flex-start;
      gap: 12px;
      padding: 16px;
      background: linear-gradient(135deg, var(--td-brand-color-1) 0%, var(--td-brand-color-2) 100%);
      border: 1px solid var(--td-brand-color-3);
      border-radius: 8px;
      border-left: 4px solid var(--td-brand-color);

      .description-content {
        flex: 1;

        .config-description {
          margin: 0;
          color: var(--td-text-color-primary);
          font-size: 14px;
          line-height: 1.6;
        }
      }
    }
  }

  .mode-description {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 16px;
    padding: 12px;
    background: var(--td-bg-color-page);
    border-radius: 6px;
    font-size: 14px;
    color: var(--td-text-color-secondary);

    .t-icon {
      color: var(--td-brand-color);
    }
  }

  // 表格表头样式 - 所有表头居中对齐
  :deep(.t-table__header) {
    th {
      text-align: center !important;
    }
  }

  // 表格表尾样式
  :deep(.t-table__footer) {
    .direct-mode-footer {
      background: var(--td-bg-color-page) !important;
      border-top: 2px solid var(--td-brand-color) !important;

      td {
        background: var(--td-bg-color-page) !important;
        font-weight: 600 !important;
        text-align: center !important;
        color: var(--td-text-color-primary) !important;
        padding: 12px 16px !important;

        // 占比列的状态颜色
        &:nth-child(3) {
          &.percentage-valid {
            color: var(--td-success-color) !important;
            background: var(--td-success-color-1) !important;
          }

          &.percentage-invalid {
            color: var(--td-error-color) !important;
            background: var(--td-error-color-1) !important;
          }
        }

        // 说明列的状态颜色
        &:nth-child(4) {
          &.description-valid {
            color: var(--td-success-color) !important;
            background: var(--td-success-color-1) !important;
          }

          &.description-invalid {
            color: var(--td-error-color) !important;
            background: var(--td-error-color-1) !important;
          }
        }
      }
    }
  }

  .validation-message {
    display: flex;
    align-items: center;
    gap: 6px;
    margin-top: 12px;
    padding: 12px 16px;
    color: var(--td-error-color);
    font-size: 13px;
    background: var(--td-error-color-1);
    border: 1px solid var(--td-error-color-3);
    border-radius: 6px;

    .t-icon {
      font-size: 14px;
    }
  }

  .invalid-percentage {
    color: var(--td-warning-color);
  }
}

// 新增：详细录入配置全屏弹窗内容样式
.detailed-config-content {
  .config-header {
    margin-bottom: 24px;

    .description-card {
      display: flex;
      align-items: flex-start;
      gap: 12px;
      padding: 16px;
      background: linear-gradient(135deg, var(--td-brand-color-1) 0%, var(--td-brand-color-2) 100%);
      border: 1px solid var(--td-brand-color-3);
      border-radius: 8px;
      border-left: 4px solid var(--td-brand-color);

      .description-content {
        flex: 1;

        .config-description {
          margin: 0;
          color: var(--td-text-color-primary);
          font-size: 14px;
          line-height: 1.6;
        }
      }
    }
  }

  .mode-description {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 24px;
    padding: 12px;
    background: var(--td-bg-color-page);
    border-radius: 6px;
    font-size: 14px;
    color: var(--td-text-color-secondary);

    .t-icon {
      color: var(--td-brand-color);
    }
  }

  > * + * {
    margin-top: 32px;
  }

  // 课程目标概览样式
  .objective-overview {
    h5 {
      margin: 0 0 20px 0;
      font-size: 18px;
      font-weight: 600;
      color: var(--td-text-color-primary);
      border-bottom: 2px solid var(--td-brand-color);
      padding-bottom: 10px;
    }

    .objectives-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
      gap: 20px;

      .objective-card {
        padding: 24px;
        border: 1px solid var(--td-border-level-1-color);
        border-radius: 12px;
        background: var(--td-bg-color-container);
        transition: all 0.3s ease;

        &:hover {
          border-color: var(--td-brand-color);
          box-shadow: 0 6px 16px rgba(0, 123, 255, 0.15);
          transform: translateY(-2px);
        }

        .objective-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 16px;

          .objective-number {
            font-size: 18px;
            font-weight: 600;
            color: var(--td-brand-color);
          }

          .objective-weight {
            font-size: 14px;
            color: var(--td-text-color-secondary);
            padding: 6px 12px;
            background: var(--td-brand-color-light);
            border-radius: 6px;
            border: 1px solid var(--td-brand-color-3);
          }
        }

        .objective-name {
          font-size: 16px;
          font-weight: 500;
          color: var(--td-text-color-primary);
          margin-bottom: 12px;
        }

        .objective-description {
          font-size: 14px;
          color: var(--td-text-color-secondary);
          line-height: 1.6;
          margin-bottom: 16px;
        }

        .graduation-requirement {
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 13px;

          .label {
            color: var(--td-text-color-secondary);
            font-weight: 500;
          }

          .value {
            color: var(--td-success-color);
            font-weight: 500;
            padding: 4px 10px;
            background: var(--td-success-color-1);
            border-radius: 6px;
            border: 1px solid var(--td-success-color-3);
          }
        }
      }
    }
  }

  // 题目明细表格样式
  .question-detail-section {
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;

      h5 {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        color: var(--td-text-color-primary);
        border-bottom: 2px solid var(--td-brand-color);
        padding-bottom: 10px;
      }
    }

    .question-table-container {
      max-height: 500px;
      overflow: auto;
      border-radius: 8px;
      border: 1px solid var(--td-border-level-1-color);

      .question-detail-table {
        width: 100%;
        border-collapse: collapse;
        overflow: hidden;

        thead {
          background: var(--td-bg-color-page);
          position: sticky;
          top: 0;
          z-index: 10;

          th {
            padding: 16px 12px;
            text-align: center;
            font-weight: 600;
            color: var(--td-text-color-primary);
            border: 1px solid var(--td-border-level-1-color);
            font-size: 14px;
          }
        }

        tbody {
          tr {
                          td {
                padding: 12px;
                border: 1px solid var(--td-border-level-1-color);
                vertical-align: middle;
                font-size: 13px;

                &.question-number-cell {
                  text-align: center;
                  font-weight: 600;
                  color: var(--td-brand-color);
                  background: var(--td-brand-color-1);
                  min-width: 80px;
                  border-left: 3px solid var(--td-brand-color);

                  .question-number {
                    font-size: 18px;
                    font-weight: 700;
                    color: var(--td-brand-color);
                  }
                }

              &.sub-number-cell {
                text-align: center;
                font-weight: 500;
                color: var(--td-text-color-primary);
                min-width: 80px;
              }

              &.question-type-cell {
                min-width: 160px;

                .question-type-content {
                  display: flex;
                  align-items: center;
                  gap: 8px;

                  .delete-question-btn {
                    flex-shrink: 0;
                    width: 32px;
                    height: 32px;
                    border-radius: 3px; // 与TDesign组件圆角保持一致
                    background: var(--td-error-color-1);
                    border-color: var(--td-error-color-3);
                    color: var(--td-error-color);
                    transition: all 0.2s ease;

                    &:hover {
                      background: var(--td-error-color);
                      border-color: var(--td-error-color);
                      color: white;
                      transform: translateY(-1px);
                      box-shadow: 0 2px 8px rgba(220, 38, 38, 0.3);
                    }

                    .t-icon {
                      font-size: 14px;
                    }
                  }
                }
              }

              &.question-content-cell {
                min-width: 250px;
              }

              &.answer-cell {
                min-width: 200px;
              }

              &.score-cell {
                text-align: center;
                min-width: 100px;
              }

              &.objective-cell {
                min-width: 140px;
              }

              &.action-cell {
                text-align: center;
                min-width: 120px;
                padding: 8px !important;

                .action-buttons {
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  gap: 8px;

                  .action-btn {
                    width: 28px;
                    height: 28px;
                    border-radius: 50%;
                    transition: all 0.2s ease;
                    position: relative;
                    z-index: 10;

                    &:hover {
                      transform: translateY(-1px);
                      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
                    }

                    .t-icon {
                      font-size: 14px;
                    }

                    &.add-btn {
                      background: var(--td-brand-color-1);
                      border-color: var(--td-brand-color);
                      color: var(--td-brand-color);

                      &:hover {
                        background: var(--td-brand-color);
                        color: white;
                        border-color: var(--td-brand-color);
                      }
                    }

                    &.remove-btn {
                      background: var(--td-warning-color-1);
                      border-color: var(--td-warning-color);
                      color: var(--td-warning-color);

                      &:hover:not(.t-is-disabled) {
                        background: var(--td-warning-color);
                        color: white;
                        border-color: var(--td-warning-color);
                      }
                    }


                  }
                }
              }
            }
          }
        }
      }

      .empty-questions {
        text-align: center;
        padding: 80px 20px;
        color: var(--td-text-color-secondary);

        .t-icon {
          color: var(--td-text-color-placeholder);
          margin-bottom: 20px;
        }

        p {
          margin: 0;
          font-size: 16px;
        }
      }
    }

    .table-bottom-actions {
      display: flex;
      justify-content: center;
      padding: 20px;
      border-top: 1px solid var(--td-border-level-1-color);
      background: var(--td-bg-color-container);
      margin-top: 16px;
      border-radius: 0 0 8px 8px;

      .t-button {
        width: 100%;
        transition: all 0.3s ease;
        font-size: 14px;
        padding: 8px 16px;

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(0, 123, 255, 0.25);
        }
      }
    }
  }

  // 汇总表格样式
  .objective-summary {
    .summary-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 20px;
      gap: 20px;

      h5 {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        color: var(--td-text-color-primary);
        border-bottom: 2px solid var(--td-brand-color);
        padding-bottom: 10px;
        flex-shrink: 0;
      }

      .summary-tips-inline {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 8px 14px;
        border-radius: 6px;
        font-size: 13px;
        line-height: 1.4;
        white-space: nowrap;
        flex-shrink: 0;

        .t-icon {
          font-size: 14px;
          flex-shrink: 0;
        }

        &.tip-info {
          background: var(--td-brand-color-1);
          border: 1px solid var(--td-brand-color-3);
          color: var(--td-brand-color-8);

          .t-icon {
            color: var(--td-brand-color);
          }
        }

        &.tip-warning {
          background: var(--td-warning-color-1);
          border: 1px solid var(--td-warning-color-3);
          color: var(--td-warning-color-8);

          .t-icon {
            color: var(--td-warning-color);
          }
        }

        &.tip-success {
          background: var(--td-success-color-1);
          border: 1px solid var(--td-success-color-3);
          color: var(--td-success-color-8);

          .t-icon {
            color: var(--td-success-color);
          }
        }
      }

      // 中等屏幕下缩小间距
      @media (max-width: 1600px) {
        gap: 16px;

        .summary-tips-inline {
          font-size: 12px;
          padding: 6px 12px;

          .t-icon {
            font-size: 12px;
          }
        }
      }

      // 小屏幕下的响应式处理
      @media (max-width: 1200px) {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;

        .summary-tips-inline {
          align-self: stretch;
          white-space: normal;
          font-size: 14px;
          padding: 10px 16px;

          .t-icon {
            font-size: 16px;
          }
        }
      }
    }

    .summary-table-container {
      max-height: 400px;
      overflow: auto;
      border-radius: 8px;
      border: 1px solid var(--td-border-level-1-color);

      .summary-table {
        width: 100%;
        border-collapse: collapse;

        thead {
          background: var(--td-bg-color-page);
          position: sticky;
          top: 0;
          z-index: 10;

          th {
            padding: 16px 12px;
            text-align: center;
            font-weight: 600;
            color: var(--td-text-color-primary);
            border: 1px solid var(--td-border-level-1-color);
            font-size: 14px;

            &.objective-header {
              background: var(--td-brand-color-1);
              color: var(--td-brand-color);
            }

            &.question-type-header {
              background: var(--td-success-color-1);
              color: var(--td-success-color);
            }

            &.total-header {
              background: var(--td-warning-color-1);
              color: var(--td-warning-color);
            }

            &.sub-header {
              font-size: 13px;
              background: var(--td-bg-color-container);
            }
          }
        }

        tbody {
          tr {
            &:nth-child(even) {
              background: var(--td-bg-color-container-hover);
            }

            &.summary-row {
              background: var(--td-bg-color-page);
              border-top: 2px solid var(--td-brand-color);

              td {
                font-weight: 600;
                color: var(--td-text-color-primary);
                font-size: 14px;

                &.summary-label {
                  background: var(--td-brand-color-1);
                  color: var(--td-brand-color);
                }

                &.summary-total {
                  background: var(--td-warning-color-1);
                  color: var(--td-warning-color);
                  font-size: 16px;
                }
              }
            }

            td {
              padding: 12px;
              text-align: center;
              border: 1px solid var(--td-border-level-1-color);
              font-size: 13px;

              &.objective-cell {
                text-align: left;
                font-weight: 500;
                color: var(--td-text-color-primary);
              }

              &.count-cell {
                color: var(--td-text-color-secondary);
              }

              &.sub-numbers-cell {
                font-size: 12px;
                color: var(--td-text-color-secondary);
                line-height: 1.4;
                max-width: 120px;
                word-wrap: break-word;
              }

              &.score-cell {
                font-weight: 500;
                color: var(--td-text-color-primary);
              }

              &.total-cell {
                font-weight: 600;
                color: var(--td-brand-color);
                background: var(--td-brand-color-1);
              }
            }
          }
        }
      }

      .empty-summary {
        text-align: center;
        padding: 60px 20px;
        color: var(--td-text-color-secondary);
        font-size: 16px;
      }
    }

    .validation-message {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-top: 16px;
      padding: 16px 20px;
      color: var(--td-error-color);
      font-size: 14px;
      background: var(--td-error-color-1);
      border: 1px solid var(--td-error-color-3);
      border-radius: 8px;

      .t-icon {
        font-size: 16px;
      }
    }
  }
}

// 确保详细录入弹窗中的下拉组件能正常显示
:deep(.full-screen-dialog) {
  .t-select,
  .t-select__wrap {
    position: relative;
  }
}



// 文本域容器和字数限制警告样式
.textarea-container {
  width: 100%;
  :deep(.t-textarea) {
    .t-textarea__info_wrapper{
      display: none !important;
    }
  }

  .char-limit-warning {
    margin-top: 6px;
    font-size: 12px;
    text-align: right;
    padding: 6px 12px;
    border-radius: 4px;
    transition: all 0.2s ease;
    font-weight: 500;

    // 字数超限警告样式
    &.limit-exceeded {
      color: var(--td-error-color);
      background: var(--td-error-color-1);
      border: 1px solid var(--td-error-color-3);
    }
  }
}

// 发布弹窗样式
.publish-dialog-content {
  .content-info-section {
    margin-bottom: 24px;

    h4 {
      margin: 0 0 16px 0;
      font-size: 16px;
      font-weight: 600;
      color: var(--td-text-color-primary);
    }

    .content-info-card {
      background: var(--td-bg-color-container);
      border: 1px solid var(--td-border-level-1-color);
      border-radius: 8px;
      padding: 16px;

      .info-row {
        display: flex;
        margin-bottom: 12px;

        &:last-child {
          margin-bottom: 0;
        }

        .label {
          font-weight: 500;
          color: var(--td-text-color-secondary);
          min-width: 80px;
          flex-shrink: 0;
        }

        .value {
          color: var(--td-text-color-primary);
          flex: 1;
        }
      }
    }
  }

  .class-selection-section {
    margin-bottom: 24px;

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      h4 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: var(--td-text-color-primary);
      }

      .header-actions {
        display: flex;
        align-items: center;
        gap: 16px;
      }
    }

    // 班级表格样式
    :deep(.t-table) {
      .class-name-cell {
        .class-name {
          display: block;
          font-weight: 500;
          color: var(--td-text-color-primary);
          margin-bottom: 4px;
        }

        .major-name {
          display: block;
          font-size: 12px;
          color: var(--td-text-color-secondary);
        }
      }

      .student-count {
        font-weight: 500;
        color: var(--td-brand-color);
      }

      .schedule-info {
        .time-info {
          font-weight: 500;
          color: var(--td-text-color-primary);
          margin-bottom: 4px;
        }

        .classroom-info {
          font-size: 12px;
          color: var(--td-text-color-secondary);
        }
      }

      .teacher-info {
        .teacher-name {
          display: block;
          font-weight: 500;
          color: var(--td-text-color-primary);
          margin-bottom: 4px;
        }

        .teacher-role {
          display: block;
          font-size: 12px;
          color: var(--td-text-color-secondary);
        }
      }

      .publish-time {
        font-size: 12px;
        color: var(--td-text-color-placeholder);
        margin-top: 4px;
      }

      .no-action {
        color: var(--td-text-color-placeholder);
      }
    }
  }

  .statistics-section {
    .statistics-card {
      background: var(--td-bg-color-container);
      border: 1px solid var(--td-border-level-1-color);
      border-radius: 8px;
      padding: 16px;
      display: flex;
      gap: 32px;

      .stat-item {
        display: flex;
        align-items: center;
        gap: 8px;

        .stat-label {
          font-weight: 500;
          color: var(--td-text-color-secondary);
        }

        .stat-value {
          font-weight: 600;
          color: var(--td-text-color-primary);

          &.highlight {
            color: var(--td-brand-color);
            font-size: 16px;
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .assessment-content-publish {
    padding: 16px;

    .page-header {
      flex-direction: column;
      gap: 16px;
      align-items: stretch;
    }

    .course-info-grid {
      grid-template-columns: 1fr !important;
    }

    .detailed-header {
      flex-direction: column !important;
      gap: 12px;
      align-items: stretch !important;
    }
  }
}

// 课程目标 Tooltip 样式
.objective-tooltip-content {
  text-align: left;
  padding: 12px;
  max-width: 320px;
  min-width: 200px;
  position: relative;
  margin-bottom: 8px; // 为箭头留出空间

  .objective-tooltip-title {
    font-weight: 600;
    margin-bottom: 8px;
    font-size: 14px;
    line-height: 1.4;
    position: relative;

    // 添加一个小的装饰线
    &::after {
      content: '';
      position: absolute;
      bottom: -4px;
      left: 0;
      width: 100%;
      height: 2px;
      background: var(--td-brand-color);
      border-radius: 1px;
    }
  }

  .objective-tooltip-description {
    color: var(--td-text-color-secondary);
    font-size: 12px;
    line-height: 1.6;
    word-wrap: break-word;
    white-space: pre-wrap;
    text-align: justify;
    margin-top: 8px;
  }
}

// 强制覆盖 TDesign Tooltip 的默认样式
:deep(.t-tooltip) {
  .t-tooltip__content {
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
    padding: 0 !important;
  }

  .t-tooltip__arrow {
    display: none !important;
  }
}

// 强制覆盖 TDesign Popup 的默认样式
:deep(.t-popup) {
  .t-popup__content {
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
    padding: 0 !important;
  }
}
