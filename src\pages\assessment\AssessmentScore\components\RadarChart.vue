<template>
  <div class="radar-chart-container">
    <div ref="chartRef" class="chart"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, onBeforeUnmount } from 'vue'
import * as echarts from 'echarts'

interface RadarChartData {
  indicators: Array<{ name: string; max: number }>
  data: number[]
}

const props = defineProps<{
  title?: string
  data: RadarChartData
}>()

const chartRef = ref<HTMLDivElement | null>(null)
let chartInstance: echarts.ECharts | null = null

const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

const renderChart = () => {
  if (!chartRef.value || !props.data) return
  
  if (!chartInstance) {
    chartInstance = echarts.init(chartRef.value)
  }

  const option = {
    title: {
      text: props.title || '雷达图',
      left: 'center'
    },
    tooltip: {},
    radar: {
      indicator: props.data.indicators || []
    },
    series: [{
      type: 'radar',
      data: [{
        value: props.data.data || [],
        name: '达成度'
      }]
    }]
  }

  chartInstance.setOption(option)
  chartInstance.resize()
}

watch(() => props.data, renderChart, { deep: true })

onMounted(() => {
  renderChart()
  window.addEventListener('resize', handleResize)
})

onBeforeUnmount(() => {
  if (chartInstance) {
    chartInstance.dispose()
  }
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.radar-chart-container {
  width: 100%;
}

.chart {
  width: 100%;
  height: 400px;
}
</style>
