<template>
  <div class="questionnaire-detail">

    <div class="header">
      <h2>{{ questionnaire.questionnaire_name }} - 详情</h2>
      <div class="actions">
        <t-button variant="outline" @click="exportData">
          <template #icon><t-icon name="download" /></template>
          导出数据
        </t-button>
        <t-button variant="outline" @click="goBack">
          <template #icon><t-icon name="rollback" /></template>
          返回
        </t-button>
      </div>
    </div>

    <t-card>
      <div class="questionnaire-info">
        <t-descriptions :title="'基本信息'" :column="3" bordered>
          <t-descriptions-item label="问卷ID">{{ questionnaire.questionnaire_id }}</t-descriptions-item>
          <t-descriptions-item label="问卷类型">{{ getQuestionnaireTypeText(questionnaire.questionnaire_type) }}</t-descriptions-item>
          <t-descriptions-item label="毕业年份">{{ questionnaire.graduation_year || '无' }}</t-descriptions-item>
          <t-descriptions-item label="创建时间">{{ questionnaire.create_time }}</t-descriptions-item>
          <t-descriptions-item label="更新时间">{{ questionnaire.update_time }}</t-descriptions-item>
          <t-descriptions-item label="状态">
            <t-tag :theme="getStatusTagTheme(questionnaire.status)" variant="light">
              {{ getStatusText(questionnaire.status) }}
            </t-tag>
          </t-descriptions-item>
        </t-descriptions>
      </div>
    </t-card>

    <t-card title="题目统计" class="questions-card">
      <t-tabs v-model="activeTab">
        <t-tab-panel value="statistics" label="统计概览">
          <div v-for="(question, index) in questions" :key="question.question_id" class="question-item">
            <h3>{{ index + 1 }}. {{ question.question_name }} ({{ question.question_type === 1 ? '选择题' : '文本题' }})</h3>

            <!-- 选择题统计 -->
            <div v-if="question.question_type === 1" class="choice-statistics">
              <div v-for="option in question.options" :key="option.option_id" class="option-row">
                <div class="option-info">
                  <span class="option-text">{{ option.option_text }}</span>
                  <span class="option-count">{{ option.count }}人</span>
                </div>
                <t-progress
                  :percentage="option.percentage"
                  :color="getProgressColor(option.percentage)"
                  :label="`${option.percentage}%`"
                  :track-color="'var(--td-bg-color-secondarycontainer)'"
                />
              </div>
            </div>

            <!-- 文本题统计 -->
            <div v-else class="text-statistics">
              <div class="summary">
                <t-tag>共收到 {{ question.answer_count }} 条回答</t-tag>
                <t-tag>平均字数: {{ question.avg_word_count }}</t-tag>
                <t-tag>最短回答: {{ question.min_word_count }}字</t-tag>
                <t-tag>最长回答: {{ question.max_word_count }}字</t-tag>
              </div>

              <div class="filter-controls">
                <t-input
                  v-model="textFilter.keyword"
                  placeholder="关键词筛选"
                  clearable
                  @enter="filterTextAnswers(question.question_id)"
                  @clear="filterTextAnswers(question.question_id)"
                >
                  <template #suffix-icon>
                    <t-icon name="search" />
                  </template>
                </t-input>

                <t-input-number
                  v-model="textFilter.minWords"
                  placeholder="最小字数"
                  :min="0"
                  clearable
                  @change="filterTextAnswers(question.question_id)"
                />

                <t-input-number
                  v-model="textFilter.maxWords"
                  placeholder="最大字数"
                  :min="0"
                  clearable
                  @change="filterTextAnswers(question.question_id)"
                />
              </div>

              <div class="text-answers">
                <div v-for="(answer, idx) in filteredTextAnswers[question.question_id] || []" :key="idx" class="text-answer-item">
                  <div class="answer-header">
                    <span class="respondent">答卷人{{ answer.respondent_id }}</span>
                    <span class="word-count">{{ answer.word_count }}字</span>
                    <t-button size="small" variant="text" @click="showRespondentDetail(answer.respondent_id)">
                      <t-icon name="chevron-right" />
                    </t-button>
                  </div>
                  <div class="answer-content">{{ answer.answer_text }}</div>
                </div>

                <t-pagination
                  v-if="filteredTextAnswers[question.question_id]?.length > 0"
                  v-model="textPagination.current"
                  :total="textPagination.total"
                  :page-size="textPagination.pageSize"
                  @change="handleTextPageChange"
                  class="pagination"
                />

                <div v-if="!filteredTextAnswers[question.question_id]?.length" class="empty">
                  <t-empty description="暂无符合条件的回答" />
                </div>
              </div>
            </div>
          </div>
        </t-tab-panel>

        <t-tab-panel value="responses" label="答卷列表">
          <t-table
            :data="responses"
            :columns="responseColumns"
            row-key="response_id"
            :pagination="responsePagination"
            @page-change="handleResponsePageChange"
          >
            <template #operation="{ row }">
              <t-link theme="primary" @click="showRespondentDetail(row.respondent_id)">查看详情</t-link>
            </template>
          </t-table>
        </t-tab-panel>
      </t-tabs>
    </t-card>

    <!-- 答卷详情弹窗 -->
    <t-dialog
      v-model:visible="showRespondentDialog"
      header="答卷详情"
      width="800px"
      :footer="null"
      @close="closeRespondentDialog"
    >
      <div v-if="currentRespondent" class="respondent-detail">
        <div class="respondent-info">
          <t-descriptions :title="'答卷信息'" :column="2" bordered>
            <t-descriptions-item label="答卷ID">{{ currentRespondent.response_id }}</t-descriptions-item>
            <t-descriptions-item label="答卷人">{{ currentRespondent.respondent_name || '匿名用户' }}</t-descriptions-item>
            <t-descriptions-item label="提交时间">{{ currentRespondent.submit_time }}</t-descriptions-item>
            <t-descriptions-item label="用时">{{ currentRespondent.time_spent }}秒</t-descriptions-item>
          </t-descriptions>
        </div>

        <div class="respondent-answers">
          <h3>答题详情</h3>
          <div v-for="(answer, index) in currentRespondent.answers" :key="index" class="answer-item">
            <h4>{{ index + 1 }}. {{ answer.question_name }}</h4>
            <div v-if="answer.question_type === 1" class="choice-answer">
              <t-tag v-for="choice in answer.choices" :key="choice" variant="light">{{ choice }}</t-tag>
            </div>
            <div v-else class="text-answer">
              <div class="text-content">{{ answer.answer_text }}</div>
              <div class="text-meta">{{ answer.word_count }}字</div>
            </div>
          </div>
        </div>
      </div>
    </t-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { MessagePlugin } from 'tdesign-vue-next';

// 定义类型
interface Questionnaire {
  questionnaire_id: string | number;
  questionnaire_name: string;
  questionnaire_type: number;
  graduation_year: string;
  create_time: string;
  update_time: string;
  status: number;
}

interface Question {
  question_id: string | number;
  question_name: string;
  question_type: number; // 1: 选择题, 2: 文本题
  options?: Option[];
  answer_count?: number;
  avg_word_count?: number;
  min_word_count?: number;
  max_word_count?: number;
}

interface Option {
  option_id: string | number;
  option_text: string;
  count: number;
  percentage: number;
}

interface TextAnswer {
  answer_id: string | number;
  respondent_id: string | number;
  answer_text: string;
  word_count: number;
}

interface Response {
  response_id: string | number;
  respondent_id: string | number;
  respondent_name: string;
  submit_time: string;
  time_spent: number;
}

interface RespondentAnswer {
  question_id: string | number;
  question_name: string;
  question_type: number;
  choices?: string[];
  answer_text?: string;
  word_count?: number;
}

interface RespondentDetail {
  response_id: string | number;
  respondent_id: string | number;
  respondent_name: string;
  submit_time: string;
  time_spent: number;
  answers: RespondentAnswer[];
}

interface Pagination {
  current: number;
  pageSize: number;
  total: number;
}

interface TextFilter {
  keyword: string;
  minWords: number | null;
  maxWords: number | null;
}

const route = useRoute();
const router = useRouter();

// 问卷基本信息
const questionnaire = ref<Questionnaire>({
  questionnaire_id: '',
  questionnaire_name: '',
  questionnaire_type: 1,
  graduation_year: '',
  create_time: '',
  update_time: '',
  status: 1,
});

// 题目列表
const questions = ref<Question[]>([]);

// 答卷列表
const responses = ref<Response[]>([]);

// 活动标签页
const activeTab = ref<string>('statistics');

// 文本题筛选条件
const textFilter = reactive<TextFilter>({
  keyword: '',
  minWords: null,
  maxWords: null,
});

// 过滤后的文本题答案
const filteredTextAnswers = ref<Record<string | number, TextAnswer[]>>({});

// 文本题分页
const textPagination = reactive<Pagination>({
  current: 1,
  pageSize: 5,
  total: 0,
});

// 答卷列表分页
const responsePagination = reactive<Pagination>({
  current: 1,
  pageSize: 10,
  total: 0,
});

// 答卷列表列配置
const responseColumns = [
  { colKey: 'response_id', title: '答卷ID', width: 100 },
  { colKey: 'respondent_name', title: '答卷人', ellipsis: true },
  { colKey: 'submit_time', title: '提交时间', width: 180 },
  { colKey: 'time_spent', title: '用时(秒)', width: 100 },
  { colKey: 'operation', title: '操作', width: 100, slots: { customRender: 'operation' } },
];

// 当前查看的答卷人
const currentRespondent = ref<RespondentDetail | null>(null);
const showRespondentDialog = ref<boolean>(false);

// 问卷类型选项
const questionnaireTypes = [
  { value: 1, label: '学生问卷' },
  { value: 2, label: '教师问卷' },
  { value: 3, label: '毕业生问卷' },
];

// 状态选项
const statusOptions = [
  { value: 0, label: '草稿' },
  { value: 1, label: '已发布' },
  { value: -1, label: '已删除' },
];

// 获取问卷类型文本
const getQuestionnaireTypeText = (type: number): string => {
  const item = questionnaireTypes.find(t => t.value === type);
  return item ? item.label : '未知';
};

// 获取状态文本
const getStatusText = (status: number): string => {
  const item = statusOptions.find(s => s.value === status);
  return item ? item.label : '未知';
};

// 获取状态标签主题
const getStatusTagTheme = (status: number): string => {
  switch (status) {
    case 0: return 'warning';
    case 1: return 'success';
    case -1: return 'danger';
    default: return 'default';
  }
};

// 获取进度条颜色
const getProgressColor = (percentage: number): string => {
  if (percentage > 70) return 'var(--td-success-color)';
  if (percentage > 30) return 'var(--td-warning-color)';
  return 'var(--td-error-color)';
};

// 加载问卷详情
const loadQuestionnaireDetail = async (): Promise<void> => {
  const questionnaireId = route.params.id as string;
  try {
    // 模拟数据
    questionnaire.value = {
      questionnaire_id: questionnaireId,
      questionnaire_name: '2023届毕业生调查问卷',
      questionnaire_type: 3,
      graduation_year: '2023',
      status: 1,
      create_time: '2023-01-01 10:00:00',
      update_time: '2023-01-01 10:00:00',
    };

    questions.value = [
      {
        question_id: 1,
        question_name: '您对学校的整体满意度如何？',
        question_type: 1,
        options: [
          { option_id: 1, option_text: '非常满意', count: 120, percentage: 60 },
          { option_id: 2, option_text: '满意', count: 60, percentage: 30 },
          { option_id: 3, option_text: '一般', count: 15, percentage: 7.5 },
          { option_id: 4, option_text: '不满意', count: 5, percentage: 2.5 },
        ],
      },
      {
        question_id: 2,
        question_name: '您认为学校哪些方面需要改进？',
        question_type: 2,
        answer_count: 45,
        avg_word_count: 25,
        min_word_count: 3,
        max_word_count: 120,
      },
    ];

    // 加载答卷列表
    loadResponses();
  } catch (error) {
    MessagePlugin.error('获取问卷详情失败');
    console.error(error);
  }
};

// 加载答卷列表
const loadResponses = async (): Promise<void> => {
  try {
    // 模拟数据
    responses.value = [
      {
        response_id: 1,
        respondent_id: 101,
        respondent_name: '张三',
        submit_time: '2023-01-02 10:15:00',
        time_spent: 120,
      },
      {
        response_id: 2,
        respondent_id: 102,
        respondent_name: '李四',
        submit_time: '2023-01-02 11:30:00',
        time_spent: 85,
      },
    ];
    responsePagination.total = 2;
  } catch (error) {
    MessagePlugin.error('获取答卷列表失败');
    console.error(error);
  }
};

// 筛选文本题答案
const filterTextAnswers = async (questionId: string | number): Promise<void> => {
  try {
    // 模拟数据
    const mockAnswers: TextAnswer[] = [
      {
        answer_id: 1,
        respondent_id: 101,
        answer_text: '学校的食堂饭菜质量需要提高，图书馆开放时间可以延长',
        word_count: 24,
      },
      {
        answer_id: 2,
        respondent_id: 102,
        answer_text: '教学设施老旧，希望能更新实验室设备',
        word_count: 16,
      },
      {
        answer_id: 3,
        respondent_id: 103,
        answer_text: '校园网络速度太慢',
        word_count: 7,
      },
    ].filter(answer => {
      // 关键词筛选
      if (textFilter.keyword && !answer.answer_text.includes(textFilter.keyword)) {
        return false;
      }
      // 字数筛选
      if (textFilter.minWords !== null && answer.word_count < textFilter.minWords) {
        return false;
      }
      if (textFilter.maxWords !== null && answer.word_count > textFilter.maxWords) {
        return false;
      }
      return true;
    });

    filteredTextAnswers.value = {
      ...filteredTextAnswers.value,
      [questionId]: mockAnswers,
    };
    textPagination.total = mockAnswers.length;
  } catch (error) {
    MessagePlugin.error('获取文本答案失败');
    console.error(error);
  }
};

// 文本题分页变化
const handleTextPageChange = (pageInfo: { current: number; pageSize: number }): void => {
  textPagination.current = pageInfo.current;
  textPagination.pageSize = pageInfo.pageSize;
  // 重新加载当前问题的答案
  const question = questions.value.find(q => filteredTextAnswers.value[q.question_id]);
  if (question) {
    filterTextAnswers(question.question_id);
  }
};

// 答卷列表分页变化
const handleResponsePageChange = (pageInfo: { current: number; pageSize: number }): void => {
  responsePagination.current = pageInfo.current;
  responsePagination.pageSize = pageInfo.pageSize;
  loadResponses();
};

// 查看答卷人详情
const showRespondentDetail = async (respondentId: string | number): Promise<void> => {
  try {
    // 模拟数据
    currentRespondent.value = {
      response_id: 1,
      respondent_id: respondentId,
      respondent_name: respondentId === 101 ? '张三' : '李四',
      submit_time: '2023-01-02 10:15:00',
      time_spent: 120,
      answers: [
        {
          question_id: 1,
          question_name: '您对学校的整体满意度如何？',
          question_type: 1,
          choices: ['非常满意'],
        },
        {
          question_id: 2,
          question_name: '您认为学校哪些方面需要改进？',
          question_type: 2,
          answer_text: respondentId === 101
            ? '学校的食堂饭菜质量需要提高，图书馆开放时间可以延长'
            : '教学设施老旧，希望能更新实验室设备',
          word_count: respondentId === 101 ? 24 : 16,
        },
      ],
    };

    showRespondentDialog.value = true;
  } catch (error) {
    MessagePlugin.error('获取答卷详情失败');
    console.error(error);
  }
};

// 关闭答卷人详情弹窗
const closeRespondentDialog = (): void => {
  currentRespondent.value = null;
  showRespondentDialog.value = false;
};

// 导出数据
const exportData = (): void => {
  MessagePlugin.success('导出数据功能将在实际项目中实现');
};

// 返回上一页
const goBack = (): void => {
  router.go(-1);
};

// 初始化加载数据
onMounted(() => {
  loadQuestionnaireDetail();
});
</script>

<style lang="less" scoped>
.questionnaire-detail {
  padding: 16px;

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    h2 {
      margin: 0;
    }

    .actions {
      display: flex;
      gap: 8px;
    }
  }

  .questionnaire-info {
    margin-bottom: 16px;
  }

  .questions-card {
    margin-top: 16px;

    .question-item {
      margin-bottom: 24px;
      padding-bottom: 16px;
      border-bottom: 1px solid var(--td-component-stroke);

      &:last-child {
        border-bottom: none;
      }

      h3 {
        margin-bottom: 12px;
        color: var(--td-text-color-primary);
      }
    }

    .choice-statistics {
      .option-row {
        margin-bottom: 12px;

        .option-info {
          display: flex;
          justify-content: space-between;
          margin-bottom: 4px;

          .option-text {
            font-weight: 500;
          }

          .option-count {
            color: var(--td-text-color-secondary);
          }
        }
      }
    }

    .text-statistics {
      .summary {
        display: flex;
        gap: 8px;
        margin-bottom: 12px;
        flex-wrap: wrap;
      }

      .filter-controls {
        display: flex;
        gap: 12px;
        margin-bottom: 16px;

        :deep(.t-input),
        :deep(.t-input-number) {
          width: 180px;
        }
      }

      .text-answers {
        .text-answer-item {
          padding: 12px;
          margin-bottom: 12px;
          background-color: var(--td-bg-color-container);
          border-radius: var(--td-radius-default);

          .answer-header {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            color: var(--td-text-color-secondary);

            .respondent {
              flex: 1;
            }

            .word-count {
              margin: 0 8px;
            }
          }

          .answer-content {
            line-height: 1.6;
          }
        }

        .pagination {
          margin-top: 16px;
          justify-content: center;
        }

        .empty {
          padding: 32px 0;
        }
      }
    }
  }

  .respondent-detail {
    .respondent-info {
      margin-bottom: 20px;
    }

    .respondent-answers {
      .answer-item {
        margin-bottom: 16px;
        padding-bottom: 16px;
        border-bottom: 1px dashed var(--td-component-stroke);

        &:last-child {
          border-bottom: none;
        }

        h4 {
          margin-bottom: 8px;
          color: var(--td-text-color-primary);
        }

        .choice-answer {
          display: flex;
          gap: 8px;
        }

        .text-answer {
          .text-content {
            line-height: 1.6;
            margin-bottom: 4px;
          }

          .text-meta {
            color: var(--td-text-color-secondary);
            font-size: 12px;
          }
        }
      }
    }
  }
}
</style>
