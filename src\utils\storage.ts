// src/utils/storage.ts
export const storage = {
  // 保存数据
  set(key: string, value: any): void {
    try {
      localStorage.setItem(key, JSON.stringify(value));
    } catch (error) {
      console.error('保存数据失败:', error);
    }
  },

  // 获取数据
  get(key: string): any {
    try {
      const data = localStorage.getItem(key);
      return data ? JSON.parse(data) : null;
    } catch (error) {
      console.error('获取数据失败:', error);
      return null;
    }
  },

  // 删除数据
  remove(key: string): void {
    localStorage.removeItem(key);
  },

  // 清空所有数据
  clear(): void {
    localStorage.clear();
  },

  // 检查是否存在某个key
  has(key: string): boolean {
    return localStorage.getItem(key) !== null;
  },

  // 获取所有存储的key
  keys(): string[] {
    return Object.keys(localStorage);
  },

  // 获取存储的数据大小（字节）
  size(): number {
    let total = 0;
    for (const key in localStorage) {
      if (localStorage.hasOwnProperty(key)) {
        total += (localStorage[key].length + key.length) * 2; // 每个字符2字节
      }
    }
    return total;
  }
};
