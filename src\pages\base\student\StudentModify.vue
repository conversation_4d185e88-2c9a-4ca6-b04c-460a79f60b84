<template>
  <div class="questionnaire-container">
    <h1>问卷调查</h1>
    <div class="category-container">
      <!-- 单选题 -->
      <t-card
          v-if="categorizedQuestions.single.length > 0"
          class="category-card"
      >
        <template #header>
          <div class="category-title">单选题</div>
        </template>
        <div
            v-for="question in categorizedQuestions.single"
            :key="question.id"
            class="question-item"
        >
          <t-card
              class="question-card"
              :id="'question-card-' + question.id"
          >
            <div class="question-title">
              {{ question.title }}
              <span v-if="question.required" class="required-star">*</span>
            </div>
            <div class="options-group">
              <div
                  v-for="(option, index) in question.options"
                  :key="index"
                  class="option-item"
                  :class="{ 'selected': isOptionSelected(question.id, option) }"
              >
                <input
                    type="radio"
                    :name="`question-${question.id}`"
                    :value="option"
                    disabled
                    :checked="isOptionSelected(question.id, option)"
                />
                <span class="option-text">{{ option }}</span>
              </div>
            </div>
          </t-card>
        </div>
      </t-card>

      <!-- 多选题 -->
      <t-card
          v-if="categorizedQuestions.multiple.length > 0"
          class="category-card"
      >
        <template #header>
          <div class="category-title">多选题</div>
        </template>
        <div
            v-for="question in categorizedQuestions.multiple"
            :key="question.id"
            class="question-item"
        >
          <t-card
              class="question-card"
              :id="'question-card-' + question.id"
          >
            <div class="question-title">
              {{ question.title }}
            </div>
            <div class="options-group">
              <div
                  v-for="(option, index) in question.options"
                  :key="index"
                  class="option-item"
                  :class="{ 'selected': isOptionSelected(question.id, option) }"
              >
                <input
                    type="checkbox"
                    :value="option"
                    disabled
                    :checked="isOptionSelected(question.id, option)"
                />
                <span class="option-text">{{ option }}</span>
              </div>
            </div>
          </t-card>
        </div>
      </t-card>

      <!-- 简答题 -->
      <t-card
          v-if="categorizedQuestions.text.length > 0"
          class="category-card"
      >
        <template #header>
          <div class="category-title">简答题</div>
        </template>
        <div
            v-for="question in categorizedQuestions.text"
            :key="question.id"
            class="question-item"
        >
          <t-card
              class="question-card"
              :id="'question-card-' + question.id"
          >
            <div class="question-title">
              {{ question.title }}
            </div>
            <div v-if="getAnswer(question.id)" class="student-answer-text">
              <div class="answer-content">{{ getAnswer(question.id) }}</div>
            </div>
            <div v-else class="no-answer">
              未作答
            </div>
          </t-card>
        </div>
      </t-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import {ref, reactive, onMounted, computed} from 'vue'
import {StudentQuestionInfo, StudentRepliedInfo} from "@/api/model/student/studentQuestionl";
import {getReplyList} from "@/api/base/student/studentQuestion";
import {useRoute, useRouter} from "vue-router";

const router = useRouter();
const route = useRoute();
const pid = route.query.pid as string;

const questions = ref<StudentQuestionInfo[]>([]);
const studentAnswers = ref<StudentRepliedInfo[]>([]);

const categorizedQuestions = computed(() => {
  return {
    single: questions.value.filter(q => q.type === 1),
    multiple: questions.value.filter(q => q.type === 2),
    text: questions.value.filter(q => q.type === 3)
  }
})

const isQuestionAnswered = (questionId: number) => {
  return studentAnswers.value.some(answer => answer.id === questionId);
};

const getAnswer = (questionId: number) => {
  const answer = studentAnswers.value.find(a => a.id === questionId);
  return answer?.replied;
};

const isOptionSelected = (questionId: number, option: string) => {
  const answer = getAnswer(questionId);
  if (!answer) return false;

  if (Array.isArray(answer)) {
    return answer.includes(option);
  } else {
    return answer === option;
  }
};

const fetchData = async () => {
  try {
    const { list } = await getReplyList(pid);
    questions.value = list;

    // 这里假设API返回的数据中已经包含学生的回答
    // 如果没有，你可能需要调用另一个API来获取学生的回答
    studentAnswers.value = list.map(question => ({
      id: question.id,
      type: question.type,
      replied: question.studentAnswer || null
    })).filter(a => a.replied);

    console.log('获取的数据:', questions.value);
  } catch (e) {
    console.error('数据获取失败:', e);
    alert('获取题目数据失败，请稍后再试');
  }
};

onMounted(() => {
  fetchData();
});
</script>

<style scoped lang="less">
.questionnaire-container {
  min-height: 100vh;
  padding: 30px 20px 80px;
  background: #f5f7fa;
  position: relative;
}

h1 {
  text-align: center;
  color: #2c3e50;
  margin-bottom: 30px;
  font-size: 2.2em;
  font-weight: 600;
  letter-spacing: 1px;
  position: relative;
  padding-bottom: 15px;

  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: #409eff;
    border-radius: 2px;
  }
}

.category-container {
  display: grid;
  grid-template-columns: 1fr;
  gap: 20px;
}

.category-card {
  border-radius: 12px !important;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 1px solid rgba(64, 158, 255, 0.1);

  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(64,158,255,0.12);
  }

  :deep(.t-card__header) {
    background: linear-gradient(135deg, #409eff, #66b1ff);
    padding: 18px 25px;
  }

  .category-title {
    color: white;
    font-size: 1.2em;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;

    &::before {
      content: '• ';
      margin-right: 8px;
      color: rgba(255,255,255,0.8);
    }
  }
}

.question-card {
  margin: 15px 0;
  border: 1px solid #eef0f3;
  border-radius: 10px !important;
  transition: all 0.2s ease;
  scroll-margin-top: 20px;

  &:hover {
    border-color: #cce0ff;
  }
}

.question-title {
  font-size: 1.1em;
  font-weight: 600;
  margin-bottom: 15px;
  color: #2c3e50;
  padding-left: 10px;
  border-left: 4px solid #409eff;
}

.required-star {
  color: #ff4d4d;
  margin-left: 4px;
  font-weight: bold;
}

.options-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
}

.option-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  border-radius: 6px;
  transition: all 0.2s;

  input[type="radio"],
  input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: #409eff;
    cursor: not-allowed;
  }

  &.selected {
    .option-text {
      color: #409eff;
      font-weight: 500;
    }

    input[type="radio"],
    input[type="checkbox"] {
      accent-color: #409eff;
    }
  }
}

.option-text {
  font-size: 14px;
  color: #495057;
  font-weight: 500;
}

.student-answer-text {
  margin-top: 15px;
  padding: 15px;
  background: #f5f8ff;
  border-radius: 4px;

  .answer-content {
    white-space: pre-wrap;
    line-height: 1.6;
    color: #409eff;
    font-weight: 500;
  }
}

.no-answer {
  margin-top: 15px;
  padding: 10px;
  background: #f5f5f5;
  border-radius: 4px;
  color: #999;
  text-align: center;
}

@media (max-width: 768px) {
  .questionnaire-container {
    padding: 20px 15px 70px;
  }

  h1 {
    font-size: 1.8em;
    margin-bottom: 25px;
  }
}
</style>
