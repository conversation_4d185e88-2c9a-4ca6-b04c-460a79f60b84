import request from '@/utils/request';

// 学院信息接口定义
export interface AcademyItem {
  id: string | number;
  name: string;
  dean: string;
  creator: string;
  createTime: string;
  status: number; // 状态：0-启用，1-停用，-1-删除
  updater: string;
  updateTime: string;
  majors: number;
  classes: number;
}

// 学院选项接口
export interface AcademyOptionsVO {
  value: string | number;
  label: string;
}

// 学院表单数据接口
export interface AcademyFormData {
  id?: string | number;
  academyName: string;
  academyPresidentId?: string | number;
  status?: number;
}

/**
 * 获取学院列表（分页）
 */
export function getAcademyList(params: any) {
  return request({
    url: '/base/academy/list',
    method: 'post',
    data: params
  });
}

/**
 * 获取学院详情
 */
export function getAcademyDetail(id: string | number) {
  return request({
    url: '/base/academy/detail',
    method: 'get',
    params: { id }
  });
}

/**
 * 添加学院
 */
export function addAcademy(data: AcademyFormData) {
  return request({
    url: '/base/academy',
    method: 'post',
    data
  });
}

/**
 * 更新学院
 */
export function updateAcademy(data: any) {
  return request({
    url: '/base/academy',
    method: 'put',
    data
  });
}

/**
 * 删除学院
 */
export function deleteAcademy(id: string | number) {
  return request({
    url: `/base/academy/${id}`,
    method: 'delete'
  });
}

/**
 * 停用学院
 */
export function disableAcademy(id: string | number) {
  return request({
    url: `/base/academy/using/${id}`,
    method: 'delete'
  });
}

/**
 * 启用学院
 */
export function enableAcademy(id: string | number) {
  return request({
    url: `/base/academy/enable/${id}`,
    method: 'put'
  });
}

/**
 * 获取院长选项列表
 */
export function getDeanOptions() {
  return request({
    url: '/base/academy/dean-options',
    method: 'get'
  });
}

/**
 * 导入学院
 */
export function importAcademies(formData: FormData) {
  return request({
    url: '/base/academy/import',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}

/**
 * 导出学院
 */
export function exportAcademies(params: any) {
  return request({
    url: '/base/academy/export',
    method: 'get',
    params,
    responseType: 'blob'
  });
}

/**
 * 获取学院选项列表，适用于下拉选择
 */
export function getAcademyOptions() {
  return request({
    url: '/base/academy/academy-options-for-major',
    method: 'get'
  });
}

/**
 * 获取学院统计数据
 */
export function getAcademyStatistics(id?: string | number) {
  return request({
    url: '/base/academy/statistics',
    method: 'get',
    params: id ? { id } : {}
  });
}

/**
 * 设置学院院长
 */
export function setAcademyDean(data: { academyId: string | number; deanUserId: string | number }) {
  return request({
    url: '/base/academy/set-dean',
    method: 'put',
    data
  });
}

/**
 * 检查学院名称是否存在
 */
export function checkAcademyNameExists(academyName: string, excludeId?: string | number) {
  return request({
    url: '/base/academy/check-name',
    method: 'get',
    params: { academyName, excludeId }
  });
}
