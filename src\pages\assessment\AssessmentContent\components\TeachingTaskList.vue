<template>
  <div class="teaching-task-list">
    <!-- 页面头部 -->
    <div class="list-header">
      <div class="header-info">
        <h2 class="page-title">教学任务管理</h2>
        <div class="course-info" v-if="courseInfo">
          <t-tag theme="primary" variant="light">{{ courseInfo.courseName }}</t-tag>
          <t-tag theme="success" variant="light">{{ courseInfo.academicYear }}</t-tag>
          <t-tag theme="warning" variant="light">{{ courseInfo.semester }}</t-tag>
        </div>
      </div>
      
      <!-- 操作区域 -->
      <div class="header-actions">
        <t-space>
          <t-button theme="primary" @click="handleRefresh">
            <template #icon><t-icon name="refresh" /></template>
            刷新
          </t-button>
          <t-button 
            theme="danger" 
            variant="outline"
            :disabled="selectedTaskIds.length === 0"
            @click="handleBatchDelete"
          >
            <template #icon><t-icon name="delete" /></template>
            批量删除 ({{ selectedTaskIds.length }})
          </t-button>
        </t-space>
      </div>
    </div>

    <!-- 选择控制区域 -->
    <div class="selection-controls" v-if="teachingTasks.length > 0">
      <div class="control-left">
        <t-checkbox 
          :checked="isAllSelected"
          :indeterminate="isIndeterminate"
          @change="handleSelectAll"
        >
          全选
        </t-checkbox>
        <span class="selection-info">
          已选择 <strong>{{ selectedTaskIds.length }}</strong> / {{ teachingTasks.length }} 个任务
        </span>
      </div>
      
      <div class="control-right">
        <t-button 
          size="small" 
          variant="text" 
          @click="handleClearSelection"
          :disabled="selectedTaskIds.length === 0"
        >
          清空选择
        </t-button>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="list-content">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <t-loading size="large" text="正在加载教学任务..." />
      </div>

      <!-- 空状态 -->
      <div v-else-if="teachingTasks.length === 0" class="empty-state">
        <t-icon name="inbox" size="64px" />
        <h3>暂无教学任务</h3>
        <p>当前学年学期暂无教学任务数据</p>
        <t-button theme="primary" variant="outline" @click="handleRefresh">
          <template #icon><t-icon name="refresh" /></template>
          重新加载
        </t-button>
      </div>

      <!-- 任务列表网格 -->
      <div v-else class="task-grid">
        <TeachingTaskCard
          v-for="task in teachingTasks"
          :key="task.taskId"
          :task-data="task"
          :selected="selectedTaskIds.includes(task.taskId)"
          @toggle-selection="handleTaskToggle"
        />
      </div>
    </div>

    <!-- 分页组件 -->
    <div class="pagination-container" v-if="teachingTasks.length > 0">
      <t-pagination
        v-model:current="pagination.current"
        v-model:pageSize="pagination.pageSize"
        :total="pagination.total"
        :show-sizer="true"
        :show-jumper="true"
        :page-size-options="[10, 20, 50, 100]"
        @change="handlePageChange"
        @page-size-change="handlePageSizeChange"
      />
    </div>

    <!-- 批量操作确认对话框 -->
    <t-dialog
      v-model:visible="deleteDialogVisible"
      header="确认批量删除"
      :confirm-btn="{ content: '确认删除', theme: 'danger', loading: deleting }"
      @confirm="confirmBatchDelete"
      @cancel="deleteDialogVisible = false"
    >
      <p>您确定要删除选中的 <strong>{{ selectedTaskIds.length }}</strong> 个教学任务吗？</p>
      <p class="warning-text">此操作不可撤销，请谨慎操作。</p>
    </t-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { MessagePlugin } from 'tdesign-vue-next'
import TeachingTaskCard from './TeachingTaskCard.vue'
import { getTaskWorkList, type TaskWorkDetailVO } from '@/api/teaching/task'

// Props 定义
interface Props {
  courseId: number
  taskYear?: number
  taskTerm?: number
  courseName?: string
}

const props = withDefaults(defineProps<Props>(), {
  taskYear: new Date().getFullYear(),
  taskTerm: 1,
  courseName: '未知课程'
})

// Emits 定义
const emit = defineEmits<{
  'task-selected': [taskIds: number[]]
  'task-deleted': [taskIds: number[]]
}>()

// 响应式数据
const loading = ref(false)
const deleting = ref(false)
const deleteDialogVisible = ref(false)
const teachingTasks = ref<TaskWorkDetailVO[]>([])
const selectedTaskIds = ref<number[]>([])

// 分页数据
const pagination = ref({
  current: 1,
  pageSize: 20,
  total: 0
})

// 课程信息
const courseInfo = computed(() => ({
  courseName: props.courseName,
  academicYear: `${props.taskYear}-${props.taskYear + 1}`,
  semester: props.taskTerm === 1 ? '春季学期' : '秋季学期'
}))

// 选择状态计算
const isAllSelected = computed(() => {
  return teachingTasks.value.length > 0 && selectedTaskIds.value.length === teachingTasks.value.length
})

const isIndeterminate = computed(() => {
  return selectedTaskIds.value.length > 0 && selectedTaskIds.value.length < teachingTasks.value.length
})

// 方法定义
const loadTeachingTasks = async () => {
  if (!props.courseId) {
    MessagePlugin.warning('缺少课程信息，无法加载教学任务')
    return
  }

  try {
    loading.value = true
    const response = await getTaskWorkList(props.courseId, {
      taskYear: props.taskYear,
      taskTerm: props.taskTerm,
      current: pagination.value.current,
      pageSize: pagination.value.pageSize
    })

    if (response?.code === 200 && response.data) {
      teachingTasks.value = response.data.records || []
      pagination.value.total = response.data.total || 0
      
      if (teachingTasks.value.length === 0 && pagination.value.current === 1) {
        MessagePlugin.info('当前学年学期暂无教学任务')
      }
    } else {
      const errorMsg = response?.message || '获取教学任务列表失败'
      MessagePlugin.error(errorMsg)
      teachingTasks.value = []
      pagination.value.total = 0
    }
  } catch (error) {
    console.error('加载教学任务失败:', error)
    
    if (error instanceof Error) {
      if (error.message.includes('timeout')) {
        MessagePlugin.error('请求超时，请检查网络连接后重试')
      } else if (error.message.includes('401') || error.message.includes('403')) {
        MessagePlugin.error('权限不足，请联系管理员')
      } else {
        MessagePlugin.error(`加载失败: ${error.message}`)
      }
    } else {
      MessagePlugin.error('网络异常，请检查网络连接后重试')
    }
    
    teachingTasks.value = []
    pagination.value.total = 0
  } finally {
    loading.value = false
  }
}

const handleTaskToggle = (taskId: number, selected: boolean) => {
  // 验证任务是否存在
  const task = teachingTasks.value.find(t => t.taskId === taskId)
  if (!task) {
    MessagePlugin.warning('该教学任务已失效，请刷新页面')
    return
  }
  
  if (selected) {
    if (!selectedTaskIds.value.includes(taskId)) {
      selectedTaskIds.value.push(taskId)
    }
  } else {
    const index = selectedTaskIds.value.indexOf(taskId)
    if (index > -1) {
      selectedTaskIds.value.splice(index, 1)
    }
  }
  
  // 触发选择事件
  emit('task-selected', selectedTaskIds.value)
}

const handleSelectAll = (checked: boolean) => {
  if (checked) {
    selectedTaskIds.value = teachingTasks.value.map(task => task.taskId)
    MessagePlugin.success(`已选择 ${teachingTasks.value.length} 个教学任务`)
  } else {
    selectedTaskIds.value = []
    MessagePlugin.info('已取消全部选择')
  }
  
  emit('task-selected', selectedTaskIds.value)
}

const handleClearSelection = () => {
  const count = selectedTaskIds.value.length
  selectedTaskIds.value = []
  MessagePlugin.success(`已清空 ${count} 个选择的任务`)
  emit('task-selected', selectedTaskIds.value)
}

const handleRefresh = () => {
  selectedTaskIds.value = []
  pagination.value.current = 1
  loadTeachingTasks()
}

const handlePageChange = (pageInfo: { current: number; previous: number }) => {
  pagination.value.current = pageInfo.current
  loadTeachingTasks()
}

const handlePageSizeChange = (size: number) => {
  pagination.value.pageSize = size
  pagination.value.current = 1
  loadTeachingTasks()
}

const handleBatchDelete = () => {
  if (selectedTaskIds.value.length === 0) {
    MessagePlugin.warning('请先选择要删除的教学任务')
    return
  }
  
  deleteDialogVisible.value = true
}

const confirmBatchDelete = async () => {
  try {
    deleting.value = true
    
    // TODO: 调用批量删除API
    // await batchDeleteTasks(selectedTaskIds.value)
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const deletedCount = selectedTaskIds.value.length
    const deletedIds = [...selectedTaskIds.value]
    
    // 从列表中移除已删除的任务
    teachingTasks.value = teachingTasks.value.filter(
      task => !selectedTaskIds.value.includes(task.taskId)
    )
    
    // 清空选择
    selectedTaskIds.value = []
    
    // 更新总数
    pagination.value.total -= deletedCount
    
    deleteDialogVisible.value = false
    MessagePlugin.success(`成功删除 ${deletedCount} 个教学任务`)
    
    // 触发删除事件
    emit('task-deleted', deletedIds)
    
    // 如果当前页没有数据且不是第一页，回到上一页
    if (teachingTasks.value.length === 0 && pagination.value.current > 1) {
      pagination.value.current -= 1
      loadTeachingTasks()
    }
  } catch (error) {
    console.error('批量删除失败:', error)
    MessagePlugin.error('删除失败，请重试')
  } finally {
    deleting.value = false
  }
}

// 监听 props 变化
watch([() => props.courseId, () => props.taskYear, () => props.taskTerm], () => {
  selectedTaskIds.value = []
  pagination.value.current = 1
  loadTeachingTasks()
}, { immediate: false })

// 组件挂载时加载数据
onMounted(() => {
  loadTeachingTasks()
})

// 暴露方法给父组件
defineExpose({
  refresh: loadTeachingTasks,
  clearSelection: handleClearSelection,
  getSelectedTasks: () => selectedTaskIds.value
})
</script>

<style lang="less" scoped>
.teaching-task-list {
  padding: var(--td-comp-paddingTB-xl) var(--td-comp-paddingLR-xl);
  background: var(--td-bg-color-page);
  min-height: 100vh;
}

// 页面头部
.list-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--td-comp-margin-xl);
  padding-bottom: var(--td-comp-paddingTB-l);
  border-bottom: 1px solid var(--td-border-level-1-color);

  .header-info {
    .page-title {
      margin: 0 0 var(--td-comp-margin-m) 0;
      font-size: var(--td-font-size-title-large);
      font-weight: var(--td-font-weight-bold);
      color: var(--td-text-color-primary);
    }

    .course-info {
      display: flex;
      gap: var(--td-comp-margin-s);
      flex-wrap: wrap;
    }
  }

  .header-actions {
    flex-shrink: 0;
  }
}

// 选择控制区域
.selection-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--td-comp-paddingTB-m) var(--td-comp-paddingLR-l);
  background: var(--td-bg-color-container);
  border: 1px solid var(--td-border-level-1-color);
  border-radius: var(--td-radius-medium);
  margin-bottom: var(--td-comp-margin-l);

  .control-left {
    display: flex;
    align-items: center;
    gap: var(--td-comp-margin-m);

    .selection-info {
      font-size: var(--td-font-size-body-medium);
      color: var(--td-text-color-secondary);

      strong {
        color: var(--td-brand-color);
        font-weight: var(--td-font-weight-semi-bold);
      }
    }
  }

  .control-right {
    flex-shrink: 0;
  }
}

// 主要内容区域
.list-content {
  margin-bottom: var(--td-comp-margin-xl);
}

// 加载状态
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  background: var(--td-bg-color-container);
  border-radius: var(--td-radius-large);
  border: 1px solid var(--td-border-level-1-color);
}

// 空状态
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: var(--td-comp-paddingTB-xxl) var(--td-comp-paddingLR-xl);
  background: var(--td-bg-color-container);
  border-radius: var(--td-radius-large);
  border: 1px solid var(--td-border-level-1-color);
  text-align: center;

  :deep(.t-icon) {
    color: var(--td-text-color-disabled);
    margin-bottom: var(--td-comp-margin-l);
  }

  h3 {
    margin: 0 0 var(--td-comp-margin-s) 0;
    font-size: var(--td-font-size-title-medium);
    font-weight: var(--td-font-weight-semi-bold);
    color: var(--td-text-color-secondary);
  }

  p {
    margin: 0 0 var(--td-comp-margin-l) 0;
    font-size: var(--td-font-size-body-medium);
    color: var(--td-text-color-placeholder);
  }
}

// 任务网格布局
.task-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: var(--td-comp-margin-l);

  // 响应式设计
  @media (min-width: 1920px) {
    grid-template-columns: repeat(5, 1fr);
  }

  @media (max-width: 1600px) {
    grid-template-columns: repeat(4, 1fr);
  }

  @media (max-width: 1200px) {
    grid-template-columns: repeat(3, 1fr);
  }

  @media (max-width: 900px) {
    grid-template-columns: repeat(2, 1fr);
  }

  @media (max-width: 600px) {
    grid-template-columns: 1fr;
  }
}

// 分页容器
.pagination-container {
  display: flex;
  justify-content: center;
  padding: var(--td-comp-paddingTB-l) 0;
  background: var(--td-bg-color-container);
  border-radius: var(--td-radius-medium);
  border: 1px solid var(--td-border-level-1-color);
}

// 对话框样式
.warning-text {
  color: var(--td-warning-color);
  font-size: var(--td-font-size-body-small);
  margin-top: var(--td-comp-margin-s);
}

// 响应式布局优化
@media (max-width: 768px) {
  .teaching-task-list {
    padding: var(--td-comp-paddingTB-l) var(--td-comp-paddingLR-l);
  }

  .list-header {
    flex-direction: column;
    gap: var(--td-comp-margin-m);
    align-items: stretch;

    .header-actions {
      align-self: flex-end;
    }
  }

  .selection-controls {
    flex-direction: column;
    gap: var(--td-comp-margin-s);
    align-items: stretch;

    .control-left,
    .control-right {
      justify-content: center;
    }
  }
}
</style>
