<template>
  <Transition
    :name="animationClass"
    :duration="animationConfig.duration"
    appear
    @before-enter="onAnimationStart"
    @after-enter="onAnimationEnd"
    @before-leave="onAnimationStart"
    @after-leave="onAnimationEnd"
  >
    <div
      v-if="visible"
      class="full-screen-dialog-wrapper"
      :class="[`animation-${animationConfig.type}`, { 'overlay-blur': overlayConfig.blur }]"
      :style="animationStyles"
    >
      <t-dialog
        :visible="true"
      :header="header"
      :show-overlay="true"
      :close-on-overlay-click="false"
      :close-btn="true"
      :confirm-btn="confirmBtn"
      :cancel-btn="cancelBtn"
      class="full-screen-dialog"
      attach="body"
      placement="top"
      :destroy-on-close="false"
      :draggable="false"
      @confirm="handleConfirm"
      @cancel="handleCancel"
      @close="handleClose"
    >
        <div class="full-screen-content">
          <slot></slot>
        </div>
      </t-dialog>
    </div>
  </Transition>
</template>

<script setup lang="ts">
import { computed, watch, nextTick, ref, onMounted } from 'vue'
import type { FullScreenDialogProps, FullScreenDialogEmits, AnimationConfig, OverlayConfig } from './types'
import './override.css'

const props = withDefaults(defineProps<FullScreenDialogProps>(), {
  header: '弹窗',
  confirmBtn: () => ({ content: '确定' }),
  cancelBtn: () => ({ content: '取消' }),
  animation: () => ({
    type: 'fade',
    duration: 300,
    easing: 'ease-out',
    delay: 0
  }),
  overlay: () => ({
    blur: false,
    opacity: 0.6,
    color: 'rgba(0, 0, 0, 0.6)'
  })
})

const emit = defineEmits<FullScreenDialogEmits>()

// 动画配置
const animationConfig = computed<AnimationConfig>(() => ({
  type: props.animation?.type || 'fade',
  duration: props.animation?.duration || 300,
  easing: props.animation?.easing || 'ease-out',
  delay: props.animation?.delay || 0
}))

// 遮罩配置
const overlayConfig = computed<OverlayConfig>(() => ({
  blur: props.overlay?.blur || false,
  opacity: props.overlay?.opacity || 0.6,
  color: props.overlay?.color || `rgba(0, 0, 0, ${props.overlay?.opacity || 0.6})`
}))

// 动画类名
const animationClass = computed(() => {
  return `fullscreen-${animationConfig.value.type}`
})

// 动画样式
const animationStyles = computed(() => ({
  '--animation-duration': `${animationConfig.value.duration}ms`,
  '--animation-easing': animationConfig.value.easing,
  '--animation-delay': `${animationConfig.value.delay}ms`,
  '--overlay-opacity': overlayConfig.value.opacity,
  '--overlay-color': overlayConfig.value.color
}))

const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 动画事件处理
const onAnimationStart = () => {
  emit('animation-start')
}

const onAnimationEnd = () => {
  emit('animation-end')
}

// 监听弹窗显示状态，控制body样式
watch(visible, (newVal) => {
  nextTick(() => {
    if (newVal) {
      // 弹窗打开时
      document.body.classList.add('dialog-open')
      document.body.style.overflow = 'hidden'
      
      // 确保弹窗容器有正确的类名和样式
      const dialogElement = document.querySelector('.full-screen-dialog')
      if (dialogElement) {
        dialogElement.classList.add('full-screen-dialog')
        const dialogWrap = dialogElement.closest('.t-dialog__wrap')
        if (dialogWrap) {
          dialogWrap.classList.add('full-screen-dialog')
        }
      }
      
      // 应用遮罩样式
      const overlay = document.querySelector('.t-overlay')
      if (overlay) {
        const overlayElement = overlay as HTMLElement
        overlayElement.style.setProperty('--overlay-opacity', String(overlayConfig.value.opacity))
        overlayElement.style.setProperty('--overlay-color', overlayConfig.value.color)
        if (overlayConfig.value.blur) {
          overlayElement.classList.add('overlay-blur')
        }
      }
    } else {
      // 弹窗关闭时
      document.body.classList.remove('dialog-open')
      document.body.style.overflow = ''
    }
  })
}, { immediate: true })

const handleConfirm = () => {
  emit('confirm')
}

const handleCancel = () => {
  emit('cancel')
}

const handleClose = () => {
  // 只触发close事件，让父组件决定如何处理
  emit('close')
}
</script>

<style lang="less">
// 使用无作用域样式确保全局生效
.full-screen-dialog-wrapper {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  z-index: 3000 !important;
}

.full-screen-dialog {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  z-index: 3000 !important;
  
  .t-dialog {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    margin: 0 !important;
    max-width: none !important;
    max-height: none !important;
    border-radius: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    display: flex !important;
    flex-direction: column !important;
    z-index: 3000 !important;
    transform: none !important; // 防止 transform 影响层级
  }

  .t-dialog__header {
    padding: 20px 24px 16px 24px;
    border-bottom: 1px solid var(--td-border-level-1-color);
    background: var(--td-bg-color-container);
    flex-shrink: 0;
    position: relative;
    z-index: 1 !important;
    
    .t-dialog__title {
      font-size: 18px;
      font-weight: 600;
      color: var(--td-text-color-primary);
    }
  }

  .t-dialog__body {
    padding: 0 !important;
    flex: 1 !important;
    overflow: hidden !important;
    display: flex !important;
    flex-direction: column !important;
    position: relative;
    z-index: 1 !important;
  }

  .t-dialog__footer {
    padding: 16px 24px 20px 24px;
    border-top: 1px solid var(--td-border-level-1-color);
    background: var(--td-bg-color-container);
    display: flex !important;
    justify-content: flex-end !important;
    gap: 12px;
    flex-shrink: 0;
    position: relative;
    z-index: 1 !important;
  }

  // 覆盖层样式
  .t-overlay {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    background: var(--overlay-color, rgba(0, 0, 0, 0.6)) !important;
    z-index: 999998 !important;
    opacity: var(--overlay-opacity, 0.6) !important;
    transition: opacity var(--animation-duration, 300ms) var(--animation-easing, ease-out) !important;
  }
}

// 确保覆盖层在全局范围内生效
.t-overlay {
  &.full-screen-overlay {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    background: var(--overlay-color, rgba(0, 0, 0, 0.6)) !important;
    z-index: 999998 !important;
    opacity: var(--overlay-opacity, 0.6) !important;
  }
  
  &.overlay-blur {
    backdrop-filter: blur(8px) !important;
    -webkit-backdrop-filter: blur(8px) !important;
  }
}

// 背景模糊效果
.overlay-blur {
  .t-overlay {
    backdrop-filter: blur(8px) !important;
    -webkit-backdrop-filter: blur(8px) !important;
  }
}

// 额外的全局样式确保弹窗覆盖所有内容
body {
  &.dialog-open {
    overflow: hidden; // 防止背景滚动
  }
}

// 强制覆盖任何可能存在的高z-index元素
.full-screen-dialog {
  // 确保弹窗容器本身有足够高的层级
  & > .t-dialog {
    position: fixed !important;
    inset: 0 !important; // 等同于 top:0; right:0; bottom:0; left:0;
    margin: 0 !important;
    transform: none !important;
    z-index: 3000 !important;
  }
}

// 确保确认对话框能在全屏弹窗之上显示
.t-dialog {
  &[data-testid="confirm"] {
    z-index: 3001 !important;
  }
}

// 全局确认对话框层级设置
.t-dialog__wrap {
  &.t-dialog__wrap--confirm {
    z-index: 3001 !important;
  }
}

// 覆盖可能的TDesign默认样式
.t-dialog__wrap {
  &.full-screen-dialog {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    z-index: 3000 !important;
  }
}

.full-screen-content {
  height: 100%;
  padding: 20px; // 右侧间距
  overflow-y: auto;

  // 自定义滚动条样式
  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: var(--td-bg-color-page);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: var(--td-text-color-placeholder);
    border-radius: 4px;
    
    &:hover {
      background: var(--td-text-color-secondary);
    }
  }

  // Firefox 滚动条样式
  scrollbar-width: none;
  scrollbar-color: var(--td-text-color-placeholder) var(--td-bg-color-page);
}

// 确保TDesign下拉组件的弹出层有正确的层级
.full-screen-dialog {
  // 针对select组件的弹出层
  :deep(.t-select__popup),
  :deep(.t-popup),
  :deep(.t-select-option) {
    z-index: 1000001 !important;
    position: fixed !important;
  }
  
  // 针对dropdown组件
  :deep(.t-dropdown),
  :deep(.t-dropdown__menu) {
    z-index: 1000001 !important;
    position: fixed !important;
  }
}

// 全局TDesign弹出层样式调整
:deep(.t-popup-container) {
  z-index: 1000001 !important;
  
  .t-popup {
    z-index: 1000001 !important;
  }
}

:deep(.t-popup__content) {
  z-index: 1000001 !important;
}

:deep(.t-select__popup-reference) {
  z-index: 1000001 !important;
}

// 确保全局弹出层都有足够高的层级
:deep(.t-popup) {
  z-index: 1000001 !important;
}

:deep(.t-select-option) {
  z-index: 1000001 !important;
}

// ==================== 动画样式 ====================

// 1. 淡入淡出动画 (fade)
.fullscreen-fade-enter-active,
.fullscreen-fade-leave-active {
  transition: opacity var(--animation-duration, 300ms) var(--animation-easing, ease-out) var(--animation-delay, 0ms);
}

.fullscreen-fade-enter-from,
.fullscreen-fade-leave-to {
  opacity: 0;
}

.fullscreen-fade-enter-to,
.fullscreen-fade-leave-from {
  opacity: 1;
}

// 2. 滑入滑出动画 (slide)
.fullscreen-slide-enter-active,
.fullscreen-slide-leave-active {
  transition: transform var(--animation-duration, 300ms) var(--animation-easing, ease-out) var(--animation-delay, 0ms);
}

.fullscreen-slide-enter-from {
  transform: translateY(-100%);
}

.fullscreen-slide-leave-to {
  transform: translateY(-100%);
}

.fullscreen-slide-enter-to,
.fullscreen-slide-leave-from {
  transform: translateY(0);
}

// 3. 缩放动画 (scale)
.fullscreen-scale-enter-active,
.fullscreen-scale-leave-active {
  transition: transform var(--animation-duration, 300ms) var(--animation-easing, ease-out) var(--animation-delay, 0ms);
}

.fullscreen-scale-enter-from,
.fullscreen-scale-leave-to {
  transform: scale(0.8);
}

.fullscreen-scale-enter-to,
.fullscreen-scale-leave-from {
  transform: scale(1);
}

// 4. 放大动画 (zoom)
.fullscreen-zoom-enter-active,
.fullscreen-zoom-leave-active {
  transition: all var(--animation-duration, 300ms) var(--animation-easing, ease-out) var(--animation-delay, 0ms);
}

.fullscreen-zoom-enter-from,
.fullscreen-zoom-leave-to {
  transform: scale(0.3);
  opacity: 0;
}

.fullscreen-zoom-enter-to,
.fullscreen-zoom-leave-from {
  transform: scale(1);
  opacity: 1;
}

// 5. 弹跳动画 (bounce)
.fullscreen-bounce-enter-active {
  animation: fullscreen-bounce-in var(--animation-duration, 600ms) var(--animation-easing, ease-out) var(--animation-delay, 0ms);
}

.fullscreen-bounce-leave-active {
  animation: fullscreen-bounce-out var(--animation-duration, 300ms) var(--animation-easing, ease-in) var(--animation-delay, 0ms);
}

@keyframes fullscreen-bounce-in {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
  70% {
    transform: scale(0.9);
    opacity: 0.9;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes fullscreen-bounce-out {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(0.3);
    opacity: 0;
  }
}

// 6. 翻转动画 (flip)
.fullscreen-flip-enter-active,
.fullscreen-flip-leave-active {
  transition: transform var(--animation-duration, 600ms) var(--animation-easing, ease-out) var(--animation-delay, 0ms);
  transform-style: preserve-3d;
}

.fullscreen-flip-enter-from {
  transform: rotateY(-90deg);
}

.fullscreen-flip-leave-to {
  transform: rotateY(90deg);
}

.fullscreen-flip-enter-to,
.fullscreen-flip-leave-from {
  transform: rotateY(0deg);
}

// 7. 无动画 (none)
.fullscreen-none-enter-active,
.fullscreen-none-leave-active {
  transition: none;
}

// 动画增强效果
.animation-fade,
.animation-slide,
.animation-scale,
.animation-zoom,
.animation-bounce,
.animation-flip {
  .t-dialog {
    will-change: transform, opacity;
  }
}

// 预设动画组合
.animation-fade.overlay-blur {
  .t-overlay {
    transition: opacity var(--animation-duration, 300ms) var(--animation-easing, ease-out), backdrop-filter var(--animation-duration, 300ms) var(--animation-easing, ease-out);
  }
}

// 响应式动画调整
@media (max-width: 768px) {
  .fullscreen-slide-enter-from {
    transform: translateY(100%);
  }
  
  .fullscreen-slide-leave-to {
    transform: translateY(100%);
  }
}

// 减少动画的用户偏好设置
@media (prefers-reduced-motion: reduce) {
  .fullscreen-fade-enter-active,
  .fullscreen-fade-leave-active,
  .fullscreen-slide-enter-active,
  .fullscreen-slide-leave-active,
  .fullscreen-scale-enter-active,
  .fullscreen-scale-leave-active,
  .fullscreen-zoom-enter-active,
  .fullscreen-zoom-leave-active,
  .fullscreen-bounce-enter-active,
  .fullscreen-bounce-leave-active,
  .fullscreen-flip-enter-active,
  .fullscreen-flip-leave-active {
    transition: none !important;
    animation: none !important;
  }
}
</style> 