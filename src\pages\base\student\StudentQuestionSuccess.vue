<template>
  <div class="success-container">
    <div class="success-content">
      <div class="icon-wrapper">
        <CheckCircleFilledIcon class="success-icon" />
      </div>

      <h1 class="title">提交成功！</h1>
      <p class="subtitle">感谢您的参与，问卷已成功提交</p>

      <div class="action-buttons">
        <t-button
            theme="primary"
            shape="round"
            class="action-btn"
            @click="handleBackHome"
        >
          返回首页
        </t-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router';

const router = useRouter();

const handleBackHome = () => {
  router.push('/');
};

</script>

<style scoped lang="less">
.success-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #e6f7ff 100%);
  padding: 20px;
  animation: fadeIn 0.5s ease;

  .success-content {
    text-align: center;
    max-width: 600px;
    padding: 40px;
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(64, 158, 255, 0.1);
    transform: translateY(0);
    animation: floatUp 0.6s ease;

    .icon-wrapper {
      margin-bottom: 24px;

      .success-icon {
        font-size: 80px;
        color: #2ed573;
        animation: scaleIn 0.4s cubic-bezier(0.68, -0.55, 0.27, 1.55);
      }
    }

    .title {
      color: #2c3e50;
      font-size: 32px;
      margin-bottom: 16px;
      font-weight: 600;
    }

    .subtitle {
      color: #7f8c8d;
      font-size: 18px;
      margin-bottom: 32px;
      line-height: 1.6;
    }

    .action-buttons {
      display: flex;
      gap: 20px;
      justify-content: center;

      .action-btn {
        padding: 12px 32px;
        font-size: 16px;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 5px 15px rgba(46, 213, 115, 0.2);
        }
      }
    }
  }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes floatUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

@media (max-width: 768px) {
  .success-content {
    padding: 30px 20px;
    width: 90%;

    .title {
      font-size: 24px;
    }

    .subtitle {
      font-size: 16px;
    }

    .action-buttons {
      flex-direction: column;

      .action-btn {
        width: 100%;
      }
    }
  }
}
</style>
