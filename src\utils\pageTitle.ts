import pinia from '@/store'
import { titleReverse, titleSeparator } from '@/config'
import { useSettingStore } from '@/store/modules/setting'

/**
 * @description 设置标题
 * @param pageTitle
 * @returns {string}
 */
export default function getPageTitle(pageTitle: string | undefined) {
  const { getTitle } = useSettingStore(pinia)
  let newTitles = []
  if (pageTitle) newTitles.push(pageTitle)
  if (getTitle) newTitles.push(getTitle)
  if (titleReverse) newTitles = newTitles.reverse()
  return newTitles.join(titleSeparator)
}
