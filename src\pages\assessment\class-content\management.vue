<template>
  <div class="class-assessment-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="back-section">
          <t-button variant="text" @click="handleGoBack">
            <template #icon>
              <t-icon name="chevron-left" />
            </template>
            返回班级列表
          </t-button>
        </div>
        <h1 class="page-title">
          <t-icon name="assignment" />
          {{ currentClass?.className }} 考核管理
        </h1>
      </div>
      <div class="header-actions">
        <t-button theme="default" variant="outline" @click="refreshData">
          <template #icon>
            <t-icon name="refresh" />
          </template>
          刷新
        </t-button>
      </div>
    </div>

    <!-- 班级信息和切换 -->
    <div class="class-info-section">
      <div class="class-basic-info">
        <div class="class-details">
          <h2>{{ currentClass?.className }}</h2>
          <div class="class-meta">
            <div class="meta-item">
              <span class="label">授课教师：</span>
              <span class="value">{{ currentClass?.teacherName }}</span>
            </div>
            <div class="meta-item">
              <span class="label">学生人数：</span>
              <span class="value">{{ currentClass?.studentCount }}人</span>
            </div>
            <div class="meta-item">
              <span class="label">总学时：</span>
              <span class="value">{{ currentClass?.totalHours }}h</span>
            </div>
          </div>
        </div>
        <div class="class-actions">
          <t-button theme="primary" variant="outline" @click="handleSwitchClass">
            <template #icon>
              <t-icon name="swap" />
            </template>
            切换班级
          </t-button>
        </div>
      </div>
    </div>

    <!-- 考核统计 -->
    <div class="assessment-stats-section">
      <div class="section-title">
        <h2>考核统计</h2>
      </div>
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-icon">
            <t-icon name="user" />
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ assessmentStats.totalStudents }}</div>
            <div class="stat-label">学生人数</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon">
            <t-icon name="assignment" />
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ assessmentStats.totalAssessments }}</div>
            <div class="stat-label">考核项目</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon">
            <t-icon name="edit" />
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ assessmentStats.pendingInput }}</div>
            <div class="stat-label">待录入</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon">
            <t-icon name="check-circle" />
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ assessmentStats.completionRate }}%</div>
            <div class="stat-label">完成率</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 考核内容列表 -->
    <div class="assessment-content-section">
      <div class="section-header">
        <h2>考核内容</h2>
        <div class="header-filters">
          <t-select
            v-model="selectedType"
            placeholder="考核类型"
            style="width: 150px;"
            clearable
            @change="filterAssessments"
          >
            <t-option value="平时成绩" label="平时成绩" />
            <t-option value="作业" label="作业" />
            <t-option value="实验" label="实验" />
            <t-option value="期中考试" label="期中考试" />
            <t-option value="期末考试" label="期末考试" />
          </t-select>
          <t-select
            v-model="selectedInputMode"
            placeholder="录入方式"
            style="width: 120px;"
            clearable
            @change="filterAssessments"
          >
            <t-option value="direct" label="直接录入" />
            <t-option value="detailed" label="详细录入" />
          </t-select>
        </div>
      </div>

      <div class="content-table-container">
        <t-loading :loading="contentLoading">
          <div v-if="filteredAssessments.length === 0" class="empty-content">
            <t-icon name="inbox" size="48px" />
            <p>暂无考核内容</p>
          </div>
          <t-table
            v-else
            :data="filteredAssessments"
            :columns="contentColumns"
            :pagination="contentPagination"
            row-key="id"
            @page-change="handlePageChange"
          >
            <template #inputMode="{ row }">
              <t-tag size="small" :theme="row.inputMode === 'direct' ? 'primary' : 'success'" variant="light">
                {{ row.inputMode === 'direct' ? '直接录入' : '详细录入' }}
              </t-tag>
            </template>

            <template #progress="{ row }">
              <div class="progress-cell">
                <div class="progress-text">
                  {{ row.inputCount }}/{{ row.totalCount }}
                </div>
                <t-progress
                  :percentage="Math.round((row.inputCount / row.totalCount) * 100)"
                  :color="getProgressColor(row.inputCount / row.totalCount)"
                  size="small"
                />
              </div>
            </template>

            <template #lastUpdate="{ row }">
              <div class="time-cell">
                <div class="time-value">{{ formatDate(row.lastUpdate) }}</div>
                <div class="time-detail">{{ formatTime(row.lastUpdate) }}</div>
              </div>
            </template>

            <template #actions="{ row }">
              <t-space>
                <t-button
                  theme="primary"
                  variant="text"
                  size="small"
                  @click="handleGradeManagement(row)"
                >
                  成绩管理
                </t-button>
                <t-button
                  theme="success"
                  variant="text"
                  size="small"
                  @click="handleStatisticsAnalysis(row)"
                >
                  统计分析
                </t-button>
              </t-space>
            </template>
          </t-table>
        </t-loading>
      </div>
    </div>

    <!-- 班级切换弹窗 -->
    <ClassSelector
      v-model:visible="classSelectorVisible"
      :current-class-id="currentClassId"
      :classes="allClasses"
      @select="handleClassSelect"
    />

    <!-- 成绩管理弹窗 -->
    <GradeManagementDialog
      v-model:visible="gradeManagementVisible"
      :assessment-content="assessmentContent"
      :class-info="classInfo"
      :score-status="scoreStatus"
      :grade-list="gradeList"
      :grade-stats="gradeStats"
      :question-structure="questionStructure"
      :loading="loading"
      v-model:visible-question-types="visibleQuestionTypes"
      :all-question-types="allQuestionTypes"
      :all-question-type-names="allQuestionTypeNames"
      @save-changes="handleSaveChanges"
      @cancel-changes="handleCancelChanges"
      @submit-grades="handleSubmitGrades"
      @refresh="handleRefreshGrades"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { MessagePlugin } from 'tdesign-vue-next'
import { ClassSelector, GradeManagementDialog } from './components'
import { useAssessment } from './composables/useAssessment'
import type { AssessmentContent } from './types'
import type { WorklistItem } from '@/api/base/classes'
import * as dictUtil from '@/utils/dictUtil'

// 路由相关
const route = useRoute()
const router = useRouter()

// 当前班级ID
const currentClassId = computed(() => route.params.classId as string)

// 响应式数据
const contentLoading = ref(false)
const classSelectorVisible = ref(false)
const gradeManagementVisible = ref(false)
const selectedType = ref('')
const selectedInputMode = ref('')
const currentAssessment = ref<any>(null)

// 当前班级信息
const currentClass = ref<WorklistItem | null>(null)

// 所有班级列表
const allClasses = ref<WorklistItem[]>([])

// 考核统计数据
const assessmentStats = ref({
  totalStudents: 0,
  totalAssessments: 0,
  pendingInput: 0,
  completionRate: 0
})

// 考核内容列表
const assessmentContents = ref([
  {
    id: '1',
    sectionName: '期中考试',
    title: '数据结构期中测试',
    inputMode: 'direct',
    inputCount: 45,
    totalCount: 52,
    completionRate: 87,
    lastUpdate: '2024-11-20 14:30:00'
  },
  {
    id: '2',
    sectionName: '课程作业',
    title: '链表实现作业',
    inputMode: 'detailed',
    inputCount: 38,
    totalCount: 52,
    completionRate: 73,
    lastUpdate: '2024-11-19 16:45:00'
  },
  {
    id: '3',
    sectionName: '实验',
    title: '二叉树遍历实验',
    inputMode: 'detailed',
    inputCount: 52,
    totalCount: 52,
    completionRate: 100,
    lastUpdate: '2024-11-18 10:20:00'
  },
  {
    id: '4',
    sectionName: '期末考试',
    title: '数据结构期末考试',
    inputMode: 'direct',
    inputCount: 0,
    totalCount: 52,
    completionRate: 0,
    lastUpdate: '2024-11-15 09:00:00'
  }
])

// 1. 考核内容表格列配置
const contentColumns = [
  { colKey: 'sectionName', title: '考核环节', width: 120 },
  { colKey: 'title', title: '考核内容', width: 200 },
  { colKey: 'inputMode', title: '录入方式', width: 100 },
  { colKey: 'inputCount', title: '已录入人数', width: 150 },
  { colKey: 'completionRate', title: '完成率', width: 100 },
  { colKey: 'lastUpdate', title: '最后更新', width: 140 },
  { colKey: 'actions', title: '操作', width: 160 }
]

// 2. 分页配置
const contentPagination = reactive({
  current: 1,
  pageSize: 10,
  total: assessmentContents.value.length
})

// 3. 过滤后的考核内容（只保留这一个声明）
const filteredAssessments = computed(() => {
  let filtered = [...assessmentContents.value]
  if (selectedType.value) {
    filtered = filtered.filter(item => item.sectionName.includes(selectedType.value))
  }
  if (selectedInputMode.value) {
    filtered = filtered.filter(item => item.inputMode === selectedInputMode.value)
  }
  contentPagination.total = filtered.length
  const start = (contentPagination.current - 1) * contentPagination.pageSize
  const end = start + contentPagination.pageSize
  return filtered.slice(start, end)
})

// 成绩管理相关状态
const assessmentContent = ref({ title: '期末考试', inputMode: 'detailed' })
const classInfo = ref({ className: 'RB软工数241', studentCount: 2 })
const scoreStatus = ref(1)
const loading = ref(false)

// 详细录入模式模拟数据
let rawQuestionStructure = [
  {
    questionType: '1',
    questions: [
      {
        questionNumber: '1',
        questionId: 101,
        subItems: [
          { repositoryAnswerId: 1001, subNumber: 1 },
          { repositoryAnswerId: 1002, subNumber: 2 }
        ]
      },
      {
        questionNumber: '2',
        questionId: 102,
        subItems: [
          { repositoryAnswerId: 1003, subNumber: 1 }
        ]
      }
    ]
  },
  {
    questionType: '2',
    questions: [
      {
        questionNumber: '3',
        questionId: 103,
        subItems: [
          { repositoryAnswerId: 1004, subNumber: 1 }
        ]
      }
    ]
  },
  {
    questionType: '3',
    questions: [
      {
        questionNumber: '4',
        questionId: 104,
        subItems: [
          { repositoryAnswerId: 1005, subNumber: 1 },
          { repositoryAnswerId: 1006, subNumber: 2 }
        ]
      }
    ]
  },
  {
    questionType: '4',
    questions: [
      {
        questionNumber: '5',
        questionId: 105,
        subItems: [
          { repositoryAnswerId: 1007, subNumber: 1 }
        ]
      }
    ]
  },
  {
    questionType: '5',
    questions: [
      {
        questionNumber: '6',
        questionId: 106,
        subItems: [
          { repositoryAnswerId: 1008, subNumber: 1 }
        ]
      }
    ]
  },
  {
    questionType: '6',
    questions: [
      {
        questionNumber: '7',
        questionId: 107,
        subItems: [
          { repositoryAnswerId: 1009, subNumber: 1 },
          { repositoryAnswerId: 1010, subNumber: 2 }
        ]
      }
    ]
  }
]

const questionStructure = ref([])
const visibleQuestionTypes = ref<string[]>([])
const allQuestionTypes = ref<string[]>([])
const allQuestionTypeNames = ref<Record<string, string>>({})

async function initQuestionStructureAndTypes() {
  await dictUtil.initializeGlobalDict();
  const typeNames: string[] = [];
  const typeNos: string[] = [];
  const typeNameMap: Record<string, string> = {};
  for (const typeGroup of rawQuestionStructure) {
    const name = await dictUtil.getDictLabelByTypeTitle('题目类型', typeGroup.questionType)
    const typeNo = typeGroup.questionType;
    typeNames.push(name || `未知类型(${typeNo})`);
    typeNos.push(typeNo);
    typeNameMap[typeNo] = name || `未知类型(${typeNo})`;
  }
  questionStructure.value = rawQuestionStructure.map((typeGroup, idx) => ({
    ...typeGroup,
    questionTypeName: typeNames[idx]
  }))
  visibleQuestionTypes.value = [...typeNos];
  allQuestionTypes.value = [...typeNos];
  allQuestionTypeNames.value = { ...typeNameMap };
  console.log('题型编号:', typeNos, '题型名称:', typeNames, '映射:', typeNameMap);
}

const gradeList = ref([
  {
    studentId: 1,
    studentNumber: '20230001',
    studentName: '张三',
    classId: 1,
    className: 'RB软工数241',
    totalScore: 85,
    scoreGrade: '良好',
    detailScores: [
      { repositoryAnswerId: 1001, score: 4 },
      { repositoryAnswerId: 1002, score: 3 },
      { repositoryAnswerId: 1003, score: 5 },
      { repositoryAnswerId: 1004, score: 2 },
      { repositoryAnswerId: 1005, score: 3 },
      { repositoryAnswerId: 1006, score: 2 },
      { repositoryAnswerId: 1007, score: 4 },
      { repositoryAnswerId: 1008, score: 5 },
      { repositoryAnswerId: 1009, score: 3 },
      { repositoryAnswerId: 1010, score: 2 }
    ],
    entryStatus: 'ENTERED'
  },
  {
    studentId: 2,
    studentNumber: '20230002',
    studentName: '李四',
    classId: 1,
    className: 'RB软工数241',
    totalScore: 100,
    scoreGrade: '满分',
    detailScores: [
      { repositoryAnswerId: 1001, score: 10 },
      { repositoryAnswerId: 1002, score: 10 },
      { repositoryAnswerId: 1003, score: 10 },
      { repositoryAnswerId: 1004, score: 10 },
      { repositoryAnswerId: 1005, score: 10 },
      { repositoryAnswerId: 1006, score: 10 },
      { repositoryAnswerId: 1007, score: 10 },
      { repositoryAnswerId: 1008, score: 10 },
      { repositoryAnswerId: 1009, score: 10 },
      { repositoryAnswerId: 1010, score: 10 }
    ],
    entryStatus: 'ENTERED'
  },
  {
    studentId: 3,
    studentNumber: '20230003',
    studentName: '王五',
    classId: 1,
    className: 'RB软工数241',
    totalScore: 0,
    scoreGrade: '不及格',
    detailScores: [
      { repositoryAnswerId: 1001, score: 0 },
      { repositoryAnswerId: 1002, score: 0 },
      { repositoryAnswerId: 1003, score: 0 },
      { repositoryAnswerId: 1004, score: 0 },
      { repositoryAnswerId: 1005, score: 0 },
      { repositoryAnswerId: 1006, score: 0 },
      { repositoryAnswerId: 1007, score: 0 },
      { repositoryAnswerId: 1008, score: 0 },
      { repositoryAnswerId: 1009, score: 0 },
      { repositoryAnswerId: 1010, score: 0 }
    ],
    entryStatus: 'ENTERED'
  },
  {
    studentId: 4,
    studentNumber: '20230004',
    studentName: '赵六',
    classId: 1,
    className: 'RB软工数241',
    totalScore: 65,
    scoreGrade: '及格',
    detailScores: [
      { repositoryAnswerId: 1001, score: 2 },
      { repositoryAnswerId: 1002, score: 2 },
      { repositoryAnswerId: 1003, score: 3 },
      { repositoryAnswerId: 1004, score: 1 },
      { repositoryAnswerId: 1005, score: 2 },
      { repositoryAnswerId: 1006, score: 1 },
      { repositoryAnswerId: 1007, score: 2 },
      { repositoryAnswerId: 1008, score: 2 },
      { repositoryAnswerId: 1009, score: 1 },
      { repositoryAnswerId: 1010, score: 1 }
    ],
    entryStatus: 'ENTERED'
  },
  {
    studentId: 5,
    studentNumber: '20230005',
    studentName: '钱七',
    classId: 1,
    className: 'RB软工数241',
    totalScore: 92,
    scoreGrade: '优秀',
    detailScores: [
      { repositoryAnswerId: 1001, score: 5 },
      { repositoryAnswerId: 1002, score: 5 },
      { repositoryAnswerId: 1003, score: 5 },
      { repositoryAnswerId: 1004, score: 4 },
      { repositoryAnswerId: 1005, score: 5 },
      { repositoryAnswerId: 1006, score: 4 },
      { repositoryAnswerId: 1007, score: 5 },
      { repositoryAnswerId: 1008, score: 5 },
      { repositoryAnswerId: 1009, score: 5 },
      { repositoryAnswerId: 1010, score: 4 }
    ],
    entryStatus: 'ENTERED'
  },
  {
    studentId: 6,
    studentNumber: '20230006',
    studentName: '孙八',
    classId: 1,
    className: 'RB软工数241',
    totalScore: 55,
    scoreGrade: '及格',
    detailScores: [
      { repositoryAnswerId: 1001, score: 1 },
      { repositoryAnswerId: 1002, score: 1 },
      { repositoryAnswerId: 1003, score: 2 },
      { repositoryAnswerId: 1004, score: 1 },
      { repositoryAnswerId: 1005, score: 1 },
      { repositoryAnswerId: 1006, score: 1 },
      { repositoryAnswerId: 1007, score: 1 },
      { repositoryAnswerId: 1008, score: 2 },
      { repositoryAnswerId: 1009, score: 1 },
      { repositoryAnswerId: 1010, score: 1 }
    ],
    entryStatus: 'ENTERED'
  },
  {
    studentId: 7,
    studentNumber: '20230007',
    studentName: '周九',
    classId: 1,
    className: 'RB软工数241',
    totalScore: 80,
    scoreGrade: '良好',
    detailScores: [
      { repositoryAnswerId: 1001, score: 4 },
      { repositoryAnswerId: 1002, score: 4 },
      { repositoryAnswerId: 1003, score: 4 },
      { repositoryAnswerId: 1004, score: 3 },
      { repositoryAnswerId: 1005, score: 3 },
      { repositoryAnswerId: 1006, score: 3 },
      { repositoryAnswerId: 1007, score: 4 },
      { repositoryAnswerId: 1008, score: 4 },
      { repositoryAnswerId: 1009, score: 3 },
      { repositoryAnswerId: 1010, score: 3 }
    ],
    entryStatus: 'ENTERED'
  },
  {
    studentId: 8,
    studentNumber: '20230008',
    studentName: '吴十',
    classId: 1,
    className: 'RB软工数241',
    totalScore: 60,
    scoreGrade: '及格',
    detailScores: [
      { repositoryAnswerId: 1001, score: 2 },
      { repositoryAnswerId: 1002, score: 2 },
      { repositoryAnswerId: 1003, score: 2 },
      { repositoryAnswerId: 1004, score: 2 },
      { repositoryAnswerId: 1005, score: 2 },
      { repositoryAnswerId: 1006, score: 2 },
      { repositoryAnswerId: 1007, score: 2 },
      { repositoryAnswerId: 1008, score: 2 },
      { repositoryAnswerId: 1009, score: 2 },
      { repositoryAnswerId: 1010, score: 2 }
    ],
    entryStatus: 'ENTERED'
  },
  {
    studentId: 9,
    studentNumber: '20230009',
    studentName: '郑十一',
    classId: 1,
    className: 'RB软工数241',
    totalScore: 88,
    scoreGrade: '良好',
    detailScores: [
      { repositoryAnswerId: 1001, score: 5 },
      { repositoryAnswerId: 1002, score: 4 },
      { repositoryAnswerId: 1003, score: 5 },
      { repositoryAnswerId: 1004, score: 4 },
      { repositoryAnswerId: 1005, score: 4 },
      { repositoryAnswerId: 1006, score: 4 },
      { repositoryAnswerId: 1007, score: 5 },
      { repositoryAnswerId: 1008, score: 4 },
      { repositoryAnswerId: 1009, score: 4 },
      { repositoryAnswerId: 1010, score: 3 }
    ],
    entryStatus: 'ENTERED'
  },
  {
    studentId: 10,
    studentNumber: '20230010',
    studentName: '冯十二',
    classId: 1,
    className: 'RB软工数241',
    totalScore: 73,
    scoreGrade: '及格',
    detailScores: [
      { repositoryAnswerId: 1001, score: 3 },
      { repositoryAnswerId: 1002, score: 3 },
      { repositoryAnswerId: 1003, score: 3 },
      { repositoryAnswerId: 1004, score: 2 },
      { repositoryAnswerId: 1005, score: 3 },
      { repositoryAnswerId: 1006, score: 2 },
      { repositoryAnswerId: 1007, score: 3 },
      { repositoryAnswerId: 1008, score: 3 },
      { repositoryAnswerId: 1009, score: 2 },
      { repositoryAnswerId: 1010, score: 2 }
    ],
    entryStatus: 'ENTERED'
  },
  // 特殊情况：部分未录入
  {
    studentId: 11,
    studentNumber: '20230011',
    studentName: '蒋十三',
    classId: 1,
    className: 'RB软工数241',
    totalScore: 50,
    scoreGrade: '不及格',
    detailScores: [
      { repositoryAnswerId: 1001, score: 2 },
      { repositoryAnswerId: 1002, score: 2 },
      { repositoryAnswerId: 1003, score: 2 },
      // 缺少1004,1005,1006,1007,1008,1009,1010
    ],
    entryStatus: 'PARTIALLY_ENTERED'
  },
  // 特殊情况：高分但部分未录入
  {
    studentId: 12,
    studentNumber: '20230012',
    studentName: '褚十四',
    classId: 1,
    className: 'RB软工数241',
    totalScore: 95,
    scoreGrade: '优秀',
    detailScores: [
      { repositoryAnswerId: 1001, score: 10 },
      { repositoryAnswerId: 1002, score: 10 },
      { repositoryAnswerId: 1003, score: 10 },
      { repositoryAnswerId: 1004, score: 10 },
      { repositoryAnswerId: 1005, score: 10 },
      { repositoryAnswerId: 1006, score: 10 },
      { repositoryAnswerId: 1007, score: 10 },
      { repositoryAnswerId: 1008, score: 10 },
      // 缺少1009,1010
    ],
    entryStatus: 'PARTIALLY_ENTERED'
  }
])

const gradeStats = ref({
  averageScore: 87.5,
  maxScore: 90,
  minScore: 85,
  submittedCount: 2,
  pendingCount: 0
})

// 加载班级数据
const loadClassData = async () => {
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 300))

    // 修正 allClasses mock 数据结构，补全 WorklistItem 所需字段
    allClasses.value = [
      {
        id: '1',
        courseId: '1',
        courseName: '数据结构与算法',
        taskNumber: 1,
        classId: '1',
        className: 'RB软工数241',
        teacherId: '1001',
        teacherName: '张老师',
        teacherTitle: '教授',
        teacherAcademyName: '计算机科学与技术学院',
        taskYear: 2024,
        taskTerm: 1,
        studentCount: 20,
        teachWeek: 18,
        weekHours: 4,
        totalHours: 100,
        courseLeaderId: '1001',
        courseLeaderName: '张老师',
        scheduleInfo: []
      },
      {
        id: '2',
        courseId: '1',
        courseName: '数据结构与算法',
        taskNumber: 2,
        classId: '2',
        className: 'RB软工数242',
        teacherId: '1002',
        teacherName: '李老师',
        teacherTitle: '副教授',
        teacherAcademyName: '计算机科学与技术学院',
        taskYear: 2024,
        taskTerm: 1,
        studentCount: 25,
        teachWeek: 18,
        weekHours: 4,
        totalHours: 120,
        courseLeaderId: '1001',
        courseLeaderName: '张老师',
        scheduleInfo: []
      },
      {
        id: '3',
        courseId: '1',
        courseName: '数据结构与算法',
        taskNumber: 3,
        classId: '3',
        className: 'RB软工数243',
        teacherId: '1003',
        teacherName: '王老师',
        teacherTitle: '讲师',
        teacherAcademyName: '计算机科学与技术学院',
        taskYear: 2024,
        taskTerm: 1,
        studentCount: 30,
        teachWeek: 18,
        weekHours: 4,
        totalHours: 150,
        courseLeaderId: '1001',
        courseLeaderName: '张老师',
        scheduleInfo: []
      }
    ]
    currentClass.value = allClasses.value.find(c => c.classId === currentClassId.value) || null

    if (!currentClass.value) {
      MessagePlugin.error('班级信息不存在')
      handleGoBack()
      return
    }

    // 计算统计数据
    const totalStudents = currentClass.value.studentCount
    const totalAssessments = assessmentContents.value.length
    const pendingInput = assessmentContents.value.reduce((sum: number, item: any) => sum + (item.totalCount - item.inputCount), 0)
    const totalInputs = assessmentContents.value.reduce((sum: number, item: any) => sum + item.inputCount, 0)
    const totalPossible = assessmentContents.value.reduce((sum: number, item: any) => sum + item.totalCount, 0)
    const completionRate = totalPossible > 0 ? Math.round((totalInputs / totalPossible) * 100) : 0

    assessmentStats.value = {
      totalStudents,
      totalAssessments,
      pendingInput,
      completionRate
    }

  } catch (error) {
    console.error('加载班级数据失败:', error)
    MessagePlugin.error('加载班级数据失败')
  }
}

// 加载考核内容数据
const loadAssessmentData = async () => {
  contentLoading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))

    assessmentContents.value = [...assessmentContents.value] // 确保数据不变
    contentPagination.total = assessmentContents.value.length

  } catch (error) {
    console.error('加载考核数据失败:', error)
    MessagePlugin.error('加载考核数据失败')
  } finally {
    contentLoading.value = false
  }
}

// 获取进度条颜色
const getProgressColor = (ratio: number) => {
  if (ratio >= 0.8) return '#00a870'  // 绿色
  if (ratio >= 0.5) return '#ed7b2f'  // 橙色
  return '#e34d59'  // 红色
}

// 格式化日期
const formatDate = (dateStr: string) => {
  return dateStr.split(' ')[0]
}

// 格式化时间
const formatTime = (dateStr: string) => {
  return dateStr.split(' ')[1]
}

// 事件处理函数
const handleGoBack = () => {
  const from = route.query.from as string
  if (from) {
    router.push(from)
  } else {
    // 检查当前路由是否在教师课程模块下
    const isTeacherCourse = route.path.includes('/teacher/course')
    const courseId = route.params.courseId as string

    if (isTeacherCourse && courseId) {
      // 如果在教师课程模块下，返回到教师课程的班级考核列表
      router.push({
        name: 'TeacherCourseClassAssessmentList',
        params: { courseId }
      })
    } else {
      // 否则返回到通用的班级考核列表
      router.push({ name: 'ClassAssessmentList' })
    }
  }
}

const refreshData = () => {
  loadClassData()
  loadAssessmentData()
}

const filterAssessments = () => {
  // 触发计算属性重新计算
}

const handleSwitchClass = () => {
  classSelectorVisible.value = true
}

const handleClassSelect = (classInfo: WorklistItem) => {
  console.log('切换班级:', classInfo.classId, typeof classInfo.classId)

  // 检查当前路由是否在教师课程模块下
  const isTeacherCourse = route.path.includes('/teacher/course')
  const courseId = route.params.courseId as string

  if (isTeacherCourse && courseId) {
    // 如果在教师课程模块下，使用教师课程的路由
    router.replace({
      name: 'TeacherCourseAssessmentClassContent',
      params: { courseId, classId: String(classInfo.classId) },
      query: route.query // 保持现有的查询参数
    })
  } else {
    // 否则使用通用的班级考核管理路由
    router.replace({
      name: 'ClassAssessmentManagement',
      params: { classId: String(classInfo.classId) },
      query: route.query // 保持现有的查询参数
    })
  }

  // 路由参数更新后会自动触发数据重新加载
  // 因为 currentClassId 是计算属性，会自动更新
}

const handlePageChange = (pageInfo: any) => {
  contentPagination.current = pageInfo.current
}

const handleGradeManagement = (content: any) => {
  assessmentContent.value = content
  gradeManagementVisible.value = true
}

const handleStatisticsAnalysis = (assessment: any) => {
  MessagePlugin.info(`查看"${assessment.title}"统计分析功能待实现`)
}

// 成绩管理成功回调
const handleGradeManagementSuccess = () => {
  // 刷新考核内容数据
  loadAssessmentData()
  MessagePlugin.success('成绩管理操作成功')
}

// 事件处理函数（如无实际逻辑，先补空函数）
const handleSaveChanges = () => {}
const handleCancelChanges = () => {}
const handleSubmitGrades = () => {}
const handleRefreshGrades = () => {}

// 监听路由参数变化
watch(() => route.params.classId, (newClassId, oldClassId) => {
  if (newClassId && newClassId !== oldClassId) {
    loadClassData()
    loadAssessmentData()
  }
}, { immediate: false })

// 页面初始化
onMounted(() => {
  loadClassData()
  loadAssessmentData()
  initQuestionStructureAndTypes()
})
</script>

<style lang="less" scoped>
.class-assessment-management {
  padding: 24px;
  background: var(--td-bg-color-page);
  min-height: 100vh;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;

    .header-content {
      .back-section {
        margin-bottom: 8px;
      }

      .page-title {
        display: flex;
        align-items: center;
        gap: 8px;
        margin: 0;
        font-size: 24px;
        font-weight: 600;
        color: var(--td-text-color-primary);
      }
    }

    .header-actions {
      display: flex;
      gap: 12px;
    }
  }

  .class-info-section {
    background: var(--td-bg-color-container);
    border-radius: 6px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: var(--td-shadow-1);

    .class-basic-info {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;

      .class-details {
        flex: 1;

        h2 {
          margin: 0 0 16px 0;
          font-size: 20px;
          font-weight: 600;
          color: var(--td-text-color-primary);
        }

        .class-meta {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 16px;

          .meta-item {
            display: flex;
            align-items: center;

            .label {
              color: var(--td-text-color-secondary);
              margin-right: 8px;
            }

            .value {
              color: var(--td-text-color-primary);
              font-weight: 500;
            }
          }
        }
      }

      .class-actions {
        flex-shrink: 0;
      }
    }
  }

  .assessment-stats-section {
    background: var(--td-bg-color-container);
    border-radius: 6px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: var(--td-shadow-1);

    .section-title {
      margin-bottom: 20px;

      h2 {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        color: var(--td-text-color-primary);
      }
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 16px;

      .stat-card {
        display: flex;
        align-items: center;
        padding: 20px;
        background: var(--td-bg-color-page);
        border-radius: 6px;
        border: 1px solid var(--td-border-level-1-color);
        transition: all 0.3s ease;

        &:hover {
          box-shadow: var(--td-shadow-2);
          transform: translateY(-2px);
        }

        .stat-icon {
          width: 48px;
          height: 48px;
          background: var(--td-brand-color);
          border-radius: 6px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 16px;

          :deep(.t-icon) {
            color: white;
            font-size: 24px;
          }
        }

        .stat-content {
          .stat-number {
            font-size: 24px;
            font-weight: 600;
            color: var(--td-text-color-primary);
            line-height: 1;
            margin-bottom: 4px;
          }

          .stat-label {
            font-size: 14px;
            color: var(--td-text-color-secondary);
          }
        }
      }
    }
  }

  .assessment-content-section {
    background: var(--td-bg-color-container);
    border-radius: 6px;
    padding: 24px;
    box-shadow: var(--td-shadow-1);

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;

      h2 {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        color: var(--td-text-color-primary);
      }

      .header-filters {
        display: flex;
        gap: 12px;
      }
    }

    .content-table-container {
      .empty-content {
        text-align: center;
        padding: 60px 20px;
        color: var(--td-text-color-secondary);

        .t-icon {
          color: var(--td-text-color-placeholder);
          margin-bottom: 16px;
        }

        p {
          margin: 0;
        }
      }

      .progress-cell {
        .progress-text {
          font-size: 12px;
          color: var(--td-text-color-secondary);
          margin-bottom: 4px;
        }
      }

      .time-cell {
        .time-value {
          font-size: 14px;
          color: var(--td-text-color-primary);
          margin-bottom: 2px;
        }

        .time-detail {
          font-size: 12px;
          color: var(--td-text-color-secondary);
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .class-assessment-management {
    padding: 16px;

    .page-header {
      flex-direction: column;
      gap: 16px;
      align-items: stretch;
    }

    .class-info-section {
      padding: 16px;

      .class-basic-info {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;

        .class-details .class-meta {
          grid-template-columns: 1fr;
        }
      }
    }

    .assessment-stats-section {
      padding: 16px;

      .stats-grid {
        grid-template-columns: repeat(2, 1fr);
      }
    }

    .assessment-content-section {
      padding: 16px;

      .section-header {
        flex-direction: column;
        gap: 12px;
        align-items: stretch;

        .header-filters {
          justify-content: center;
        }
      }
    }
  }
}
</style>
