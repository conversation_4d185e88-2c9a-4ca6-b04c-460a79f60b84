<template>
  <div class="course-timeline">
    <t-timeline mode="alternate">
      <t-timeline-item
        v-for="course in courses"
        :key="course.id"
        :label="`${course.year}年${course.semester}`"
        :dot="course.version"
      >
        <t-card class="timeline-card" @click="$emit('open-achievement', course)">
          <div><strong>版本：</strong>{{ course.version }}</div>
          <div><strong>开课时间：</strong>{{ course.year }}年{{ course.semester }}</div>
        </t-card>
      </t-timeline-item>
    </t-timeline>
  </div>
</template>

<script setup lang="ts">
const props = defineProps<{ courses: Array<any> }>();
</script>

<style scoped>
.course-timeline {
  padding: 8px 0;
}
.timeline-card {
  cursor: pointer;
  transition: box-shadow 0.2s;
}
.timeline-card:hover {
  box-shadow: 0 4px 16px rgba(0,0,0,0.12);
}
</style>
