<template>
  <div class="course-timeline-horizontal">
    <div class="timeline-header">
      <h3 class="timeline-title">
        <t-icon name="time" class="title-icon" />
        课程历史版本时间轴
      </h3>
      <div class="timeline-subtitle">点击任意版本卡片查看详细达成度分析</div>
    </div>

    <div class="timeline-container">
      <div class="timeline-line"></div>
      <div class="timeline-items">
        <div
          v-for="(course, index) in courses"
          :key="course.id"
          class="timeline-item"
          :class="{ 'timeline-item-top': index % 2 === 0, 'timeline-item-bottom': index % 2 === 1 }"
        >
          <!-- 时间点 -->
          <div class="timeline-dot">
            <div class="dot-inner">{{ index + 1 }}</div>
          </div>

          <!-- 课程卡片 -->
          <div class="timeline-card" @click="$emit('open-achievement', course)">
            <div class="card-header">
              <div class="version-badge">{{ course.version }}</div>
              <t-icon name="chevron-right" class="card-arrow" />
            </div>
            <div class="card-content">
              <div class="course-info">
                <div class="info-row">
                  <span class="info-label">开课时间</span>
                  <span class="info-value">{{ course.year }}年{{ course.semester }}</span>
                </div>
                <div class="info-row">
                  <span class="info-label">课程版本</span>
                  <span class="info-value">{{ course.version }}</span>
                </div>
              </div>
            </div>
            <div class="card-footer">
              <t-button theme="primary" variant="text" size="small">
                查看详情
              </t-button>
            </div>
          </div>

          <!-- 连接线 -->
          <div v-if="index < courses.length - 1" class="timeline-connector"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const props = defineProps<{ courses: Array<any> }>();
</script>

<style scoped>
.course-timeline-horizontal {
  padding: 24px 0;
}

.timeline-header {
  text-align: center;
  margin-bottom: 32px;
}

.timeline-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.title-icon {
  color: #3b82f6;
}

.timeline-subtitle {
  color: #6b7280;
  font-size: 14px;
}

.timeline-container {
  position: relative;
  padding: 40px 0;
}

.timeline-line {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(to right, #e5e7eb, #3b82f6, #e5e7eb);
  transform: translateY(-50%);
  z-index: 1;
}

.timeline-items {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 2;
}

.timeline-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  flex: 1;
}

.timeline-item-top .timeline-card {
  margin-bottom: 60px;
}

.timeline-item-bottom .timeline-card {
  margin-top: 60px;
}

.timeline-dot {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 3;
}

.dot-inner {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #3b82f6;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.timeline-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  width: 200px;
  border: 1px solid #e5e7eb;
}

.timeline-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  border-color: #3b82f6;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.version-badge {
  background: #eff6ff;
  color: #3b82f6;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 600;
}

.card-arrow {
  color: #9ca3af;
  transition: color 0.2s;
}

.timeline-card:hover .card-arrow {
  color: #3b82f6;
}

.card-content {
  margin-bottom: 16px;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 12px;
  color: #6b7280;
}

.info-value {
  font-size: 14px;
  color: #1f2937;
  font-weight: 500;
}

.card-footer {
  text-align: center;
  border-top: 1px solid #f3f4f6;
  padding-top: 12px;
}

.timeline-connector {
  position: absolute;
  top: 50%;
  right: -50%;
  width: 100%;
  height: 2px;
  background: #e5e7eb;
  transform: translateY(-50%);
  z-index: 1;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .timeline-items {
    flex-direction: column;
    gap: 32px;
  }

  .timeline-item {
    width: 100%;
  }

  .timeline-line {
    display: none;
  }

  .timeline-card {
    width: 100%;
    max-width: 300px;
  }

  .timeline-item-top .timeline-card,
  .timeline-item-bottom .timeline-card {
    margin: 0;
  }

  .timeline-dot {
    position: relative;
    top: auto;
    transform: none;
    margin-bottom: 16px;
  }
}
</style>
