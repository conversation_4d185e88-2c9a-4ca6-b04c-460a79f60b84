<template>
  <t-dialog
    v-model:visible="dialogVisible"
    :width="1200"
    :height="700"
    :close-btn="false"
    :cancel-btn="null"
    :confirm-btn="null"
    :close-on-overlay-click="!!currentMajorId"
    placement="center"
    :destroy-on-close="false"
    class="major-selector-dialog"
  >
  <template #header>
    <div class="selector-header">
        <h2 class="selector-title">选择专业后将进入工作台</h2>
      </div>
  </template>
    <div class="major-selector">

      <div class="major-grid">

        <!-- 空数据状态 -->
        <div v-if="majorList.length === 0" class="empty-container">
          <t-icon name="info-circle" size="48px" />
          <p>暂无专业数据</p>
        </div>

        <!-- 专业列表 -->
        <div
          v-for="(major, index) in majorList"
          :key="major.majorId"
          class="major-card"
          :style="{ '--delay': `${index * 0.1}s` }"
          @click="handleSelectMajor(major)"
        >
          <div class="major-card-content" :class="{ 'current-major': major.majorId === currentMajorId }">
            <!-- 当前专业角标 -->
            <div v-if="major.majorId?.toString() === currentMajorId?.toString()" class="current-major-corner">
              <div class="corner-content">
                <t-icon name="check-circle-filled" size="14px" />
                <span>当前</span>
              </div>
            </div>

            <!-- 右上角类型标签 -->
            <div class="major-status" v-if="major.discipline">
              <t-tag
                :theme="major.majorId % 2 === 0 ? 'primary' : 'success'"
                size="small"
              >
                {{ majorTypeDictMap[major.discipline] || major.discipline }}
              </t-tag>
            </div>

            <!-- 主要内容 -->
            <div class="major-info">
              <h3 class="major-name">{{ major.majorName }}</h3>
              <p class="major-goal">{{ major.professionalOverview }}</p>

              <!-- 统计信息 -->
              <div class="major-statistics">
                <div class="stat-item">
                  <div class="stat-number">{{ major.courseCount }}</div>
                  <div class="stat-label">授课门数</div>
                </div>
                <div class="stat-divider"></div>
                <div class="stat-item">
                  <div class="stat-number">{{ major.classCount }}</div>
                  <div class="stat-label">班级数</div>
                </div>
                <div class="stat-divider"></div>
                <div class="stat-item">
                  <div class="stat-number">{{ major.semesterCount }}</div>
                  <div class="stat-label">学期数</div>
                </div>
              </div>
            </div>

            <!-- 底部操作区 -->
            <div class="major-actions">
              <t-button theme="primary" variant="text" block>
                进入工作台
                <template #suffix>
                  <t-icon name="chevron-right" />
                </template>
              </t-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </t-dialog>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
// 移除后端请求相关内容
// import { getMajorSelectorByTeacher } from '@/api/base/major'
import {
  Dialog as TDialog,
  Button as TButton,
  Tag as TTag,
  Icon as TIcon,
} from 'tdesign-vue-next'
// 引入字典工具
import { getDictDataByTypeTitle } from '@/utils/dictUtil'

// 专业类型字典缓存
const majorTypeDictMap = ref<{ [key: string]: string }>({})

onMounted(async () => {
  // 加载“专业类型”字典
  const dictArr = await getDictDataByTypeTitle('专业类型')
  const map: { [key: string]: string } = {}
  dictArr.forEach(item => {
    map[item.value] = item.label
  })
  majorTypeDictMap.value = map
})

// Props定义
interface Props {
  visible: boolean;
  currentMajorId?: number | string;
  loading?: boolean;
  majorList?: MajorSelectorVO[];
}

// Emits定义
interface Emits {
  (e: 'update:visible', visible: boolean): void;
  (e: 'select', major: MajorSelectorVO): void;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  currentMajorId: undefined,
  loading: false,
  majorList: () => []
})

const emit = defineEmits<Emits>()

// 弹窗显示状态的双向绑定
const dialogVisible = computed({
  get: () => props.visible,
  set: (value: boolean) => emit('update:visible', value)
})

// 选择专业处理
const handleSelectMajor = (major: MajorSelectorVO) => {
  emit('select', major)
  emit('update:visible', false)
}
// 删除 loadMajorData 和 onBeforeMount

</script>

<style lang="less" scoped>
  .selector-header {
    text-align: center;
    width: 100%;
    .selector-title {
      font-size: 24px;
      font-weight: 600;
      color: var(--td-text-color-primary);
      margin-bottom: 8px;
    }

    .selector-subtitle {
      font-size: 14px;
      color: var(--td-text-color-secondary);
      margin: 0;
    }
  }
.major-selector {
  padding: 20px;
  display: flex;
  flex-direction: column;



  .major-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 24px;
    flex: 1;

    .loading-container,
    .error-container,
    .empty-container {
      grid-column: 1 / -1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 60px 20px;
      text-align: center;
      color: var(--td-text-color-secondary);

      p {
        margin: 16px 0;
        font-size: 14px;
      }
    }

    .error-container {
      .t-icon {
        color: var(--td-error-color);
      }
    }

    .empty-container {
      .t-icon {
        color: var(--td-warning-color);
      }
    }

    .major-card {
      cursor: pointer;
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);

      &:hover {
        transform: translateY(-8px) scale(1.02);

        .major-card-content {
          box-shadow: var(--td-shadow-3);

          &.current-major {
            box-shadow: var(--td-shadow-3), 0 0 0 4px var(--td-warning-color-1), 0 0 20px var(--td-warning-color-2);
          }
        }
      }

      &:active {
        transform: translateY(-4px) scale(1.01);
        transition: all 0.1s ease;
      }

      .major-card-content {
        height: 100%;
        background: var(--td-bg-color-container);
        border-radius: 12px;
        border: 1px solid var(--td-component-border);
        box-shadow: var(--td-shadow-1);
        overflow: hidden;
        position: relative;
        padding: 20px;
        display: flex;
        flex-direction: column;

        // 添加光效
        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent, var(--td-bg-color-container-hover), transparent);
          transition: left 0.6s ease;
        }

        &:hover::before {
          left: 100%;
        }

        .current-major-corner {
          position: absolute;
          top: -5px;
          left: -5px;
          width: 48px;
          height: 48px;
          overflow: hidden;
          z-index: 4;
          border-top-left-radius: 12px; // 与卡片圆角匹配

          .corner-content {
            position: absolute;
            top: 10px;
            left: -16px;
            width: 70px;
            height: 20px;
            background: linear-gradient(135deg, var(--td-warning-color), var(--td-warning-color-8));
            color: var(--td-text-color-anti);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 3px;
            font-size: 10px;
            font-weight: 600;
            transform: rotate(-45deg);
            box-shadow: 0 2px 8px rgba(255, 153, 0, 0.3);

            span {
              font-size: 9px;
              letter-spacing: 0.5px;
            }
          }
        }

        .major-status {
          position: absolute;
          top: 16px;
          right: 16px;
          z-index: 2;
        }

        // 当前专业卡片特殊样式
        &.current-major {
          border: 2px solid var(--td-warning-color);
          box-shadow: var(--td-shadow-2), 0 0 0 4px var(--td-warning-color-1);
          position: relative;

          .major-info .major-name {
            color: var(--td-warning-color);
            font-weight: 700; // 当前专业名称更加突出
          }

          // 确保角标在特殊边框上方显示
          .current-major-corner {
            z-index: 5;
          }
        }

        .major-info {
          flex: 1;
          position: relative;
          z-index: 1;

          .major-name {
            font-size: 18px;
            font-weight: 600;
            color: var(--td-text-color-primary);
            margin-bottom: 12px;
            line-height: 1.4;
            padding-right: 80px; // 为右上角标签留空间
            padding-left: 1px; // 为左上角角标留出一点空间
          }

          .major-goal {
            font-size: 13px;
            color: var(--td-text-color-secondary);
            line-height: 1.5;
            margin-bottom: 20px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .major-statistics {
            display: flex;
            align-items: center;
            justify-content: space-around;
            padding: 14px 0;
            background: var(--td-bg-color-container-hover);
            border-radius: 8px;
            border: 1px solid var(--td-brand-color-1);

            .stat-item {
              text-align: center;
              flex: 1;
              transition: transform 0.3s ease;

              &:hover {
                transform: scale(1.05);
              }

              .stat-number {
                font-size: 20px;
                font-weight: 700;
                background: linear-gradient(135deg, var(--td-brand-color) 0%, var(--td-brand-color-8) 100%);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
                line-height: 1;
                margin-bottom: 4px;
              }

              .stat-label {
                font-size: 11px;
                color: var(--td-text-color-secondary);
                line-height: 1;
                font-weight: 500;
              }
            }

            .stat-divider {
              width: 1px;
              height: 28px;
              background: linear-gradient(to bottom, transparent, var(--td-component-border), transparent);
            }
          }
        }

        .major-actions {
          margin-top: auto;
          padding-top: 16px;

          :deep(.t-button) {
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
            background: linear-gradient(135deg, var(--td-brand-color) 0%, var(--td-brand-color-8) 100%);
            border: none;
            color: var(--td-text-color-anti);
            border-radius: 6px;
            padding: 10px 20px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;

            &::before {
              content: '';
              position: absolute;
              top: 0;
              left: -100%;
              width: 100%;
              height: 100%;
              background: linear-gradient(90deg, transparent, var(--td-bg-color-container-hover), transparent);
              transition: left 0.5s ease;
            }

            &:hover {
              background: linear-gradient(135deg, var(--td-brand-color-9) 0%, var(--td-brand-color-7) 100%);
              transform: translateY(-2px);

              &::before {
                left: 100%;
              }
            }

            &:active {
              transform: translateY(0);
            }
          }
        }
      }
    }

    // 卡片加载时有轻微的渐入效果
    .major-card {
      animation: fadeInScale 0.6s cubic-bezier(0.4, 0, 0.2, 1) var(--delay) both;
    }
  }

  // 简化的渐入动画
  @keyframes fadeInScale {
    from {
      transform: scale(0.95);
      opacity: 0.8;
    }
    to {
      transform: scale(1);
      opacity: 1;
    }
  }

  // 响应式设计 - 弹窗内适配
  @media (max-width: 1200px) {
    .major-grid {
      grid-template-columns: 1fr;
      gap: 20px;
    }
  }

  @media (max-width: 768px) {
    padding: 16px;

    .selector-header {
      margin-bottom: 24px;

      .selector-title {
        font-size: 20px;
      }
    }

    .major-grid {
      gap: 16px;

      .major-card {
        .major-card-content {
          .major-info {
            .major-name {
              font-size: 16px;
            }

            .major-goal {
              font-size: 12px;
              margin-bottom: 16px;
              -webkit-line-clamp: 2;
            }

            .major-statistics {
              padding: 12px 0;

              .stat-item {
                .stat-number {
                  font-size: 18px;
                }

                .stat-label {
                  font-size: 10px;
                }
              }

              .stat-divider {
                height: 24px;
              }
            }
          }
        }
      }
    }
  }
}

// 自定义弹窗样式
:deep(.major-selector-dialog) {
  .t-dialog__header {
    padding: 24px 24px 0;
    border-bottom: 1px solid var(--td-component-border);

    .t-dialog__title {
      font-size: 18px;
      font-weight: 600;
      color: var(--td-text-color-primary);
    }
  }

  .t-dialog__body {
    padding: 0;
    overflow-y: auto;
    max-height: 600px;
  }
}

// 响应式弹窗尺寸
@media (max-width: 1240px) {
  :deep(.major-selector-dialog .t-dialog) {
    width: 95vw !important;
    max-width: 1000px;
  }
}

@media (max-width: 768px) {
  :deep(.major-selector-dialog .t-dialog) {
    width: 95vw !important;
    height: 80vh !important;
    max-height: 600px;
  }
}
</style>
