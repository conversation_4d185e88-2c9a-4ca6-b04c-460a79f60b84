<template>
  <div class="course-base-info">

    <!-- 调试信息（开发时使用） -->
    <div v-if="debugMode" class="debug-info" style="background: #f0f0f0; padding: 10px; margin-bottom: 20px; border-radius: 4px; font-size: 12px;">
      <h4>调试信息:</h4>
      <div><strong>Course ID:</strong> {{ courseId }}</div>
      <div><strong>Initial Data Keys:</strong> {{ Object.keys(props.initialData || {}).join(', ') }}</div>
      <div><strong>Form Data:</strong> {{ JSON.stringify(formData, null, 2) }}</div>
      <div><strong>Main Textbook:</strong> {{ JSON.stringify(mainTextbook, null, 2) }}</div>
      <div><strong>Reference Books Count:</strong> {{ referenceBooks.length }}</div>
    </div>

    <!-- 操作按钮区域 -->
    <div class="course-header">
      <div class="header-info">
        <h3 class="section-title">
          <t-icon name="book-open" class="title-icon" />
          课程基本信息
        </h3>
        <!-- <p class="section-desc">{{ formData.courseName || '课程详情' }}</p> -->
      </div>
      <div class="header-actions">
        <t-space>
          <t-button
            v-if="!isEditing"
            theme="primary"
            @click="toggleEdit"
          >
            <template #icon><t-icon name="edit" /></template>
            编辑
          </t-button>
          <template v-else>
            <t-button theme="primary" @click="handleSave" :loading="saving">
              <template #icon><t-icon name="check" /></template>
              保存
            </t-button>
            <t-button theme="default" @click="handleCancel">
              <template #icon><t-icon name="close" /></template>
              取消
            </t-button>
          </template>
        </t-space>
      </div>
    </div>

    <!-- 基本课程信息配置 -->
    <!-- 不可变属性区域（只读展示） -->
    <div class="info-section readonly-section">
      <h3 class="section-title">
        <t-icon name="info-circle-filled" class="title-icon" />
        课程基本信息
      </h3>
      <div class="readonly-notice">
        <t-icon name="info-circle" class="notice-icon" />
        以下信息由培养方案设定，不可修改
      </div>

      <div class="info-grid readonly-grid">
        <div class="info-row">
          <div class="info-item">
            <span class="label readonly-label">课程名称</span>
            <span class="value readonly-value">{{ formData.courseName || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label readonly-label">课程编号</span>
            <span class="value readonly-value">{{ formData.courseCode || '-' }}</span>
          </div>
        </div>

        <div class="info-row">
          <div class="info-item">
            <span class="label readonly-label">课程性质</span>
            <span class="value readonly-value">{{ getCourseNatureLabel(formData.courseNature) || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label readonly-label">课程类别</span>
            <span class="value readonly-value">{{ getCourseTypeLabel(formData.courseType) || '-' }}</span>
          </div>
        </div>

        <div class="info-row">
          <div class="info-item">
            <span class="label readonly-label">开课单位</span>
            <span class="value readonly-value">{{ getDepartmentLabel(formData.department) || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label readonly-label">开课学期</span>
            <span class="value readonly-value">{{ getSemesterLabel(formData.semester) || '-' }}</span>
          </div>
        </div>

        <div class="info-row">
          <div class="info-item">
            <span class="label readonly-label">学分/学时</span>
            <span class="value readonly-value">{{ formData.credit || '-' }}/{{ formData.hours || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label readonly-label">开课系或教研室</span>
            <span class="value readonly-value">{{ formData.teachingUnit || '-' }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 可变属性区域（可编辑） -->
    <div class="info-section editable-section">
      <h3 class="section-title">
        <t-icon name="edit" class="title-icon" />
        课程详细信息
      </h3>
      <div class="editable-notice" v-if="!isEditing">
        <t-icon name="info-circle" class="notice-icon" />
        点击右上角"编辑"按钮可修改以下信息
      </div>

      <div class="info-grid">
        <div class="info-row">
          <div class="info-item">
            <span class="label">课程英文名称</span>
            <t-input
              v-if="isEditing"
              v-model="formData.courseEnglishName"
              placeholder="请输入课程英文名称"
            />
            <span v-else class="value">{{ formData.courseEnglishName || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">授课形式</span>
            <t-select
              v-if="isEditing"
              v-model="formData.teachingMethod"
              :options="teachingMethodOptions"
              placeholder="请选择授课形式"
            />
            <span v-else class="value">{{ getTeachingMethodLabel(formData.teachingMethod) || '-' }}</span>
          </div>
        </div>

        <div class="info-row">
          <div class="info-item">
            <span class="label">先修课程</span>
            <t-input
              v-if="isEditing"
              v-model="formData.prerequisite"
              placeholder="请输入先修课程"
            />
            <span v-else class="value">{{ formData.prerequisite || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">适用专业</span>
            <t-input
              v-if="isEditing"
              v-model="formData.applicableMajor"
              placeholder="请输入适用专业"
            />
            <span v-else class="value">{{ formData.applicableMajor || '-' }}</span>
          </div>
        </div>

        <div class="info-row">
          <div class="info-item full-width">
            <span class="label">开课平台或教学工具</span>
            <t-input
              v-if="isEditing"
              v-model="formData.teachingPlatform"
              placeholder="请输入开课平台或教学工具"
            />
            <span v-else class="value">{{ formData.teachingPlatform || '-' }}</span>
          </div>
        </div>

        <div class="info-row">
          <div class="info-item full-width">
            <span class="label">引入课程名称/开课学校</span>
            <t-input
              v-if="isEditing"
              v-model="formData.introducedCourse"
              placeholder="请输入引入课程名称/开课学校"
            />
            <span v-else class="value">{{ formData.introducedCourse || '-' }}</span>
          </div>
        </div>

        <div class="info-row">
          <div class="info-item full-width">
            <span class="label">课程简介</span>
            <t-textarea
              v-if="isEditing"
              v-model="formData.courseDescription"
              placeholder="请输入课程简介"
              :autosize="{ minRows: 4, maxRows: 8 }"
            />
            <div v-else class="value description">{{ formData.courseDescription || '-' }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 教材信息 -->
    <div class="materials-section">
      <h3 class="section-title">
        <t-icon name="folder" class="title-icon" />
        教材信息
      </h3>

      <!-- 主教材 -->
      <div class="material-group">
        <div class="group-header">
          <h4 class="group-title">主教材</h4>
          <t-button
            v-if="isEditing"
            theme="primary"
            size="small"
            @click="addTextbook"
          >
            <template #icon><t-icon name="add" /></template>
            添加教材
          </t-button>
        </div>
        <div v-if="mainTextbook" class="textbook-item">
          <div class="textbook-info">
            <div class="textbook-row">
              <div class="textbook-field">
                <span class="field-label">书名</span>
                <t-input
                  v-if="isEditing"
                  v-model="mainTextbook.title"
                  placeholder="请输入书名"
                />
                <span v-else class="field-value">{{ mainTextbook.title || '-' }}</span>
              </div>
              <div class="textbook-field">
                <span class="field-label">作者</span>
                <t-input
                  v-if="isEditing"
                  v-model="mainTextbook.author"
                  placeholder="请输入作者"
                />
                <span v-else class="field-value">{{ mainTextbook.author || '-' }}</span>
              </div>
            </div>
            <div class="textbook-row">
              <div class="textbook-field">
                <span class="field-label">出版社</span>
                <t-input
                  v-if="isEditing"
                  v-model="mainTextbook.publisher"
                  placeholder="请输入出版社"
                />
                <span v-else class="field-value">{{ mainTextbook.publisher || '-' }}</span>
              </div>
              <div class="textbook-field">
                <span class="field-label">出版时间</span>
                <t-input
                  v-if="isEditing"
                  v-model="mainTextbook.publishDate"
                  placeholder="请输入出版时间"
                />
                <span v-else class="field-value">{{ mainTextbook.publishDate || '-' }}</span>
              </div>
            </div>
          </div>
        </div>
        <div v-else class="empty-textbook">
          <span>暂无主教材信息</span>
          <t-button
            v-if="isEditing"
            theme="primary"
            variant="text"
            size="small"
            @click="addMainTextbook"
          >
            添加主教材
          </t-button>
        </div>
      </div>

      <!-- 参考书 -->
      <div class="material-group">
        <div class="group-header">
          <h4 class="group-title">参考书</h4>
          <t-button
            v-if="isEditing"
            theme="primary"
            size="small"
            @click="addReferenceBook"
          >
            添加参考书
          </t-button>
        </div>

        <div v-if="referenceBooks.length > 0">
          <div
            v-for="(book, index) in referenceBooks"
            :key="index"
            class="textbook-item"
          >
            <div class="textbook-header">
              <span class="textbook-index">参考书 {{ index + 1 }}</span>
              <t-button
                v-if="isEditing"
                theme="danger"
                variant="text"
                size="small"
                @click="removeReferenceBook(index)"
              >
                <template #icon><t-icon name="delete" /></template>
              </t-button>
            </div>
            <div class="textbook-info">
              <div class="textbook-row">
                <div class="textbook-field">
                  <span class="field-label">书名</span>
                  <t-input
                    v-if="isEditing"
                    v-model="book.title"
                    placeholder="请输入书名"
                  />
                  <span v-else class="field-value">{{ book.title || '-' }}</span>
                </div>
                <div class="textbook-field">
                  <span class="field-label">作者</span>
                  <t-input
                    v-if="isEditing"
                    v-model="book.author"
                    placeholder="请输入作者"
                  />
                  <span v-else class="field-value">{{ book.author || '-' }}</span>
                </div>
              </div>
              <div class="textbook-row">
                <div class="textbook-field">
                  <span class="field-label">出版社</span>
                  <t-input
                    v-if="isEditing"
                    v-model="book.publisher"
                    placeholder="请输入出版社"
                  />
                  <span v-else class="field-value">{{ book.publisher || '-' }}</span>
                </div>
                <div class="textbook-field">
                  <span class="field-label">出版时间</span>
                  <t-input
                    v-if="isEditing"
                    v-model="book.publishDate"
                    placeholder="请输入出版时间"
                  />
                  <span v-else class="field-value">{{ book.publishDate || '-' }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-else class="empty-textbook">
          <span>暂无参考书信息</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue'
import { MessagePlugin } from 'tdesign-vue-next'

// Props
interface Props {
  courseId?: string | number
  initialData?: Record<string, any>
  textbookData?: {
    mainTextbook: Textbook | null
    referenceBooks: Textbook[]
  }
}

const props = withDefaults(defineProps<Props>(), {
  initialData: () => ({}),
  textbookData: (): { mainTextbook: Textbook | null; referenceBooks: Textbook[] } => ({
    mainTextbook: null,
    referenceBooks: []
  })
})

// Emits
const emit = defineEmits<{
  save: [data: Record<string, any>]
  reset: []
}>()

// 响应式数据
const saving = ref(false)
const isEditing = ref(false)

// 教材接口
interface Textbook {
  title: string
  author: string
  publisher: string
  publishDate: string
}

// 表单数据
const formData = reactive({
  courseName: '',
  courseCode: '',
  courseEnglishName: '',
  courseNature: '',
  courseType: '',
  credit: 0,
  hours: 0,
  semester: '',
  prerequisite: '',
  department: '',
  teachingUnit: '',
  applicableMajor: '',
  teachingMethod: '',
  teachingPlatform: '',
  introducedCourse: '',
  courseDescription: ''
})

// 课程目标数据
const courseObjectives = ref([
  '掌握Java语言的基本语法和面向对象编程思想',
  '能够使用Java开发简单的应用程序',
  '理解Java程序的运行机制和开发环境',
  '具备解决实际编程问题的能力'
])

// 教材数据
const mainTextbook = ref<Textbook | null>(null)
const referenceBooks = ref<Textbook[]>([])

// 选项数据
const courseNatureOptions = ref([
  { label: '必修课程', value: '必修' },
  { label: '选修课程', value: '选修' },
  { label: '专业限选课', value: '专业限选' }
])

const courseTypeOptions = ref([
  { label: '专业基础课', value: '专业基础课' },
  { label: '专业核心课', value: '专业核心课' },
  { label: '专业选修课', value: '专业选修课' },
  { label: '通识教育课', value: '通识教育课' }
])

const semesterOptions = ref([
  { label: '第1学期', value: '1' },
  { label: '第2学期', value: '2' },
  { label: '第3学期', value: '3' },
  { label: '第4学期', value: '4' },
  { label: '第5学期', value: '5' },
  { label: '第6学期', value: '6' },
  { label: '第7学期', value: '7' },
  { label: '第8学期', value: '8' }
])

const departmentOptions = ref([
  { label: '数学学院', value: '数学学院' },
  { label: '计算机学院', value: '计算机学院' },
  { label: '软件学院', value: '软件学院' },
  { label: '物理学院', value: '物理学院' }
])

const teachingMethodOptions = ref([
  { label: '线上线下混合式', value: '线上线下混合式' },
  { label: '线下教学', value: '线下教学' },
  { label: '线上教学', value: '线上教学' },
  { label: '实践教学', value: '实践教学' }
])

// 获取选项标签的辅助方法
const getCourseNatureLabel = (value: string) => {
  return courseNatureOptions.value.find(option => option.value === value)?.label || value
}

const getCourseTypeLabel = (value: string) => {
  return courseTypeOptions.value.find(option => option.value === value)?.label || value
}

const getSemesterLabel = (value: string) => {
  return semesterOptions.value.find(option => option.value === value)?.label || value
}

const getDepartmentLabel = (value: string) => {
  return departmentOptions.value.find(option => option.value === value)?.label || value
}

const getTeachingMethodLabel = (value: string) => {
  return teachingMethodOptions.value.find(option => option.value === value)?.label || value
}

// 调试模式（开发时使用）
const debugMode = ref(false)

// 临时启用调试的方法（可以在浏览器控制台调用）
if (typeof window !== 'undefined') {
  (window as any).enableCourseDebug = () => {
    debugMode.value = true
    console.log('课程调试模式已启用')
  }
  
  (window as any).disableCourseDebug = () => {
    debugMode.value = false
    console.log('课程调试模式已禁用')
  }
}

// 方法
const toggleEdit = () => {
  isEditing.value = true
}

const handleSave = async () => {
  try {
    saving.value = true
    const saveData = {
      ...formData,
      mainTextbook: mainTextbook.value,
      referenceBooks: referenceBooks.value
    }
    emit('save', saveData)
    isEditing.value = false
    MessagePlugin.success('保存成功')
  } catch (error) {
    MessagePlugin.error('保存失败')
  } finally {
    saving.value = false
  }
}

const handleCancel = () => {
  isEditing.value = false
  initializeData()
  MessagePlugin.info('已取消编辑')
}

// 教材管理方法
const addTextbook = () => {
  // 可以打开一个对话框来选择添加主教材还是参考书
  addReferenceBook()
}

const addMainTextbook = () => {
  mainTextbook.value = {
    title: '',
    author: '',
    publisher: '',
    publishDate: ''
  }
}

const addReferenceBook = () => {
  referenceBooks.value.push({
    title: '',
    author: '',
    publisher: '',
    publishDate: ''
  })
}

const removeReferenceBook = (index: number) => {
  referenceBooks.value.splice(index, 1)
}

// 初始化数据
const initializeData = () => {
  console.log('开始初始化数据:', {
    initialData: props.initialData,
    textbookData: props.textbookData
  });

  // 初始化课程基本信息
  if (props.initialData && Object.keys(props.initialData).length > 0) {
    Object.assign(formData, props.initialData)
  } else {
    // 设置默认数据
    Object.assign(formData, {
      courseName: '',
      courseCode: '',
      courseEnglishName: '',
      courseNature: '',
      courseType: '',
      credit: 0,
      hours: 0,
      semester: '',
      prerequisite: '',
      department: '',
      teachingUnit: '',
      applicableMajor: '',
      teachingMethod: '',
      teachingPlatform: '',
      introducedCourse: '',
      courseDescription: ''
    })
  }

  // 初始化教材数据
  if (props.textbookData) {
    // 优先使用 textbookData prop
    if (props.textbookData.mainTextbook) {
      mainTextbook.value = { ...props.textbookData.mainTextbook }
    } else {
      mainTextbook.value = null;
    }
    
    if (props.textbookData.referenceBooks && Array.isArray(props.textbookData.referenceBooks)) {
      referenceBooks.value = [...props.textbookData.referenceBooks]
    } else {
      referenceBooks.value = [];
    }
  } else if (props.initialData) {
    // 如果没有 textbookData，尝试从 initialData 获取
    if (props.initialData.mainTextbook) {
      mainTextbook.value = { ...props.initialData.mainTextbook }
    } else {
      mainTextbook.value = null;
    }
    
    if (props.initialData.referenceBooks && Array.isArray(props.initialData.referenceBooks)) {
      referenceBooks.value = [...props.initialData.referenceBooks]
    } else {
      referenceBooks.value = [];
    }
  } else {
    // 设置默认教材数据
    mainTextbook.value = null;
    referenceBooks.value = [];
  }
  
  console.log('初始化数据完成:', {
    formData: formData,
    mainTextbook: mainTextbook.value,
    referenceBooks: referenceBooks.value
  });
}

onMounted(() => {
  initializeData();
})

// 监听 props 变化，当父组件数据更新时重新初始化
watch(() => props.initialData, (newData) => {
  console.log('initialData 发生变化:', newData);
  initializeData();
}, { deep: true });

watch(() => props.textbookData, (newData) => {
  console.log('textbookData 发生变化:', newData);
  initializeData();
}, { deep: true });
</script>

<style lang="less" scoped>
.course-base-info {
  background: #fff;

  .course-objectives-section {
    margin-bottom: 32px;
    padding: 20px;
    background: var(--td-bg-color-container);
    border-radius: 6px;
    border: 1px solid var(--td-border-level-1-color);

    .section-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 16px;
      font-weight: 600;
      color: var(--td-text-color-primary);
      margin-bottom: 20px;

      .title-icon {
        color: var(--td-brand-color);
        font-size: 18px;
      }
    }

    .objectives-cards {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 16px;

      .objective-card {
        background: #fff;
        border: 1px solid var(--td-border-level-1-color);
        border-radius: 6px;
        padding: 16px;
        transition: all 0.2s ease;

        &:hover {
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          border-color: var(--td-brand-color);
        }

        .card-header {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 12px;

          .objective-number {
            width: 24px;
            height: 24px;
            background: var(--td-brand-color);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 600;
            flex-shrink: 0;
          }

          .objective-label {
            font-weight: 500;
            color: var(--td-text-color-primary);
            flex: 1;
          }

          .status-tag {
            flex-shrink: 0;
          }
        }

        .objective-content {
          color: var(--td-text-color-secondary);
          line-height: 1.5;
          font-size: 14px;
        }
      }
    }
  }

  .course-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 24px 24px 16px;
    border-bottom: 1px solid var(--td-border-level-1-color);

    .header-info {
      .section-title {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 18px;
        font-weight: 600;
        margin: 0 0 8px 0;
        color: var(--td-text-color-primary);

        .title-icon {
          color: var(--td-brand-color);
          font-size: 20px;
        }
      }

      .section-desc {
        margin: 0;
        color: var(--td-text-color-secondary);
        font-size: 14px;
      }
    }

    .header-actions {
      flex-shrink: 0;
    }
  }

  .info-section {
    margin-bottom: 32px;
    padding: 0 24px;

    .section-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 18px;
      font-weight: 600;
      color: var(--td-text-color-primary);
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 2px solid var(--td-brand-color);

      .title-icon {
        color: var(--td-brand-color);
        font-size: 18px;
      }
    }

    // 只读区域样式
    &.readonly-section {
      background: var(--td-bg-color-container);
      border: 1px solid var(--td-border-level-1-color);
      border-radius: 8px;
      padding: 24px;
      position: relative;

      .section-title {
        border-bottom-color: var(--td-warning-color);
        
        .title-icon {
          color: var(--td-warning-color);
        }
      }

      .readonly-notice {
        display: flex;
        align-items: center;
        gap: 8px;
        background: var(--td-warning-color-1);
        border: 1px solid var(--td-warning-color-3);
        border-radius: 6px;
        padding: 12px 16px;
        margin-bottom: 20px;
        font-size: 14px;
        color: var(--td-warning-color-7);

        .notice-icon {
          color: var(--td-warning-color);
          font-size: 16px;
          flex-shrink: 0;
        }
      }

      .readonly-grid {
        .info-item {
          .readonly-label {
            color: var(--td-text-color-secondary);
            font-weight: 500;

            &::before {
              display: none; // 移除必填标记
            }
          }

          .readonly-value {
            background: var(--td-bg-color-secondarycontainer);
            border: 1px solid var(--td-border-level-2-color);
            border-radius: 4px;
            padding: 8px 12px;
            color: var(--td-text-color-primary);
            font-weight: 500;
            min-height: 32px;
            display: flex;
            align-items: center;
          }
        }
      }
    }

    // 可编辑区域样式
    &.editable-section {
      .editable-notice {
        display: flex;
        align-items: center;
        gap: 8px;
        background: var(--td-brand-color-1);
        border: 1px solid var(--td-brand-color-3);
        border-radius: 6px;
        padding: 12px 16px;
        margin-bottom: 20px;
        font-size: 14px;
        color: var(--td-brand-color-7);

        .notice-icon {
          color: var(--td-brand-color);
          font-size: 16px;
          flex-shrink: 0;
        }
      }
    }

    .info-grid {
      .info-row {
        display: flex;
        gap: 40px;
        margin-bottom: 20px;

        .info-item {
          flex: 1;
          display: flex;
          align-items: center;

          &.full-width {
            flex: 1 1 100%;
            align-items: flex-start;

            .label {
              margin-top: 8px;
            }

            .description {
              line-height: 1.6;
              white-space: pre-wrap;
            }
          }

          .label {
            min-width: 120px;
            font-weight: 500;
            color: var(--td-text-color-secondary);
            margin-right: 16px;

            &::before {
              content: '*';
              color: var(--td-error-color);
              margin-right: 4px;
            }
          }

          .value {
            flex: 1;
            color: var(--td-text-color-primary);

            &:empty::before {
              content: '-';
              color: var(--td-text-color-placeholder);
            }
          }

          .credit-hours-input {
            display: flex;
            align-items: center;
            gap: 8px;

            .separator {
              color: var(--td-text-color-secondary);
            }
          }
        }
      }
    }
  }

  .materials-section {
    margin-bottom: 32px;
    padding: 0 24px 24px;

    .section-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 18px;
      font-weight: 600;
      color: var(--td-text-color-primary);
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 2px solid var(--td-brand-color);

      .title-icon {
        color: var(--td-brand-color);
        font-size: 18px;
      }
    }

    .material-group {
      margin-bottom: 32px;

      .group-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
      }

      .group-title {
        font-size: 16px;
        font-weight: 600;
        color: var(--td-text-color-primary);
        margin: 0 0 16px 0;
      }

      .textbook-item {
        background: var(--td-bg-color-container);
        border: 1px solid var(--td-border-level-1-color);
        border-radius: 6px;
        padding: 20px;
        margin-bottom: 16px;

        .textbook-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 16px;

          .textbook-index {
            font-weight: 600;
            color: var(--td-text-color-primary);
          }
        }

        .textbook-info {
          .textbook-row {
            display: flex;
            gap: 40px;
            margin-bottom: 16px;

            &:last-child {
              margin-bottom: 0;
            }

            .textbook-field {
              flex: 1;
              display: flex;
              align-items: center;

              .field-label {
                min-width: 80px;
                font-weight: 500;
                color: var(--td-text-color-secondary);
                margin-right: 16px;
              }

              .field-value {
                flex: 1;
                color: var(--td-text-color-primary);

                &:empty::before {
                  content: '-';
                  color: var(--td-text-color-placeholder);
                }
              }
            }
          }
        }
      }

      .empty-textbook {
        text-align: center;
        padding: 40px;
        color: var(--td-text-color-placeholder);
        background: var(--td-bg-color-container);
        border: 1px dashed var(--td-border-level-1-color);
        border-radius: 6px;

        span {
          display: block;
          margin-bottom: 12px;
        }
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    padding: 16px;
    
    .action-bar {
      flex-direction: column;
      gap: 12px;
      align-items: flex-start;
      
      .course-title {
        margin-bottom: 8px;
        font-size: 18px;
      }
    }

    .course-objectives-section {
      padding: 16px;

      .objectives-cards {
        grid-template-columns: 1fr;
        gap: 12px;

        .objective-card {
          padding: 12px;

          .card-header {
            .objective-label {
              font-size: 14px;
            }
          }

          .objective-content {
            font-size: 13px;
          }
        }
      }
    }

    .info-section {
      padding: 16px;

      &.readonly-section {
        padding: 16px;

        .readonly-notice {
          padding: 10px 12px;
          font-size: 13px;
        }
      }

      &.editable-section {
        .editable-notice {
          padding: 10px 12px;
          font-size: 13px;
        }
      }

      .info-grid .info-row {
        flex-direction: column;
        gap: 16px;

        .info-item {
          flex-direction: column;
          align-items: flex-start;

          .label {
            margin-bottom: 8px;
            margin-right: 0;
          }

          .readonly-value {
            width: 100%;
          }
        }
      }
    }

    .materials-section {
      padding: 16px;

      .textbook-info .textbook-row {
        flex-direction: column;
        gap: 16px;

        .textbook-field {
          flex-direction: column;
          align-items: flex-start;

          .field-label {
            margin-bottom: 8px;
            margin-right: 0;
          }
        }
      }
    }
  }
}
</style>
