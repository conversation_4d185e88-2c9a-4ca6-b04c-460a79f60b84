<template>
  <div class="teaching-data-container">
    <!-- 头部信息 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-info">
          <h1 class="page-title">教学成绩管理</h1>
          <p class="page-subtitle">每学期教学综合成绩统计与分析</p>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="page-content">
      <!-- 成绩统计表格 -->
      <t-card>
        <template #title>
          <div class="card-header">
            <t-icon name="file-excel" class="title-icon" />
            <span>学生成绩统计</span>
          </div>
        </template>
        
        <!-- 筛选和操作区域 -->
        <div class="filter-section">
          <div class="filter-left">
            <t-space>
              <t-select v-model="filterOptions.semester" :options="semesterOptions" placeholder="选择学期" style="width: 180px;"></t-select>
              <t-button theme="primary" @click="handleRefresh">
                <template #icon>
                  <t-icon name="refresh" />
                </template>
                刷新数据
              </t-button>
              <t-button theme="default">
                <template #icon>
                  <t-icon name="download" />
                </template>
                导出数据
              </t-button>
            </t-space>
          </div>
          <div class="filter-right">
            <t-button theme="primary" variant="outline" @click="handleDataAnalysis">
              <template #icon>
                <t-icon name="chart-line" />
              </template>
              数据分析
            </t-button>
          </div>
        </div>
        
        <t-table
          :data="teachingData"
          :columns="columns"
          :bordered="true"
          :hover="true"
          :loading="loading"
          row-key="studentId"
        >
          <template #score="{ row }">
            <div :class="getScoreClass(row.score)">{{ row.score }}</div>
          </template>
          <template #pass="{ row }">
            <t-tag :theme="row.pass ? 'success' : 'danger'">{{ row.pass ? '通过' : '未通过' }}</t-tag>
          </template>
        </t-table>
      </t-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { MessagePlugin } from 'tdesign-vue-next';

const route = useRoute();
const router = useRouter();
const courseId = ref(Number(route.params.courseId) || 1); // 确保有默认值
const loading = ref(false);

// 学生成绩表格列配置
const columns = [
  { colKey: 'academicYear', title: '学年', width: 100 },
  { colKey: 'semester', title: '学期', width: 100 },
  { colKey: 'studentId', title: '学号', width: 120 },
  { colKey: 'name', title: '姓名', width: 100 },
  { colKey: 'class', title: '班级', width: 150 },
  { colKey: 'score', title: '总成绩', width: 100 },
  { colKey: 'homework', title: '平时成绩', width: 100 },
  { colKey: 'exam', title: '考试成绩', width: 100 },
  { colKey: 'pass', title: '是否通过', width: 100 }
];

// 学期选项（成绩统计用）
const semesterOptions = [
  { label: '2025春季学期', value: '2025-spring' },
  { label: '2024秋季学期', value: '2024-autumn' },
  { label: '2024春季学期', value: '2024-spring' },
  { label: '2023秋季学期', value: '2023-autumn' }
];

// 筛选选项
const filterOptions = ref({
  semester: '2024-spring'
});

// 教学数据列表（学生成绩数据）
const teachingData = ref([
  {
    studentId: '2021001',
    name: '张三',
    class: '计算机科学与技术2021-1班',
    academicYear: '2024-2025',
    semester: '春季学期',
    score: 92,
    homework: 88,
    exam: 95,
    pass: true
  },
  {
    studentId: '2021002',
    name: '李四',
    class: '计算机科学与技术2021-1班',
    academicYear: '2024-2025',
    semester: '春季学期',
    score: 78,
    homework: 82,
    exam: 75,
    pass: true
  },
  {
    studentId: '2021003',
    name: '王五',
    class: '计算机科学与技术2021-2班',
    academicYear: '2024-2025',
    semester: '春季学期',
    score: 85,
    homework: 88,
    exam: 83,
    pass: true
  },
  {
    studentId: '2021004',
    name: '赵六',
    class: '计算机科学与技术2021-2班',
    academicYear: '2024-2025',
    semester: '春季学期',
    score: 58,
    homework: 65,
    exam: 52,
    pass: false
  }
]);

// 获取成绩样式类
const getScoreClass = (score: number) => {
  if (score >= 90) return 'score-excellent';
  if (score >= 80) return 'score-good';
  if (score >= 70) return 'score-medium';
  if (score >= 60) return 'score-pass';
  return 'score-fail';
};

// 刷新数据
const handleRefresh = () => {
  loading.value = true;
  console.log('刷新成绩数据，学期:', filterOptions.value.semester);
  // 模拟API调用
  setTimeout(() => {
    loading.value = false;
    MessagePlugin.success('数据刷新成功');
  }, 1000);
};

// 跳转到数据分析页面
const handleDataAnalysis = () => {
  router.push({
    path: `/course/leader/score/analysis/${courseId.value}`,
    query: {
      semester: filterOptions.value.semester,
      courseId: courseId.value
    }
  });
};

onMounted(() => {
  console.log('教学成绩管理页面加载，课程ID:', courseId.value);
});
</script>

<style lang="less" scoped>
.teaching-data-container {
  padding: 20px;
  
  .page-header {
    background: white;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    border: 1px solid rgba(0, 0, 0, 0.06);
    
    .header-content {
      .title-info {
        .page-title {
          font-size: 24px;
          font-weight: 600;
          margin: 0 0 8px 0;
          color: var(--td-text-color-primary);
        }
        
        .page-subtitle {
          font-size: 14px;
          color: var(--td-text-color-secondary);
          margin: 0;
        }
      }
    }
  }
  
  .page-content {
    .card-header {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .title-icon {
        color: var(--td-brand-color);
      }
    }
    
    .filter-section {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      
      .filter-left {
        flex: 1;
      }
      
      .filter-right {
        margin-left: 16px;
      }
    }
    
    // 成绩样式
    .score-excellent {
      color: #ed7b2f;
      font-weight: 600;
    }
    
    .score-good {
      color: #00a870;
      font-weight: 600;
    }
    
    .score-medium {
      color: #0052d9;
    }
    
    .score-pass {
      color: #888;
    }
    
    .score-fail {
      color: #e34d59;
      font-weight: 600;
    }
  }
}

// 表格单元格支持换行
:deep(.t-table) {
  td {
    white-space: normal;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .teaching-data-container {
    padding: 16px;
    
    .page-content {
      .filter-section {
        flex-direction: column;
        gap: 12px;
        align-items: stretch;
        
        .filter-left {
          width: 100%;
        }
        
        .filter-right {
          margin-left: 0;
          width: 100%;
          
          :deep(.t-button) {
            width: 100%;
          }
        }
      }
    }
  }
}
</style>
