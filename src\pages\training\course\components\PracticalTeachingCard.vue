<template>
  <div class="practical-teaching-card">
    <t-card>
      <template #header>
        <div class="card-header">
          <div class="header-title">
            <t-icon name="tools" class="header-icon" />
            实践教学内容分配
          </div>
          <div class="header-actions">
            <div class="header-info">说明：实践教学内容包括课内上机、课外实践、实验课、项目实训、综合实践等</div>
            <t-button v-if="isEditing" theme="primary" size="small" @click="addPracticalItem">
              <template #icon><t-icon name="add" /></template>
              添加实践内容
            </t-button>
          </div>
        </div>
      </template>

      <!-- 空状态 -->
      <div v-if="practicalData.length === 0" class="empty-state">
        <div class="empty-icon">
          <t-icon name="tools" size="32px" />
        </div>
        <div class="empty-text">暂无实践教学内容</div>
        <t-button v-if="isEditing" theme="primary" variant="outline" @click="addPracticalItem">
          <template #icon><t-icon name="add" /></template>
          添加第一个实践内容
        </t-button>
      </div>

      <!-- 实践教学表格 -->
      <t-table
        v-else
        :data="practicalData"
        :columns="columns"
        :bordered="true"
        :hover="true"
        :stripe="true"
        row-key="id"
        size="medium"
      >
        <!-- 序号列 -->
        <template #index="{ rowIndex }">
          {{ rowIndex + 1 }}
        </template>

        <!-- 教学类型列 -->
        <template #type="{ row }">
          <div v-if="!isEditing">{{ row?.type || '-' }}</div>
          <t-select
            v-else-if="row"
            v-model="row.type"
            placeholder="请选择教学类型"
            :options="typeOptions"
            @change="emitUpdate"
          />
          <div v-else>-</div>
        </template>

        <!-- 教学内容列 -->
        <template #content="{ row }">
          <div v-if="!isEditing" class="content-display">{{ row?.content || '-' }}</div>
          <t-textarea
            v-else-if="row"
            v-model="row.content"
            placeholder="请输入教学内容"
            :autosize="{ minRows: 2, maxRows: 4 }"
            @blur="emitUpdate"
          />
          <div v-else>-</div>
        </template>

        <!-- 教学要求列 -->
        <template #requirement="{ row }">
          <div v-if="!isEditing" class="requirement-display">{{ row?.requirement || '-' }}</div>
          <t-textarea
            v-else-if="row"
            v-model="row.requirement"
            placeholder="请输入教学要求"
            :autosize="{ minRows: 2, maxRows: 3 }"
            @blur="emitUpdate"
          />
          <div v-else>-</div>
        </template>

        <!-- 对应课程目标列 -->
        <template #targets="{ row }">
          <div v-if="!isEditing">
            <t-tag v-for="target in (row?.targets || [])" :key="target" size="small" class="target-tag">
              目标{{ target }}
            </t-tag>
          </div>
          <t-select
            v-else-if="row"
            v-model="row.targets"
            placeholder="请选择课程目标"
            multiple
            :options="targetOptions"
            @change="emitUpdate"
          />
          <div v-else>-</div>
        </template>

        <!-- 学时分配列 -->
        <template #hours="{ row }">
          <div v-if="!isEditing">{{ row?.hours || 0 }}</div>
          <t-input-number
            v-else-if="row"
            v-model="row.hours"
            :min="0"
            :max="100"
            placeholder="学时"
            @change="emitUpdate"
          />
          <div v-else>0</div>
        </template>

        <!-- 操作列 -->
        <template #operation="{ rowIndex }">
          <t-space v-if="isEditing">
            <t-button
              theme="danger"
              variant="text"
              size="small"
              @click="removePracticalItem(rowIndex)"
            >
              <template #icon><t-icon name="delete" /></template>
              删除
            </t-button>
          </t-space>
        </template>
      </t-table>
    </t-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { MessagePlugin } from 'tdesign-vue-next'

// Props
interface Props {
  data: any[]
  isEditing: boolean
}

const props = withDefaults(defineProps<Props>(), {
  data: () => [],
  isEditing: false
})

// Emits
const emit = defineEmits<{
  update: [data: any[]]
}>()

// 实践教学数据
const practicalData = ref<any[]>([])

// 表格列配置
const columns = [
  { colKey: 'index', title: '序号', width: 80 },
  { colKey: 'type', title: '教学类型', width: 120 },
  { colKey: 'content', title: '教学内容', width: 200 },
  { colKey: 'requirement', title: '教学要求', width: 200 },
  { colKey: 'targets', title: '对应课程目标', width: 150 },
  { colKey: 'hours', title: '学时分配', width: 100 },
  { colKey: 'operation', title: '操作', width: 100, fixed: 'right' as const }
]

// 教学类型选项
const typeOptions = [
  { label: '课内上机', value: '课内上机' },
  { label: '课外实践', value: '课外实践' },
  { label: '实验课', value: '实验课' },
  { label: '项目实训', value: '项目实训' },
  { label: '综合实践', value: '综合实践' }
]

// 课程目标选项
const targetOptions = computed(() => {
  const options = []
  for (let i = 1; i <= 10; i++) {
    options.push({ label: `目标${i}`, value: i })
  }
  return options
})

// 监听props变化
watch(() => props.data, (newData) => {
  if (Array.isArray(newData)) {
    practicalData.value = newData.map(item => ({
      id: item?.id || `practical_${Date.now()}_${Math.random()}`,
      type: item?.type || '',
      content: item?.content || '',
      requirement: item?.requirement || '',
      targets: Array.isArray(item?.targets) ? item.targets : [],
      hours: typeof item?.hours === 'number' ? item.hours : 0
    }))
  } else {
    practicalData.value = []
  }
}, { immediate: true, deep: true })

// 手动触发更新的方法
const emitUpdate = () => {
  nextTick(() => {
    emit('update', practicalData.value)
  })
}

// 添加实践内容
const addPracticalItem = () => {
  const newItem = {
    id: `practical_${Date.now()}_${Math.random()}`,
    type: '',
    content: '',
    requirement: '',
    targets: [] as number[],
    hours: 0
  }
  practicalData.value.push(newItem)
  emitUpdate()
}

// 删除实践内容
const removePracticalItem = (index: number) => {
  practicalData.value.splice(index, 1)
  emitUpdate()
  MessagePlugin.success('已删除实践教学内容')
}
</script>

<style scoped lang="less">
.practical-teaching-card {
  margin-bottom: 24px;

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;

    .header-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 16px;
      font-weight: 600;
      color: var(--td-text-color-primary);

      .header-icon {
        color: var(--td-warning-color);
        font-size: 18px;
      }
    }
    
    .header-actions {
      display: flex;
      gap: 8px;
      align-items: center;
      justify-content: flex-end;
    }
    
    .header-info {
      font-style: italic;
      color: var(--td-text-color-placeholder);
      font-size: 14px;
      margin-right: 8px;
    }
  }

  .empty-state {
    text-align: center;
    padding: 40px 20px;

    .empty-icon {
      margin-bottom: 16px;
      color: var(--td-text-color-placeholder);
    }

    .empty-text {
      margin-bottom: 20px;
      color: var(--td-text-color-secondary);
    }
  }

  .content-display,
  .requirement-display {
    line-height: 1.5;
    white-space: pre-line;
  }

  .target-tag {
    margin-right: 4px;
    margin-bottom: 4px;
  }
}

@media (max-width: 768px) {
  .practical-teaching-card {
    .card-header {
      flex-direction: column;
      gap: 12px;
      align-items: stretch;
      padding: 8px 0;

      .header-title {
        justify-content: center;
      }

      .header-actions {
        justify-content: center;
      }
    }
  }
}
</style>
