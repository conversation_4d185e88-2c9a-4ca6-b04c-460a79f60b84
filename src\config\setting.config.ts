import {debounce} from "@/config/index";

/**
 * @description 导出通用配置
 */
export const settingConfig: {
  [key: string]: string | boolean | number | Array<string>
} = {
  // 开发以及部署时的URL
  // hash模式时在不确定二级目录名称的情况下建议使用""代表相对路径或者"/二级目录/"
  // history模式默认使用"/"或者"/二级目录/"，记住只有hash时publicPath可以为空！！！
  publicPath: '',
  // 标题，此项修改后需要重启项目
  title: '工程教育专业认证智能管理信息系统',
  shortTitle: '工证智链',
  // token失效回退到登录页时是否记录本次的路由（是否记录当前tab页）
  recordRoute: true,
  // 标题分隔符
  titleSeparator: ' - ',
  // 标题是否反转
  // 如果为false: "page - title"
  // 如果为true : "title - page"
  titleReverse: false,
  // 简写
  abbreviation: 'obe',
  // 路由模式，是否为hash模式
  isHashRouterMode: true,
  // 不经过token校验的路由，白名单路由建议配置到与login页面同级，如果需要放行带传参的页面，请使用query传参，配置时只配置path即可
  routesWhiteList: ['/login', '/register', '/404', '/403'],
  loginRouter: '/login',
  // 加载时显示文字
  loadingText: '正在加载中...',
  // token名称,与后端保持一致
  tokenName: 'Authorization',
  // token在localStorage、sessionStorage存储的key的名称
  tokenTableName: 'obe-token',
  // token存储位置localStorage sessionStorage
  storage: 'localStorage',
  // 语言类型zh、en
  i18n: 'zh',
  // 消息框消失时间
  messageDuration: 3000,
  // 在哪些环境下显示高亮错误 ['development', 'production']
  errorLog: 'development',
  // 是否开启登录拦截
  loginInterception: true,
  // intelligence(前端导出路由)和all(后端导出路由)两种方式
  authentication: 'all',
  // 是否开启roles字段进行角色权限控制(如果是all模式后端完全处理角色并进行json组装，可设置false不处理路由中的roles字段)
  rolesControl: true,
  debounce: ['edit', 'save', 'add', 'update'],
}
