<template>
  <FullScreenDialog
    v-model:visible="data.visible"
    :header="data.title"
    :animation="{
      type: 'flip',
      duration: 600,
      easing: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)'
    }"
    :overlay="{
      blur: true,
      opacity: 0.85,
      color: 'rgba(13, 110, 253, 0.1)'
    }"
    :confirm-btn="{ content: '保存配置', loading: data.submitting, disabled: !isConfigValid }"
    @confirm="handleSave"
    @cancel="handleCancel"
    @close="handleClose"
  >
    <div class="detailed-config-content">
      <div class="config-header">
        <div class="description-card">
          <div class="description-content">
            <p class="config-description">{{ data?.description }}</p>
          </div>
        </div>
      </div>

      <div class="mode-description">
        <t-icon name="info-circle" />
        <span>详细录入模式：录入具体题目及对应课程目标明细</span>
      </div>

      <!-- 1. 课程目标概览 -->
      <div class="objective-overview">
        <h5>课程目标概览</h5>
        <div class="objectives-grid">
          <div 
            v-for="objective in courseObjectives"
            :key="objective.id"
            class="objective-card"
          >
            <div class="objective-header">
              <span class="objective-number">{{ objective.objectiveCode }}</span>
              <span class="objective-weight">权重: {{ objective.weight || 0 }}%</span>
            </div>
            <div class="objective-name">{{ objective.objectiveName }}</div>
            <div class="objective-description">{{ objective.description }}</div>
            <div v-if="objective.graduationRequirement" class="graduation-requirement">
              <span class="label">毕业要求:</span>
              <span class="value">{{ objective.graduationRequirement }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 2. 题目明细表格 -->
      <div class="question-detail-section">
        <div class="section-header">
          <h5>题目对应的课程目标明细</h5>
          <t-button theme="primary" @click="handleAddQuestion">
            <template #icon>
              <t-icon name="add" />
            </template>
            添加题目
          </t-button>
        </div>

        <div class="question-table-container">
          <table class="question-detail-table" v-if="detailedModeData.length > 0">
            <thead>
              <tr>
                <th rowspan="2" class="question-number-col">题目编号</th>
                <th rowspan="2" class="sub-number-col">子项编号</th>
                <th rowspan="2" class="question-type-col">题目类型</th>
                <th rowspan="2" class="question-content-col">题目描述</th>
                <th rowspan="2" class="answer-col">子项（答案）</th>
                <th rowspan="2" class="score-col">分值</th>
                <th rowspan="2" class="objective-col">课程目标</th>
                <th rowspan="2" class="action-col">操作</th>
              </tr>
            </thead>
            <tbody>
              <template v-for="(question, questionIndex) in detailedModeData" :key="question.id">
                <template v-for="(subItem, subIndex) in question.subItems" :key="subItem.id">
                  <tr>
                    <!-- 题目编号 - 合并单元格 -->
                    <td 
                      v-if="subIndex === 0" 
                      :rowspan="question.subItems.length"
                      class="question-number-cell"
                    >
                      <span class="question-number">{{ question.questionNumber }}</span>
                    </td>
                    
                    <!-- 子项编号 -->
                    <td class="sub-number-cell">{{ subItem.subNumber }}</td>
                    
                    <!-- 题目类型 - 合并单元格 -->
                    <td 
                      v-if="subIndex === 0" 
                      :rowspan="question.subItems.length"
                      class="question-type-cell"
                    >
                      <div class="question-type-content">
                        <t-select
                          v-model="question.questionType"
                          :ref="(el) => setQuestionTypeRef(el, questionIndex)"
                          placeholder="选择或输入题目类型"
                          style="width: calc(100% - 36px)"
                          attach="body"
                          filterable
                          creatable
                          @change="(value) => handleQuestionTypeChange(questionIndex, value as string)"
                          @create="(value) => {
                            const newValue = addCustomQuestionType(value as string)
                            if (newValue) {
                              question.questionType = newValue
                              // 触发计算更新
                              nextTick(() => {
                                calculateDetailedModePercentage()
                              })
                            }
                          }"
                        >
                          <t-option 
                            v-for="type in questionTypes" 
                            :key="type.value" 
                            :value="type.value" 
                            :label="type.label" 
                          />
                        </t-select>
                        <t-tooltip content="删除题目" placement="top">
                          <t-button
                            theme="danger"
                            variant="outline"
                            size="small"
                            shape="circle"
                            class="delete-question-btn"
                            @click="handleRemoveQuestion(questionIndex)"
                          >
                            <template #icon>
                              <t-icon name="delete" />
                            </template>
                          </t-button>
                        </t-tooltip>
                      </div>
                    </td>
                    
                    <!-- 题目描述 - 合并单元格 -->
                    <td 
                      v-if="subIndex === 0" 
                      :rowspan="question.subItems.length"
                      class="question-content-cell"
                    >
                      <div class="textarea-container">
                        <t-textarea
                          v-model="question.questionContent"
                          placeholder="请输入题目内容"
                          :maxlength="500"
                          :autosize="{ minRows: 2, maxRows: 8 }"
                          style="resize: none; overflow: hidden;"
                        />
                        <div 
                          v-if="question.questionContent && question.questionContent.length >= 500"
                          class="char-limit-warning limit-exceeded"
                        >
                          字数已达上限：{{ question.questionContent.length }}/500 字
                        </div>
                      </div>
                    </td>
                    
                    <!-- 子项（答案） -->
                    <td class="answer-cell">
                      <div class="textarea-container">
                        <t-textarea
                          v-model="subItem.answer"
                          placeholder="请输入答案内容"
                          :maxlength="200"
                          :autosize="{ minRows: 1, maxRows: 5 }"
                          style="resize: none; overflow: hidden;"
                        />
                        <div 
                          v-if="subItem.answer && subItem.answer.length >= 200"
                          class="char-limit-warning limit-exceeded"
                        >
                          字数已达上限：{{ subItem.answer.length }}/200 字
                        </div>
                      </div>
                    </td>
                    
                    <!-- 分值 -->
                    <td class="score-cell">
                      <t-input-number
                        v-model="subItem.score"
                        :min="0"
                        :max="1000"
                        :step="0.5"
                        placeholder="分值"
                        @change="calculateDetailedModePercentage"
                      />
                    </td>
                    
                    <!-- 课程目标 -->
                    <td class="objective-cell">
                      <t-select
                        v-model="subItem.objectiveId"
                        placeholder="选择课程目标"
                        style="width: 100%"
                        attach="body"
                        @change="calculateDetailedModePercentage"
                      >
                        <t-option 
                          v-for="objective in courseObjectives"
                          :key="objective.id"
                          :value="objective.id"
                          :label="`${objective.objectiveCode} - ${objective.objectiveName}`"
                        />
                      </t-select>
                    </td>
                    
                    <!-- 操作 -->
                    <td class="action-cell">
                      <div class="action-buttons">
                        <t-tooltip content="添加子项" placement="top">
                          <t-button
                            theme="primary"
                            variant="outline"
                            size="small"
                            shape="circle"
                            class="action-btn add-btn"
                            @click="handleAddSubItem(questionIndex)"
                          >
                            <template #icon>
                              <t-icon name="add" />
                            </template>
                          </t-button>
                        </t-tooltip>
                        <t-tooltip content="删除子项" placement="top">
                          <t-button
                            theme="warning"
                            variant="outline"
                            size="small"
                            shape="circle"
                            class="action-btn remove-btn"
                            :disabled="question.subItems.length <= 1"
                            @click="handleRemoveSubItem(questionIndex, subIndex)"
                          >
                            <template #icon>
                              <t-icon name="minus" />
                            </template>
                          </t-button>
                        </t-tooltip>
                      </div>
                    </td>
                  </tr>
                </template>
              </template>
            </tbody>
          </table>
          
          <div v-else class="empty-questions">
            <t-icon name="file" size="48px" />
            <p>暂无题目，请添加题目</p>
          </div>
        </div>
        
        <!-- 表格底部添加题目按钮 -->
        <div class="table-bottom-actions">
          <t-button theme="primary" variant="outline" @click="handleAddQuestion">
            <template #icon>
              <t-icon name="add" />
            </template>
            继续添加题目
          </t-button>
        </div>
      </div>

      <!-- 3. 题目与课程目标汇总表 -->
      <div class="objective-summary">
        <div class="summary-header">
          <h5>题目与课程目标汇总表</h5>
          <div class="summary-tips-inline" :class="`tip-${summaryTableTips.type}`">
            <t-icon :name="summaryTableTips.type === 'success' ? 'check-circle' : summaryTableTips.type === 'warning' ? 'error-circle' : 'info-circle'" />
            <span>{{ summaryTableTips.message }}</span>
          </div>
        </div>
        <div class="summary-table-container">
          <table class="summary-table" v-if="summaryTableData.length > 0">
            <thead>
              <tr>
                <th rowspan="2" class="objective-header">课程目标</th>
                <th 
                  v-for="questionType in usedQuestionTypes" 
                  :key="questionType.value"
                  :colspan="2"
                  class="question-type-header"
                >
                  {{ questionType.label }}
                </th>
                <th rowspan="2" class="total-header">合计</th>
              </tr>
              <tr>
                <template v-for="questionType in usedQuestionTypes" :key="questionType.value">
                  <th class="sub-header">子项编号</th>
                  <th class="sub-header">总分</th>
                </template>
              </tr>
            </thead>
            <tbody>
              <tr v-for="row in summaryTableData" :key="row.objectiveId">
                <td class="objective-cell">{{ row.objectiveName }}</td>
                <template v-for="questionType in usedQuestionTypes" :key="questionType.value">
                  <td class="sub-numbers-cell">
                    {{ row.questionTypes[questionType.value]?.subNumbers?.join(', ') || '-' }}
                  </td>
                  <td class="score-cell">{{ row.questionTypes[questionType.value]?.score || 0 }}</td>
                </template>
                <td class="total-cell">{{ row.totalScore }}</td>
              </tr>
              <!-- 合计行 -->
              <tr class="summary-row">
                <td class="summary-label">合计</td>
                <template v-for="questionType in usedQuestionTypes" :key="questionType.value">
                  <td class="summary-count">{{ getSummaryCount(questionType.value) }}</td>
                  <td class="summary-score">{{ getSummaryScore(questionType.value) }}</td>
                </template>
                <td class="summary-total">{{ getTotalScore() }}</td>
              </tr>
            </tbody>
          </table>
          
          <div v-else class="empty-summary">
            <p>请先添加题目后查看汇总</p>
          </div>
        </div>

  
      </div>
    </div>
  </FullScreenDialog>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, watch } from 'vue'
import { MessagePlugin, DialogPlugin } from 'tdesign-vue-next'
import FullScreenDialog from '@/components/FullScreenDialog/index.vue'
import { storage } from '@/utils/storage'
import { saveAssessmentContentDetail , getAssessmentContentDetail, type DirectEntryConfig, saveDirectEntryConfig } from '@/api/assessment/assessment'
import { getCourseTargetList } from '@/api/training/course'

// Props 定义
interface DetailedConfigData {
  description: string
  title: string
  visible: boolean
  courseId: string
  sectionId: string
  contentId: string
  submitting?: boolean
  assessmentId: string
  taskId?: string
}

const props = defineProps<{ data: DetailedConfigData }>()
const data = props.data

// 响应式数据
const detailedModeData = ref<any[]>([])
const summaryTableData = ref<any[]>([])
const customQuestionTypes = ref<any[]>([])
const isLoadingCache = ref(false)

// 模拟数据（实际应该通过 API 获取）
const currentContent = ref({
  description: '详细录入模式配置'
})

const courseObjectives = ref<any[]>([])

const defaultQuestionTypes = [
  { value: 'single', label: '单选题' },
  { value: 'multiple', label: '多选题' },
  { value: 'fill', label: '填空题' },
  { value: 'short', label: '简答题' },
  { value: 'essay', label: '论述题' },
  { value: 'calculation', label: '计算题' },
  { value: 'design', label: '设计题' },
  { value: 'program', label: '编程题' }
]

const questionTypes = computed(() => {
  return [...defaultQuestionTypes, ...customQuestionTypes.value]
})

const usedQuestionTypes = computed(() => {
  const usedTypes = new Set<string>()
  detailedModeData.value.forEach(question => {
    if (question.questionType) {
      usedTypes.add(question.questionType)
    }
  })
  return questionTypes.value.filter((type: any) => usedTypes.has(type.value))
})

const summaryTableTips = computed(() => {
  if (detailedModeData.value.length === 0) {
    return {
      type: 'info',
      message: '请先添加题目，汇总表将自动更新'
    }
  }

  const missingQuestionTypes = detailedModeData.value.filter(q => !q.questionType).length
  const missingObjectives = detailedModeData.value.reduce((count, question) => {
    return count + question.subItems.filter((sub: any) => !sub.objectiveId).length
  }, 0)

  if (missingQuestionTypes > 0 && missingObjectives > 0) {
    return {
      type: 'warning',
      message: `存在 ${missingQuestionTypes} 个题目未选择题目类型，${missingObjectives} 个子项未选择课程目标`
    }
  } else if (missingQuestionTypes > 0) {
    return {
      type: 'warning',
      message: `存在 ${missingQuestionTypes} 个题目未选择题目类型`
    }
  } else if (missingObjectives > 0) {
    return {
      type: 'warning',
      message: `存在 ${missingObjectives} 个子项未选择课程目标`
    }
  } else {
    return {
      type: 'success',
      message: '当前题目数据完整，汇总表已更新'
    }
  }
})

const totalDetailedPercentage = computed(() => {
  const total = getTotalScore()
  if (total === 0) return 0
  return Math.round(detailedModeData.value.reduce((sum, question) => {
    return sum + question.subItems.reduce((subSum: number, subItem: any) => {
      if (subItem.objectiveId) {
        const objective = courseObjectives.value.find(obj => obj.id === subItem.objectiveId)
        if (objective) {
          return subSum + (subItem.score || 0)
        }
      }
      return subSum
    }, 0)
  }, 0) / total * 100)
})

const isConfigValid = computed(() => {
  return totalDetailedPercentage.value === 100
})

// 题目类型下拉框的引用
const questionTypeRefs = ref<Record<number, any>>({})

// 方法定义
const getTotalScore = (): number => {
  return Math.round(summaryTableData.value.reduce((total, row) => 
    total + row.totalScore, 0) * 100) / 100
}

const getSummaryCount = (questionType: string): number => {
  return summaryTableData.value.reduce((total, row) => 
    total + (row.questionTypes[questionType]?.count || 0), 0)
}

const getSummaryScore = (questionType: string): number => {
  return Math.round(summaryTableData.value.reduce((total, row) => 
    total + (row.questionTypes[questionType]?.score || 0), 0) * 100) / 100
}

const calculateDetailedModePercentage = () => {
  if (!courseObjectives.value || courseObjectives.value.length === 0) return

  // 计算每个课程目标的总分
  const objectiveScores: Record<string, number> = {}
  
  courseObjectives.value.forEach(objective => {
    objectiveScores[objective.id] = 0
  })

  // 遍历所有子项计算分数
  detailedModeData.value.forEach(question => {
    question.subItems.forEach((subItem: any) => {
      if (subItem.objectiveId && objectiveScores[subItem.objectiveId] !== undefined) {
        objectiveScores[subItem.objectiveId] += subItem.score || 0
      }
    })
  })

  const totalScore = Object.values(objectiveScores).reduce((sum, score) => sum + score, 0)

  // 更新汇总表格数据
  summaryTableData.value = courseObjectives.value.map(objective => {
    const questionTypeStats: Record<string, { count: number; score: number; subNumbers: string[] }> = {}
    
    // 初始化各题型统计
    questionTypes.value.forEach((type: any) => {
      questionTypeStats[type.value] = { count: 0, score: 0, subNumbers: [] }
    })

    let totalScore = 0

    // 遍历题目统计各课程目标的题型分布
    detailedModeData.value.forEach(question => {
      question.subItems.forEach((subItem: any) => {
        if (subItem.objectiveId === objective.id && question.questionType) {
          questionTypeStats[question.questionType].count += 1
          questionTypeStats[question.questionType].score += subItem.score || 0
          questionTypeStats[question.questionType].subNumbers.push(subItem.subNumber)
          totalScore += subItem.score || 0
        }
      })
    })

    return {
      objectiveId: objective.id,
      objectiveName: objective.objectiveCode,
      questionTypes: questionTypeStats,
      totalScore: Math.round(totalScore * 100) / 100
    }
  })
}

const addCustomQuestionType = (newTypeName: string) => {
  const typeNameStr = String(newTypeName).trim()
  if (!typeNameStr) return
  
  // 检查是否已存在
  const exists = questionTypes.value.some((type: any) => 
    type.label === typeNameStr || type.value === typeNameStr
  )
  
  if (!exists) {
    const newType = {
      value: `custom_${Date.now()}`,
      label: typeNameStr
    }
    customQuestionTypes.value.push(newType)
    // 立即触发汇总表更新
    nextTick(() => {
      calculateDetailedModePercentage()
    })
    saveToCache() // 保存自定义类型到缓存
    MessagePlugin.success(`已添加自定义题目类型: ${typeNameStr}`)
    return newType.value
  } else {
    MessagePlugin.warning('该题目类型已存在')
    return null
  }
}

const setQuestionTypeRef = (el: any, questionIndex: number) => {
  if (el) {
    questionTypeRefs.value[questionIndex] = el
  } else {
    delete questionTypeRefs.value[questionIndex]
  }
}

const handleQuestionTypeChange = (questionIndex: number, value: string) => {
  detailedModeData.value[questionIndex].questionType = value
  calculateDetailedModePercentage()
  saveToCache()
}

const handleAddQuestion = () => {
  const newQuestion = {
    id: `q_${Date.now()}`,
    questionNumber: detailedModeData.value.length + 1,
    questionType: '',
    questionContent: '',
    subItems: [{
      id: `sub_${Date.now()}`,
      subNumber: '1.1',
      answer: '',
      score: 0,
      objectiveId: ''
    }]
  }
  detailedModeData.value.push(newQuestion)
  updateQuestionNumbers()
  calculateDetailedModePercentage()
  saveToCache() // 自动保存到缓存
}

const handleRemoveQuestion = (index: number) => {
  detailedModeData.value.splice(index, 1)
  updateQuestionNumbers()
  calculateDetailedModePercentage()
  saveToCache() // 自动保存到缓存
}

const handleAddSubItem = (questionIndex: number) => {
  const question = detailedModeData.value[questionIndex]
  const newSubItem = {
    id: `sub_${Date.now()}`,
    subNumber: `${question.questionNumber}.${question.subItems.length + 1}`,
    answer: '',
    score: 0,
    objectiveId: ''
  }
  question.subItems.push(newSubItem)
  updateSubItemNumbers(questionIndex)
  calculateDetailedModePercentage()
  saveToCache() // 自动保存到缓存
}

const handleRemoveSubItem = (questionIndex: number, subIndex: number) => {
  const question = detailedModeData.value[questionIndex]
  question.subItems.splice(subIndex, 1)
  updateSubItemNumbers(questionIndex)
  calculateDetailedModePercentage()
  saveToCache() // 自动保存到缓存
}

const updateQuestionNumbers = () => {
  detailedModeData.value.forEach((question, index) => {
    question.questionNumber = index + 1
    updateSubItemNumbers(index)
  })
}

const updateSubItemNumbers = (questionIndex: number) => {
  const question = detailedModeData.value[questionIndex]
  question.subItems.forEach((subItem: any, subIndex: number) => {
    subItem.subNumber = `${question.questionNumber}.${subIndex + 1}`
  })
}

// 缓存相关函数
const getCacheKey = (contentId: string) => {
  return `assessment_content_config_${contentId}`
}

const saveToCache = () => {
  if (data.contentId && !isLoadingCache.value) {
    const cacheKey = getCacheKey(data.contentId)
    const cacheData = {
      inputMode: 'detailed',
      detailedModeData: detailedModeData.value,
      customQuestionTypes: customQuestionTypes.value,
      timestamp: Date.now()
    }
    storage.set(cacheKey, cacheData)
  }
}

const loadFromCache = () => {
  if (data.contentId) {
    const cacheKey = getCacheKey(data.contentId)
    const cacheData = storage.get(cacheKey)
    
    if (cacheData && cacheData.detailedModeData) {
      isLoadingCache.value = true
      detailedModeData.value = cacheData.detailedModeData
      if (cacheData.customQuestionTypes) {
        customQuestionTypes.value = cacheData.customQuestionTypes
      }
      nextTick(() => {
        calculateDetailedModePercentage()
        isLoadingCache.value = false
      })
      return true
    }
  }
  return false
}
const configSubmitting = ref(false)


/**
 * 保存配置数据
 * @returns {Promise<void>}
 */
// 事件处理
const handleSave = async () => {
  try {
    configSubmitting.value = true
    // 构造DTO参数
    const totalScore = getTotalScore()
    const questions = detailedModeData.value.map((q, qIdx) => {
      return {
        questionId: q.questionId,
        questionNumber: q.questionNumber,
        questionOrder: qIdx + 1,
        questionTopic: q.questionContent,
        questionDetail: q.questionContent,
        questionType: getQuestionTypeInt(q.questionType),
        questionScore: q.subItems.reduce((sum, sub) => sum + (Number(sub.score) || 0), 0),
        answers: q.subItems.map((sub, subIdx) => ({
          answerId: sub.answerId,
          questionId: q.questionId,
          questionAnswer: sub.answer,
          answerNo: subIdx + 1,
          answerScore: Number(sub.score) || 0,
          courseObjectiveId: sub.objectiveId
        }))
      }
    })
    const payload = {
      assessmentId: data.sectionId ? Number(data.sectionId) : undefined,
      assessmentName: data.title,
      totalScore: totalScore,
      questions
    }
   // 保存课程目标的分数配置
    await handleSaveObjectiveScores()
    // 发送POST请求，详细录入下的保存配置数据
    const res = await saveAssessmentContentDetail(payload)
    if (res && res.code === 200) {
      console.log('配置保存成功:', res)
      // 保存成功后，更新缓存
      saveToCache()
      // 清除缓存
      resetConfigData();
      if (data.contentId) {
        const cacheKey = getCacheKey(data.contentId)
        storage.remove(cacheKey)
      }
      // 更新父组件状态
      data.submitting = false
      data.visible = false
      // 显示成功消息
      MessagePlugin.success('详细录入配置保存成功')
      data.visible = false
    } else {
      console.log('配置保存失败:', res)
      // 显示错误消息
      MessagePlugin.error(res?.message ||   '配置保存失败，请重试')
    }
  } catch (error) {
    console.error('配置保存失败:', error)
    MessagePlugin.error('配置保存失败，请重试')
  } finally {
    configSubmitting.value = false
  }
}

// 题目类型字符串转为后端枚举int（如需调整请根据后端实际类型映射）
function getQuestionTypeInt(type: string): number {
  const map: Record<string, number> = {
    single: 1,
    multiple: 2,
    fill: 3,
    short: 4,
    essay: 5,
    calculation: 6,
    design: 7,
    program: 8
  }
  if (type && map[type]) return map[type]
  // 自定义类型或未匹配，返回99
  return 99
}
//}
// 课程目标最终分值存储在 summaryTableData 中，每个元素的 totalScore 即为该目标的分值
async function handleSaveObjectiveScores() {
  try {
    // 构建API请求参数
    const configs: DirectEntryConfig[] = summaryTableData.value.map(item => ({
      courseObjectiveId: item.objectiveId,
      description: item.objectiveName,
      totalScore: item.totalScore,
      percentage: 0 // 可根据需要计算百分比
    }));

    console.log('准备保存的配置:', configs);

    // 调用API保存配置
    const response = await saveDirectEntryConfig({
      assessmentId: data.assessmentId,
      configs: configs
    });

    if (response.code === 0 || response.code === 200) {
      MessagePlugin.success('配置已成功保存');
      handleCancel();
    } else {
      MessagePlugin.error(`保存失败: ${response.message || '未知错误'}`);
    }
  } catch (error) {
    console.error('保存直接录入配置失败:', error);
    MessagePlugin.error('保存失败，请检查网络连接后重试');
  }
}




// 防止重复触发的标志位
const isHandlingCancel = ref(false)
// 检查是否有未保存的数据
const hasUnsavedData = computed(() => {
  return detailedModeData.value.length > 0 || customQuestionTypes.value.length > 0

})
const resetConfigData = () => {
  detailedModeData.value = []
  summaryTableData.value = []
}
const handleCancel = () => {
   // 设置标志位，防止重复触发
   isHandlingCancel.value = true
  
  if (hasUnsavedData.value) {
    // 临时降低全屏弹窗的z-index
    const fullScreenDialog = document.querySelector('.full-screen-dialog-wrapper')
    if (fullScreenDialog) {
      (fullScreenDialog as HTMLElement).style.zIndex = '2000'
    }
    const confirmDialog = DialogPlugin.confirm({
      header: '数据处理选择',
      body: '当前有未保存的配置数据，请选择如何处理：',
      confirmBtn: '清空数据',
      cancelBtn: '保留数据',
      theme: 'warning',
      zIndex: 3001, // 设置比全屏弹窗更高的层级
      onConfirm: () => {
        // 用户选择清空数据，关闭弹窗并清空数据和缓存
        data.visible = false
        resetConfigData()
        // 清除缓存
        if (data.contentId) {
          const cacheKey = getCacheKey(data.contentId)
          storage.remove(cacheKey)
        }
        MessagePlugin.info('已清空配置数据和缓存')
        confirmDialog.destroy()
        // 恢复全屏弹窗的z-index（虽然弹窗已关闭，但为了保险起见）
        if (fullScreenDialog) {
          (fullScreenDialog as HTMLElement).style.zIndex = '3000'
        }
        // 重置标志位
        isHandlingCancel.value = false
      },
      onCancel: () => {
        // 用户选择保留数据，关闭弹窗但保留数据和缓存
        data.visible = false
        MessagePlugin.info('数据已保留，下次打开时将自动恢复')
        confirmDialog.destroy()
        // 恢复全屏弹窗的z-index（虽然弹窗已关闭，但为了保险起见）
        if (fullScreenDialog) {
          (fullScreenDialog as HTMLElement).style.zIndex = '3000'
        }
        // 重置标志位
        isHandlingCancel.value = false
      }
    })
  } else {
    // 没有未保存数据，直接关闭
    data.visible = false
    resetConfigData()
    // 重置标志位
    isHandlingCancel.value = false
  }
}

const handleClose = () => {
   // 只有当不是通过取消按钮触发时才处理关闭事件
   if (!isHandlingCancel.value && data.visible) {
    handleCancel()
  }
}

// 监听data.visible变化，自动加载缓存
watch(() => data.visible, (newVal) => {
  if (newVal) {
    // 拉取课程目标数据
    if (data.courseId) {
      getCourseTargetList(Number(data.courseId)).then((list) => {
        courseObjectives.value = (list || []).map((item: any) => ({
          id: String(item.objectiveId),
          objectiveCode: `课程目标${item.number}`,
          objectiveName: item.objectiveName,
          description: item.description || '',
          weight: item.expectedScore || 0,
          graduationRequirement: item.po?.description || ''
        }))
        nextTick(() => {
          calculateDetailedModePercentage()
        })
      }).catch(() => {})
    }

    // 拉取考核内容详情
    if (data.assessmentId) {
      getAssessmentContentDetail(data.assessmentId).then((res) => {
        console.log('响应的数据：', res)
          if (res && res.code === 200 && res.data && Array.isArray(res.data.questions)) {
            
            // 转换为页面数据结构
            detailedModeData.value = res.data.questions.map((q: any, idx: number) => ({
              id: String(q.questionId),
              questionId:String(q.questionId),
              questionNo:q.questionNo,// 题目顺序号
              questionNumber: idx + 1,
              questionType: getQuestionTypeStr(q.questionType),
              questionContent: q.questionTopic || q.questionDetail || '',
              subItems: (q.answers || []).map((a: any, subIdx: number) => ({
                id: String(a.id),
                answerId: String(a.answerId),
                questionId: String(q.questionId),
                subNumber: `${idx + 1}.${subIdx + 1}`,
                answer: a.questionAnswer || '',
                score: Number(a.answerScore) || 0,
                objectiveId: a.courseObjectiveId || ''
              }))
            }))
            nextTick(() => {
              calculateDetailedModePercentage()
            })
          }
        }).catch(() => {})
    }

    console.log('拉取的数据：', detailedModeData.value)
    // 尝试从缓存加载数据
    const hasCache = loadFromCache()
    // 如果没有缓存数据，初始化示例数据
    if (!hasCache && detailedModeData.value.length === 0) {
      detailedModeData.value = [
        {
          id: 'demo_q1',
          questionNumber: 1,
          questionType: 'single',
          questionContent: '请选择正确的Java语法',
          subItems: [
            {
              id: 'demo_sub1',
              subNumber: '1.1',
              answer: 'int x = 10;',
              score: 5,
              objectiveId: 'obj1'
            },
            {
              id: 'demo_sub2',
              subNumber: '1.2',
              answer: 'String name = "Java";',
              score: 5,
              objectiveId: 'obj1'
            }
          ]
        }
      ]
      saveToCache()
    }
    // 确保数据被正确计算
    nextTick(() => {
      calculateDetailedModePercentage()
    })
  }
// 后端题目类型int转页面字符串
function getQuestionTypeStr(type: number): string {
  const map: Record<number, string> = {
    1: 'single',
    2: 'multiple',
    3: 'fill',
    4: 'short',
    5: 'essay',
    6: 'calculation',
    7: 'design',
    8: 'program'
  }
  return map[type] || 'custom'
}
})

// 监听数据变化，自动保存到缓存
watch(detailedModeData, () => {
  if (!isLoadingCache.value) {
    saveToCache()
    nextTick(() => {
      calculateDetailedModePercentage()
    })
  }
}, { deep: true })

watch(customQuestionTypes, () => {
  if (!isLoadingCache.value) {
    saveToCache()
    nextTick(() => {
      calculateDetailedModePercentage()
    })
  }
}, { deep: true })

// function saveDirectEntryConfig(arg0: { assessmentId: string; configs: DirectEntryConfig[] }) {
//   throw new Error('Function not implemented.')
// }
</script>

<style lang="less" scoped>
@import '../styles/detailed-config.less';
</style> 