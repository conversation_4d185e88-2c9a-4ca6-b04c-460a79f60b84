<template>
  <div class="layout">
    <!-- 主页面内容 -->
      <!-- 主内容区域 -->
      <t-content class="main-content">
        <!-- 欢迎区域与快速概览 -->
        <div class="quick-access">
          <div class="welcome-box">
            <h1>问卷主页</h1>
            <p class="subtitle">今日新增数据已更新</p>
          </div>
          <div class="quick-actions">
            <t-button
              variant="outline"
              class="action-btn"
              @click="goto"
            >
              <template #icon><t-icon name="file"/></template>
              问卷管理
            </t-button>
          </div>
        </div>

        <!-- 数据统计卡片 -->
        <t-row :gutter="16" class="stats-row">
          <t-col v-for="(card, index) in statsCards" :key="index" :xs="12" :lg="3">
            <t-card class="stat-card" :class="`card-${index}`" hover-shadow>
              <div class="card-content">
                <div class="icon-wrapper" :style="{backgroundColor: card.bgColor}">
                  <t-icon :name="card.icon" class="card-icon"/>
                </div>
                <div class="card-text">
                  <div class="card-value">{{ card.value }}</div>
                  <div class="card-title">{{ card.title }}</div>
                </div>
              </div>
            </t-card>
          </t-col>
        </t-row>

        <!-- 图表区域 -->
        <div class="chart-container">
          <t-card class="main-chart" hover-shadow>
            <div class="chart-header">
              <h3>内容数据趋势（近7日）</h3>
              <t-select v-model="timeRange" class="time-select" @change="updateChartData">
                <t-option value="week" label="近7天"/>
                <t-option value="month" label="近30天"/>
              </t-select>
            </div>
            <div id="lineChart" style="height: 300px"/>
            <div class="chart-footer">
              <t-link theme="primary" hover="color" class="more-link">
                <template #icon><t-icon name="chevron-right"/></template>
                查看更多数据
              </t-link>
            </div>
          </t-card>

          <t-card class="side-chart" hover-shadow>
            <h3>问卷类别占比</h3>
            <div id="pieChart" style="height: 260px"/>
            <div class="legend">
              <div v-for="item in pieLegend" :key="item.name" class="legend-item">
                <span class="dot" :style="{backgroundColor: item.color}"></span>
                <span class="label">{{ item.name }}</span>
                <span class="value">{{ item.value }}</span>
              </div>
            </div>
          </t-card>
        </div>

        <!-- 数据统计表 -->
        <t-card class="data-table" hover-shadow>
          <div class="table-header">
            <h3>问卷数据明细</h3>
            <t-space>
              <t-input
                v-model="tableSearchQuery"
                placeholder="搜索..."
                class="search-input"
                clearable
                @change="handleTableSearch"
              >
                <template #prefix-icon><t-icon name="search"/></template>
              </t-input>
              <t-button variant="outline">
                <template #icon><t-icon name="download"/></template>
                导出数据
              </t-button>
              <t-button theme="primary">
                <template #icon><t-icon name="filter"/></template>
                筛选
              </t-button>
            </t-space>
          </div>
          <t-table
            :columns="tableColumns"
            :data="filteredTableData"
            row-key="id"
            :pagination="pagination"
            hover
            stripe
            @page-change="handleTablePageChange"
          />
        </t-card>
      </t-content>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, reactive, computed, nextTick, onUnmounted } from 'vue'
import * as echarts from 'echarts'
import router from "@/router";


// 类型定义
interface Question {
  title: string
  type: 'single' | 'multiple' | 'text'
  options?: {
    text: string
    percentage?: number
  }[]
}



interface Questionnaire {
  id: string;
  title: string;
  description: string;
  type: string;
  status: keyof typeof statusMap; // status 类型限定为 'draft' | 'published'
  deadline: string;
  questions: Question[];
  responses: number;
  creator: string;
  createdAt?: string;
}

interface TableItem {
  id: string
  date: string
  name: string
  user: string
  class: string
  type: string
  publisher: string
}

const tableSearchQuery = ref('')
const timeRange = ref('week')

const statusMap = {
  draft: '草稿',
  published: '已发布'
}

const goto = () => {
  router.push('./questionnaire_management')
}

// 问卷列表数据
const questionnaireList = ref<Questionnaire[]>([
  {
    id: '1001',
    title: '学生满意度调查',
    type: 'normal',
    status: 'published',
    responses: 142,
    deadline: '2025-03-31',
    creator: '管理员',
    description: '关于学生对教学质量的满意度调查',
    questions: [
      {
        title: '您对教学质量是否满意？',
        type: 'single',
        options: [
          { text: '非常满意', percentage: 65 },
          { text: '满意', percentage: 25 },
          { text: '一般', percentage: 7 },
          { text: '不满意', percentage: 3 }
        ]
      },
      {
        title: '您对哪些方面不满意？',
        type: 'multiple',
        options: [
          { text: '教学方式', percentage: 45 },
          { text: '课程内容', percentage: 30 },
          { text: '作业量', percentage: 60 },
          { text: '考核方式', percentage: 25 },
          { text: '师生互动', percentage: 15 }
        ]
      },
      {
        title: '您对课程有什么建议？',
        type: 'text'
      }
    ],
    createdAt: '2025-01-15'
  },
  // 其他问卷数据...
])


// 统计卡片数据
const statsCards = [
  {
    icon: 'file',
    title: '问卷个数',
    value: computed(() => `${questionnaireList.value.length}套`),
    bgColor: 'rgba(91, 143, 249, 0.1)'
  },
  {
    icon: 'file',
    title: '收集问卷数',
    value: computed(() => questionnaireList.value.reduce((sum, item) => sum + item.responses, 0).toLocaleString()),
    bgColor: 'rgba(90, 216, 166, 0.1)'
  },
  {
    icon: 'file-add',
    title: '日新增答卷',
    value: '874',
    bgColor: 'rgba(255, 161, 90, 0.1)'
  },
  {
    icon: 'error-circle',
    title: '问题率',
    value: '2.8%',
    bgColor: 'rgba(250, 112, 112, 0.1)'
  }
]

// 饼图数据
const pieLegend = [
  { name: '心理测试类', value: '16%', color: '#5B8FF9' },
  { name: '班级问题类', value: '48%', color: '#5AD8A6' },
  { name: '专业反馈类', value: '36%', color: '#5D7092' }
]

// 表格配置
const tableColumns = [
  { colKey: 'date', title: '日期', width: 120 },
  { colKey: 'id', title: '答卷ID', width: 100 },
  { colKey: 'name', title: '问卷名', ellipsis: true },
  { colKey: 'user', title: '答卷人', width: 100 },
  { colKey: 'class', title: '班级', width: 100 },
  { colKey: 'type', title: '问卷类型', width: 120 },
  { colKey: 'publisher', title: '发布人', width: 120 }
]

const tableData = ref<TableItem[]>([
  {
    id: '01',
    date: '2025-01-19',
    name: '《学生自主学习时间调查问卷》',
    user: '小明',
    class: '233',
    type: '普通问卷',
    publisher: '小城'
  },
  {
    id: '02',
    date: '2025-01-18',
    name: '《科研成果调查问卷》',
    user: '小赵',
    class: '234',
    type: '科研问卷',
    publisher: '雄安承'
  }
])

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: computed(() => tableData.value.length)
})


const filteredTableData = computed(() => {
  const query = tableSearchQuery.value?.toString().trim().toLowerCase() || '';
  return tableData.value.filter(item =>
    (item.name?.toLowerCase().includes(query) ||
      item.user?.toLowerCase().includes(query) ||
      (item.publisher && item.publisher.toLowerCase().includes(query)))
  );
});



const handleTableSearch = () => {
  pagination.current = 1
}

const handleTablePageChange = (pageInfo: { current: number }) => {
  pagination.current = pageInfo.current
}

const updateChartData = () => {
  // 这里可以添加根据时间范围更新图表数据的逻辑
  initCharts()
}

// 图表初始化
let lineChart: echarts.ECharts | null = null
let pieChart: echarts.ECharts | null = null

const initCharts = () => {
  // 销毁之前的图表实例
  if (lineChart) lineChart.dispose()
  if (pieChart) pieChart.dispose()

  // 折线图
  lineChart = echarts.init(document.getElementById('lineChart'))
  lineChart.setOption({
    tooltip: {
      trigger: 'axis',
      formatter: '{b}<br/>问卷数量: {c}'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: timeRange.value === 'week'
        ? ['03-09', '03-10', '03-11', '03-12', '03-13', '03-14', '03-15']
        : Array.from({ length: 30 }, (_, i) => `03-${i + 1}`),
      axisLine: {
        lineStyle: {
          color: '#E5E5E5'
        }
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: false
      },
      splitLine: {
        lineStyle: {
          color: '#F5F5F5'
        }
      }
    },
    series: [{
      data: timeRange.value === 'week'
        ? [820, 932, 901, 934, 1290, 1330, 1320]
        : Array.from({ length: 30 }, () => Math.floor(Math.random() * 1500) + 500),
      type: 'line',
      smooth: true,
      symbol: 'circle',
      symbolSize: 8,
      itemStyle: {
        color: '#5B8FF9',
        borderWidth: 2
      },
      lineStyle: {
        width: 3
      },
      areaStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: 'rgba(91, 143, 249, 0.4)' },
          { offset: 1, color: 'rgba(91, 143, 249, 0.1)' }
        ])
      }
    }]
  })

  // 饼图
  pieChart = echarts.init(document.getElementById('pieChart'))
  pieChart.setOption({
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c} ({d}%)'
    },
    series: [{
      type: 'pie',
      radius: ['50%', '70%'],
      avoidLabelOverlap: false,
      itemStyle: {
        borderRadius: 6,
        borderColor: '#fff',
        borderWidth: 2
      },
      label: {
        show: false,
        position: 'center'
      },
      emphasis: {
        label: {
          show: true,
          fontSize: '18',
          fontWeight: 'bold'
        }
      },
      data: [
        { value: 16, name: '心理测试类' },
        { value: 48, name: '班级问题类' },
        { value: 36, name: '专业反馈类' }
      ]
    }]
  })
}

onMounted(() => {
  nextTick(() => {
    initCharts()
  })

  // 响应式调整
  window.addEventListener('resize', () => {
    lineChart?.resize()
    pieChart?.resize()
  })
})

// 组件卸载时销毁图表实例
onUnmounted(() => {
  lineChart?.dispose()
  pieChart?.dispose()
  window.removeEventListener('resize', () => {
    lineChart?.resize()
    pieChart?.resize()
  })
})
</script>

<style scoped lang="less">
.layout {
  background-color: var(--td-bg-color-page);
}

.main-content {
  padding: var(--td-comp-paddingTB-xl) var(--td-comp-paddingLR-xl);
}

.quick-access {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--td-comp-margin-xl);
  padding: var(--td-comp-paddingTB-xxl) var(--td-comp-paddingLR-xxl);
  background: var(--td-bg-color-container);
  border-radius: var(--td-radius-extraLarge);
  box-shadow: var(--td-shadow-1);

  .welcome-box {
    h1 {
      margin: 0;
      font: var(--td-font-title-large);
      color: var(--td-text-color-primary);
    }

    .subtitle {
      margin-top: var(--td-comp-margin-s);
      color: var(--td-text-color-secondary);
      font: var(--td-font-body-medium);
    }
  }
}

.stats-row {
  margin-bottom: var(--td-comp-margin-xl);

  :deep(.t-col) {
    margin-bottom: var(--td-comp-margin-l);
  }

  .stat-card {
    height: 120px;
    border-radius: var(--td-radius-extraLarge);
    transition: transform 0.2s var(--td-bezier-easeInOut);

    &:hover {
      transform: translateY(-4px);
    }

    .card-content {
      display: flex;
      align-items: center;
      height: 100%;
      padding: 0 var(--td-comp-paddingLR-xl);

      .icon-wrapper {
        flex: 0 0 48px;
        width: 48px;
        height: 48px;
        border-radius: var(--td-radius-medium);
        display: grid;
        place-items: center;
        margin-right: var(--td-comp-margin-xxl);

        .card-icon {
          font-size: var(--td-font-size-title-large);
          color: var(--td-brand-color);
        }
      }

      .card-text {
        .card-value {
          font: var(--td-font-title-extraLarge);
          color: var(--td-text-color-primary);
          line-height: 1.2;
        }

        .card-title {
          font: var(--td-font-body-medium);
          color: var(--td-text-color-secondary);
        }
      }
    }
  }
}

.chart-container {
  display: grid;
  gap: var(--td-comp-margin-xl);
  grid-template-columns: 1fr 320px;
  margin-bottom: var(--td-comp-margin-xl);

  @media (max-width: 992px) {
    grid-template-columns: 1fr;
  }

  .main-chart,
  .side-chart {
    border-radius: var(--td-radius-extraLarge);
    padding: var(--td-comp-paddingTB-xxl) var(--td-comp-paddingLR-xxl);

    h3 {
      margin: 0 0 var(--td-comp-margin-xxl);
      font: var(--td-font-title-medium);
    }
  }

  .chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--td-comp-margin-xxl);
  }

  .legend {
    margin-top: var(--td-comp-margin-xxl);

    &-item {
      display: flex;
      align-items: center;
      margin-bottom: var(--td-comp-margin-s);
      font: var(--td-font-body-medium);

      .dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-right: var(--td-comp-margin-s);
      }

      .value {
        margin-left: auto;
        color: var(--td-text-color-primary);
      }
    }
  }
}

.data-table {
  border-radius: var(--td-radius-extraLarge);
  padding: var(--td-comp-paddingTB-xxl) var(--td-comp-paddingLR-xxl);

  .table-header {
    display: flex;
    flex-wrap: wrap;
    gap: var(--td-comp-margin-l);
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--td-comp-margin-xl);

    h3 {
      margin: 0;
      font: var(--td-font-title-medium);
    }

    .search-input {
      width: 240px;
    }
  }
}

.quick-actions {
  display: flex;
  gap: var(--td-comp-margin-l);
}
</style>
