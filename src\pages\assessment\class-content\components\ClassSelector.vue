<template>
  <t-dialog
    v-model:visible="dialogVisible"
    width="80%"
    :top="50"
    :close-btn="true"
    :cancel-btn="null"
    :confirm-btn="null"
    placement="center"
    :destroy-on-close="false"
    class="class-selector"
    :footer="false"
  >
    <div class="selector-content">
      <div class="selector-header">
        <div class="header-info">
          <t-icon name="layers" class="header-icon" />
          <h2>选择要管理的班级</h2>
        </div>
        <div class="current-class" v-if="currentClassInfo">
          <span class="current-label">当前班级：</span>
          <span class="current-name">{{ currentClassInfo.className }}</span>
        </div>
      </div>
      
      <div class="class-grid">
        <ClassCardWithSelection
          v-for="classItem in classList"
          :key="classItem.classId"
          :worklist-data="classItem"
          :current-class-id="currentClassId"
          @card-click="handleCardClick"
        />
      </div>

      <div class="selector-footer">
        <t-icon name="info-circle" />
        <span>点击班级卡片可快速切换到对应班级的考核管理</span>
      </div>
    </div>
  </t-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { WorklistItem } from '@/api/base/classes'
import ClassCardWithSelection from './ClassCardWithSelection.vue'

interface Props {
  visible: boolean
  currentClassId: string | number
  classes: WorklistItem[]
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'update:visible': [value: boolean]
  'select': [classInfo: WorklistItem]
}>()

const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const classList = computed(() => props.classes)

const currentClassInfo = computed(() => {
  return props.classes.find(c => String(c.classId) === String(props.currentClassId))
})

const handleCardClick = (classId: string | number) => {
  const classInfo = props.classes.find(c => String(c.classId) === String(classId))
  if (classInfo && String(classInfo.classId) !== String(props.currentClassId)) {
    emit('select', classInfo)
    emit('update:visible', false)
  }
}

const handleSelectClass = (classInfo: WorklistItem) => {
  if (String(classInfo.classId) !== String(props.currentClassId)) {
    emit('select', classInfo)
    emit('update:visible', false)
  }
}
</script>

<style lang="less" scoped>
.class-selector {
  :deep(.t-dialog__body) {
    padding: 0;
  }

  .selector-content {
    .selector-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;
      padding-bottom: 16px;
      border-bottom: 1px solid var(--td-border-level-1-color);

      .header-info {
        display: flex;
        align-items: center;
        gap: 12px;

        .header-icon {
          color: var(--td-brand-color);
          font-size: 24px;
        }

        h2 {
          margin: 0;
          font-size: 18px;
          font-weight: 600;
          color: var(--td-text-color-primary);
        }
      }

      .current-class {
        display: flex;
        align-items: center;
        gap: 8px;

        .current-label {
          color: var(--td-text-color-secondary);
          font-size: 14px;
        }

        .current-name {
          color: var(--td-text-color-primary);
          font-weight: 500;
        }
      }
    }

    .class-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: 16px;
      margin-bottom: 24px;
      max-height: 400px;
      overflow-y: auto;
      padding-top: 10px;
    }

    .selector-footer {
      display: flex;
      align-items: center;
      gap: 8px;
      padding-top: 16px;
      border-top: 1px solid var(--td-border-level-1-color);
      color: var(--td-text-color-secondary);
      font-size: 12px;

      .t-icon {
        color: var(--td-brand-color);
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .class-selector {
    .selector-content {
      padding: 16px;

      .selector-header {
        flex-direction: column;
        gap: 12px;
        align-items: stretch;
      }

      .class-grid {
        grid-template-columns: 1fr;
        max-height: 300px;
      }
    }
  }
}
</style> 