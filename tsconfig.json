{"compilerOptions": {"target": "esnext", "module": "esnext", "moduleResolution": "bundler", "jsx": "preserve", "jsxImportSource": "vue", "sourceMap": true, "resolveJsonModule": true, "esModuleInterop": true, "skipLibCheck": true, "allowSyntheticDefaultImports": true, "lib": ["esnext", "dom"], "types": ["vite/client"], "noEmit": true, "allowJs": true, "baseUrl": "./", "paths": {"@/*": ["src/*"], "/@types/*": ["src/types/*"]}, "noImplicitAny": true, "strictFunctionTypes": true, "strictBindCallApply": true, "noImplicitThis": true, "alwaysStrict": true}, "include": ["**/*.ts", "**/*.js", "src/**/*.d.ts", "src/**/*.ts", "src/**/*.tsx", "src/**/*.vue", "src/**/*.js", "node_modules/tdesign-vue-next/global.d.ts"], "compileOnSave": false}