import Mock from 'mockjs';
import { MockMethod } from 'vite-plugin-mock';

export default [
  {
    url: '/api/student/GraduationCourse/list',
    method: 'get',
    response: ({ query }) => {
      const gid = query.gid;
      let list = [];
      if (gid === '1') {
        list = Mock.mock({
          'list|1-1': [
            {
              cid: 1,
              courseName: '道德',
              semester: '第一学期',
              type:'选修',
              sid:1,
            },
            {
              cid: 2,
              courseName: '思想',
              semester: '第二学期',
              type:'必修',
              sid:2,
            },
            {
              cid: 3,
              courseName: 'Java',
              semester: '第三学期',
              type:'必修',
              sid:3,
            },
          ],
        }).list;
      }
      if (gid === '2') {
        list = Mock.mock({
          'list|1-1': [
            {
              cid: 4,
              courseName: 'C语言',
              semester: '第一学期',
              type:'必修',
              sid:1,
            },
            {
              cid: 5,
              courseName: 'spark',
              semester: '第二学期',
              type:'必修',
              sid:2,
            },
          ],
        }).list;
      }
      if (gid === '3') {
        list = Mock.mock({
          'list|1-1': [
            {
              cid: 6,
              courseName: 'hadoop',
              semester: '第一学期',
              type:'必修',
              sid:1,
            },
            {
              cid: 7,
              courseName: 'unity',
              semester: '第二学期',
              type:'选修',
              sid:2,
            },
            {
              cid: 8,
              courseName: 'scala',
              semester: '第五学期',
              type:'选修',
              sid:5,
            },
          ],
        }).list;
      }
      return {
        code: 200,
        message:'success',
        data: { list },
      };
    },
  },
] as MockMethod[];
