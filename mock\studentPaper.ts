import Mock from 'mockjs';
import { MockMethod } from 'vite-plugin-mock';

export default [
  {
    url: '/api/student/paper/list',
    method: 'get',
    response: () => {
      let list = [];
      list = Mock.mock({
        'list|1-1': [
          {
            pid: 1,
            paperName: '第一章测试',
            courseName: '道德',
            cid: 1,
            teacher: '王老师',
            released: '2025年04月14号',
            startTime: '2025年04月16号09:00:00',
            endTime: '2025年04月20号23:59:59',
            status: 0
          },
          {
            pid: 2,
            paperName: '第二章测试',
            courseName: '道德',
            cid: 1,
            teacher: '王老师',
            released: '2025年04月10号',
            startTime: '2025年04月12号08:00:00',
            endTime: '2025年04月14号23:59:59',
            status: 1
          },
          {
            pid: 3,
            paperName: '基础数据',
            courseName: 'Java',
            cid: 3,
            teacher: '李老师',
            released: '2025-04-13',
            startTime: '2025-04-15T00:00:00',
            endTime: '2025-04-17T23:59:59',
            status: 1
          },
          {
            pid: 4,
            paperName: '基本数据类型',
            courseName: 'C语言',
            cid: 4,
            teacher: '张老师',
            released: '2025-04-15',
            startTime: '2024-',
            endTime: '',
            status: 2
          },
          {
            pid: 5,
            paperName: '代码作业',
            courseName: 'C语言',
            cid: 4,
            teacher: '张老师',
            released: '2025-04-01',
            startTime: '2025-04-05T00:00:00',
            endTime: '2025-04-14T23:59:59',
            status:3
          },
          {
            pid: 6,
            paperName: '中级编程',
            courseName: 'Python',
            cid: 5,
            teacher: '赵老师',
            released: '2025-04-14',
            startTime: '2025-04-16T14:00:00',
            endTime: '2025-04-18T14:00:00',
            status: 3 // 已结束
          },
        ],
      }).list;
      return {
        code: 200,
        message:'success',
        data: { list },
      };
    },
  },
  {
    url: '/api/student/paper/noStart/list',
    method: 'get',
    response: ({query}) => {
      const pid = query.pid
      let list = [];
      if (pid==='1'){
        list = Mock.mock({
          'list|1-1': [
            {
              paperName: '第一章测试',
              courseName: '道德',
              startTime:'2024年5月8号18点30分',
              endTime:'2024年6月10号12点30分'
            },
          ],
        }).list;
      }
      return {
        code: 200,
        message:'success',
        data: { list },
      };
    },
  },
  {
    url: '/api/student/paper/write/list',
    method: 'get',
    response: ({query}) => {
      const pid = query.pid
      let list = [];
      if (pid==='2'){
        list = Mock.mock({
          'list|1-1': [
            {
              id: 1,
              type: 1,
              title: 'C语言的发明者是？',
              options: ['Dennis Ritchie', 'Bjarne Stroustrup', 'James Gosling', 'Guido van Rossum'],
            },
            {
              id: 2,
              type: 1,
              title: '下列哪个是C语言的关键字？',
              options: ['class', 'printf', 'auto', 'namespace'],
            },
            {
              id: 3,
              type: 2,
              title: '下列哪些是C语言的基本数据类型？（多选）',
              options: ['int', 'string', 'float', 'bool', 'double'],
            },
            {
              id: 4,
              type: 3,
              title: '请描述指针和引用的主要区别',
            },
            {
              id: 5,
              type: 1,
              title: '用于取地址的运算符是？',
              options: ['*', '&', '#', '@'],
            },
            {
              id: 6,
              type: 2,
              title: '下列哪些属于动态内存分配函数？（多选）',
              options: ['malloc()', 'new', 'calloc()', 'alloc()', 'realloc()'],
            },
            {
              id: 7,
              type: 1,
              title: 'C程序执行入口函数是？',
              options: ['main()', 'start()', 'init()', 'run()'],
            },
            {
              id: 8,
              type: 3,
              title: '举例说明结构体和联合体的区别',
            },
            {
              id: 9,
              type: 2,
              title: '下列哪些是文件操作函数？（多选）',
              options: ['fopen()', 'readfile()', 'fwrite()', 'save()', 'fclose()'],
            },
            {
              id: 10,
              type: 1,
              title: '预处理指令以什么符号开头？',
              options: ['#', '@', '$', '//'],
            }
          ],
        }).list;
      }
      return {
        code: 200,
        message:'success',
        data: { list },
      };
    },
  },
  {
    url: '/api/student/paper/correct/list',
    method: 'get',
    response: ({query}) => {
      const pid = query.pid
      let list = [];
      if (pid==='4'){
        list = Mock.mock({
          'list|1-1': [
            {
              paperName: '基本数据类型',
              courseName: 'c语言',
              startTime:'2024年5月8号18点30分',
              endTime:'2024年6月10号12点30分',
              submitTime:'2024年6月5号11点34分',
            },
          ],
        }).list;
      }
      if (pid==='2'){
        list = Mock.mock({
          'list|1-1': [
            {
              paperName: '第二章测试',
              courseName: 'c语言',
              startTime:'2024年5月8号18点30分',
              endTime:'2024年6月10号12点30分',
              submitTime:'2024年6月5号11点34分',
            },
          ],
        }).list;
      }

      return {
        code: 200,
        message:'success',
        data: { list },
      };
    },
  },
  {
    url: '/api/student/paper/end/list',
    method: 'get',
    response: ({query}) => {
      const pid = query.pid
      let list = [];
      if (pid==='5'){
        list = Mock.mock({
          'list|1-1': [
            {
              paperName: '代码作业',
              courseName: 'c语言',
              startTime:'2024年5月8号18点30分',
              endTime:'2024年6月10号12点30分',
              submitTime:'2024年6月5号11点34分',
              grade:89,
            },
          ],
        }).list;
      }
      return {
        code: 200,
        message:'success',
        data: { list },
      };
    },
  },
] as MockMethod[];
