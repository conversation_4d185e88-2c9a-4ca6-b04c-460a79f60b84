<template>
  <div class="plan-management-container">
    <!-- 顶部信息栏 -->
    <div class="top-info-bar">
      <div class="user-info">
        <div class="user-avatar">
          <t-icon name="user-circle" size="24px" />
        </div>
        <div class="user-details">
          <div class="user-role">
            <t-tag shape="round" theme="primary" size="small">
              {{ currentUser.role }}
            </t-tag>
            <span class="divider">|</span>
            <!-- 专业切换下拉框 -->
            <t-select
              v-model="currentMajor"
              :options="majorOptions"
              class="major-select"
              :borderless="true"
              @change="handleMajorChange"
            >
            </t-select>
          </div>
          <div class="welcome-message">欢迎回来！今天是{{ currentDate }}</div>
        </div>
      </div>
      <div class="action-buttons">
        <t-button theme="default" variant="outline" @click="handleLogout">
          <t-icon name="poweroff" /> 退出界面
        </t-button>
      </div>
    </div>

    <!-- 专业信息概览 -->
    <t-card class="major-info-card" :bordered="false" hover-shadow>
      <template #header>
        <div class="card-header">
          <t-icon name="department" size="20px" />
          <span class="header-title">专业信息概览</span>
        </div>
      </template>
      <div class="info-grid">
        <div class="info-block" v-for="(item, index) in majorInfoItems" :key="index">
          <div class="info-label">
            <t-icon :name="item.icon" size="16px" />
            <span>{{ item.label }}</span>
          </div>
          <div class="info-value" :class="{highlight: item.highlight}">
            {{ item.value }}
          </div>
        </div>
      </div>
    </t-card>

    <!-- 专业概述 -->
    <t-card class="major-overview-card" :bordered="false" hover-shadow>
      <template #header>
        <div class="card-header">
          <t-icon name="file-text" size="20px" />
          <span class="header-title">专业概述</span>
        </div>
      </template>
      <t-tabs :default-value="activeTab" @change="handleTabChange">
        <t-tab-panel value="intro" label="专业简介">
          <div class="overview-content">
            <div class="content-header">
              <t-icon name="info-circle" />
              <h3>{{ majorInfo.majorName }}专业简介</h3>
            </div>
            <div class="content-text">{{ majorInfo.introduction }}</div>
          </div>
        </t-tab-panel>
        <t-tab-panel value="feature" label="专业特色">
          <div class="overview-content">
            <div class="content-header">
              <t-icon name="star" />
              <h3>专业特色与优势</h3>
            </div>
            <ul class="feature-list">
              <li v-for="(feature, index) in majorInfo.features.split('\n')" :key="index">
                <t-icon name="check-circle" size="16px" />
                {{ feature }}
              </li>
            </ul>
          </div>
        </t-tab-panel>
        <t-tab-panel value="status" label="专业状态">
          <div class="overview-content">
            <div class="status-indicator">
              <t-tag :theme="majorInfo.status === 'active' ? 'success' : 'warning'" shape="round" size="large">
                {{ majorInfo.status === 'active' ? '招生中' : '已停招' }}
              </t-tag>
              <div class="status-details">
                <div class="detail-item">
                  <span class="detail-label">最近招生年份：</span>
                  <span class="detail-value">2023年</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">招生计划：</span>
                  <span class="detail-value">180人/年</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">更新时间：</span>
                  <span class="detail-value">{{ majorInfo.lastUpdateTime }}</span>
                </div>
              </div>
            </div>
          </div>
        </t-tab-panel>
      </t-tabs>
    </t-card>

    <!-- 培养方案管理 - 更新为卡片式布局 -->
    <t-card class="plan-management-card" :bordered="false" hover-shadow>
      <template #header>
        <div class="card-header">
          <t-icon name="book" size="20px" />
          <span class="header-title">培养方案管理</span>
          <div class="card-actions">
            <t-button theme="primary" @click="handleAddPlanClick">
              <t-icon name="add" /> 新增培养方案
            </t-button>
          </div>
        </div>
      </template>

      <div class="overview-container">
        <t-row :gutter="[16, 16]" class="card-row">
          <t-col
            v-for="plan in planList"
            :key="plan.id"
            :xs="24"
            :sm="12"
            :md="8"
            :lg="4"
          >
            <t-card
              hover-shadow
              class="plan-card"
            >
              <template #header>
                <div class="card-header">
                  <t-icon name="education" class="card-icon" />
                  <div class="header-text">
                    <h3 class="card-title">{{ plan.planName }}</h3>
                    <span class="version-tag">版本 {{ plan.planVersion }}</span>
                  </div>
                </div>
              </template>

              <div class="basic-info">
                <div class="standard-info">
                  <div class="info-item">
                    <span class="info-label">适用年级:</span>
                    <span class="info-value">{{ plan.planVersion }}级</span>
                  </div>
                  <div class="info-item">
                    <span class="info-label">毕业要求标准:</span>
                    <span class="info-value">{{ getStandardName(plan.standardId) }}</span>
                  </div>
                  <div class="info-item">
                    <span class="info-label">状态:</span>
                    <span class="info-value">{{ plan.status === 1 ? '已启用' : '未启用' }}</span>
                  </div>
                  <div class="info-item">
                    <span class="info-label">描述:</span>
                    <span class="info-value">{{ getStandardDescription(plan.standardId) }}</span>
                  </div>
                </div>

                <div class="card-actions">
                  <t-button
                    variant="text"
                    theme="primary"
                    class="edit-btn"
                    @click="handleEditPlan(plan)"
                  >
                    <t-icon name="edit" /> 编辑
                  </t-button>

                  <t-popconfirm
                    content="确定要删除此培养计划吗？"
                    @confirm="handleDeletePlan(plan.id)"
                    theme="danger"
                    placement="top"
                  >
                    <t-button variant="text" theme="danger" class="action-btn">
                      <t-icon name="delete" /> 删除
                    </t-button>
                  </t-popconfirm>

                  <t-button
                    variant="text"
                    theme="default"
                    class="action-btn"
                    @click="handleCopyPlan(plan)"
                  >
                    <t-icon name="copy" /> 复制
                  </t-button>
                </div>
              </div>
            </t-card>
          </t-col>

          <t-col :xs="24" :sm="12" :md="8" :lg="4">
            <t-card
              hover-shadow
              class="plan-card add-card"
              @click="handleAddPlanClick"
            >
              <div class="add-card-content">
                <t-icon name="add" class="add-icon" />
                <div class="add-text">新建培养方案</div>
              </div>
            </t-card>
          </t-col>
        </t-row>
      </div>
    </t-card>

    <!-- 新增/编辑培养方案对话框 -->
    <t-dialog
      v-model:visible="planDialog.visible"
      :header="planDialog.mode === 'add'
        ? (planFormData.id ? '编辑培养计划' : '新建培养计划') + (planFormData.planName.includes('(副本)') ? ' (从复制创建)' : '')
        : '默认标题'"
      :width="680"
      :footer="false"
      @close="handleDialogClose"
      :closeOnEscKeydown="false"
    >
      <t-form
        ref="planFormRef"
        :data="planFormData"
        :rules="addPlanRules"
        label-width="80px"
        @submit="handlePlanSubmit"
      >
        <t-row :gutter="[16, 16]">
          <t-col :span="12">
            <t-form-item label="计划名称" name="planName">
              <t-input
                v-model="planFormData.planName"
                placeholder="请输入培养计划名称"
                clearable
                size="small"
              />
            </t-form-item>

            <t-form-item label="计划版本" name="planVersion">
              <t-input-number
                v-model="planFormData.planVersion"
                placeholder="请输入版本号"
                :min="1"
                :max="9999"
                size="small"
              />
            </t-form-item>

            <t-form-item label="适用年级" name="applicableGrade">
              <t-input
                v-model="planFormData.planVersion"
                placeholder="自动根据版本号生成"
                disabled
                size="small"
              />
            </t-form-item>

            <t-form-item label="学科类型" name="disciplineType">
              <t-select
                v-model="planFormData.disciplineType"
                placeholder="请选择学科类型"
                clearable
                :options="enumData?.list?.disciplineType"
                size="small"
              />
            </t-form-item>

            <t-form-item label="毕业要求标准" name="standardId">
              <t-select
                v-model="planFormData.standardId"
                placeholder="请选择毕业要求标准"
                clearable
                :options="filteredStandardList.map(item => ({
                  label: `${item.standardName} (${item.standardVersion})`,
                  value: item.id
                }))"
                :disabled="!planFormData.disciplineType"
                size="small"
              />
            </t-form-item>

            <t-form-item label="是否启用" name="status">
              <t-switch
                v-model="planFormData.status"
                :customValue="[1, 0]"
                :label="['启用', '禁用']"
                size="small"
              />
            </t-form-item>
          </t-col>

          <t-col :span="12">
            <t-loading :loading="standardDetailLoading">
              <div v-if="standardDetail" class="standard-preview">
                <h3 class="preview-title">标准预览</h3>
                <t-descriptions bordered size="small" :column="1">
                  <t-descriptions-item label="标准名称">
                    {{ standardDetail.standardName }}
                  </t-descriptions-item>
                  <t-descriptions-item label="版本">
                    {{ standardDetail.standardVersion }}
                  </t-descriptions-item>
                  <t-descriptions-item label="学科类型">
                    {{ enumData?.map?.disciplineType?.[standardDetail.disciplineType] }}
                  </t-descriptions-item>
                  <t-descriptions-item label="发布日期">
                    {{ formatDate(standardDetail.releaseDate) }}
                  </t-descriptions-item>
                  <t-descriptions-item label="描述">
                    {{ standardDetail.standardDescription }}
                  </t-descriptions-item>
                </t-descriptions>

                <div class="requirements-preview" v-if="standardDetail.requirements?.length">
                  <h4 class="preview-subtitle">毕业要求项（{{ standardDetail.requirements.length }}项）</h4>
                  <t-table
                    :data="standardDetail.requirements"
                    :columns="[
                      { colKey: 'standardName', title: '标题', width: 120 },
                      { colKey: 'standardDescription', title: '描述' }
                    ]"
                    size="small"
                    bordered
                    row-key="id"
                    :max-height="180"
                  />
                </div>
              </div>
              <t-empty v-else description="请选择毕业要求标准" />
            </t-loading>
          </t-col>
        </t-row>

        <t-form-item class="dialog-footer">
          <t-space>
            <t-button theme="primary" type="submit" :loading="planDialog.loading" size="small">
              {{ planDialog.mode === 'add' ? '确认添加' : '确认修改' }}
            </t-button>
            <t-button theme="default" variant="base" @click="handleDialogClose" size="small">
              取消
            </t-button>
          </t-space>
        </t-form-item>
      </t-form>
    </t-dialog>
  </div>
</template>

<script setup lang="ts">
import type { Ref } from 'vue'
import {MessagePlugin} from "tdesign-vue-next";
import { ref, onMounted, watch } from 'vue'
import { getEnum } from '@/api/system/enum'
import { getPlanList, addPlan, updatePlan, deleteByplanId } from '@/api/training/plan'
import { getGraduationStandardList, getGraduationStandardDetail } from '@/api/base/standard'
import dayjs from 'dayjs'
import type { SelectValue, SelectOption, SelectValueChangeTrigger } from 'tdesign-vue-next'

interface MajorOption {
  label: string
  value: string
}

interface PlanData {
  id?: number
  planName: string
  planVersion: number
  standardId: number | null
  disciplineType: string
  status: number
  standardDescription?: string
}

// Current user data
const currentUser = ref({
  role: '专业负责人',
})

// Major selection
const currentMajor: Ref<string> = ref('080901') // 当前选中的专业代码
const majorOptions: Ref<MajorOption[]> = ref([
  { label: '计算机科学与技术', value: '080901' },
  { label: '软件工程', value: '080902' },
  { label: '人工智能', value: '080903' },
  { label: '数据科学与大数据技术', value: '080904' },
  { label: '网络空间安全', value: '080905' }
])

// Current date
const currentDate = ref(new Date().toLocaleDateString('zh-CN', {
  year: 'numeric',
  month: 'long',
  day: 'numeric',
  weekday: 'long'
}))

// Tabs
const activeTab = ref('intro')

// Major info
const majorInfo = ref({
  majorName: '计算机科学与技术',
  majorCode: '080901',
  college: '计算机科学与工程学院',
  totalStudents: 2560,
  currentStudents: 680,
  courseCount: 48,
  planCount: 5,
  establishYear: '2002',
  leader: '张教授',
  lastUpdateTime: '2023-06-15 14:30:22',
  introduction: '本专业培养具有良好的科学素养，系统地掌握计算机科学与技术包括计算机硬件、软件与应用的基本理论、基本知识和基本技能与方法，能在科研部门、教育单位、企业、事业、技术和行政管理部门等单位从事计算机教学、科学研究和应用的计算机科学与技术学科的高级专门科学技术人才。',
  features: '1. 强调理论与实践相结合\n2. 注重创新能力培养\n3. 与企业合作紧密\n4. 国际化课程体系',
  status: 'active',
  statusDetail: '本专业目前正常招生，2023年计划招生180人'
})

const majorInfoItems = ref([
  { label: '专业名称', value: '计算机科学与技术', icon: 'edit-1' },
  { label: '专业代码', value: '080901', icon: 'barcode' },
  { label: '所属学院', value: '计算机科学与工程学院', icon: 'building' },
  { label: '累计培养人数', value: '2560人', icon: 'user-group', highlight: true },
  { label: '在校学生数', value: '680人', icon: 'user-1', highlight: true },
  { label: '专业课程数', value: '48门', icon: 'bookmark' },
  { label: '培养方案数', value: '5个', icon: 'file-text', highlight: true },
  { label: '建立年份', value: '2002年', icon: 'history' },
  { label: '专业负责人', value: '张教授', icon: 'user-talk' },
  { label: '最近更新', value: '2023-06-15 14:30', icon: 'time' }
])

// Methods
const handleLogout = () => {
  console.log('退出登录')
}

// 修改方法定义如下：
const handleMajorChange = (
  value: SelectValue,
  context: {
    option?: SelectOption;
    selectedOptions?: SelectOption[];
    trigger?: SelectValueChangeTrigger;
    e?: MouseEvent | KeyboardEvent;
  }
) => {
  console.log('切换专业:', value)
  const major = majorOptions.value.find(item => item.value === value as string)
  if (major) {
    currentUser.value.role = major.label + '专业负责人'
  }
}


const handleTabChange = (value: string | number) => {
  console.log('当前 tab:', value);
};


//培养方案管理部分
const planList = ref([])
const getPlanListData = async () => {
  const res = await getPlanList({
    current: 1,
    size: 100
  })
  planList.value = res.data.records
  await getStandardList()
}

const getStandardName = (standardId: number) => {
  if (!standardId) return '未设置'
  const standard = standardList.value.find(item => item.id == standardId)
  return standard ? `${standard.standardName} (${standard.standardVersion})` : '未设置'
}

// 枚举数据
const enumData = ref<any>(null)

const fetchEnumData = async () => {
  try {
    const res = await getEnum()
    enumData.value = res.data
  } catch (error) {
    console.error('获取枚举数据失败:', error)
    MessagePlugin.error('获取枚举数据失败')
  }
}

interface PlanData {
  id?: number
  planName: string
  planVersion: number
  standardId: number | null
  disciplineType: string
  status: number
  standardDescription?: string
}

const emptyPlanData: PlanData = {
  planName: '',
  planVersion: new Date().getFullYear(),
  standardId: null,
  disciplineType: '',
  status: 1
}

const planDialog = ref({
  visible: false,
  loading: false,
  mode: 'add'
})

const planFormData = ref<PlanData>({
  ...emptyPlanData
})

const planFormRef = ref(null)

const standardList = ref([])
const filteredStandardList = ref([])

const getStandardList = async () => {
  try {
    const res = await getGraduationStandardList({
      current: 1,
      size: 100,
      status: 0
    })
    standardList.value = res.data.records
  } catch (error) {
    console.error('获取标准列表失败:', error)
    MessagePlugin.error('获取标准列表失败')
  }
}

const getStandardDescription = (standardId: number) => {
  if (!standardId) return '暂无描述'
  const standard = standardList.value.find(item => item.id == standardId)
  return standard?.standardDescription || '暂无描述'
}

watch(() => planFormData.value.disciplineType, (newType) => {
  if (newType) {
    filteredStandardList.value = standardList.value.filter(
      standard => standard.disciplineType === newType
    )
  } else {
    filteredStandardList.value = []
  }
  planFormData.value.standardId = null
})

const addPlanRules = {
  planName: [
    { required: true, message: '请输入培养计划名称', trigger: 'blur' as const },
    { max: 100, message: '计划名称不能超过100个字符', trigger: 'blur' as const }
  ],
  planVersion: [
    { required: true, message: '请输入计划版本', trigger: 'blur' as const }
  ],
  disciplineType: [
    { required: true, message: '请选择学科类型', trigger: 'change' as const }
  ],
  standardId: [
    { required: true, message: '请选择毕业要求标准', trigger: 'change' as const }
  ]
}


const handleCopyPlan = (plan: any) => {
  planFormData.value = {
    ...JSON.parse(JSON.stringify(plan)),
    id: undefined,
    planName: `${plan.planName} (副本)`,
    planVersion: plan.planVersion + 1
  }

  planDialog.value = {
    visible: true,
    loading: false,
    mode: 'add'
  }

  getStandardList()

  if (plan.standardId) {
    standardDetailLoading.value = true
    getGraduationStandardDetail(String(plan.standardId)).then(res => {
      standardDetail.value = res.data
    }).catch(error => {
      console.error('获取标准详情失败:', error)
      MessagePlugin.error('获取标准详情失败')
    }).finally(() => {
      standardDetailLoading.value = false
    })
  }
}

const handleAddPlanClick = () => {
  planFormData.value = {
    ...emptyPlanData
  }
  planDialog.value = {
    visible: true,
    loading: false,
    mode: 'add'
  }
  getStandardList()
}

const handleEditPlan = (plan: any) => {
  planFormData.value = {
    id: plan.id,
    planName: plan.planName,
    planVersion: plan.planVersion,
    standardId: plan.standardId,
    disciplineType: plan.disciplineType,
    status: Number(plan.status),  // 确保转换为数字
    standardDescription: plan.standardDescription
  }
  planDialog.value = {
    visible: true,
    loading: false,
    mode: 'edit'
  }
  getStandardList()
}

const handlePlanSubmit = async (context: any) => {
  if (context.validateResult === true) {
    planDialog.value.loading = true
    try {
      let res
      if (planDialog.value.mode === 'add') {
        res = await addPlan(planFormData.value)
      } else {
        res = await updatePlan(planFormData.value.id, planFormData.value)
      }

      if (res.code === 200) {
        MessagePlugin.success(planDialog.value.mode === 'add' ? '培养计划添加成功' : '培养计划更新成功')
        planDialog.value.visible = false
        await getPlanListData()
      } else {
        MessagePlugin.error(res.message || '操作失败')
      }
    } catch (error: any) {
      console.error('操作培养计划失败:', error)
      MessagePlugin.error(error.message || '操作失败，请稍后重试')
    } finally {
      planDialog.value.loading = false
    }
  } else {
    MessagePlugin.warning(context.firstError)
  }
}

const handleDeletePlan = async (id: number) => {
  try {
    const res = await deleteByplanId(id, planFormData.value)
    if (res.code === 200) {
      MessagePlugin.success('培养计划删除成功')
      await getPlanListData()
    } else {
      MessagePlugin.error(res.message || '删除失败')
    }
  } catch (error) {
    console.error('删除培养计划失败:', error)
    MessagePlugin.error('删除失败，请稍后重试')
  }
}

const handleDialogClose = () => {
  planDialog.value.visible = false
  // 重置表单数据
  planFormData.value = {
    ...emptyPlanData
  }
  // 如果有表单引用，重置验证状态
  if (planFormRef.value) {
    planFormRef.value.reset()
  }
}

const standardDetail = ref<any>(null)
const standardDetailLoading = ref(false)

const formatDate = (date: string | number | Date | null | undefined) => {
  if (!date) return ''
  return dayjs(date).format('YYYY-MM-DD')
}

watch(() => planFormData.value.standardId, async (newId) => {
  if (newId) {
    standardDetailLoading.value = true
    try {
      const res = await getGraduationStandardDetail(String(newId))
      standardDetail.value = res.data
      planFormData.value.standardDescription = res.data.standardDescription
    } catch (error) {
      console.error('获取标准详情失败:', error)
      MessagePlugin.error('获取标准详情失败')
    } finally {
      standardDetailLoading.value = false
    }
  } else {
    standardDetail.value = null
    planFormData.value.standardDescription = ''
  }
})

onMounted(() => {
  getPlanListData()
  fetchEnumData()
})
</script>

<style scoped>
.plan-management-container {
  padding: 20px;
  background-color: #f5f7fa;
}

/* 顶部信息栏样式 */
.top-info-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: linear-gradient(135deg, #1976d2, #2196f3);
  color: white;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* 专业选择器样式 */
.major-select {
  width: 200px;
  margin-left: 8px;
}

.major-name {
  margin: 0 8px;
  font-weight: 500;
}

.major-select:deep(.t-input) {
  background: transparent;
  color: white;
  padding-left: 0;
}

.major-select:deep(.t-input__inner) {
  color: white;
  font-weight: 500;
}

.major-select:deep(.t-select__right-icon) {
  color: white;
}

.major-select:deep(.t-select__prefix-icon) {
  color: white;
  margin-right: 6px;
}

.selected-major {
  font-weight: 500;
}

/* 夜间模式适配 */
:deep(.t-select__dropdown) {
  background: white;
  color: #333;
}

.user-info {
  display: flex;
  align-items: center;
}

.user-avatar {
  margin-right: 12px;
}

.user-avatar .t-icon {
  color: white;
}

.user-details {
  display: flex;
  flex-direction: column;
}

.user-role {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 4px;
}

.user-role .divider {
  margin: 0 8px;
  opacity: 0.7;
}

.position {
  font-weight: 400;
}

.welcome-message {
  font-size: 12px;
  opacity: 0.9;
}

.action-buttons {
  display: flex;
  align-items: center;
}

/* 卡片通用样式 */
.major-info-card,
.major-overview-card,
.plan-management-card {
  border-radius: 8px;
  margin-bottom: 20px;
  border: 1px solid #e0e0e0;
}

.card-header {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.card-header .t-icon {
  margin-right: 8px;
  color: #1976d2;
}

.header-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-right: 10px;
}

/* 专业信息概览样式 */
.info-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 16px;
  padding: 16px;
}

.info-block {
  background: white;
  padding: 12px;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  border-left: 3px solid #1976d2;
}

.info-label {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
}

.info-label .t-icon {
  margin-right: 6px;
  color: #1976d2;
}

.info-value {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.info-value.highlight {
  color: #1976d2;
  font-weight: 600;
}

/* 专业概述样式 */
.overview-content {
  padding: 16px;
}

.content-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.content-header .t-icon {
  margin-right: 8px;
  color: #1976d2;
}

.content-header h3 {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.content-text {
  line-height: 1.8;
  color: #555;
  text-align: justify;
}

.feature-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.feature-list li {
  display: flex;
  align-items: flex-start;
  margin-bottom: 8px;
  line-height: 1.6;
}

.feature-list .t-icon {
  margin-right: 8px;
  color: #4caf50;
  flex-shrink: 0;
}

.status-indicator {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.status-details {
  margin-left: 16px;
}

.detail-item {
  margin-bottom: 8px;
  font-size: 14px;
}

.detail-label {
  color: #666;
}

.detail-value {
  color: #333;
  font-weight: 500;
}

/* 培养方案卡片样式 */
.card-row {
  margin-bottom: 12px;
}

.plan-card {
  height: 100%;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
}

.plan-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
}

.add-card {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border: 1px dashed var(--td-border-level-2-color);
  background-color: var(--td-bg-color-container-hover);
  min-height: 180px;
}

.add-card:hover {
  border-color: var(--td-brand-color);
}

.add-card-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.add-icon {
  font-size: 24px;
  color: var(--td-brand-color);
  margin-bottom: 8px;
}

.add-text {
  color: var(--td-brand-color);
  font-weight: 500;
}

.card-header {
  display: flex;
  align-items: center;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--td-border-level-2-color);
}

.card-icon {
  font-size: 18px;
  color: var(--td-brand-color);
  margin-right: 8px;
}

.header-text {
  flex: 1;
}

.card-title {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  line-height: 1.4;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.version-tag {
  font-size: 12px;
  color: var(--td-text-color-secondary);
}

.basic-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding-top: 12px;
}

.standard-info {
  flex: 1;
}

.info-item {
  display: flex;
  margin-bottom: 8px;
  font-size: 13px;
  line-height: 1.5;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  color: var(--td-text-color-secondary);
  margin-right: 6px;
  white-space: nowrap;
}

.info-value {
  color: var(--td-text-color-primary);
  word-break: break-word;
}

.card-actions {
  display: flex;
  justify-content: space-between;
  padding-top: 12px;
  border-top: 1px solid var(--td-border-level-2-color);
  margin-top: auto;
}

.action-btn {
  padding: 4px 8px;
  font-size: 12px;
}

/* 对话框样式 */
.standard-preview {
  padding: 12px;
  background-color: var(--td-bg-color-secondarycontainer);
  border-radius: var(--td-radius-default);
}

.preview-title {
  margin-top: 0;
  margin-bottom: 12px;
  font-size: 14px;
  font-weight: 500;
}

.dialog-footer {
  margin-top: 16px;
  text-align: right;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .info-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .major-select {
    width: 250px;
  }
}

@media (max-width: 992px) {
  .plan-card .card-actions {
    flex-wrap: wrap;
  }

  .plan-card .action-btn {
    margin-bottom: 4px;
  }
}

@media (max-width: 768px) {
  .major-select {
    width: 150px;
  }

  .info-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .top-info-bar {
    flex-direction: column;
    align-items: flex-start;
  }

  .action-buttons {
    margin-top: 12px;
    width: 100%;
    justify-content: flex-end;
  }
}

@media (max-width: 576px) {
  .info-grid {
    grid-template-columns: 1fr;
  }

  .plan-card .card-actions {
    flex-direction: column;
  }

  .plan-card .action-btn {
    width: 100%;
    margin-bottom: 4px;
    justify-content: flex-start;
  }
}
</style>
