import { App } from 'vue'
import { MessagePlugin } from 'tdesign-vue-next'
import { DialogPlugin } from 'tdesign-vue-next'
import { LoadingPlugin } from 'tdesign-vue-next'
import { NotifyPlugin } from 'tdesign-vue-next'
import mitt from 'mitt'
import _ from 'lodash'
import { globalPropertiesType } from '/@types/globals'

const _emitter = mitt()
const loadingText = '加载中...'
const messageDuration = 3000

export const gp = {
  /**
   * @description 全局加载层
   * @param {string} text 显示在加载图标下方的加载文案
   */
  $baseLoading: (text = loadingText) => {
    const loading = LoadingPlugin({
      fullscreen: true,
      attach: 'body',
      text: text,
      preventScrollThrough: false,
    })
    return {
      close: () => loading.hide()
    }
  },
  /**
   * @description 全局Message
   * @param {string} message 消息文字
   * @param {'success'|'warning'|'info'|'error'} type 主题
   * @param {string} customClass 自定义类名
   * @param {boolean} dangerouslyUseHTMLString 是否将message属性作为HTML片段处理
   */
  $baseMessage: (
    message: string,
    type: 'info' | 'success' | 'warning' | 'error' | 'question' | 'loading' = 'info',
  ) => {
    return MessagePlugin(type,{
      content: message,
      duration: messageDuration,
    })
  },
  /**
   * @description 全局Alert
   * @param {string|VNode} content 消息正文内容
   * @param {string} title 标题
   * @param {function} callback 若不使用Promise,可以使用此参数指定MessageBox关闭后的回调
   */
  $baseAlert: (content: string, title = '温馨提示', callback?: () => void) => {
    if (title && typeof title === 'function') {
      callback = title
      title = '温馨提示'
    }
    return DialogPlugin.alert({
      header: title,
      body: content,
      confirmBtn: {
        content: '确定',
        theme: 'primary',
      },
      onConfirm: () => {
        if (callback) callback()
      },
    })
  },
  /**
   * @description 全局Confirm
   * @param {string|VNode} content 消息正文内容
   * @param {string} title 标题
   * @param {string} confirmButtonText 确定按钮的文本内容
   * @param {string} cancelButtonText 取消按钮的自定义类名
   * @param callback1
   * @param callback2
   * @returns {Promise<boolean>} 返回用户操作结果，true 表示确认，false 表示取消
   */
  $baseConfirm: (
    content: string,
    title = '温馨提示',
    confirmButtonText = '确定',
    cancelButtonText = '取消',
    callback1?: () => void,
    callback2?: () => void
  ): Promise<boolean> => {
    return new Promise((resolve) => {
      const confirmDialog = DialogPlugin.confirm({
        header: title,
        body: content,
        confirmBtn: confirmButtonText,
        cancelBtn: cancelButtonText,
        onConfirm: () => {
          resolve(true);
          callback1 && callback1();
          confirmDialog.hide();
        },
        onClose: () => {
          resolve(false);
          callback2 && callback2();
          confirmDialog.hide();
        },
      });
    });
  },
  /**
   * @description 全局Notification
   * @param {string} message 说明文字
   * @param {string} title 标题
   * @param {'success'|'warning'|'info'|'error'} type 主题样式,如果不在可选值内将被忽略
   * @param {'top-right'|'top-left'|'bottom-right'|'bottom-left'} position 自定义弹出位置
   * @param duration 显示时间,毫秒
   */
  $baseNotify: (
    message: string,
    title?: string,
    type: 'success' | 'warning' | 'info' | 'error' = 'success',
    position: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' = 'top-right',
    duration = messageDuration
  ) => {
    return NotifyPlugin(type,{
      title: title,
      content: message,
      duration: duration,
      placement: position,
    })
  },
  /**
   * @description 表格高度
   * @param {*} formType
   */
  $baseTableHeight: (formType?: number) => {
    let height = window.innerHeight
    const paddingHeight = 291
    const formHeight = 60

    if (typeof formType === 'number') {
      height = height - paddingHeight - formHeight * formType
    } else {
      height = height - paddingHeight
    }
    return height
  },
  $pub: (...args: any[]) => {
    _emitter.emit(_.head(args), args[1])
  },
  $sub: function () {
    Reflect.apply(_emitter.on, _emitter, _.toArray(arguments))
  },
  $unsub: function () {
    Reflect.apply(_emitter.off, _emitter, _.toArray(arguments))
  },
}

export default {
  install(app: App<Element>) {
    app.config.globalProperties.$baseMessage = gp.$baseMessage
    app.config.globalProperties.$baseLoading = gp.$baseLoading
    app.config.globalProperties.$baseAlert = gp.$baseAlert
    app.config.globalProperties.$baseConfirm = gp.$baseConfirm
    app.config.globalProperties.$baseNotify = gp.$baseNotify
    app.config.globalProperties.$baseTableHeight = gp.$baseTableHeight
    app.config.globalProperties.$pub = gp.$pub
    app.config.globalProperties.$sub = gp.$sub
    app.config.globalProperties.$unsub = gp.$unsub
  },
}
