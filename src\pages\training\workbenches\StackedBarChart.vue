<template>
  <div ref="chartRef" class="stacked-bar-chart-container"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, watch } from 'vue';
import * as echarts from 'echarts';
import type { ECharts } from 'echarts';
import type { EChartsOption } from 'echarts/types/dist/shared';

const props = defineProps<{
  data: {
    categories: string[];
    series: {
      name: string;
      data: number[];
    }[];
  };
}>();

const chartRef = ref<HTMLElement>();
let chartInstance: ECharts | null = null;

const initChart = () => {
  if (!chartRef.value) return;

  chartInstance = echarts.init(chartRef.value);
  updateChart();
};

const updateChart = () => {
  if (!chartInstance) return;

  const option: EChartsOption = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: (params: any) => {
        let total = 0;
        let res = params[0].name + '<br/>';
        params.forEach((item: any) => {
          res += `${item.marker} ${item.seriesName}: ${item.value}<br/>`;
          total += item.value;
        });
        res += `<b>总计: ${total}</b>`;
        return res;
      }
    },
    legend: {
      data: props.data.series.map(item => item.name),
      right: 10,
      top: 0
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: props.data.categories,
      axisLabel: {
        interval: 0,
        rotate: props.data.categories.length > 6 ? 30 : 0
      }
    },
    yAxis: {
      type: 'value',
      name: '课程数量/学分',
      nameLocation: 'middle',
      nameGap: 30
    },
    series: props.data.series.map(item => ({
      name: item.name,
      type: 'bar',
      stack: 'total',
      emphasis: {
        focus: 'series'
      },
      data: item.data,
      label: {
        show: true,
        position: 'inside'
      }
    }))
  };

  chartInstance.setOption(option);
};

const resizeChart = () => {
  chartInstance?.resize();
};

onMounted(() => {
  initChart();
  window.addEventListener('resize', resizeChart);
});

onBeforeUnmount(() => {
  window.removeEventListener('resize', resizeChart);
  chartInstance?.dispose();
});

watch(() => props.data, () => {
  updateChart();
}, { deep: true });
</script>

<style scoped>
.stacked-bar-chart-container {
  width: 100%;
  height: 100%;
}
</style>
