<template>
  <div class="theory-teaching-card">
    <t-card>
      <template #header>
        <div class="card-header">
          <div class="header-title">
            <t-icon name="book" class="header-icon" />
            理论教学内容分配
          </div>
          <div class="header-actions">
            <div class="header-info">说明：教学内容包括章节标题、教学要求、教学方法、教学形式等</div>
            
            <!-- 编辑状态下的操作按钮 -->
            <t-space v-if="isEditing">
              <t-dropdown :options="templateOptions" @click="handleTemplateClick">
                <t-button theme="success" variant="outline" size="small">
                  <template #icon><t-icon name="template" /></template>
                  应用模板
                  <template #suffix><t-icon name="chevron-down" /></template>
                </t-button>
              </t-dropdown>
              <t-dropdown :options="importExportOptions" @click="handleDropdownClick">
                <t-button theme="default" variant="outline" size="small">
                  <template #icon><t-icon name="download" /></template>
                  导入/导出
                  <template #suffix><t-icon name="chevron-down" /></template>
                </t-button>
              </t-dropdown>
              <t-button theme="primary" size="small" @click="addTeachingContent">
                <template #icon><t-icon name="add" /></template>
                添加教学单元
              </t-button>
            </t-space>
            
            <!-- 非编辑状态下的操作按钮 -->
            <t-space v-else>
              <t-button
                theme="default"
                variant="outline"
                size="small"
                @click="exportToExcel"
              >
                <template #icon><t-icon name="download" /></template>
                导出Excel
              </t-button>
              <t-button
                theme="warning"
                variant="outline"
                size="small"
                @click="showVersionHistory"
              >
                <template #icon><t-icon name="time" /></template>
                版本历史
              </t-button>
              <t-button
                theme="default"
                variant="outline"
                size="small"
                @click="printSyllabus"
              >
                <template #icon><t-icon name="print" /></template>
                打印
              </t-button>
            </t-space>
          </div>
        </div>
      </template>

      <!-- 空状态显示 -->
      <div v-if="teachingData.length === 0" class="empty-state">
        <div class="empty-icon">
          <t-icon name="book" size="32px" />
        </div>
        <div class="empty-text">暂无教学内容</div>
        <t-button v-if="isEditing" theme="primary" variant="outline" @click="addTeachingContent">
          <template #icon><t-icon name="add" /></template>
          添加第一个教学单元
        </t-button>
      </div>

      <!-- 教学内容表格 -->
      <t-table
        v-else
        :data="teachingData"
        :columns="tableColumns"
        row-key="id"
        :pagination="null"
        :loading="loading"
        :bordered="true"
        :show-header="true"
        stripe
        hover
        size="medium"
        class="teaching-content-table"
      >
        <!-- 序号列 -->
        <template #index="{ rowIndex }">
          {{ rowIndex + 1 }}
        </template>

        <!-- 教学内容列 -->
        <template #content="{ row }">
          <div v-if="!isEditing && row" class="content-display">
            <div class="content-title">{{ row.title || '未设置标题' }}</div>
            <div class="content-details">{{ row.content || '暂无内容' }}</div>
          </div>
          <div v-else-if="row" class="content-edit">
            <t-input
              v-model="row.title"
              placeholder="请输入章节标题"
              class="title-input"
              @blur="emitUpdate"
            />
            <t-textarea
              v-model="row.content"
              placeholder="请输入教学内容"
              :autosize="{ minRows: 2, maxRows: 4 }"
              class="content-textarea"
              @blur="emitUpdate"
            />
          </div>
          <div v-else class="content-error">
            数据加载中...
          </div>
        </template>

        <!-- 教学要求列 -->
        <template #requirements="{ row }">
          <div v-if="!isEditing && row">{{ row.requirements || '暂无要求' }}</div>
          <t-textarea
            v-else-if="row"
            v-model="row.requirements"
            placeholder="请输入教学要求"
            :autosize="{ minRows: 2, maxRows: 3 }"
            @blur="emitUpdate"
          />
          <div v-else>-</div>
        </template>

        <!-- 教学方法列 -->
        <template #method="{ row }">
          <div v-if="!isEditing && row">{{ row.method || '未设置' }}</div>
          <t-select
            v-else-if="row"
            v-model="row.method"
            :options="teachingMethodOptions"
            placeholder="请选择教学方法"
            @change="emitUpdate"
          />
          <div v-else>-</div>
        </template>

        <!-- 教学形式列 -->
        <template #format="{ row }">
          <div v-if="!isEditing && row">{{ row.format || '未设置' }}</div>
          <t-select
            v-else-if="row"
            v-model="row.format"
            :options="teachingFormatOptions"
            placeholder="请选择教学形式"
            @change="emitUpdate"
          />
          <div v-else>-</div>
        </template>

        <!-- 对应课程目标列 -->
        <template #targets="{ row }">
          <div v-if="!isEditing && row">
            <t-tag
              v-for="target in (row.targets || [])"
              :key="target"
              size="small"
              theme="primary"
              variant="light"
              class="target-tag"
            >
              目标{{ target }}
            </t-tag>
            <span v-if="!row.targets || row.targets.length === 0" class="no-targets">未设置</span>
          </div>
          <t-select
            v-else-if="row"
            v-model="row.targets"
            :options="courseTargetOptions"
            placeholder="请选择对应目标"
            multiple
            @change="emitUpdate"
          />
          <div v-else>-</div>
        </template>

        <!-- 学时分配列 -->
        <template #hours="{ row }">
          <div v-if="!isEditing && row">{{ row.hours || 0 }}</div>
          <t-input-number
            v-else-if="row"
            v-model="row.hours"
            :min="0"
            :max="100"
            placeholder="学时"
            @change="emitUpdate"
          />
          <div v-else>0</div>
        </template>

        <!-- 布置任务列 -->
        <template #assignment="{ row }">
          <div v-if="!isEditing && row">{{ row.assignment || '暂无任务' }}</div>
          <t-input
            v-else-if="row"
            v-model="row.assignment"
            placeholder="请输入布置任务"
            @blur="emitUpdate"
          />
          <div v-else>-</div>
        </template>

        <!-- 操作列 -->
        <template #actions="{ rowIndex }">
          <t-space v-if="isEditing">
            <t-button
              theme="danger"
              variant="text"
              size="small"
              @click="removeTeachingContent(rowIndex)"
            >
              <template #icon><t-icon name="delete" /></template>
              删除
            </t-button>
          </t-space>
        </template>
      </t-table>
    </t-card>

    <!-- 版本历史对话框 -->
    <t-dialog
      v-model:visible="showVersionDialog"
      title="版本历史"
      width="800px"
      :footer="false"
    >
      <div class="version-history">
        <div class="current-version">
          <t-tag theme="primary" size="large">当前版本: {{ currentVersion }}</t-tag>
        </div>

        <div class="version-list">
          <div
            v-for="version in versionHistory"
            :key="version.id"
            class="version-item"
          >
            <div class="version-header">
              <div class="version-info">
                <span class="version-number">{{ version.version }}</span>
                <span class="version-time">{{ new Date(version.createTime).toLocaleString() }}</span>
                <span class="version-creator">{{ version.creator }}</span>
              </div>
              <div class="version-actions">
                <t-button
                  theme="primary"
                  size="small"
                  @click="restoreVersion(version)"
                >
                  恢复此版本
                </t-button>
              </div>
            </div>
            <div class="version-comment">{{ version.comment }}</div>
            <div class="version-stats">
              教学内容数量: {{ version.data.length }} 条 |
              总学时: {{ version.data.reduce((sum: number, item: any) => sum + (item.hours || 0), 0) }}
            </div>
          </div>
        </div>

        <div v-if="versionHistory.length === 0" class="empty-versions">
          <t-empty description="暂无版本历史" />
        </div>
      </div>
    </t-dialog>

    <!-- 隐藏的文件输入 -->
    <input
      ref="fileInputRef"
      type="file"
      accept=".xlsx,.xls"
      style="display: none"
      @change="handleFileImport"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { MessagePlugin } from 'tdesign-vue-next'
import * as XLSX from 'xlsx'

// 教学内容接口
interface TeachingContent {
  id: string
  title: string
  content: string
  requirements: string
  method: string
  format: string
  targets: number[]
  hours: number
  assignment: string
}

// Props
interface Props {
  data?: TeachingContent[]
  isEditing?: boolean
  courseObjectives?: any[]
}

const props = withDefaults(defineProps<Props>(), {
  data: () => [],
  isEditing: false,
  courseObjectives: () => []
})

// Emits
const emit = defineEmits<{
  update: [data: TeachingContent[]]
}>()

// 响应式数据
const loading = ref(false)
const teachingData = ref<TeachingContent[]>([...props.data])

// 表格列配置
const tableColumns = [
  {
    colKey: 'index',
    title: '序号',
    width: 60,
    align: 'center' as const
  },
  {
    colKey: 'content',
    title: '教学内容',
    width: 300,
    ellipsis: true
  },
  {
    colKey: 'requirements',
    title: '教学要求',
    width: 200,
    ellipsis: true
  },
  {
    colKey: 'method',
    title: '教学方法',
    width: 120
  },
  {
    colKey: 'format',
    title: '教学形式',
    width: 120
  },
  {
    colKey: 'targets',
    title: '对应课程目标',
    width: 150
  },
  {
    colKey: 'hours',
    title: '学时分配',
    width: 130,
    align: 'center' as const
  },
  {
    colKey: 'assignment',
    title: '布置任务',
    width: 150,
    ellipsis: true
  },
  {
    colKey: 'actions',
    title: '操作',
    width: 80,
    align: 'center' as const
  }
]

// 选项数据
const teachingMethodOptions = ref([
  { label: '讲授法', value: '讲授法' },
  { label: '演示教学法', value: '演示教学法' },
  { label: '问题教学法', value: '问题教学法' },
  { label: '案例教学法', value: '案例教学法' },
  { label: '项目教学法', value: '项目教学法' }
])

const teachingFormatOptions = ref([
  { label: '线上自学', value: '线上自学' },
  { label: '课堂演示', value: '课堂演示' },
  { label: '实践教学', value: '实践教学' },
  { label: '混合式教学', value: '混合式教学' }
])

const courseTargetOptions = ref([
  { label: '目标1', value: 1 },
  { label: '目标2', value: 2 },
  { label: '目标3', value: 3 },
  { label: '目标4', value: 4 }
])

// 导入导出选项
const importExportOptions = ref([
  { content: '导入Excel', value: 'import' },
  { content: '导出Excel', value: 'export' },
  { content: '下载模板', value: 'template' }
])

// 模板选项
const templateOptions = ref([
  { content: '理论课程模板', value: 'theory' },
  { content: '实践课程模板', value: 'practice' },
  { content: '混合式课程模板', value: 'hybrid' },
  { content: '保存为自定义模板', value: 'save_custom' }
])

// 版本管理
const currentVersion = ref('1.0.0')
const versionHistory = ref<any[]>([])
const showVersionDialog = ref(false)
const fileInputRef = ref<HTMLInputElement | null>(null)

// 计算属性
const totalHours = computed(() => {
  return teachingData.value.reduce((sum: number, item: TeachingContent) => sum + (item.hours || 0), 0)
})

// 方法
const emitUpdate = () => {
  emit('update', [...teachingData.value])
}

const addTeachingContent = () => {
  const newContent: TeachingContent = {
    id: `content_${Date.now()}`,
    title: '',
    content: '',
    requirements: '',
    method: '',
    format: '',
    targets: [],
    hours: 0,
    assignment: ''
  }
  teachingData.value.push(newContent)
  emitUpdate()
}

const removeTeachingContent = (index: number) => {
  teachingData.value.splice(index, 1)
  MessagePlugin.success('已删除教学内容')
  emitUpdate()
}

// 导入导出方法
const handleDropdownClick = (data: any) => {
  switch (data.value) {
    case 'import':
      importFromExcel()
      break
    case 'export':
      exportToExcel()
      break
    case 'template':
      downloadTemplate()
      break
  }
}

const importFromExcel = () => {
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = '.xlsx,.xls'
  input.onchange = async (e) => {
    const file = (e.target as HTMLInputElement).files?.[0]
    if (!file) return

    try {
      const data = await readExcelFile(file)
      const importedContents = data.map((row: any, index: number) => ({
        id: `imported_${Date.now()}_${index}`,
        title: row['章节标题'] || row['教学内容'] || '',
        content: row['教学内容详情'] || row['内容'] || '',
        requirements: row['教学要求'] || '',
        method: row['教学方法'] || '',
        format: row['教学形式'] || '',
        targets: parseTargets(row['对应课程目标'] || ''),
        hours: parseInt(row['学时分配']) || 0,
        assignment: row['布置任务'] || ''
      }))

      teachingData.value = importedContents
      emitUpdate()
      MessagePlugin.success(`成功导入 ${importedContents.length} 条教学内容`)
    } catch (error) {
      MessagePlugin.error('导入失败：' + (error as Error).message)
    }
  }
  input.click()
}

const exportToExcel = () => {
  const exportData = teachingData.value.map((item: TeachingContent, index: number) => ({
    index: index + 1,
    title: item.title,
    content: item.content,
    requirements: item.requirements,
    method: item.method,
    format: item.format,
    targets: item.targets.map((t: number) => `目标${t}`).join(', '),
    hours: item.hours,
    assignment: item.assignment
  }))

  const ws = XLSX.utils.json_to_sheet(exportData)
  const wb = XLSX.utils.book_new()
  XLSX.utils.book_append_sheet(wb, ws, '理论教学内容')

  const fileName = `理论教学内容_${new Date().toISOString().split('T')[0]}.xlsx`
  XLSX.writeFile(wb, fileName)
  MessagePlugin.success('导出成功')
}

const downloadTemplate = () => {
  const templateData = [
    {
      '序号': 1,
      '章节标题': 'Java概述',
      '教学内容详情': '1.1 Java语言的特点、平台\n1.2 Java开发环境的搭建',
      '教学要求': '了解Java程序的特点、JDK、JRE、JVM基本概念',
      '教学方法': '讲授法',
      '教学形式': '线上自学',
      '对应课程目标': '目标1',
      '学时分配': 2,
      '布置任务': '随堂练习'
    }
  ]

  const ws = XLSX.utils.json_to_sheet(templateData)
  const wb = XLSX.utils.book_new()
  XLSX.utils.book_append_sheet(wb, ws, '理论教学内容模板')

  XLSX.writeFile(wb, '理论教学内容导入模板.xlsx')
  MessagePlugin.success('模板下载成功')
}

// 模板处理方法
const handleTemplateClick = (data: any) => {
  switch (data.value) {
    case 'theory':
      applyTheoryTemplate()
      break
    case 'practice':
      applyPracticeTemplate()
      break
    case 'hybrid':
      applyHybridTemplate()
      break
    case 'save_custom':
      saveCustomTemplate()
      break
  }
}

const applyTheoryTemplate = () => {
  teachingData.value = [
    {
      id: 'theory_1',
      title: '第一章 基础理论',
      content: '1.1 基本概念\n1.2 理论基础\n1.3 核心原理',
      requirements: '掌握基本概念和理论基础，理解核心原理',
      method: '讲授法',
      format: '课堂演示',
      targets: [1],
      hours: 4,
      assignment: '课后习题1-10'
    },
    {
      id: 'theory_2',
      title: '第二章 深入理论',
      content: '2.1 高级概念\n2.2 理论应用\n2.3 案例分析',
      requirements: '深入理解高级概念，能够进行理论应用',
      method: '案例教学法',
      format: '课堂演示',
      targets: [1, 2],
      hours: 6,
      assignment: '案例分析报告'
    }
  ]
  emitUpdate()
  MessagePlugin.success('已应用理论课程模板')
}

const applyPracticeTemplate = () => {
  teachingData.value = [
    {
      id: 'practice_1',
      title: '实验一 基础操作',
      content: '1.1 环境搭建\n1.2 基本操作\n1.3 实验验证',
      requirements: '熟练掌握基础操作，能够独立完成实验',
      method: '演示教学法',
      format: '实践教学',
      targets: [2, 3],
      hours: 4,
      assignment: '实验报告'
    },
    {
      id: 'practice_2',
      title: '实验二 综合应用',
      content: '2.1 综合设计\n2.2 实现方案\n2.3 测试验证',
      requirements: '能够进行综合设计和实现，具备测试验证能力',
      method: '项目教学法',
      format: '实践教学',
      targets: [3, 4],
      hours: 8,
      assignment: '项目作品'
    }
  ]
  emitUpdate()
  MessagePlugin.success('已应用实践课程模板')
}

const applyHybridTemplate = () => {
  teachingData.value = [
    {
      id: 'hybrid_1',
      title: '第一章 理论基础',
      content: '1.1 基本概念\n1.2 理论学习\n1.3 在线练习',
      requirements: '掌握基本概念，完成在线学习和练习',
      method: '混合式教学',
      format: '线上自学',
      targets: [1],
      hours: 3,
      assignment: '在线测试'
    },
    {
      id: 'hybrid_2',
      title: '第二章 实践应用',
      content: '2.1 课堂讲解\n2.2 实践操作\n2.3 在线讨论',
      requirements: '理论联系实际，参与在线讨论',
      method: '混合式教学',
      format: '混合式教学',
      targets: [2, 3],
      hours: 5,
      assignment: '实践报告+讨论参与'
    }
  ]
  emitUpdate()
  MessagePlugin.success('已应用混合式课程模板')
}

const saveCustomTemplate = () => {
  if (teachingData.value.length === 0) {
    MessagePlugin.warning('请先添加教学内容再保存模板')
    return
  }

  const templateName = prompt('请输入模板名称：')
  if (!templateName) return

  const customTemplate = {
    name: templateName,
    contents: JSON.parse(JSON.stringify(teachingData.value)),
    createTime: new Date().toISOString()
  }

  const savedTemplates = JSON.parse(localStorage.getItem('customTheoryTemplates') || '[]')
  savedTemplates.push(customTemplate)
  localStorage.setItem('customTheoryTemplates', JSON.stringify(savedTemplates))

  MessagePlugin.success(`自定义模板"${templateName}"保存成功`)
}

// 版本管理方法
const showVersionHistory = () => {
  loadVersionHistory()
  showVersionDialog.value = true
}

const loadVersionHistory = () => {
  const saved = localStorage.getItem(`theory_versions`)
  versionHistory.value = saved ? JSON.parse(saved) : []
}

const restoreVersion = (version: any) => {
  teachingData.value = JSON.parse(JSON.stringify(version.data))
  currentVersion.value = version.version
  showVersionDialog.value = false
  emitUpdate()
  MessagePlugin.success(`已恢复到版本 ${version.version}`)
}

// 打印功能
const printSyllabus = () => {
  const printContent = generatePrintContent()
  const printWindow = window.open('', '_blank')

  if (printWindow) {
    printWindow.document.write(printContent)
    printWindow.document.close()
    printWindow.focus()
    printWindow.print()
  }
}

const generatePrintContent = (): string => {
  let tableRows = ''
  teachingData.value.forEach((item: TeachingContent, index: number) => {
    tableRows += `
      <tr>
        <td style="text-align: center; border: 1px solid #000; padding: 8px;">${index + 1}</td>
        <td style="border: 1px solid #000; padding: 8px;">
          <strong>${item.title}</strong><br/>
          ${item.content.replace(/\n/g, '<br/>')}
        </td>
        <td style="border: 1px solid #000; padding: 8px;">${item.requirements}</td>
        <td style="border: 1px solid #000; padding: 8px;">${item.method}</td>
        <td style="border: 1px solid #000; padding: 8px;">${item.format}</td>
        <td style="border: 1px solid #000; padding: 8px;">${item.targets.map((t: number) => `目标${t}`).join(', ')}</td>
        <td style="text-align: center; border: 1px solid #000; padding: 8px;">${item.hours}</td>
        <td style="border: 1px solid #000; padding: 8px;">${item.assignment}</td>
      </tr>
    `
  })

  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>理论教学内容分配</title>
      <style>
        body { font-family: SimSun, serif; font-size: 12px; margin: 20px; }
        .header { text-align: center; margin-bottom: 20px; }
        .title { font-size: 18px; font-weight: bold; margin-bottom: 10px; }
        table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
        th { background-color: #f5f5f5; border: 1px solid #000; padding: 8px; text-align: center; font-weight: bold; }
        @media print {
          body { margin: 0; }
          .no-print { display: none; }
        }
      </style>
    </head>
    <body>
      <div class="header">
        <div class="title">理论教学内容分配</div>
      </div>

      <table>
        <thead>
          <tr>
            <th style="width: 50px;">序号</th>
            <th style="width: 200px;">教学内容</th>
            <th style="width: 150px;">教学要求</th>
            <th style="width: 80px;">教学方法</th>
            <th style="width: 80px;">教学形式</th>
            <th style="width: 100px;">对应课程目标</th>
            <th style="width: 60px;">学时分配</th>
            <th style="width: 120px;">布置任务</th>
          </tr>
        </thead>
        <tbody>
          ${tableRows}
        </tbody>
      </table>

      <div class="summary">
        <div><strong>总学时：</strong>${totalHours.value}</div>
      </div>
    </body>
    </html>
  `
}

// 辅助方法
const readExcelFile = (file: File): Promise<any[]> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target?.result as ArrayBuffer)
        const workbook = XLSX.read(data, { type: 'array' })
        const firstSheetName = workbook.SheetNames[0]
        const worksheet = workbook.Sheets[firstSheetName]
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { defval: '' })
        resolve(jsonData)
      } catch (error) {
        reject(error)
      }
    }
    reader.onerror = () => reject(new Error('文件读取失败'))
    reader.readAsArrayBuffer(file)
  })
}

const parseTargets = (targetStr: string): number[] => {
  if (!targetStr) return []
  const matches = targetStr.match(/目标(\d+)/g)
  return matches ? matches.map(m => parseInt(m.replace('目标', ''))) : []
}

const handleFileImport = async (e: Event) => {
  const file = (e.target as HTMLInputElement).files?.[0]
  if (!file) return

  try {
    const data = await readExcelFile(file)
    const importedContents = data.map((row: any, index: number) => ({
      id: `imported_${Date.now()}_${index}`,
      title: row['章节标题'] || row['教学内容'] || '',
      content: row['教学内容详情'] || row['内容'] || '',
      requirements: row['教学要求'] || '',
      method: row['教学方法'] || '',
      format: row['教学形式'] || '',
      targets: parseTargets(row['对应课程目标'] || ''),
      hours: parseInt(row['学时分配']) || 0,
      assignment: row['布置任务'] || ''
    }))

    teachingData.value = importedContents
    emitUpdate()
    MessagePlugin.success(`成功导入 ${importedContents.length} 条教学内容`)
  } catch (error) {
    MessagePlugin.error('导入失败：' + (error as Error).message)
  }
}

// 监听 props 变化
watch(() => props.data, (newData) => {
  if (newData && Array.isArray(newData)) {
    teachingData.value = [...newData]
  }
}, { deep: true })
</script>

<style lang="less" scoped>
.theory-teaching-card {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;

    .header-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 16px;
      font-weight: 600;
      color: var(--td-text-color-primary);

      .header-icon {
        color: var(--td-brand-color);
        font-size: 18px;
      }
    }
    
    .header-actions {
      display: flex;
      gap: 8px;
      align-items: center;
      justify-content: flex-end;
    }
    
    .header-info {
      font-style: italic;
      color: var(--td-text-color-placeholder);
      font-size: 14px;
      margin-right: 8px;
    }
  }

  .empty-state {
    text-align: center;
    padding: 40px 20px;

    .empty-icon {
      margin-bottom: 16px;
      color: var(--td-text-color-placeholder);
    }

    .empty-text {
      margin-bottom: 20px;
      color: var(--td-text-color-secondary);
    }
  }

  .content-display {
    .content-title {
      font-weight: 600;
      color: var(--td-text-color-primary);
      margin-bottom: 8px;
    }

    .content-details {
      color: var(--td-text-color-secondary);
      line-height: 1.5;
      white-space: pre-line;
    }
  }

  .content-edit {
    .title-input {
      margin-bottom: 8px;
    }

    .content-textarea {
      width: 100%;
    }
  }

  .content-error {
    color: var(--td-text-color-placeholder);
    font-style: italic;
    text-align: center;
    padding: 8px;
  }

  .target-tag {
    margin-right: 4px;
    margin-bottom: 4px;
  }

  .no-targets {
    color: var(--td-text-color-placeholder);
    font-size: 12px;
  }

  .version-history {
    .current-version {
      margin-bottom: 20px;
      text-align: center;
    }

    .version-list {
      max-height: 400px;
      overflow-y: auto;
    }

    .version-item {
      border: 1px solid var(--td-border-level-1-color);
      border-radius: 6px;
      padding: 16px;
      margin-bottom: 12px;

      .version-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        .version-info {
          display: flex;
          align-items: center;
          gap: 12px;

          .version-number {
            font-weight: 600;
            color: var(--td-brand-color);
          }

          .version-time {
            color: var(--td-text-color-secondary);
            font-size: 12px;
          }

          .version-creator {
            color: var(--td-text-color-placeholder);
            font-size: 12px;
          }
        }
      }

      .version-comment {
        color: var(--td-text-color-secondary);
        margin-bottom: 8px;
      }

      .version-stats {
        color: var(--td-text-color-placeholder);
        font-size: 12px;
      }
    }

    .empty-versions {
      text-align: center;
      padding: 40px;
    }
  }
}

@media (max-width: 768px) {
  .theory-teaching-card {
    .card-header {
      flex-direction: column;
      gap: 12px;
      align-items: stretch;
      padding: 8px 0;

      .header-title {
        justify-content: center;
      }

      .header-actions {
        justify-content: center;
        flex-wrap: wrap;
      }
    }
  }
}
</style>
