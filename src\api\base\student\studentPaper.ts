import {StudentInformationListResult, StudentPaperListResult} from "@/api/model/student/studentPaperModel";
import {
  StudentQuestionAnswerResult,
  StudentQuestionListResult,
  StudentRepliedListResult
} from "@/api/model/student/studentQuestionl";
import request from "@/utils/request";

const Api = {
  PaperList: '/student/paper/list',
  NoStartList: '/student/paper/noStart/list',
  WriteList: '/student/paper/write/list',
  CorrectList: '/student/paper/correct/list',
  EndList: '/student/paper/end/list',
};

//获取试卷数据
export async function getList(cid:string): Promise<StudentPaperListResult> {
  try {
    const res = await request({
      url: 'api/student/paper/list',
      method: 'get',
      params: { cid }
    });
    return res;
  } catch (error) {
    console.error('获取试卷数据失败:', error);
    throw error;
  }
}

//获取试卷未开始数据
export async function getNoStartList(pid:string): Promise<StudentInformationListResult> {
  try {
    const res = await request({
      url: 'api/student/paper/noStart/list',
      method: 'get',
      params: { pid }
    });
    return res;
  } catch (error) {
    console.error('获取试卷未开始数据失败:', error);
    throw error;
  }
}

//获取试卷题目数据
export async function getWriteList(pid:string): Promise<StudentQuestionListResult> {
  try {
    const res = await request({
      url: 'api/student/paper/write/list',
      method: 'get',
      params: { pid }
    });
    return res;
  } catch (error) {
    console.error('获取试卷题目数据失败:', error);
    throw error;
  }
}



//获取试卷未批阅数据
export async function getCorrectList(pid:string): Promise<StudentInformationListResult> {
  try {
    const res = await request({
      url: 'api/student/paper/correct/list',
      method: 'get',
      params: { pid }
    });
    return res;
  } catch (error) {
    console.error('获取试卷未批阅数据失败:', error);
    throw error;
  }
}


//获取试卷结束后数据
export async function getEndList(pid:string): Promise<StudentInformationListResult> {
  try {
    const res = await request({
      url: 'api/student/paper/end/list',
      method: 'get',
      params: { pid }
    });
    return res;
  } catch (error) {
    console.error('获取试卷结束后数据失败:', error);
    throw error;
  }
}

//提交
export async function putPaperList(data:StudentRepliedListResult): Promise<string> {
  try {
    const res = await request({
      url: 'api/student/paperReplied/list',
      method: 'post',
      data
    });
    return res;
  } catch (error) {
    console.error('提交问卷数据失败:', error);
    throw error;
  }
}

//答案页面数据
export async function getAnswerList(pid:string): Promise<StudentQuestionAnswerResult> {
  try {
    const res = await request({
      url: 'api/student/paperAnswer/list',
      method: 'get',
      params: { pid }
    });
    return res;
  } catch (error) {
    console.error('提交问卷数据失败:', error);
    throw error;
  }
}
