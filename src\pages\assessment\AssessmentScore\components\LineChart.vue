<template>
  <div class="line-chart-container">
    <div ref="chartRef" class="chart" :id="chartId" style="width: 100%; height: 400px;"></div>
  </div>
</template>
<script setup lang="ts">
import { ref, watch, onMounted, onBeforeUnmount } from 'vue';
import * as echarts from 'echarts';
interface LineChartData {
  label: string;
  value: number;
}
const props = defineProps<{ chartId?: string; title?: string; data: LineChartData[]; xLabel?: string; yLabel?: string }>();
const chartRef = ref<HTMLDivElement | null>(null);
let chartInstance: echarts.ECharts | null = null;
const renderChart = () => {
  if (!chartRef.value) return;
  if (!chartInstance) chartInstance = echarts.init(chartRef.value);
  
  const dataArr = Array.isArray(props.data) ? props.data : [];
  const option = {
    title: { text: props.title || '折线图', left: 'center' },
    tooltip: {},
    xAxis: { type: 'category', name: props.xLabel || '', data: dataArr.map(d => d.label) },
    yAxis: { type: 'value', name: props.yLabel || '' },
    series: [{ type: 'line', data: dataArr.map(d => d.value), name: '成绩' }],
    grid: { left: '10%', right: '10%', top: '20%', bottom: '15%' }
  };
  chartInstance.setOption(option);
  chartInstance.resize();
};
watch(() => props.data, renderChart, { deep: true });
onMounted(() => { renderChart(); window.addEventListener('resize', handleResize); });
onBeforeUnmount(() => { chartInstance?.dispose(); window.removeEventListener('resize', handleResize); });
function handleResize() { chartInstance?.resize(); }
</script>
<style scoped>
.line-chart-container { width: 100%; }
.chart { width: 100%; height: 400px; }
</style>
