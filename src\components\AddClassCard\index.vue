<template>
  <div class="add-class-card" @click="handleAddClass">
    <div class="add-content">
      <div class="add-icon">
        <t-icon name="add" size="32px" />
      </div>
      <div class="add-text">
        <h3>新增班级</h3>
        <p>点击创建新的授课班级</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Icon as TIcon } from 'tdesign-vue-next'

// 定义事件
const emit = defineEmits<{
  addClass: []
}>()

// 事件处理函数
const handleAddClass = () => {
  emit('addClass')
}
</script>

<style lang="less" scoped>
.add-class-card {
  background: var(--td-bg-color-container);
  border: 2px dashed var(--td-border-level-2-color);
  border-radius: 8px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 280px;

  &:hover {
    border-color: var(--td-brand-color);
    background: var(--td-bg-color-container-hover);
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 82, 217, 0.1);

    .add-icon {
      background: var(--td-brand-color);
      color: white;
      transform: scale(1.1);
    }

    .add-text h3 {
      color: var(--td-brand-color);
    }
  }

  .add-content {
    text-align: center;

    .add-icon {
      width: 64px;
      height: 64px;
      background: var(--td-bg-color-page);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 16px;
      transition: all 0.3s ease;
      color: var(--td-text-color-secondary);
    }

    .add-text {
      h3 {
        margin: 0 0 8px 0;
        font-size: 18px;
        font-weight: 600;
        color: var(--td-text-color-primary);
        transition: color 0.3s ease;
      }

      p {
        margin: 0;
        font-size: 14px;
        color: var(--td-text-color-secondary);
        line-height: 1.4;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .add-class-card {
    min-height: 240px;
    padding: 16px;

    .add-content {
      .add-icon {
        width: 56px;
        height: 56px;
        margin-bottom: 12px;

        :deep(.t-icon) {
          font-size: 28px;
        }
      }

      .add-text h3 {
        font-size: 16px;
      }

      .add-text p {
        font-size: 13px;
      }
    }
  }
}
</style> 