<template>
  <div class="teaching-task-management">
    <!-- 页面头部筛选区域 -->
    <div class="filter-section">
      <t-card class="filter-card" :bordered="false">
        <div class="filter-content">
          <div class="filter-row">
            <div class="filter-item">
              <label class="filter-label">课程选择</label>
              <t-select
                v-model="selectedCourseId"
                placeholder="请选择课程"
                :options="courseOptions"
                :loading="loadingCourses"
                clearable
                @change="handleCourseChange"
              />
            </div>
            
            <div class="filter-item">
              <label class="filter-label">学年</label>
              <t-select
                v-model="selectedYear"
                placeholder="请选择学年"
                :options="yearOptions"
                @change="handleFilterChange"
              />
            </div>
            
            <div class="filter-item">
              <label class="filter-label">学期</label>
              <t-select
                v-model="selectedTerm"
                placeholder="请选择学期"
                :options="termOptions"
                @change="handleFilterChange"
              />
            </div>
            
            <div class="filter-actions">
              <t-button theme="primary" @click="handleSearch">
                <template #icon><t-icon name="search" /></template>
                查询
              </t-button>
              <t-button variant="outline" @click="handleReset">
                <template #icon><t-icon name="refresh" /></template>
                重置
              </t-button>
            </div>
          </div>
        </div>
      </t-card>
    </div>

    <!-- 教学任务列表 -->
    <div class="task-list-section">
      <TeachingTaskList
        v-if="selectedCourseId"
        ref="taskListRef"
        :course-id="selectedCourseId"
        :task-year="selectedYear"
        :task-term="selectedTerm"
        :course-name="selectedCourseName"
        @task-selected="handleTaskSelected"
        @task-deleted="handleTaskDeleted"
      />
      
      <!-- 未选择课程的提示 -->
      <div v-else class="no-course-selected">
        <t-card class="prompt-card" :bordered="false">
          <div class="prompt-content">
            <t-icon name="course" size="64px" />
            <h3>请选择课程</h3>
            <p>请在上方筛选区域选择要查看的课程，然后查看该课程的教学任务列表</p>
          </div>
        </t-card>
      </div>
    </div>

    <!-- 选中任务信息面板 -->
    <div v-if="selectedTaskIds.length > 0" class="selected-info-panel">
      <t-card class="info-card" :bordered="false">
        <div class="panel-content">
          <div class="panel-left">
            <t-icon name="check-circle" />
            <span class="selected-text">
              已选择 <strong>{{ selectedTaskIds.length }}</strong> 个教学任务
            </span>
          </div>
          
          <div class="panel-right">
            <t-space>
              <t-button size="small" variant="outline" @click="handleExportSelected">
                <template #icon><t-icon name="download" /></template>
                导出选中
              </t-button>
              <t-button size="small" theme="success" variant="outline" @click="handleBatchEdit">
                <template #icon><t-icon name="edit" /></template>
                批量编辑
              </t-button>
              <t-button size="small" variant="text" @click="handleClearSelection">
                清空选择
              </t-button>
            </t-space>
          </div>
        </div>
      </t-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { MessagePlugin } from 'tdesign-vue-next'
import TeachingTaskList from './AssessmentContent/components/TeachingTaskList.vue'

// 响应式数据
const taskListRef = ref()
const loadingCourses = ref(false)
const selectedCourseId = ref<number>()
const selectedYear = ref<number>(new Date().getFullYear())
const selectedTerm = ref<number>(1)
const selectedTaskIds = ref<number[]>([])
const courseOptions = ref<Array<{ label: string; value: number }>>([])

// 计算属性
const selectedCourseName = computed(() => {
  const course = courseOptions.value.find(c => c.value === selectedCourseId.value)
  return course?.label || '未知课程'
})

// 年份选项（近5年）
const yearOptions = computed(() => {
  const currentYear = new Date().getFullYear()
  const years = []
  for (let i = currentYear - 2; i <= currentYear + 2; i++) {
    years.push({
      label: `${i}-${i + 1}学年`,
      value: i
    })
  }
  return years
})

// 学期选项
const termOptions = [
  { label: '春季学期', value: 1 },
  { label: '秋季学期', value: 2 }
]

// 方法定义
const loadCourseOptions = async () => {
  try {
    loadingCourses.value = true
    
    // TODO: 调用获取课程列表的API
    // const response = await getCourseList()
    
    // 模拟数据
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    courseOptions.value = [
      { label: '高等数学A', value: 1001 },
      { label: '线性代数', value: 1002 },
      { label: '概率论与数理统计', value: 1003 },
      { label: '数据结构与算法', value: 1004 },
      { label: '计算机网络', value: 1005 },
      { label: '操作系统', value: 1006 },
      { label: '数据库系统原理', value: 1007 },
      { label: '软件工程', value: 1008 }
    ]
  } catch (error) {
    console.error('加载课程列表失败:', error)
    MessagePlugin.error('加载课程列表失败')
  } finally {
    loadingCourses.value = false
  }
}

const handleCourseChange = (value: number) => {
  selectedCourseId.value = value
  selectedTaskIds.value = []
}

const handleFilterChange = () => {
  selectedTaskIds.value = []
}

const handleSearch = () => {
  if (!selectedCourseId.value) {
    MessagePlugin.warning('请先选择课程')
    return
  }
  
  selectedTaskIds.value = []
  taskListRef.value?.refresh()
}

const handleReset = () => {
  selectedCourseId.value = undefined
  selectedYear.value = new Date().getFullYear()
  selectedTerm.value = 1
  selectedTaskIds.value = []
}

const handleTaskSelected = (taskIds: number[]) => {
  selectedTaskIds.value = taskIds
}

const handleTaskDeleted = (deletedIds: number[]) => {
  // 从选中列表中移除已删除的任务
  selectedTaskIds.value = selectedTaskIds.value.filter(
    id => !deletedIds.includes(id)
  )
  
  MessagePlugin.success(`已删除 ${deletedIds.length} 个教学任务`)
}

const handleClearSelection = () => {
  taskListRef.value?.clearSelection()
}

const handleExportSelected = () => {
  if (selectedTaskIds.value.length === 0) {
    MessagePlugin.warning('请先选择要导出的教学任务')
    return
  }
  
  // TODO: 实现导出功能
  MessagePlugin.success(`正在导出 ${selectedTaskIds.value.length} 个教学任务`)
}

const handleBatchEdit = () => {
  if (selectedTaskIds.value.length === 0) {
    MessagePlugin.warning('请先选择要编辑的教学任务')
    return
  }
  
  // TODO: 实现批量编辑功能
  MessagePlugin.info(`批量编辑 ${selectedTaskIds.value.length} 个教学任务`)
}

// 组件挂载时加载数据
onMounted(() => {
  loadCourseOptions()
})
</script>

<style lang="less" scoped>
.teaching-task-management {
  background: var(--td-bg-color-page);
  min-height: 100vh;
}

// 筛选区域
.filter-section {
  padding: var(--td-comp-paddingTB-xl) var(--td-comp-paddingLR-xl) 0;
  
  .filter-card {
    margin-bottom: var(--td-comp-margin-xl);
    
    :deep(.t-card__body) {
      padding: var(--td-comp-paddingTB-l) var(--td-comp-paddingLR-l);
    }
  }
  
  .filter-content {
    .filter-row {
      display: flex;
      align-items: flex-end;
      gap: var(--td-comp-margin-l);
      flex-wrap: wrap;
      
      .filter-item {
        display: flex;
        flex-direction: column;
        gap: var(--td-comp-margin-s);
        min-width: 200px;
        
        .filter-label {
          font-size: var(--td-font-size-body-medium);
          font-weight: var(--td-font-weight-medium);
          color: var(--td-text-color-primary);
        }
      }
      
      .filter-actions {
        display: flex;
        gap: var(--td-comp-margin-s);
        margin-left: auto;
      }
    }
  }
}

// 任务列表区域
.task-list-section {
  .no-course-selected {
    padding: 0 var(--td-comp-paddingLR-xl);
    
    .prompt-card {
      :deep(.t-card__body) {
        padding: var(--td-comp-paddingTB-xxl) var(--td-comp-paddingLR-xl);
      }
    }
    
    .prompt-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
      
      :deep(.t-icon) {
        color: var(--td-text-color-disabled);
        margin-bottom: var(--td-comp-margin-l);
      }
      
      h3 {
        margin: 0 0 var(--td-comp-margin-s) 0;
        font-size: var(--td-font-size-title-medium);
        font-weight: var(--td-font-weight-semi-bold);
        color: var(--td-text-color-secondary);
      }
      
      p {
        margin: 0;
        font-size: var(--td-font-size-body-medium);
        color: var(--td-text-color-placeholder);
        max-width: 400px;
      }
    }
  }
}

// 选中信息面板
.selected-info-panel {
  position: fixed;
  bottom: var(--td-comp-margin-xl);
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
  
  .info-card {
    box-shadow: var(--td-shadow-3);
    border: 1px solid var(--td-border-level-1-color);
    
    :deep(.t-card__body) {
      padding: var(--td-comp-paddingTB-m) var(--td-comp-paddingLR-l);
    }
  }
  
  .panel-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--td-comp-margin-xl);
    
    .panel-left {
      display: flex;
      align-items: center;
      gap: var(--td-comp-margin-s);
      
      :deep(.t-icon) {
        color: var(--td-success-color);
        font-size: var(--td-font-size-title-small);
      }
      
      .selected-text {
        font-size: var(--td-font-size-body-medium);
        color: var(--td-text-color-primary);
        
        strong {
          color: var(--td-brand-color);
          font-weight: var(--td-font-weight-semi-bold);
        }
      }
    }
    
    .panel-right {
      flex-shrink: 0;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .filter-section {
    padding: var(--td-comp-paddingTB-l) var(--td-comp-paddingLR-l) 0;
    
    .filter-content .filter-row {
      flex-direction: column;
      align-items: stretch;
      
      .filter-item {
        min-width: auto;
      }
      
      .filter-actions {
        margin-left: 0;
        justify-content: center;
      }
    }
  }
  
  .selected-info-panel {
    left: var(--td-comp-margin-l);
    right: var(--td-comp-margin-l);
    transform: none;
    
    .panel-content {
      flex-direction: column;
      gap: var(--td-comp-margin-m);
      text-align: center;
    }
  }
}
</style>
