import request from '@/utils/request';


export function getPlanList(params: any) {
  return request({
    url: '/tp/plan/list',
    method: 'get',
    params: params
  });
}


export function addPlan(data: any) {
  return request({
    url: '/tp/plan',
    method: 'post',
    data
  });
}

export function getPlan(id: number) {
  return request({
    url: `/tp/plan/${id}`,
    method: 'get'
  });
}

export function updatePlan(id:number,data:any){
  return request({
    url:`/tp/plan/${id}`,
    method:'put',
    data
  });
}

export function deleteByplanId(id:number ,data:any) {
  return request( {
    url:`/tp/plan/${id}`,
    method:'delete',
    data
  })
}
