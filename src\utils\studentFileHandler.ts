import * as XLSX from 'xlsx';
import { MessagePlugin } from 'tdesign-vue-next';

// 学生数据接口
export interface StudentInfo {
  id: string;
  studentId: string; // 学号
  name: string;
  gender: 'male' | 'female';
  major: string;
  className: string;
  academy: string;
  isAssistant: boolean; // 助教标识
  remark: string;
}

// 导入数据接口
export interface ImportStudentData {
  学号: string;
  姓名: string;
  性别: string;
  专业: string;
  班级: string;
  学院: string;
  是否助教: string;
  备注: string;
}

/**
 * 导出学生导入模板
 * @param fileName 文件名
 * @returns 是否成功导出
 */
export const exportStudentTemplate = (fileName: string = '学生导入模板'): boolean => {
  try {
    // 创建工作簿
    const workbook = XLSX.utils.book_new();
    
    // 创建表头
    const headers = ['学号', '姓名', '性别', '专业', '班级', '学院', '是否助教', '备注'];
    
    // 创建示例数据
    const exampleData = [
      ['2021001', '张三', '男', '软件工程', '软件工程2021级1班', '计算机学院', '是', '学习委员'],
      ['2021002', '李四', '女', '软件工程', '软件工程2021级1班', '计算机学院', '否', ''],
      ['2021003', '王五', '男', '计算机科学与技术', '计科2021级1班', '计算机学院', '是', '班长'],
      ['2021004', '赵六', '女', '数据科学与大数据技术', '数据2021级1班', '计算机学院', '否', '团支书'],
    ];
    
    // 创建工作表数据
    const sheetData = [headers, ...exampleData];
    const worksheet = XLSX.utils.aoa_to_sheet(sheetData);
    
    // 设置列宽
    worksheet['!cols'] = [
      { width: 12 }, // 学号
      { width: 10 }, // 姓名
      { width: 6 },  // 性别
      { width: 20 }, // 专业
      { width: 25 }, // 班级
      { width: 15 }, // 学院
      { width: 10 }, // 是否助教
      { width: 20 }, // 备注
    ];
    
    // 将工作表添加到工作簿
    XLSX.utils.book_append_sheet(workbook, worksheet, '学生信息');
    
    // 导出工作簿
    const wbout = XLSX.write(workbook, {
      bookType: 'xlsx',
      type: 'array'
    });
    
    // 创建Blob并下载
    const blob = new Blob([wbout], { type: 'application/octet-stream' });
    const url = URL.createObjectURL(blob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = `${fileName}.xlsx`;
    document.body.appendChild(link);
    link.click();
    
    // 清理资源
    setTimeout(() => {
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    }, 100);
    
    MessagePlugin.success('学生导入模板下载成功');
    return true;
  } catch (error: any) {
    console.error('导出模板失败:', error);
    MessagePlugin.error(`导出模板失败: ${error.message}`);
    return false;
  }
};

/**
 * 导出学生名单
 * @param students 学生数据列表
 * @param fileName 文件名
 * @returns 是否成功导出
 */
export const exportStudentList = (students: StudentInfo[], fileName: string = '学生名单'): boolean => {
  try {
    if (!students || students.length === 0) {
      MessagePlugin.warning('没有可导出的学生数据');
      return false;
    }

    // 创建工作簿
    const workbook = XLSX.utils.book_new();
    
    // 转换数据格式
    const exportData = students.map((student, index) => ({
      '序号': index + 1,
      '学号': student.studentId,
      '姓名': student.name,
      '性别': student.gender === 'male' ? '男' : '女',
      '专业': student.major,
      '班级': student.className,
      '学院': student.academy,
      '是否助教': student.isAssistant ? '是' : '否',
      '备注': student.remark || ''
    }));
    
    // 创建工作表
    const worksheet = XLSX.utils.json_to_sheet(exportData);
    
    // 设置列宽
    worksheet['!cols'] = [
      { width: 8 },  // 序号
      { width: 12 }, // 学号
      { width: 10 }, // 姓名
      { width: 6 },  // 性别
      { width: 20 }, // 专业
      { width: 25 }, // 班级
      { width: 15 }, // 学院
      { width: 10 }, // 是否助教
      { width: 20 }, // 备注
    ];
    
    // 将工作表添加到工作簿
    XLSX.utils.book_append_sheet(workbook, worksheet, '学生名单');
    
    // 导出工作簿
    const wbout = XLSX.write(workbook, {
      bookType: 'xlsx',
      type: 'array'
    });
    
    // 创建Blob并下载
    const blob = new Blob([wbout], { type: 'application/octet-stream' });
    const url = URL.createObjectURL(blob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = `${fileName}_${new Date().toISOString().slice(0, 10)}.xlsx`;
    document.body.appendChild(link);
    link.click();
    
    // 清理资源
    setTimeout(() => {
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    }, 100);
    
    MessagePlugin.success('学生名单导出成功');
    return true;
  } catch (error: any) {
    console.error('导出学生名单失败:', error);
    MessagePlugin.error(`导出失败: ${error.message}`);
    return false;
  }
};

/**
 * 解析Excel文件中的学生数据
 * @param file Excel文件
 * @returns Promise<StudentInfo[]> 解析后的学生数据
 */
export const parseStudentExcel = (file: File): Promise<StudentInfo[]> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    
    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target?.result as ArrayBuffer);
        const workbook = XLSX.read(data, { type: 'array' });
        const firstSheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[firstSheetName];
        
        // 转换为JSON数据
        const jsonData: ImportStudentData[] = XLSX.utils.sheet_to_json(worksheet, { defval: '' });
        
        if (!jsonData || jsonData.length === 0) {
          throw new Error('Excel文件中没有有效数据');
        }
        
        // 验证必要字段
        const requiredFields = ['学号', '姓名', '性别', '专业', '班级', '学院'];
        const firstRow = jsonData[0];
        const missingFields = requiredFields.filter(field => !(field in firstRow));
        
        if (missingFields.length > 0) {
          throw new Error(`Excel文件缺少必要字段: ${missingFields.join(', ')}`);
        }
        
        // 转换数据格式并验证
        const students: StudentInfo[] = [];
        const errors: string[] = [];
        
        jsonData.forEach((row, index) => {
          const rowNum = index + 2; // Excel行号（从第2行开始）
          
          try {
            // 验证必填字段
            if (!row.学号?.trim()) {
              errors.push(`第${rowNum}行：学号不能为空`);
              return;
            }
            if (!row.姓名?.trim()) {
              errors.push(`第${rowNum}行：姓名不能为空`);
              return;
            }
            if (!row.性别?.trim()) {
              errors.push(`第${rowNum}行：性别不能为空`);
              return;
            }
            
            // 验证性别格式
            const genderMap: Record<string, 'male' | 'female'> = {
              '男': 'male',
              '女': 'female',
              'male': 'male',
              'female': 'female'
            };
            
            const gender = genderMap[row.性别.trim()];
            if (!gender) {
              errors.push(`第${rowNum}行：性别格式错误，应为"男"或"女"`);
              return;
            }
            
            // 验证是否助教字段
            const assistantMap: Record<string, boolean> = {
              '是': true,
              '否': false,
              'true': true,
              'false': false,
              '1': true,
              '0': false,
              '': false
            };
            
            const isAssistantStr = (row.是否助教 || '').toString().trim();
            const isAssistant = assistantMap[isAssistantStr] ?? false;
            
            // 创建学生对象
            const student: StudentInfo = {
              id: `import_${Date.now()}_${index}`,
              studentId: row.学号.trim(),
              name: row.姓名.trim(),
              gender,
              major: row.专业?.trim() || '',
              className: row.班级?.trim() || '',
              academy: row.学院?.trim() || '',
              isAssistant,
              remark: row.备注?.trim() || ''
            };
            
            students.push(student);
          } catch (error: any) {
            errors.push(`第${rowNum}行：数据解析错误 - ${error.message}`);
          }
        });
        
        if (errors.length > 0) {
          throw new Error(`数据验证失败:\n${errors.join('\n')}`);
        }
        
        if (students.length === 0) {
          throw new Error('没有解析到有效的学生数据');
        }
        
        resolve(students);
      } catch (error: any) {
        reject(error);
      }
    };
    
    reader.onerror = () => {
      reject(new Error('文件读取失败'));
    };
    
    reader.readAsArrayBuffer(file);
  });
};

/**
 * 获取学生导入模板数据
 * @returns 模板数据数组
 */
export const getStudentTemplateData = (): string[][] => {
  return [
    ['学号', '姓名', '性别', '专业', '班级', '学院', '是否助教', '备注'],
    ['2021001', '张三', '男', '软件工程', '软件工程2021级1班', '计算机学院', '是', '学习委员'],
    ['2021002', '李四', '女', '软件工程', '软件工程2021级1班', '计算机学院', '否', ''],
    ['2021003', '王五', '男', '计算机科学与技术', '计科2021级1班', '计算机学院', '是', '班长'],
    ['2021004', '赵六', '女', '数据科学与大数据技术', '数据2021级1班', '计算机学院', '否', '团支书'],
  ];
}; 