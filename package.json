{"name": "pre-study-target", "version": "0.12.0", "type": "module", "scripts": {"dev:mock": "vite --open --mode mock", "dev": "vite --open --mode development --port 5173", "dev:linux": "vite --mode development", "build:test": "vite build --mode test", "build": "vite build --mode release", "build:type": "vue-tsc --noEmit", "build:site": "vite build --mode site", "preview": "vite preview", "lint": "eslint --ext .vue,.js,.jsx,.ts,.tsx ./ --max-warnings 0", "lint:fix": "eslint --ext .vue,.js,jsx,.ts,.tsx ./ --max-warnings 0 --fix", "stylelint": "stylelint src/**/*.{html,vue,sass,less}", "stylelint:fix": "stylelint --fix src/**/*.{html,vue,css,sass,less}", "site:preview": "npm run build && cp -r dist _site", "test": "echo \"no test specified,work in process\"", "test:coverage": "echo \"no test:coverage specified,work in process\""}, "dependencies": {"@tailwindcss/vite": "^4.1.11", "@types/date-fns": "^2.6.3", "@types/node": "^20.11.24", "@vitejs/plugin-vue": "^5.0.4", "@vueuse/core": "^12.3.0", "axios": "^1.6.7", "date-fns": "^4.1.0", "dayjs": "^1.11.10", "echarts": "^5.6.0", "lodash": "^4.17.21", "nprogress": "^0.2.0", "pinia": "^2.1.7", "pinia-plugin-persistedstate": "^3.2.0", "qrcode.vue": "^3.4.1", "qs": "^6.11.2", "tailwindcss": "^4.1.11", "tdesign-icons-vue-next": "^0.2.2", "tdesign-vue-next": "^1.9.0", "tvision-color": "^1.6.0", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.7.0", "vue": "^3.4.19", "vue-i18n": "^9.9.1", "vue-router": "^4.3.0", "xlsx": "^0.18.5"}, "devDependencies": {"@commitlint/cli": "^18.6.0", "@commitlint/config-conventional": "^18.6.0", "@types/echarts": "^4.9.21", "@types/lodash": "^4.17.6", "@types/mockjs": "^1.0.10", "@types/nprogress": "^0.2.3", "@types/qs": "^6.9.11", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@vitejs/plugin-vue-jsx": "^3.0.2", "@vue/compiler-sfc": "~3.3.8", "@vue/eslint-config-typescript": "^12.0.0", "eslint": "^8.56.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-simple-import-sort": "^12.0.0", "eslint-plugin-vue": "^9.21.1", "eslint-plugin-vue-scoped-css": "^2.7.2", "less": "^4.2.0", "lint-staged": "^15.2.2", "mockjs": "^1.1.0", "postcss-html": "^1.6.0", "postcss-less": "^6.0.0", "prettier": "^3.2.5", "stylelint": "~16.2.1", "stylelint-config-standard": "^36.0.0", "stylelint-order": "~6.0.4", "typescript": "^5.2.2", "vite": "^5.1.4", "vite-plugin-mock": "^3.0.1", "vite-svg-loader": "^5.1.0", "vue-tsc": "^1.8.27"}, "lint-staged": {"*.{js,jsx,vue,ts,tsx}": ["prettier --write", "npm run lint:fix"], "*.{html,vue,css,sass,less}": ["npm run stylelint:fix"]}, "engines": {"node": ">=18.0.0"}, "description": "base on tdesign-starter-cli for our study target project"}