<template>
  <div class="hours-distribution-table">
    <t-card>
      <template #header>
        <div class="card-header">
          <div class="header-title">
            <t-icon name="table" class="header-icon" />
            学时分配总表
          </div>
          <div class="header-actions">
            <t-space v-if="isEditing">
              <t-button theme="primary" size="small" @click="handleSave">
                <template #icon><t-icon name="check" /></template>
                保存
              </t-button>
              <t-button theme="default" size="small" @click="handleCancel">
                <template #icon><t-icon name="close" /></template>
                取消
              </t-button>
            </t-space>
            <t-space v-else>
              <t-button theme="primary" variant="outline" size="small" @click="handleEdit">
                <template #icon><t-icon name="edit" /></template>
                编辑
              </t-button>
              <t-button theme="default" variant="outline" size="small" @click="exportTable">
                <template #icon><t-icon name="download" /></template>
                导出表格
              </t-button>
            </t-space>
          </div>
        </div>
      </template>

      <div class="table-container">
        <t-table
          :data="distributionData"
          :columns="columns"
          :bordered="true"
          :hover="true"
          :stripe="true"
          row-key="id"
          size="medium"
          class="distribution-table"
          :row-class-name="getRowClassName"
        >
          <!-- 教学单元列 -->
          <template #unitName="{ row }">
            <div class="unit-cell">
              <span class="unit-name">{{ row?.unitName || '未命名单元' }}</span>
            </div>
          </template>

          <!-- 学时分配列 -->
          <template #lecture="{ row }">
            <div class="hours-cell">
              <t-input-number
                v-if="isEditing && row?.type !== 'summary'"
                v-model="row.lecture"
                :min="0"
                :max="200"
                size="small"
                @change="updateRowTotal(row)"
              />
              <span v-else>{{ row?.lecture || 0 }}</span>
            </div>
          </template>

          <template #experiment="{ row }">
            <div class="hours-cell">
              <t-input-number
                v-if="isEditing && row?.type !== 'summary'"
                v-model="row.experiment"
                :min="0"
                :max="200"
                size="small"
                @change="updateRowTotal(row)"
              />
              <span v-else>{{ row?.experiment || 0 }}</span>
            </div>
          </template>

          <template #computer="{ row }">
            <div class="hours-cell">
              <t-input-number
                v-if="isEditing && row?.type !== 'summary'"
                v-model="row.computer"
                :min="0"
                :max="200"
                size="small"
                @change="updateRowTotal(row)"
              />
              <span v-else>{{ row?.computer || 0 }}</span>
            </div>
          </template>

          <template #discussion="{ row }">
            <div class="hours-cell">
              <t-input-number
                v-if="isEditing && row?.type !== 'summary'"
                v-model="row.discussion"
                :min="0"
                :max="200"
                size="small"
                @change="updateRowTotal(row)"
              />
              <span v-else>{{ row?.discussion || 0 }}</span>
            </div>
          </template>

          <template #exercise="{ row }">
            <div class="hours-cell">
              <t-input-number
                v-if="isEditing && row?.type !== 'summary'"
                v-model="row.exercise"
                :min="0"
                :max="200"
                size="small"
                @change="updateRowTotal(row)"
              />
              <span v-else>{{ row?.exercise || 0 }}</span>
            </div>
          </template>

          <template #other="{ row }">
            <div class="hours-cell">
              <t-input-number
                v-if="isEditing && row?.type !== 'summary'"
                v-model="row.other"
                :min="0"
                :max="200"
                size="small"
                @change="updateRowTotal(row)"
              />
              <span v-else>{{ row?.other || 0 }}</span>
            </div>
          </template>

          <template #total="{ row }">
            <div class="hours-cell total-cell">{{ row?.total || 0 }}</div>
          </template>
        </t-table>
      </div>
    </t-card>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { MessagePlugin } from 'tdesign-vue-next'

// Props
interface Props {
  teachingContents: any[]
  practicalTeaching: any[]
  ideologicalEducation: any[]
  isEditing?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  teachingContents: () => [],
  practicalTeaching: () => [],
  ideologicalEducation: () => [],
  isEditing: false
})

// Emits
const emit = defineEmits<{
  'update:teachingContents': [value: any[]]
  'update:practicalTeaching': [value: any[]]
  'update:ideologicalEducation': [value: any[]]
  'update:isEditing': [value: boolean]
}>()

// 确保数组是有效的并且每个项目都有适当的默认值
const ensureValidArray = (arr: any[] | undefined | null): any[] => {
  if (!arr || !Array.isArray(arr)) return []
  return arr
}

// 表格列配置
const columns = [
  { 
    colKey: 'unitName', 
    title: '教学单元', 
    width: 200, 
    fixed: 'left' as const  // 使用类型断言确保固定为字面量类型
  },
  { colKey: 'lecture', title: '讲课', width: 80, align: 'center' as const },
  { colKey: 'experiment', title: '实验课', width: 80, align: 'center' as const },
  { colKey: 'computer', title: '上机课', width: 80, align: 'center' as const },
  { colKey: 'discussion', title: '讨论课', width: 80, align: 'center' as const },
  { colKey: 'exercise', title: '习题课', width: 80, align: 'center' as const },
  { colKey: 'other', title: '其它', width: 80, align: 'center' as const },
  { 
    colKey: 'total', 
    title: '合计', 
    width: 80, 
    align: 'center' as const, 
    fixed: 'right' as const  // 使用类型断言确保固定为字面量类型
  }
]

// 学时分配数据
const distributionData = computed(() => {
  // 如果在编辑模式且有编辑数据，使用编辑数据
  if (props.isEditing && editingData.value.length > 0) {
    const data = [...editingData.value]

    // 计算总计
    const totals = {
      lecture: 0,
      experiment: 0,
      computer: 0,
      discussion: 0,
      exercise: 0,
      other: 0,
      total: 0
    }

    data.forEach(item => {
      if (!item) return
      totals.lecture += item?.lecture || 0
      totals.experiment += item?.experiment || 0
      totals.computer += item?.computer || 0
      totals.discussion += item?.discussion || 0
      totals.exercise += item?.exercise || 0
      totals.other += item?.other || 0
      totals.total += item?.total || 0
    })

    // 添加总计行
    data.push({
      id: 'summary_total',
      unitName: '合计',
      lecture: totals.lecture,
      experiment: totals.experiment,
      computer: totals.computer,
      discussion: totals.discussion,
      exercise: totals.exercise,
      other: totals.other,
      total: totals.total,
      type: 'summary'
    })

    return data
  }

  // 非编辑模式，使用原始逻辑
  const data: any[] = []

  // 处理教学内容
  const validTeachingContents = ensureValidArray(props.teachingContents)
  validTeachingContents.forEach((content, index) => {
    if (!content) return; // 跳过无效的内容项
    
    // 确保 hours 是有效数字
    const hours = typeof content.hours === 'number' && !isNaN(content.hours) ? content.hours : 0
    const lectureHours = Math.floor(hours * 0.7) // 70%为讲课
    const experimentHours = Math.floor(hours * 0.2) // 20%为实验
    const discussionHours = hours - lectureHours - experimentHours // 剩余为讨论

    data.push({
      id: content.id || `teaching_${index}`,
      unitName: (content.title || `第${index + 1}章`),
      lecture: lectureHours,
      experiment: experimentHours,
      computer: 0,
      discussion: discussionHours,
      exercise: 0,
      other: 0,
      total: hours
    })
  })

  // 处理实践教学内容
  const validPracticalTeaching = ensureValidArray(props.practicalTeaching)
  validPracticalTeaching.forEach((practical, index) => {
    if (!practical) return; // 跳过无效的实践教学项
    
    // 确保 hours 是有效数字
    const hours = typeof practical.hours === 'number' && !isNaN(practical.hours) ? practical.hours : 0
    let computerHours = 0
    let experimentHours = 0
    let otherHours = 0

    switch (practical.type) {
      case '课内上机':
        computerHours = hours
        break
      case '实验课':
        experimentHours = hours
        break
      default:
        otherHours = hours
    }

    const practicalContent = practical.content || ''
    const contentPreview = typeof practicalContent === 'string' ? 
      (practicalContent.length > 20 ? practicalContent.substring(0, 20) : practicalContent) : 
      '实践内容'

    data.push({
      id: practical.id || `practical_${index}`,
      unitName: practical.type ? `${practical.type} - ${contentPreview}` : `实践教学项目${index + 1}`,
      lecture: 0,
      experiment: experimentHours,
      computer: computerHours,
      discussion: 0,
      exercise: 0,
      other: otherHours,
      total: hours
    })
  })

  // 处理课程思政内容
  const validIdeologicalEducation = ensureValidArray(props.ideologicalEducation)
  if (validIdeologicalEducation.length > 0) {
    const ideologicalHours = validIdeologicalEducation.length * 2 // 每个思政点2学时
    data.push({
      id: 'ideological_education',
      unitName: '课程思政教学设计',
      lecture: ideologicalHours,
      experiment: 0,
      computer: 0,
      discussion: 0,
      exercise: 0,
      other: 0,
      total: ideologicalHours
    })
  }

  // 计算总计
  const totals = {
    lecture: 0,
    experiment: 0,
    computer: 0,
    discussion: 0,
    exercise: 0,
    other: 0,
    total: 0
  }

  data.forEach(item => {
    if (!item) return
    totals.lecture += item?.lecture || 0
    totals.experiment += item?.experiment || 0
    totals.computer += item?.computer || 0
    totals.discussion += item?.discussion || 0
    totals.exercise += item?.exercise || 0
    totals.other += item?.other || 0
    totals.total += item?.total || 0
  })

  // 添加总计行作为表格数据的最后一行
  data.push({
    id: 'summary_total',
    unitName: '合计',
    lecture: totals.lecture,
    experiment: totals.experiment,
    computer: totals.computer,
    discussion: totals.discussion,
    exercise: totals.exercise,
    other: totals.other,
    total: totals.total,
    type: 'summary'
  })

  return data
})

// 总学时统计（保留用于其他地方使用）
const totalHours = computed(() => {
  const totals = {
    lecture: 0,
    experiment: 0,
    computer: 0,
    discussion: 0,
    exercise: 0,
    other: 0,
    total: 0
  }

  // 从distributionData中排除总计行来计算
  const dataWithoutSummary = distributionData.value.filter(item => item?.type !== 'summary')
  dataWithoutSummary.forEach(item => {
    if (!item) return; // 跳过无效的条目
    totals.lecture += item?.lecture || 0
    totals.experiment += item?.experiment || 0
    totals.computer += item?.computer || 0
    totals.discussion += item?.discussion || 0
    totals.exercise += item?.exercise || 0
    totals.other += item?.other || 0
    totals.total += item?.total || 0
  })

  return totals
})

// 编辑相关状态
const editingData = ref<any[]>([])

// 更新行总计
const updateRowTotal = (row: any) => {
  if (!row || row.type === 'summary') return

  const total = (row.lecture || 0) +
                (row.experiment || 0) +
                (row.computer || 0) +
                (row.discussion || 0) +
                (row.exercise || 0) +
                (row.other || 0)

  row.total = total
}

// 编辑模式处理函数
const handleEdit = () => {
  // 创建数据的深拷贝用于编辑
  editingData.value = JSON.parse(JSON.stringify(distributionData.value.filter(item => item.type !== 'summary')))
  emit('update:isEditing', true)
}

const handleSave = () => {
  try {
    // 这里需要根据实际的数据结构来更新对应的props
    // 由于数据来源于多个props，需要分别更新

    // 更新教学内容数据
    const updatedTeachingContents = props.teachingContents.map(content => {
      const editedRow = editingData.value.find(row => row.id === content.id)
      if (editedRow) {
        return {
          ...content,
          hours: editedRow.total
        }
      }
      return content
    })

    emit('update:teachingContents', updatedTeachingContents)
    emit('update:isEditing', false)
    MessagePlugin.success('保存成功')
  } catch (error) {
    console.error('保存失败:', error)
    MessagePlugin.error('保存失败，请重试')
  }
}

const handleCancel = () => {
  editingData.value = []
  emit('update:isEditing', false)
  MessagePlugin.info('已取消编辑')
}

// 获取行样式类名
const getRowClassName = ({ row }: { row: any }) => {
  if (row?.type === 'summary') {
    return 'summary-row'
  }
  return ''
}

// 导出表格
const exportTable = () => {
  try {
    // 创建CSV内容
    const headers = ['教学单元', '讲课', '实验课', '上机课', '讨论课', '习题课', '其它', '合计']
    const csvContent = [
      headers.join(','),
      ...distributionData.value.map(row => [
        `"${row?.unitName || '未命名单元'}"`,
        row?.lecture || 0,
        row?.experiment || 0,
        row?.computer || 0,
        row?.discussion || 0,
        row?.exercise || 0,
        row?.other || 0,
        row?.total || 0
      ].join(','))
    ].join('\n')

    // 创建下载链接
    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `学时分配总表_${new Date().toLocaleDateString()}.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    MessagePlugin.success('学时分配总表导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    MessagePlugin.error('导出失败，请重试')
  }
}
</script>

<style scoped lang="less">
.hours-distribution-table {
  margin-bottom: 24px;

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .header-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 16px;
      font-weight: 600;
      color: var(--td-text-color-primary);

      .header-icon {
        color: var(--td-brand-color);
      }
    }

    .header-actions {
      display: flex;
      gap: 8px;
      align-items: center;
      justify-content: flex-end;
    }
    
    .header-info {
      font-style: italic;
      color: var(--td-text-color-placeholder);
      font-size: 14px;
      margin-right: 8px;
    }
  }

  .table-container {
    .distribution-table {
      :deep(.t-table__header) {
        background: var(--td-bg-color-container-hover);
        
        th {
          font-weight: 600;
          text-align: center;
          border: 1px solid var(--td-border-level-1-color);
        }
      }

      :deep(.t-table__body) {
        td {
          border: 1px solid var(--td-border-level-1-color);
          text-align: center;
        }
      }
    }

    .unit-cell {
      text-align: left;
      padding: 4px 8px;

      .unit-name {
        font-weight: 500;
        color: var(--td-text-color-primary);
      }
    }

    .hours-cell {
      font-weight: 500;
      color: var(--td-text-color-primary);

      &.total-cell {
        font-weight: 600;
        color: var(--td-brand-color);
      }
    }

    // 总计行样式（表格行）
    :deep(.summary-row) {
      background: var(--td-bg-color-container-hover) !important;
      border-top: 2px solid var(--td-brand-color) !important;

      td {
        font-weight: 600 !important;
        color: var(--td-text-color-primary) !important;
        background: var(--td-bg-color-container-hover) !important;

        .hours-cell {
          font-weight: 700;
          color: var(--td-brand-color);
        }

        .unit-cell {
          .unit-name {
            font-weight: 700;
            color: var(--td-brand-color);
            font-size: 16px;
          }
        }

        &.total-cell {
          .hours-cell {
            font-size: 16px;
            font-weight: 700;
            color: var(--td-brand-color);
          }
        }
      }

      &:hover {
        background: var(--td-bg-color-container-hover) !important;

        td {
          background: var(--td-bg-color-container-hover) !important;
        }
      }
    }

    // 编辑模式样式
    .hours-cell {
      .t-input-number {
        width: 100%;

        :deep(.t-input__inner) {
          text-align: center;
          padding: 4px 8px;
        }
      }
    }

    // 编辑模式下的表格行样式
    :deep(.t-table__body) {
      tr:not(.summary-row) {
        &:hover {
          background: var(--td-bg-color-container-hover);
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .hours-distribution-table {
    .card-header {
      flex-direction: column;
      gap: 12px;
      align-items: stretch;
    }
  }
}
</style>
