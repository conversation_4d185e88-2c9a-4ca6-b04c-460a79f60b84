<template>
  <t-dialog
    v-model:visible="dialogVisible"
    :header="isEdit ? '编辑班级' : '新增班级'"
    width="600px"
    :confirm-btn="confirmBtn"
    :cancel-btn="cancelBtn"
    @confirm="handleConfirm"
    @cancel="handleCancel"
  >
    <t-form
      ref="formRef"
      :data="formData"
      :rules="formRules"
      label-align="left"
      label-width="100px"
      @submit="handleSubmit"
    >
      <t-form-item label="班级名称" name="className">
        <t-input
          v-model="formData.className"
          placeholder="请输入班级名称，如：RB软工数241"
        />
      </t-form-item>

      <t-form-item label="所属专业" name="majorId">
        <t-input v-model:value="formData.majorMame" disabled />
      </t-form-item>

      <t-form-item label="入学年份" name="entranceYear">
        <t-date-picker
          v-model="formData.entranceYear"
          mode="year"
          placeholder="请选择入学年份"
          style="width: 100%"
        />
      </t-form-item>

      <t-form-item label="班主任" name="headteacherId">
        <t-select
          v-model="formData.headteacherId"
          placeholder="请选择班主任"
          :options="teacherOptions"
          :loading="teacherLoading"
          filterable
        />
      </t-form-item>

      <t-form-item label="班级状态" name="classStatus">
        <t-radio-group v-model="formData.classStatus">
          <t-radio :value="0">在读</t-radio>
          <t-radio :value="-1">毕业</t-radio>
        </t-radio-group>
      </t-form-item>

      <!-- 学生导入选项 -->
      <t-form-item label="学生导入">
        <t-checkbox v-model="enableStudentImport">
          创建班级后立即导入学生
        </t-checkbox>
      </t-form-item>
    </t-form>

    <!-- 学生导入区域 -->
    <div v-if="enableStudentImport" class="student-import-section">
      <t-divider>学生导入</t-divider>

      <div class="import-options">
        <t-radio-group v-model="importMethod">
          <t-radio value="excel">Excel文件导入</t-radio>
          <t-radio value="manual">手动添加</t-radio>
        </t-radio-group>
      </div>

      <!-- Excel导入 -->
      <div v-if="importMethod === 'excel'" class="excel-import">
        <t-upload
          v-model="fileList"
          :action="uploadAction"
          :before-upload="handleBeforeUpload"
          :on-success="handleUploadSuccess"
          :on-fail="handleUploadFail"
          accept=".xlsx,.xls"
          theme="file-input"
          :placeholder="'点击选择Excel文件'"
        />
        <div class="import-tip">
          <t-icon name="info-circle" />
          <span>请上传包含学号和姓名列的Excel文件，系统将自动匹配现有学生</span>
        </div>
      </div>

      <!-- 手动添加 -->
      <div v-if="importMethod === 'manual'" class="manual-import">
        <div class="student-list">
          <div
            v-for="(student, index) in manualStudents"
            :key="index"
            class="student-item"
          >
            <t-input
              v-model="student.studentNumber"
              placeholder="学号"
              style="width: 45%"
            />
            <t-input
              v-model="student.studentName"
              placeholder="姓名"
              style="width: 45%"
            />
            <t-button
              theme="danger"
              variant="text"
              shape="square"
              @click="removeStudent(index)"
            >
              <template #icon>
                <t-icon name="remove" />
              </template>
            </t-button>
          </div>
        </div>
        <t-button theme="primary" variant="text" @click="addStudent">
          <template #icon>
            <t-icon name="add" />
          </template>
          添加学生
        </t-button>
      </div>
    </div>
  </t-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue'
import {
  Dialog as TDialog,
  Form as TForm,
  FormItem as TFormItem,
  Input as TInput,
  Select as TSelect,
  DatePicker as TDatePicker,
  RadioGroup as TRadioGroup,
  Radio as TRadio,
  Checkbox as TCheckbox,
  Divider as TDivider,
  Upload as TUpload,
  Button as TButton,
  Icon as TIcon,
  MessagePlugin,
  type FormInstanceFunctions,
  type FormRule
} from 'tdesign-vue-next'
import { getTeacherOptions, addClass, updateClass } from '@/api/base/classes'
import {getMajorByCourseId, getMajorOptions} from '@/api/base/major'
import type { ClassItem } from '@/api/base/classes'

// 定义 props
interface Props {
  visible: boolean
  editData?: ClassItem | null
  courseId?: string | number
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  editData: null,
  courseId: ''
})

// 定义事件
const emit = defineEmits<{
  'update:visible': [visible: boolean]
  success: []
}>()

// 表单引用
const formRef = ref<FormInstanceFunctions>()
// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 是否编辑模式
const isEdit = computed(() => !!props.editData)

// 表单数据
const formData = reactive({
  className: '',
  majorId: '',
  majorMame: '',
  entranceYear: '',
  headteacherId: '',
  classStatus: 0
})

// 表单验证规则
const formRules: Record<string, FormRule[]> = {
  className: [
    { required: true, message: '请输入班级名称' },
    { min: 2, max: 50, message: '班级名称长度为2-50个字符' }
  ],
  majorId: [{ required: true, message: '请选择所属专业' }],
  entranceYear: [{ required: true, message: '请选择入学年份' }],
  headteacherId: [{ required: true, message: '请选择班主任' }]
}

// 学生导入相关
const enableStudentImport = ref(false)
const importMethod = ref<'excel' | 'manual'>('excel')
const fileList = ref([])
const uploadAction = '/api/base/classes/preview-import'

// 手动添加学生
const manualStudents = ref([
  { studentNumber: '', studentName: '' }
])

// 专业选项
const majorOptions = ref([])
const majorLoading = ref(false)

// 教师选项
const teacherOptions = ref([])
const teacherLoading = ref(false)

// 按钮配置
const confirmBtn = {
  content: isEdit.value ? '保存' : '创建',
  theme: 'primary' as const,
  loading: false
}

const cancelBtn = {
  content: '取消',
  theme: 'default' as const
}

// 监听编辑数据变化
watch(() => props.editData, (newData) => {
  if (newData) {
    Object.assign(formData, {
      className: newData.className,
      majorId: newData.majorId,
      entranceYear: newData.entranceYear.toString(),
      headteacherId: newData.headteacherId,
      classStatus: newData.classStatus
    })
  }
}, { immediate: true })

// 监听对话框显示状态
watch(dialogVisible, (visible) => {
  if (visible) {
    loadMajorOptions()
    loadTeacherOptions()

    // 如果不是编辑模式，重置表单
    if (!isEdit.value) {
      resetForm()
    }
  }
})

// 加载专业选项
const loadMajorOptions = async () => {
  try {
    majorLoading.value = true
    const response = await getMajorByCourseId(props.courseId)
    formData.majorId = response.data?.id || ''
    formData.majorMame = response.data?.name || ''
  } catch (error) {
    MessagePlugin.error('专业名称获取失败')
  } finally {
    majorLoading.value = false
  }
}

// 加载教师选项
const loadTeacherOptions = async () => {
  try {
    teacherLoading.value = true
    const response = await getTeacherOptions()
    teacherOptions.value = response.data?.map((item: any) => ({
      label: `${item.name} (${item.number})`,
      value: item.id
    })) || []
  } catch (error) {
    MessagePlugin.error('加载教师选项失败')
  } finally {
    teacherLoading.value = false
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    className: '',
    majorId: '',
    entranceYear: '',
    headteacherId: '',
    classStatus: 0
  })
  enableStudentImport.value = false
  importMethod.value = 'excel'
  manualStudents.value = [{ studentNumber: '', studentName: '' }]
  fileList.value = []
}

// 添加学生
const addStudent = () => {
  manualStudents.value.push({ studentNumber: '', studentName: '' })
}

// 移除学生
const removeStudent = (index: number) => {
  if (manualStudents.value.length > 1) {
    manualStudents.value.splice(index, 1)
  }
}

// 文件上传前处理
const handleBeforeUpload = (file: File) => {
  const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                  file.type === 'application/vnd.ms-excel'
  if (!isExcel) {
    MessagePlugin.error('只能上传Excel文件')
    return false
  }
  const isLt2M = file.size / 1024 / 1024 < 2
  if (!isLt2M) {
    MessagePlugin.error('文件大小不能超过2MB')
    return false
  }
  return true
}

// 文件上传成功
const handleUploadSuccess = (response: any) => {
  MessagePlugin.success('文件上传成功')
}

// 文件上传失败
const handleUploadFail = () => {
  MessagePlugin.error('文件上传失败')
}

// 表单提交
const handleSubmit = () => {
  // 这里会在handleConfirm中调用
}

// 确认按钮点击
const handleConfirm = async () => {
  const validateResult = await formRef.value?.validate()
  if (validateResult !== true) {
    return
  }

  try {
    confirmBtn.loading = true

    const submitData = {
      ...formData,
      entranceYear: parseInt(formData.entranceYear)
    }

    if (isEdit.value && props.editData) {
      await updateClass({ ...submitData, classId: props.editData.classId })
      MessagePlugin.success('班级更新成功')
    } else {
      await addClass(submitData)
      MessagePlugin.success('班级创建成功')
    }

    emit('success')
    dialogVisible.value = false
  } catch (error) {
    MessagePlugin.error('操作失败')
  } finally {
    confirmBtn.loading = false
  }
}

// 取消按钮点击
const handleCancel = () => {
  dialogVisible.value = false
}

// 组件挂载时初始化
onMounted(() => {
  // 初始化逻辑
})
</script>

<style lang="less" scoped>
.student-import-section {
  margin-top: 16px;

  .import-options {
    margin-bottom: 16px;
  }

  .excel-import {
    .import-tip {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-top: 8px;
      padding: 8px 12px;
      background: var(--td-bg-color-container-select);
      border-radius: 4px;
      font-size: 12px;
      color: var(--td-text-color-secondary);
    }
  }

  .manual-import {
    .student-list {
      max-height: 200px;
      overflow-y: auto;
      margin-bottom: 12px;

      .student-item {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 8px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}
</style>
