import { defineStore } from 'pinia';

// 题目接口
export interface Problem {
  number: string | number;
  title: string;
  type?: string;
  answer?: string;
  score?: number | string;
  [key: string]: any; // 允许任意数量的课程目标 (例如: 'goal1', 'goal2', ..., 'goalN')
}
//导入的成绩文件接口
export interface StudentScore {
  studentId: string;
  studentName: string;
  scores: {
    [problemNo: string]: {
      score: number;
      problemNo: string;  //题号
    };
  };
}

// 导入的题目文件接口
export interface ImportedProblemFile {
  id: string;         // 唯一标识
  fileName: string;   // 文件名
  importDate: string; // 导入日期
  problems: Problem[]; // 题目列表
  weight: number;     // 权重
  scores?: StudentScore[];     // 成绩数据
}


// 指标点接口
export interface Indicator {
  id: string;
  name: string;
  weight: number;
  preset?: boolean;
  scores?: StudentScore[];
  categoryId?: string;
  importedFile?: string;
  importedProblemFiles?: ImportedProblemFile[];
  problems?: any[];
  [key: string]: any;
}

// 分类接口
export interface Category {
  id: string;
  name: string;
  type: string;
  weight: number;
  indicators: Indicator[];
}

// 指标点存储状态接口
export interface IndicatorState {
  categories: Category[];
  loaded: boolean;
}

export const useIndicatorStore = defineStore('indicator', {
  state: (): IndicatorState => ({
    categories: [],
    loaded: false
  }),

  getters: {
    getCategories: (state): Category[] => state.categories
  },

  actions: {
    // 初始化分类
    async initCategories() {
      if (this.loaded && this.categories.length > 0) return;

      try {
        // 尝试从localStorage加载
        const savedData = localStorage.getItem('assessmentCategories');
        if (savedData) {
          this.categories = JSON.parse(savedData);
          console.log('从本地存储加载了分类数据:', this.categories);
        } else {
          // 默认数据
          this.categories = [
            {
              id: 'final',
              name: '期末考核',
              type: 'final',
              weight: 60,
              indicators: [
                { id: 'final-exam', name: '期末考试', weight: 100, preset: true, scores: [], categoryId: 'final' }
              ]
            },
            {
              id: 'regular',
              name: '平时考核',
              type: 'regular',
              weight: 40,
              indicators: [
                { id: 'attendance', name: '签到', weight: 25, preset: true, scores: [], categoryId: 'regular' },
                { id: 'homework', name: '平时作业', weight: 25, preset: true, scores: [], categoryId: 'regular' },
                { id: 'quiz', name: '课堂测验', weight: 25, preset: true, scores: [], categoryId: 'regular' },
                { id: 'participation', name: '课堂参与', weight: 25, preset: true, scores: [], categoryId: 'regular' }
              ]
            }
          ];
          console.log('使用默认分类数据:', this.categories);
        }

        this.loaded = true;
      } catch (error) {
        console.error('加载指标点数据失败:', error);
      }
    },

    // 更新所有分类
    updateCategories(categories: Category[]) {
      this.categories = categories;
      // 保存到localStorage
      localStorage.setItem('assessmentCategories', JSON.stringify(categories));
      console.log('更新并保存了分类数据:', categories);
    }
  }
});
