import request from '@/utils/request';

// 操作日志接口类型定义
export interface LogItem {
  id: number;
  username: string;
  operationType: string;
  module: string;
  description: string;
  ip: string;
  location: string;
  status: number;
  errorMsg: string;
  operationTime: string;
  costTime: number;
  // 详情页可能需要的额外字段
  browser?: string;
  os?: string;
  requestUrl?: string;
  requestMethod?: string;
  requestParams?: string;
  responseResult?: string;
}

/**
 * 获取操作日志列表
 * @param params 查询参数
 */
export function getLogList(params: any) {
  return request({
    url: '/log/list',
    method: 'get',
    params: {
      current: params.current || 1,
      pageSize: params.pageSize || 10,
      username: params.username || undefined,
      operationType: params.operationType || undefined,
      module: params.module || undefined,
      status: params.status !== undefined ? params.status : undefined,
      startTime: params.startTime || params.timeRange?.[0] || undefined,
      endTime: params.endTime || params.timeRange?.[1] || undefined
    }
  });
}

/**
 * 获取操作日志详情
 * @param id 日志ID
 */
export function getLogDetail(id: number) {
  return request({
    url: '/log/detail',
    method: 'get',
    params: { id }
  });
}

/**
 * 删除操作日志
 * @param ids 日志ID数组
 */
export function deleteLog(ids: number[]) {
  return request({
    url: '/log/delete',
    method: 'delete',
    params: { ids: ids.join(',') }
  });
}

/**
 * 导出操作日志
 * @param params 查询参数
 */
export function exportLog(params: any) {
  return request({
    url: '/log/export',
    method: 'get',
    params: {
      username: params.username || undefined,
      operationType: params.operationType || undefined,
      module: params.module || undefined,
      status: params.status !== undefined ? params.status : undefined,
      startTime: params.startTime || params.timeRange?.[0] || undefined,
      endTime: params.endTime || params.timeRange?.[1] || undefined
    },
    responseType: 'blob'
  });
} 