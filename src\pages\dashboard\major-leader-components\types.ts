export interface Major {
  id: number;
  name: string;
  code: string;
  academyId: number;
  academyName: string;
  directorId: number;
  directorName: string;
  professionalOverview: string;
  studentTotalCount: number;
  studentInSchoolCount: number;
  courseTotalCount: number;
  planCount: number;
  createTime: string;
  modifyTime: string;
  discipline: string;
}

export interface Plan {
  id: number;
  planName: string;
  planVersion: number;
  standardId: number;
  status: number;
}

export interface Standard {
  id: number;
  standardName: string;
  standardVersion: string;
}

export interface PlanData {
  id?: number;
  planName: string;
  planVersion: number;
  standardId: number | null;
  status: number;
  standardDescription?: string;
}

export interface PlanFormData {
  id?: number;
  planName: string;
  planVersion: number;
  standardId: number | null;
  status: number;
}

export interface StandardDetail {
  standardName: string;
  standardVersion: string;
  disciplineType: string;
  releaseDate: string;
  standardDescription: string;
  requirements?: Array<{
    id: number;
    standardName: string;
    standardDescription: string;
  }>;
}

export interface EnumData {
  list?: {
    disciplineType?: Array<{ label: string; value: string }>;
  };
  map?: {
    disciplineType?: Record<string, string>;
  };
}
