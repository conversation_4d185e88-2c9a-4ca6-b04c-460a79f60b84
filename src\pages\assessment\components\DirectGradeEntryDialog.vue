<template>
  <div>
    <!-- 按课程目标录入成绩 - 全屏对话框 -->
    <t-dialog
      v-model:visible="dialogVisible"
      :header="dialogTitle"
      width="95vw"
      :top="20"
      :footer="false"
      @close="handleClose"
      class="direct-grade-entry-dialog"
    >
      <div class="grade-management-container">
        <!-- 考核内容信息 -->
        <div class="assessment-info-section">
          <div class="info-content">
            <h3>{{ assessmentInfo?.assessmentName || '考核内容' }}</h3>
            <div class="info-meta">
              <div class="meta-item">
                <span class="label">考核ID：</span>
                <span class="value">{{ assessmentId }}</span>
              </div>
              <div class="meta-item">
                <span class="label">录入方式：</span>
                <t-tag size="small" theme="primary">按课程目标录入</t-tag>
              </div>
              <div class="meta-item">
                <span class="label">状态：</span>
                <t-tag size="small" theme="success">编辑中</t-tag>
              </div>
            </div>
          </div>
          <div class="info-actions">
            <t-button theme="primary" @click="handleImportGrades">
              <template #icon>
                <t-icon name="upload" />
              </template>
              导入成绩
            </t-button>
            <t-button theme="default" variant="outline" @click="handleExportGrades">
              <template #icon>
                <t-icon name="file-export" />
              </template>
              导出成绩
            </t-button>
            <t-button theme="default" variant="outline" @click="handleRefresh">
              <template #icon>
                <t-icon name="refresh" />
              </template>
              刷新
            </t-button>
          </div>
        </div>

        <!-- 成绩录入表格区域 -->
        <div class="grade-table-section">
          <div class="table-header">
            <h4>成绩录入表</h4>
            <div class="table-actions">
              <t-button theme="primary" @click="handleSaveGrades">
                <template #icon>
                  <t-icon name="save" />
                </template>
                保存成绩
              </t-button>
            </div>
          </div>
          
          <!-- 成绩表格 -->
          <div class="grade-table-wrapper">
            <t-table
              :data="gradeData"
              :columns="gradeColumns"
              :loading="loading"
              row-key="studentId"
              bordered
              stripe
              hover
              :max-height="500"
              class="grade-table"
            >
              <template #studentName="{ row }">
                <div class="student-info">
                  <span class="student-name">{{ row.studentName }}</span>
                  <span class="student-id">{{ row.studentId }}</span>
                </div>
              </template>
              
              <template #gradeInput="{ row, col }">
                <t-input
                  v-model="row[col.colKey]"
                  type="number"
                  :min="0"
                  :max="100"
                  size="small"
                  placeholder="请输入成绩"
                  @blur="handleGradeChange(row, col.colKey)"
                />
              </template>
              
              <template #totalScore="{ row }">
                <div class="total-score">
                  <strong>{{ calculateTotalScore(row) }}</strong>
                </div>
              </template>
            </t-table>
          </div>
        </div>
      </div>
    </t-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';

// Props 定义
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  assessmentId: {
    type: Number,
    required: true
  },
  assessmentInfo: {
    type: Object,
    default: () => ({})
  }
});

// Emits 定义
const emit = defineEmits(['update:visible', 'close']);

// 响应式数据
const loading = ref(false);
const gradeData = ref([]);
const courseObjectives = ref([]);

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

const dialogTitle = computed(() => {
  return `按课程目标录入成绩 - 考核ID: ${props.assessmentId}`;
});

// 表格列定义
const gradeColumns = computed(() => {
  const baseColumns = [
    {
      colKey: 'studentName',
      title: '学生信息',
      width: 150,
      fixed: 'left',
      cell: 'studentName'
    }
  ];
  
  // 动态添加课程目标列
  const objectiveColumns = courseObjectives.value.map((objective, index) => ({
    colKey: `objective_${index + 1}`,
    title: `课程目标${index + 1}`,
    width: 120,
    cell: 'gradeInput'
  }));
  
  const endColumns = [
    {
      colKey: 'totalScore',
      title: '总分',
      width: 100,
      fixed: 'right',
      cell: 'totalScore'
    }
  ];
  
  return [...baseColumns, ...objectiveColumns, ...endColumns];
});

// 方法定义
const handleClose = () => {
  emit('close');
  emit('update:visible', false);
};

const handleImportGrades = () => {
  MessagePlugin.info('导入成绩功能开发中...');
};

const handleExportGrades = () => {
  MessagePlugin.info('导出成绩功能开发中...');
};

const handleRefresh = () => {
  loadGradeData();
};

const handleSaveGrades = () => {
  MessagePlugin.success('成绩保存成功！');
};

const handleGradeChange = (row: any, colKey: string) => {
  // 成绩变更处理逻辑
  console.log('成绩变更:', row, colKey);
};

const calculateTotalScore = (row: any) => {
  let total = 0;
  courseObjectives.value.forEach((_, index) => {
    const score = parseFloat(row[`objective_${index + 1}`]) || 0;
    total += score;
  });
  return total.toFixed(1);
};

const loadGradeData = async () => {
  loading.value = true;
  try {
    // 模拟加载数据
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 模拟学生数据
    gradeData.value = [
      { studentId: '2021001', studentName: '张三', objective_1: 85, objective_2: 90, objective_3: 88 },
      { studentId: '2021002', studentName: '李四', objective_1: 78, objective_2: 85, objective_3: 82 },
      { studentId: '2021003', studentName: '王五', objective_1: 92, objective_2: 88, objective_3: 90 }
    ];
    
    // 模拟课程目标
    courseObjectives.value = [
      { id: 1, name: '课程目标1' },
      { id: 2, name: '课程目标2' },
      { id: 3, name: '课程目标3' }
    ];
  } catch (error) {
    console.error('加载成绩数据失败:', error);
    MessagePlugin.error('加载数据失败');
  } finally {
    loading.value = false;
  }
};

// 监听器
watch(() => props.visible, (newVal) => {
  if (newVal) {
    loadGradeData();
  }
});

// 生命周期
onMounted(() => {
  if (props.visible) {
    loadGradeData();
  }
});
</script>

<style scoped>
.direct-grade-entry-dialog {
  --td-dialog-border-radius: 12px;
}

.grade-management-container {
  padding: 20px;
  background: #f8f9fa;
  min-height: 70vh;
}

.assessment-info-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-content h3 {
  margin: 0 0 12px 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.info-meta {
  display: flex;
  gap: 24px;
  flex-wrap: wrap;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.meta-item .label {
  color: #666;
  font-size: 14px;
}

.meta-item .value {
  color: #333;
  font-weight: 500;
}

.info-actions {
  display: flex;
  gap: 12px;
}

.grade-table-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.table-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.grade-table-wrapper {
  border-radius: 8px;
  overflow: hidden;
}

.student-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.student-name {
  font-weight: 500;
  color: #333;
}

.student-id {
  font-size: 12px;
  color: #666;
}

.total-score {
  text-align: center;
  color: #0052d9;
  font-size: 16px;
}
</style>
