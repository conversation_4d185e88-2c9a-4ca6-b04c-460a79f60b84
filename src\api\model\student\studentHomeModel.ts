export interface StudentHomeListResult {
    code:number;
    message:string;
  list: Array<StudentHomeInfo>;
}
export interface StudentHomeInfo {
  goalsName: string;
  grades: string;
  gid: number;
}

export interface StudentCourseListResult {
    code:number;
    message:string;
  list: Array<StudentCourseInfo>;
}
export interface StudentCourseInfo {
  courseName: string;
  cid: number;
  semester: string;
  sid:1;
  kid:Array<number>;
  type:string;
}

export interface StudentCourseTypeListResult {
  code:number;
  message:string;
    list: Array<StudentCourseTypeInfo>;
}
export interface StudentCourseTypeInfo{
  type: string;
}

export interface StudentListResult {
    code:number;
    message:string;
  list: Array<StudentInfo>;
}
export interface StudentInfo {
  totalCredits: number;//总学分
  revisedCredits:number;//已修学分
  number: number;//课程数量
  revisedNumber: number;//已修课程数量
  requiredCredits: number;//必修课学分
  revisedRequiredCredits: number;//已修必修课学分
  semester:string;//学期
  sid:number;//学期id
  electiveCredits:number//选修课学分
  revisedElectiveCredits:number//已修选修课学分
}

export interface SemesterResult {
    code:number;
    message:string;
  list: Array<SemesterInfo>;
}
export interface SemesterInfo {
    semester: string;//学期名称
}
