<template>
  <div class="bg-white rounded-lg shadow">
    <div class="p-6">
      <div class="flex justify-between items-center mb-6">
        <h3 class="text-lg font-medium">培养方案管理</h3>
        <t-button
          theme="primary"
          @click="handleAddPlan"
          class="flex items-center"
        >
          <span class="flex items-center">
            <t-icon name="add" class="mr-1" />
            新增培养方案
          </span>
        </t-button>
      </div>

      <div class="grid grid-cols-3 gap-4">
        <!-- 培养方案卡片 -->
        <PlanCard
          v-for="plan in planList"
          :key="plan.id"
          :plan="plan"
          :standard-list="standardList"
          @config="handleConfigPlan"
          @view="handleViewPlan"
          @edit="handleEditPlan"
          @copy="handleCopyPlan"
          @delete="handleDeletePlan"
        />

        <!-- 新增培养方案卡片 -->
        <div
          class="border border-dashed rounded-lg p-4 flex items-center justify-center cursor-pointer hover:border-blue-500 transition-colors"
          @click="handleAddPlan"
        >
          <div class="text-center">
            <t-icon name="add" class="text-3xl text-blue-500 mb-2" />
            <div class="text-gray-600">新增培养方案</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import PlanCard from "./PlanCard.vue";
import { Plan, Standard } from "./types";

// Props
defineProps<{
  planList: Plan[];
  standardList: Standard[];
}>();

// Emits
const emit = defineEmits<{
  addPlan: [];
  viewPlan: [planId: number];
  editPlan: [plan: Plan];
  copyPlan: [plan: Plan];
  deletePlan: [planId: number];
  configPlan: [plan: Plan];
}>();

// Methods
const handleAddPlan = () => {
  emit("addPlan");
};

const handleViewPlan = (planId: number) => {
  emit("viewPlan", planId);
};

const handleEditPlan = (plan: Plan) => {
  emit("editPlan", plan);
};

const handleCopyPlan = (plan: Plan) => {
  emit("copyPlan", plan);
};

const handleDeletePlan = (planId: number) => {
  emit("deletePlan", planId);
};

const handleConfigPlan = (plan: Plan) => {
  emit("configPlan", plan);
};
</script>
