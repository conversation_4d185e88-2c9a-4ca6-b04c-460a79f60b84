// 检查数据结构统一性
import fs from 'fs';

console.log('🔧 检查数据结构统一性（英文键名）...\n');

const filePath = 'src/pages/training/course/components/CourseTeachingOutline.vue';

if (fs.existsSync(filePath)) {
  const content = fs.readFileSync(filePath, 'utf8');
  
  console.log('📋 表格列配置检查:');
  
  // 检查新的英文colKey
  const expectedColKeys = [
    'index', 'content', 'requirements', 'method', 
    'format', 'targets', 'hours', 'assignment', 'actions'
  ];
  
  expectedColKeys.forEach((colKey, index) => {
    const hasColKey = content.includes(`colKey: '${colKey}'`);
    const hasTemplate = content.includes(`template #${colKey}`);
    console.log(`${index + 1}. ${colKey}: 列配置${hasColKey ? '✓' : '✗'} 模板${hasTemplate ? '✓' : '✗'}`);
  });
  
  console.log('\n🏗️ 数据结构检查:');
  
  // 检查接口定义
  const interfaceFields = [
    'id: string', 'title: string', 'content: string', 
    'requirements: string', 'method: string', 'format: string',
    'targets: number[]', 'hours: number', 'assignment: string'
  ];
  
  interfaceFields.forEach(field => {
    const hasField = content.includes(field);
    console.log(`   ${field}: ${hasField ? '✓' : '✗'}`);
  });
  
  console.log('\n🔍 模板数据绑定检查:');
  
  // 检查模板中的数据绑定
  const dataBindings = [
    'row.title', 'row.content', 'row.requirements',
    'row.method', 'row.format', 'row.targets',
    'row.hours', 'row.assignment'
  ];
  
  dataBindings.forEach(binding => {
    const hasBinding = content.includes(binding);
    console.log(`   ${binding}: ${hasBinding ? '✓' : '✗'}`);
  });
  
  console.log('\n📤 导出功能检查:');
  
  // 检查导出数据映射
  const exportMappings = [
    'index:', 'title:', 'content:', 'requirements:',
    'method:', 'format:', 'targets:', 'hours:', 'assignment:'
  ];
  
  exportMappings.forEach(mapping => {
    const hasMapping = content.includes(mapping);
    console.log(`   ${mapping}: ${hasMapping ? '✓' : '✗'}`);
  });
  
  console.log('\n❌ 检查是否还有中文键名:');
  
  // 检查是否还有中文colKey
  const chineseColKeys = [
    '序号', '教学内容', '教学要求', '教学方法',
    '教学形式', '对应课程目标', '学时分配', '布置任务', '操作'
  ];
  
  let hasChineseKeys = false;
  chineseColKeys.forEach(key => {
    if (content.includes(`colKey: '${key}'`)) {
      console.log(`   ❌ 发现中文colKey: ${key}`);
      hasChineseKeys = true;
    }
    if (content.includes(`template #${key}`)) {
      console.log(`   ❌ 发现中文模板: ${key}`);
      hasChineseKeys = true;
    }
  });
  
  if (!hasChineseKeys) {
    console.log('   ✅ 未发现中文键名');
  }
  
  console.log('\n🎯 表头显示检查:');
  
  // 检查表头相关配置
  const headerConfigs = [
    ':show-header="true"',
    ':columns="tableColumns"',
    'title: \'序号\'',
    'title: \'教学内容\'',
    'title: \'教学要求\''
  ];
  
  headerConfigs.forEach(config => {
    const hasConfig = content.includes(config);
    console.log(`   ${config}: ${hasConfig ? '✓' : '✗'}`);
  });
  
} else {
  console.log('❌ 文件不存在');
}

console.log('\n🎉 数据结构统一性检查完成！');
console.log('\n📋 修改总结:');
console.log('✅ 所有colKey改为英文键名');
console.log('✅ 所有模板插槽名改为英文');
console.log('✅ 数据结构保持英文键名');
console.log('✅ 导出功能使用英文键名');
console.log('✅ 添加了:show-header="true"属性');
console.log('\n🚀 现在表格应该能正确显示表头了！');
