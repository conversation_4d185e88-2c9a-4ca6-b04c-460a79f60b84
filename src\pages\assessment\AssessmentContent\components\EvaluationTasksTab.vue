<template>
  <div>

    
    <!-- 筛选和视图切换区域 -->
    <div class="filter-section">
      <t-card class="filter-card" :bordered="false">
        <div class="filter-header">
          <div class="filter-title">
            <t-icon name="filter" />
            <span>筛选条件</span>
          </div>
          <div class="filter-controls">
            <t-radio-group v-model="viewMode" class="view-mode-group">
              <t-radio-button value="all">全部考核</t-radio-button>
              <t-radio-button value="achievement">达成度计算</t-radio-button>
              <t-radio-button value="normal">普通考核</t-radio-button>
            </t-radio-group>
            <t-button
              variant="outline"
              size="small"
              @click="refreshData"
              :loading="loading"
            >
              <template #icon>
                <t-icon name="refresh" />
              </template>
              刷新
            </t-button>
          </div>
        </div>

        <!-- 考核年份学期筛选区域 -->
        <div class="academic-filter">
          <div class="academic-filter-title">
            <t-icon name="calendar" />
            <span>考核年份学期筛选</span>
          </div>
          <div class="academic-filter-controls">
            <div class="filter-item">
              <div class="filter-main">
                <label class="filter-label">考核年份：</label>
                <div class="filter-control-wrapper">
                  <t-select
                    v-model="selectedExamYears"
                    placeholder="请选择考核年份（可多选）"
                    clearable
                    multiple
                    collapse-tags
                    :max-tag-count="2"
                    :disabled="isYearFilterDisabled"
                    class="filter-select"
                    :class="{ 'filter-select--disabled': isYearFilterDisabled }"
                    @change="handleExamYearsChange"
                  >
                    <t-option
                      v-for="year in examYearOptions"
                      :key="year.value"
                      :value="year.value"
                      :label="year.label"
                    />
                  </t-select>
                  <t-tooltip v-if="isYearFilterDisabled" content="年份已由路由参数指定" placement="top">
                    <t-icon name="info-circle" class="filter-disabled-icon" />
                  </t-tooltip>
                </div>
              </div>
            </div>

            <div class="filter-item">
              <div class="filter-main">
                <label class="filter-label">考核学期：</label>
                <div class="filter-control-wrapper">
                  <t-select
                    v-model="selectedExamSemesters"
                    placeholder="请选择考核学期（可多选）"
                    clearable
                    multiple
                    collapse-tags
                    :max-tag-count="2"
                    :disabled="isSemesterFilterDisabled"
                    class="filter-select"
                    :class="{ 'filter-select--disabled': isSemesterFilterDisabled }"
                    @change="handleExamSemestersChange"
                  >
                    <t-option
                      v-for="semester in examSemesterOptions"
                      :key="semester.value"
                      :value="semester.value"
                      :label="semester.label"
                    />
                  </t-select>
                  <t-tooltip
                    v-if="isSemesterFilterDisabled"
                    :content="props.term && String(props.term).trim() !== '' ? '学期已由路由参数指定' : '任务ID已指定，学期筛选不可用'"
                    placement="top"
                  >
                    <t-icon name="info-circle" class="filter-disabled-icon" />
                  </t-tooltip>
                </div>
              </div>
            </div>

            <div class="filter-item">
              <div class="filter-main">
                <label class="filter-label">考核方法：</label>
                <div class="filter-control-wrapper">
                  <t-select
                    v-model="selectedExamMethods"
                    placeholder="请选择考核方法（可多选）"
                    clearable
                    multiple
                    collapse-tags
                    :max-tag-count="2"
                    class="filter-select"
                    @change="handleExamMethodsChange"
                  >
                    <t-option
                      v-for="method in examMethodOptions"
                      :key="method.value"
                      :value="method.value"
                      :label="method.label"
                    />
                  </t-select>
                </div>
              </div>
            </div>

            <div class="filter-item">
              <div class="filter-main">
                <label class="filter-label">考核状态：</label>
                <div class="filter-control-wrapper">
                  <t-select
                    v-model="selectedExamStatuses"
                    placeholder="请选择考核状态（可多选）"
                    clearable
                    multiple
                    collapse-tags
                    :max-tag-count="2"
                    class="filter-select"
                    @change="handleExamStatusesChange"
                  >
                    <t-option
                      v-for="status in examStatusOptions"
                      :key="status.value"
                      :value="status.value"
                      :label="status.label"
                    />
                  </t-select>
                </div>
              </div>
            </div>

            <div class="filter-item">
              <div class="filter-main">
                <label class="filter-label">录入方式：</label>
                <div class="filter-control-wrapper">
                  <t-select
                    v-model="selectedExamScoreTypes"
                    placeholder="请选择录入方式（可多选）"
                    clearable
                    multiple
                    collapse-tags
                    :max-tag-count="2"
                    class="filter-select"
                    @change="handleExamScoreTypesChange"
                  >
                    <t-option
                      v-for="scoreType in examScoreTypeOptions"
                      :key="scoreType.value"
                      :value="scoreType.value"
                      :label="scoreType.label"
                    />
                  </t-select>
                </div>
              </div>
            </div>

            <div class="filter-actions">
              <t-button
                theme="primary"
                size="small"
                @click="applyExamFilter"
                :loading="loading"
              >
                <template #icon>
                  <t-icon name="search" />
                </template>
                筛选
              </t-button>
              <t-button
                variant="outline"
                size="small"
                @click="resetExamFilter"
              >
                <template #icon>
                  <t-icon name="refresh" />
                </template>
                重置
              </t-button>
            </div>
          </div>
        </div>
      </t-card>
    </div>

    <!-- 达成度计算考核区域 -->
    <div v-if="viewMode === 'all' || viewMode === 'achievement'" class="achievement-section">
      <t-card class="achievement-card">
        <template #title>
          <div class="section-title">
            <div class="title-content">
              <t-icon name="chart-pie" class="title-icon achievement-icon" />
              <span>参与达成度计算的考核</span>
              <t-tag theme="success" size="small" class="count-tag">
                {{ achievementCalculationTasksTotal }} 项
              </t-tag>
            </div>
            <div class="title-description">
              这些考核将参与课程目标达成度的统计计算
            </div>
          </div>
        </template>

        <t-table
          :data="achievementCalculationTasks"
          :columns="taskColumns"
          :bordered="true"
          :hover="true"
          :loading="loading"
          row-key="id"
          class="achievement-table"
          :pagination="achievementPagination"
          @page-change="handleAchievementPageChange"
        >
        <template #majors="{ row }">
          <div class="flex flex-wrap gap-2">
            <t-tag v-for="major in row.majors" :key="major" theme="primary" variant="outline">
              {{ major }}
            </t-tag>
          </div>
        </template>
        <template #teachingTeam="{ row }">
          <div class="flex flex-col gap-1">
            <div v-for="(teacher, index) in row.teachingTeam" :key="index" class="flex items-center gap-2">
              <t-tag size="small" :theme="teacher.role === '主讲教师' ? 'primary' : 'default'">
                {{ teacher.name }}
              </t-tag>
              <span class="text-xs text-gray-500">{{ teacher.role }}</span>
            </div>
          </div>
        </template>
        <template #scoreType="{ row }">
          <div v-if="row.scoreType !== undefined" class="flex justify-center">
            <t-tag
              :theme="row.scoreType === 0 ? 'success' : 'primary'"
              variant="light-outline"
              size="medium"
            >
              {{ row.scoreType === 0 ? '直接录入' : '详细录入' }}
            </t-tag>
          </div>
          <span v-else class="text-gray-500 text-xs">尚未配置考核录入方式</span>
        </template>
        <template #assessmentStatus="{ row }">
          <t-tag :theme="getAssessmentStatusTheme(row.assessmentStatus)">{{ getAssessmentStatusLabel(row.assessmentStatus) }}</t-tag>
        </template>
        <template #assessmentMethod="{ row }">
          <t-tag theme="danger">{{ getAssessmentMethodLabel(row.assessmentMethod) }}</t-tag>
        </template>
        <template #assessmentTermCell="{ row }">
          <span class="semester-text">{{ convertSemesterNumToText(row.assessmentTerm) }}</span>
        </template>
        <template #achievementCalculation="{ row }">
          <div class="achievement-calculation-status">
            <t-tag
              :theme="getAchievementCalculationTheme(row.achievement)"
              :variant="getAchievementCalculationVariant(row.achievement)"
              size="small"
            >
              <template #icon>
                <t-icon :name="getAchievementCalculationIcon(row.achievement)" />
              </template>
              {{ getAchievementCalculationLabel(row.achievement) }}
            </t-tag>
            <div v-if="!row.achievement" class="calculation-note">
              <t-icon name="info-circle" />
              <span>不参与计算</span>
            </div>
          </div>
        </template>
        <template #operation="{ row }">
          <t-space size="small">
            <!-- 场景1: 尚未配置考核录入方式，状态为 配置中 -->
            <template v-if="row.assessmentStatus === 0">
              <t-button theme="primary" variant="text" size="small" @click="handleEditExam(row)">
                <template #icon><t-icon name="edit" /></template>
                编辑考核
              </t-button>
              <t-button theme="danger" variant="text" size="small" @click="handleDeleteAssessment(row)">
                <template #icon><t-icon name="delete" /></template>
                删除考核
              </t-button>
            </template>

            <!-- 场景2: 已配置，状态为 编辑中 -->
            <template v-else-if="row.assessmentStatus === 1">
              <t-button theme="primary" variant="text" size="small" @click="handleEditExam(row)">
                <template #icon><t-icon name="edit" /></template>
                编辑考核
              </t-button>
              <t-button theme="primary" variant="text" size="small" @click="handleContentConfig(row)">
                <template #icon><t-icon name="file-paste" /></template>
                配置内容
              </t-button>
              <t-button theme="success" variant="text" size="small" @click="handlePublishExam(row)">
                <template #icon><t-icon name="play" /></template>
                发布考核
              </t-button>
              <t-button theme="danger" variant="text" size="small" @click="handleDeleteAssessment(row)">
                <template #icon><t-icon name="delete" /></template>
                删除考核
              </t-button>
            </template>

            <!-- 场景3: 已配置，状态为 未开始 -->
            <!-- <template v-else-if="row.scoreType && row.assessmentStatus === 2">
              <t-button theme="success" variant="text" size="small" @click="handlePublishExam(row)">
                <template #icon><t-icon name="play" /></template>
                发布考核
              </t-button>
              <t-button theme="danger" variant="text" size="small">
                <template #icon><t-icon name="delete" /></template>
                删除考核
              </t-button>
            </template> -->

            <!-- 场景4: 状态为 进行中 -->
            <template v-else-if="row.assessmentStatus === 2">
              <t-button theme="primary" variant="text" size="small" @click="handleScoreManagement(row)">
                <template #icon>
                  <t-icon name="chart-bubble" />
                </template>
                考核成绩
              </t-button>
              <t-button theme="danger" variant="text" size="small" @click="handleWithdrawAssessment(row)">
                <template #icon>
                  <t-icon name="rollback" />
                </template>
                撤回任务
              </t-button>
              <t-button theme="danger" variant="text" size="small">
                <template #icon>
                  <t-icon name="stop-circle" />
                </template>
                结束考核
              </t-button>
            </template>

            <!-- 场景5: 状态为 已结束 -->
            <template v-else-if="row.assessmentStatus === 3">
              <t-button theme="primary" variant="text" size="small">
                <template #icon>
                  <t-icon name="view-list" />
                </template>
                考核详情
              </t-button>
              <t-button theme="primary" variant="text" size="small" @click="handleScoreManagement(row)">
                <template #icon>
                  <t-icon name="chart-bubble" />
                </template>
                考核成绩
              </t-button>
              <t-button theme="primary" variant="text" size="small">
                <template #icon>
                  <t-icon name="chart-line" />
                </template>
                试卷分析
              </t-button>
            </template>
          </t-space>
        </template>
      </t-table>
    </t-card>
    </div>

    <!-- 普通考核区域 -->
    <div v-if="viewMode === 'all' || viewMode === 'normal'" class="normal-section">
      <t-card class="normal-card">
        <template #title>
          <div class="section-title">
            <div class="title-content">
              <t-icon name="file-text" class="title-icon normal-icon" />
              <span>普通考核任务</span>
              <t-tag theme="warning" size="small" class="count-tag">
                {{ normalTasksTotal }} 项
              </t-tag>
            </div>
            <div class="title-description">
              这些考核不参与课程目标达成度计算，仅作为课程评价参考
            </div>
          </div>
        </template>

        <t-table
          :data="normalTasks"
          :columns="taskColumns"
          :bordered="true"
          :hover="true"
          :loading="loading"
          row-key="id"
          class="normal-table"
          :pagination="normalPagination"
          @page-change="handleNormalPageChange"
        >
          <template #majors="{ row }">
            <div class="flex flex-wrap gap-2">
              <t-tag v-for="major in row.majors" :key="major" theme="primary" variant="outline">
                {{ major }}
              </t-tag>
            </div>
          </template>
          <template #teachingTeam="{ row }">
            <div class="flex flex-col gap-1">
              <div v-for="(teacher, index) in row.teachingTeam" :key="index" class="flex items-center gap-2">
                <t-tag size="small" :theme="teacher.role === '主讲教师' ? 'primary' : 'default'">
                  {{ teacher.name }}
                </t-tag>
                <span class="text-xs text-gray-500">{{ teacher.role }}</span>
              </div>
            </div>
          </template>
          <template #scoreType="{ row }">
            <div v-if="row.scoreType !== undefined" class="flex justify-center">
              <t-tag
                :theme="row.scoreType === 0 ? 'success' : 'primary'"
                variant="light-outline"
                size="medium"
              >
                {{ row.scoreType === 0 ? '直接录入' : '详细录入' }}
              </t-tag>
            </div>
            <span v-else class="text-gray-500 text-xs">尚未配置考核录入方式</span>
          </template>
          <template #assessmentStatus="{ row }">
            <t-tag :theme="getAssessmentStatusTheme(row.assessmentStatus)">{{ getAssessmentStatusLabel(row.assessmentStatus) }}</t-tag>
          </template>
          <template #assessmentMethod="{ row }">
            <t-tag theme="danger">{{ getAssessmentMethodLabel(row.assessmentMethod) }}</t-tag>
          </template>
          <template #assessmentTermCell="{ row }">
            <span class="semester-text">{{ convertSemesterNumToText(row.assessmentTerm) }}</span>
          </template>
          <template #achievementCalculation="{ row }">
            <div class="achievement-calculation-status">
              <t-tag
                :theme="getAchievementCalculationTheme(row.achievement)"
                :variant="getAchievementCalculationVariant(row.achievement)"
                size="small"
              >
                <template #icon>
                  <t-icon :name="getAchievementCalculationIcon(row.achievement)" />
                </template>
                {{ getAchievementCalculationLabel(row.achievement) }}
              </t-tag>
              <div v-if="!row.achievement" class="calculation-note">
                <t-icon name="info-circle" />
                <span>不参与计算</span>
              </div>
            </div>
          </template>
          <template #operation="{ row }">
            <t-space size="small">
              <!-- 场景1: 尚未配置考核录入方式，状态为 配置中 -->
              <template v-if="row.assessmentStatus === 0">
                <t-button theme="primary" variant="text" size="small" @click="handleEditExam(row)">
                  <template #icon><t-icon name="edit" /></template>
                  编辑考核
                </t-button>
                <t-button theme="danger" variant="text" size="small" @click="handleDeleteAssessment(row)">
                  <template #icon><t-icon name="delete" /></template>
                  删除考核
                </t-button>
              </template>

              <!-- 场景2: 已配置，状态为 编辑中 -->
              <template v-else-if="row.assessmentStatus === 1">
                <t-button theme="primary" variant="text" size="small" @click="handleEditExam(row)">
                  <template #icon><t-icon name="edit" /></template>
                  编辑考核
                </t-button>
                <t-button theme="primary" variant="text" size="small" @click="handleContentConfig(row)">
                  <template #icon><t-icon name="file-paste" /></template>
                  配置内容
                </t-button>
                <t-button theme="success" variant="text" size="small" @click="handlePublishExam(row)">
                  <template #icon><t-icon name="play" /></template>
                  发布考核
                </t-button>
                <t-button theme="danger" variant="text" size="small" @click="handleDeleteAssessment(row)">
                  <template #icon><t-icon name="delete" /></template>
                  删除考核
                </t-button>
              </template>

              <!-- 场景4: 状态为 进行中 -->
              <template v-else-if="row.assessmentStatus === 2">
                <t-button theme="primary" variant="text" size="small" @click="handleScoreManagement(row)">
                  <template #icon>
                    <t-icon name="chart-bubble" />
                  </template>
                  考核成绩
                </t-button>
                <t-button theme="danger" variant="text" size="small" @click="handleWithdrawAssessment(row)">
                  <template #icon>
                    <t-icon name="rollback" />
                  </template>
                  撤回任务
                </t-button>
                <t-button theme="danger" variant="text" size="small">
                  <template #icon>
                    <t-icon name="stop-circle" />
                  </template>
                  结束考核
                </t-button>
              </template>

              <!-- 场景5: 状态为 已结束 -->
              <template v-else-if="row.assessmentStatus === 3">
                <t-button theme="primary" variant="text" size="small">
                  <template #icon>
                    <t-icon name="view-list" />
                  </template>
                  考核详情
                </t-button>
                <t-button theme="primary" variant="text" size="small" @click="handleScoreManagement(row)">
                  <template #icon>
                    <t-icon name="chart-bubble" />
                  </template>
                  考核成绩
                </t-button>
                <t-button theme="primary" variant="text" size="small">
                  <template #icon>
                    <t-icon name="chart-line" />
                  </template>
                  试卷分析
                </t-button>
              </template>
            </t-space>
          </template>
        </t-table>
      </t-card>
    </div>

    <!-- 详细录入配置全屏弹窗组件 -->
    <DetailedConfigDialog :data="detailedConfigData" />

    <!-- 考核发布弹窗组件 -->
    <AssessmentPublishingDialog
      v-model:visible="publishDialogVisible"
      :assessment-data="selectedAssessment"
      @success="handlePublishSuccess"
    />

    <!-- 编辑考核对话框 -->
    <AddAssessmentDialog
      v-model:visible="addAssessmentDialogVisible"
      :mode="editingAssessmentData ? 'edit' : 'create'"
      :assessment-data="editingAssessmentData"
      :course-id="props.courseId"
      :task-id="props.taskId"
      :course-info="props.courseInfo"
      @submit="handleEditAssessmentSubmit"
      @cancel="handleEditAssessmentCancel"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, reactive, withDefaults, watch } from 'vue';
import { useRouter } from 'vue-router';
import { MessagePlugin, DialogPlugin } from 'tdesign-vue-next';

import { deleteAssessment, pageAssessmentExamListByCourseId, withdrawAssessment, type AssessmentQuery, type AssessmentInfo } from '@/api/assessment/assessment';
import DetailedConfigDialog from '@/pages/assessment/AssessmentContent/components/DetailedConfigDialog.vue';
import AssessmentPublishingDialog from './AssessmentPublishingDialog.vue';
import AddAssessmentDialog from './AddAssessmentDialog.vue';
import { getDictOptionsByTypeTitle } from '@/utils/dictUtil';
import { CourseDetailInfo } from '@/api/training/course';
// Props
interface Props {
  courseId: string | number;
  taskId?: string | number;
  courseName?: string;
  year?: string | number;
  term?: string | number;
  isReturn?: boolean;
  courseInfo?: CourseDetailInfo;
  fromModule?: string;
}
// AddAssessmentDialog 相关状态
const addAssessmentDialogVisible = ref(false);
const editingAssessmentData = ref<AssessmentInfo | null>(null);

// 处理录入方式按钮点击 - 打开编辑对话框
const handleEditExam = (row: AssessmentInfo) => {
  // 使用 AddAssessmentDialog 进行编辑
  editingAssessmentData.value = row;
  addAssessmentDialogVisible.value = true;
};

// 处理编辑考核提交
const handleEditAssessmentSubmit = () => {
  try {
    // 重新加载考核任务数据以获取最新状态
    loadEvaluationTasks();

    // 清空编辑状态
    editingAssessmentData.value = null;
    addAssessmentDialogVisible.value = false;

    MessagePlugin.success('考核信息修改成功');
  } catch (error) {
    console.error('处理编辑考核提交失败:', error);
    MessagePlugin.error('修改失败，请重试');
  }
};

// 处理编辑考核取消
const handleEditAssessmentCancel = () => {
  editingAssessmentData.value = null;
  addAssessmentDialogVisible.value = false;
};
const props = withDefaults(defineProps<Props>(), {
  taskId: -1,
  courseName: '',
  year: '',
  term: '',
  isReturn: false,
  fromModule: ''
});

// Emits
const emit = defineEmits<{
  editExam: [task: AssessmentInfo];
  contentConfig: [task: AssessmentInfo];
}>();

// 路由实例
const router = useRouter();

const loading = ref(false);

// 考核筛选相关数据（多选模式）
const selectedExamYears = ref<string[]>([]);
const selectedExamSemesters = ref<string[]>([]);
const selectedExamMethods = ref<string[]>([]);
const selectedExamStatuses = ref<string[]>([]);
const selectedExamScoreTypes = ref<string[]>([]);

// 筛选器禁用状态控制
const isYearFilterDisabled = computed(() => {
  return props.isReturn || props.fromModule === 'current' //|| !!(props.year && String(props.year).trim() !== '')
  ;
});

const isSemesterFilterDisabled = computed(() => {
  return props.isReturn || props.fromModule === 'current' //|| !!(props.year && String(props.year).trim() !== '')
         ;
});

// 考核年份选项（动态生成最近几年）
const examYearOptions = computed(() => {
  const currentYear = new Date().getFullYear();
  const years = [];
  for (let i = currentYear - 3; i <= currentYear + 1; i++) {
    years.push({
      value: String(i),
      label: `${i}年`
    });
  }
  return years;
});

// 考核学期选项
const examSemesterOptions = ref([
  { value: '1', label: '春季学期' },
  { value: '2', label: '秋季学期' },
  { value: 'summer', label: '夏季学期' }
]);

// 考核方法选项（基于字典数据）
const examMethodOptions = computed(() => {
  const options = [];
  for (const [value, label] of Object.entries(assessmentMethodDict.value)) {
    options.push({
      value: String(value),
      label: String(label)
    });
  }
  return options;
});

// 考核状态选项（基于字典数据）
const examStatusOptions = computed(() => {
  const options = [];
  for (const [value, label] of Object.entries(assessmentStatusDict.value)) {
    options.push({
      value: String(value),
      label: String(label)
    });
  }
  return options;
});

// 录入方式选项（静态数据）
const examScoreTypeOptions = ref([
  { value: '0', label: '直接录入' },
  { value: '1', label: '详细录入' }
]);

// 数据格式转换方法
/**
 * 学年格式转年份格式
 * @param academicYear 学年格式，如 "2023-2024"
 * @returns 年份格式，如 "2024"
 */
const convertAcademicYearToYear = (academicYear: string): string => {
  if (!academicYear || !academicYear.includes('-')) {
    return academicYear;
  }
  const parts = academicYear.split('-');
  return parts[1] || parts[0]; // 返回后一年，如果没有则返回前一年
};



/**
 * 学期数字转文字
 * @param semesterNum 学期数字，如 "1", "2"
 * @returns 学期文字，如 "春季学期", "秋季学期"
 */
const convertSemesterNumToText = (semesterNum: string | number): string => {
  const numStr = String(semesterNum);
  const semesterMap: Record<string, string> = {
    '1': '春季学期',
    '2': '秋季学期',
    'summer': '夏季学期'
  };
  return semesterMap[numStr] || numStr;
};

/**
 * 学期文字转数字
 * @param semesterText 学期文字，如 "春季学期", "秋季学期"
 * @returns 学期数字，如 "1", "2"
 */
const convertSemesterTextToNum = (semesterText: string): string => {
  const textMap: Record<string, string> = {
    '春季学期': '1',
    '秋季学期': '2',
    '夏季学期': 'summer',
    '第一学期': '1',
    '第二学期': '2'
  };
  return textMap[semesterText] || semesterText;
};



// 表格列配置
const taskColumns = [
  { colKey: 'assessmentYear', title: '考核年份', width: 80 },
  {
    colKey: 'assessmentTerm',
    title: '考核学期',
    width: 100,
    cell: 'assessmentTermCell'
  },
  { colKey: 'assessmentName', title: '考核名称', width: 160 },
  // { colKey: 'description', title: '考核描述', width: 200 },
  // { colKey: 'majors', title: '考核班级', width: 120, cell: 'majors' },
  { colKey: 'creator', title: '创建者', width: 100 },
  { colKey: 'createTime', title: '创建时间', width: 160 },
  { colKey: 'assessmentMethod', title: '考核类型', width: 100,cell:'assessmentMethod' },
  { colKey: 'scoreType', title: '录入方式', width: 100, cell: 'scoreType' },
  { colKey: 'assessmentStatus', title: '考核状态', width: 100, cell: 'assessmentStatus' },
  // { colKey: 'achievementCalculation', title: '达成度计算', width: 120, cell: 'achievementCalculation' },
  { colKey: 'operation', title: '操作', width: 180, cell: 'operation' }
];



// 考核任务数据
const evaluationTasks = ref<AssessmentInfo[]>([]);

// 新增详细录入配置弹窗数据对象
const detailedConfigData = reactive({
  visible: false,
  description: '',
  title: '',
  courseId: '',
  sectionId: '',
  contentId: '',
  submitting: false,
  assessmentId: '-1', // 考核ID
  taskId: '-1' // 任务ID
});

// 考核发布弹窗相关数据
const publishDialogVisible = ref(false);
const selectedAssessment = ref<AssessmentInfo | null>(null);

// 视图模式：all-全部, achievement-达成度计算, normal-普通考核
const viewMode = ref<'all' | 'achievement' | 'normal'>('all');

// 通用筛选函数：根据所有筛选条件筛选任务（多选模式）
const applyTaskFilters = (tasks: AssessmentInfo[]): AssessmentInfo[] => {
  let filtered = tasks;

  // 应用考核年份筛选（多选）
  if (selectedExamYears.value.length > 0) {
    const selectedYearsSet = new Set(selectedExamYears.value);
    filtered = filtered.filter(task => {
      const taskYear = extractYearFromTask(task);
      return selectedYearsSet.has(taskYear);
    });
  }

  // 应用考核学期筛选（多选）
  if (selectedExamSemesters.value.length > 0) {
    const selectedSemestersSet = new Set(selectedExamSemesters.value);
    filtered = filtered.filter(task => {
      const taskSemester = extractSemesterFromTask(task);
      return selectedSemestersSet.has(taskSemester);
    });
  }

  // 应用考核方法筛选（多选）
  if (selectedExamMethods.value.length > 0) {
    const selectedMethodsSet = new Set(selectedExamMethods.value);
    filtered = filtered.filter(task => {
      return selectedMethodsSet.has(String(task.assessmentMethod));
    });
  }

  // 应用考核状态筛选（多选）
  if (selectedExamStatuses.value.length > 0) {
    const selectedStatusesSet = new Set(selectedExamStatuses.value);
    filtered = filtered.filter(task => {
      return selectedStatusesSet.has(String(task.assessmentStatus));
    });
  }

  // 应用录入方式筛选（多选）
  if (selectedExamScoreTypes.value.length > 0) {
    const selectedScoreTypesSet = new Set(selectedExamScoreTypes.value);
    filtered = filtered.filter(task => {
      return selectedScoreTypesSet.has(String(task.scoreType));
    });
  }

  return filtered;
};



/**
 * 从任务数据中提取年份信息
 * @param task 考核任务数据
 * @returns 年份字符串
 */
const extractYearFromTask = (task: AssessmentInfo): string => {
  // 尝试从不同字段提取年份信息
  if (task.assessmentYear) {
    // 如果是学年格式，转换为年份
    if (String(task.assessmentYear).includes('-')) {
      return convertAcademicYearToYear(String(task.assessmentYear));
    }
    return String(task.assessmentYear);
  }

  // 从兼容字段提取年份（如果包含年份信息）
  if (task.courseName) {
    const courseNameStr = String(task.courseName);
    const yearMatch = courseNameStr.match(/(\d{4})/);
    if (yearMatch) {
      return yearMatch[1];
    }
  }

  return '';
};

/**
 * 从任务数据中提取学期信息
 * @param task 考核任务数据
 * @returns 学期字符串
 */
const extractSemesterFromTask = (task: AssessmentInfo): string => {
  // 优先使用 assessmentTerm 字段
  if (task.assessmentTerm !== undefined) {
    return String(task.assessmentTerm);
  }

  // 从兼容字段提取学期信息
  if (task.courseName) {
    const courseNameStr = String(task.courseName);
    // 如果是文字格式，转换为数字格式
    return convertSemesterTextToNum(courseNameStr);
  }

  return '';
};

// 达成度计算考核分页配置
const achievementPagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  showJumper: true,
  showSizer: true,
  pageSizeOptions: [5, 10, 20, 50]
});

// 普通考核分页配置
const normalPagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  showJumper: true,
  showSizer: true,
  pageSizeOptions: [5, 10, 20, 50]
});



// 计算属性：参与达成度计算的考核（独立分页）
const achievementCalculationTasks = computed(() => {
  if (viewMode.value === 'achievement' || viewMode.value === 'all') {
    // 先应用筛选条件，再过滤达成度计算任务
    const filteredTasks = applyTaskFilters(allEvaluationTasks.value);
    const allAchievementTasks = filteredTasks.filter(task => task.achievement === true);

    // 更新分页总数
    achievementPagination.value.total = allAchievementTasks.length;

    // 应用分页
    const { current, pageSize } = achievementPagination.value;
    const startIndex = (current - 1) * pageSize;
    const endIndex = startIndex + pageSize;

    return allAchievementTasks.slice(startIndex, endIndex);
  }
  return [];
});

// 计算属性：普通考核任务（独立分页）
const normalTasks = computed(() => {
  if (viewMode.value === 'normal' || viewMode.value === 'all') {
    // 先应用筛选条件，再过滤普通任务
    const filteredTasks = applyTaskFilters(allEvaluationTasks.value);
    const allNormalTasks = filteredTasks.filter(task => task.achievement !== true);

    // 更新分页总数
    normalPagination.value.total = allNormalTasks.length;

    // 应用分页
    const { current, pageSize } = normalPagination.value;
    const startIndex = (current - 1) * pageSize;
    const endIndex = startIndex + pageSize;

    return allNormalTasks.slice(startIndex, endIndex);
  }
  return [];
});

// 计算属性：获取总数（用于显示标签，基于筛选后的数据）
const achievementCalculationTasksTotal = computed(() => {
  const filteredTasks = applyTaskFilters(allEvaluationTasks.value);
  return filteredTasks.filter(task => task.achievement === true).length;
});

const normalTasksTotal = computed(() => {
  const filteredTasks = applyTaskFilters(allEvaluationTasks.value);
  return filteredTasks.filter(task => task.achievement !== true).length;
});

// 重置分页状态的方法
const resetPaginationStates = () => {
  achievementPagination.value.current = 1;
  normalPagination.value.current = 1;
};

// 考核状态字典
// 1. 定义字典数据
const assessmentStatusDict = ref<Record<string | number, string>>({})
const assessmentMethodDict = ref<Record<string | number, string>>({})

// 2. 同步标签转换方法
const getAssessmentStatusLabel = (value: string | number) => {
  return assessmentStatusDict.value[value] || value
}
const getAssessmentMethodLabel = (value: string | number) => {
  return assessmentMethodDict.value[value] || value
}

// 加载考核任务数据
// 存储所有考核任务数据（不分页）
const allEvaluationTasks = ref<AssessmentInfo[]>([]);

const loadEvaluationTasks = async () => {
  if (!props.courseId) {
    MessagePlugin.warning('缺少课程信息，无法加载考核任务');
    return;
  }

  try {
    loading.value = true;
    const query: AssessmentQuery = {
      courseId: Number(props.courseId),
      pageNum: 1,
      pageSize: 1000, // 设置一个较大的值来获取所有数据
      orderType: 'desc'
    };

    // 添加考核年份学期筛选条件（作为扩展属性）
    // 注意：API可能不支持多选，这里取第一个值作为示例
    if (selectedExamYears.value.length > 0) {
      (query as any).examYear = selectedExamYears.value[0];
    }
    if (selectedExamSemesters.value.length > 0) {
      (query as any).examSemester = selectedExamSemesters.value[0];
    }

    const response = await pageAssessmentExamListByCourseId(query);
    if (response.code === 200) {
      // 存储所有数据
      allEvaluationTasks.value = response.data.records || [];

      if (allEvaluationTasks.value.length === 0) {
        MessagePlugin.info('暂无考核任务，请先创建考核任务');
      }


    } else {
      const errorMsg = response.message || '获取考核任务列表失败';
      MessagePlugin.error(errorMsg);
      allEvaluationTasks.value = [];
      evaluationTasks.value = [];
    }
  } catch (error) {
    console.error('加载考核任务失败:', error);

    if (error instanceof Error) {
      if (error.message.includes('timeout')) {
        MessagePlugin.error('请求超时，请检查网络连接后重试');
      } else if (error.message.includes('401') || error.message.includes('403')) {
        MessagePlugin.error('权限不足，请联系管理员');
      } else {
        MessagePlugin.error(`加载失败: ${error.message}`);
      }
    } else {
      MessagePlugin.error('网络异常，请检查网络连接后重试');
    }

    allEvaluationTasks.value = [];
    evaluationTasks.value = [];
  } finally {
    loading.value = false;
  }
};

// 监听视图模式变化，重置分页状态
watch(viewMode, () => {
  resetPaginationStates();
});

// 监听 props 变化，重新加载数据
watch(
  () => [props.courseId, props.courseName, props.year, props.term, props.isReturn,props.fromModule],
  (newValues, oldValues) => {
    // 检查是否有任何关键参数发生变化
    const hasKeyChanges = newValues[0] !== oldValues?.[0] ||  // courseId
                         newValues[2] !== oldValues?.[2] ||  // year
                         newValues[3] !== oldValues?.[3] ||  // term
                         newValues[4] !== oldValues?.[4]   // isReturn
                         newValues[5] !== oldValues?.[5]; // fromModule

    if (hasKeyChanges) {
      // 1. 清空当前状态
      allEvaluationTasks.value = [];

      // 清空发布相关状态
      publishDialogVisible.value = false;
      selectedAssessment.value = null;

      // 2. 重置分页状态
      resetPaginationStates();

      // 3. 根据新参数更新筛选条件
      const newIsReturn = newValues[4]; // isReturn 参数
      const newFromModule = newValues[5]; // fromModule 参数
      
      // 当 isReturn 为空或为 false 时，清理考核年份学期筛选表单的数据
      if (!newIsReturn || newFromModule !== 'current') {
        selectedExamYears.value = [];
        selectedExamSemesters.value = [];
      } else {
        // 当 isReturn 为 true 时，根据路由参数设置筛选条件
          selectedExamYears.value =  [String(props.courseInfo?.courseVersion)];
        }

        if (newValues[3]) { // term 参数
          selectedExamSemesters.value =  [String(props.courseInfo?.courseSemester)];
        }
      }

      // 4. 重新加载数据（只调用一次）
      if (newValues[0]) { // 确保 courseId 存在
        loadEvaluationTasks();
      }
    },
    {
      immediate: false,
      deep: true
    }
);

// 处理达成度计算考核分页变化
const handleAchievementPageChange = (pageInfo: { current: number; pageSize: number }) => {
  achievementPagination.value.current = pageInfo.current;
  achievementPagination.value.pageSize = pageInfo.pageSize;
  // 计算属性会自动重新计算显示的数据
};

// 处理普通考核分页变化
const handleNormalPageChange = (pageInfo: { current: number; pageSize: number }) => {
  normalPagination.value.current = pageInfo.current;
  normalPagination.value.pageSize = pageInfo.pageSize;
  // 计算属性会自动重新计算显示的数据
};

// 刷新数据
const refreshData = () => {
  loadEvaluationTasks();
  MessagePlugin.success('数据已刷新');
};

// 考核筛选相关方法（多选模式）
const handleExamYearsChange = (value: any) => {
  selectedExamYears.value = Array.isArray(value) ? value : [];
};

const handleExamSemestersChange = (value: any) => {
  selectedExamSemesters.value = Array.isArray(value) ? value : [];
};

const handleExamMethodsChange = (value: any) => {
  selectedExamMethods.value = Array.isArray(value) ? value : [];
};

const handleExamStatusesChange = (value: any) => {
  selectedExamStatuses.value = Array.isArray(value) ? value : [];
};

const handleExamScoreTypesChange = (value: any) => {
  selectedExamScoreTypes.value = Array.isArray(value) ? value : [];
};

// 应用考核筛选（多选模式）
const applyExamFilter = () => {
  // 重置分页状态（这会触发计算属性重新计算）
  resetPaginationStates();

  // 显示筛选结果提示
  const filterInfo = [];

  // 处理考核年份多选
  if (selectedExamYears.value.length > 0) {
    const yearLabels = selectedExamYears.value.map(year => `${year}年`);
    filterInfo.push(`考核年份: ${yearLabels.join(', ')}`);
  }

  // 处理考核学期多选
  if (selectedExamSemesters.value.length > 0) {
    const semesterLabels = selectedExamSemesters.value.map(semester => {
      return examSemesterOptions.value.find(s => s.value === semester)?.label || semester;
    });
    filterInfo.push(`考核学期: ${semesterLabels.join(', ')}`);
  }

  // 处理考核方法多选
  if (selectedExamMethods.value.length > 0) {
    const methodLabels = selectedExamMethods.value.map(method => {
      return examMethodOptions.value.find(m => m.value === method)?.label || method;
    });
    filterInfo.push(`考核方法: ${methodLabels.join(', ')}`);
  }

  // 处理考核状态多选
  if (selectedExamStatuses.value.length > 0) {
    const statusLabels = selectedExamStatuses.value.map(status => {
      return examStatusOptions.value.find(s => s.value === status)?.label || status;
    });
    filterInfo.push(`考核状态: ${statusLabels.join(', ')}`);
  }

  // 处理录入方式多选
  if (selectedExamScoreTypes.value.length > 0) {
    const scoreTypeLabels = selectedExamScoreTypes.value.map(scoreType => {
      return examScoreTypeOptions.value.find(s => s.value === scoreType)?.label || scoreType;
    });
    filterInfo.push(`录入方式: ${scoreTypeLabels.join(', ')}`);
  }

  if (filterInfo.length > 0) {
    MessagePlugin.success(`已应用筛选条件: ${filterInfo.join(' | ')}`);
  } else {
    MessagePlugin.info('已清除所有筛选条件');
  }
};

// 重置考核筛选（多选模式）
const resetExamFilter = () => {
  // 只重置可编辑的筛选条件
  if (!isYearFilterDisabled.value) {
    selectedExamYears.value = [];
  }
  if (!isSemesterFilterDisabled.value) {
    selectedExamSemesters.value = [];
  }

  // 其他筛选条件始终可以重置
  selectedExamMethods.value = [];
  selectedExamStatuses.value = [];
  selectedExamScoreTypes.value = [];

  // 重置分页状态（这会触发计算属性重新计算）
  resetPaginationStates();

  const resetItems = [];
  if (!isYearFilterDisabled.value) resetItems.push('考核年份');
  if (!isSemesterFilterDisabled.value) resetItems.push('考核学期');
  resetItems.push('考核方法', '考核状态', '录入方式');

  MessagePlugin.success(`已重置筛选条件: ${resetItems.join(', ')}`);
};



const handleContentConfig = (row: AssessmentInfo) => {
  
  // 直接录入模式：使用直接录入配置对话框（交给父组件处理）
  if (row.scoreType === 0) {
    // 将处理委托给父组件，便于传递更多的参数和状态
    emit('contentConfig', row);
    return;
  }
  
  // 详细录入模式：显示详细录入配置全屏弹窗
  if (row.scoreType === 1) {
    detailedConfigData.visible = true;
    detailedConfigData.description = `${row.assessmentName} - ${row.assessmentYear} 考核内容配置`;
    detailedConfigData.title = `配置考核内容 - ${row.assessmentName}（${row.assessmentYear}）`;
    detailedConfigData.courseId = String(props.courseId);
    detailedConfigData.sectionId = String(row.id); // 使用任务ID作为sectionId
    detailedConfigData.contentId = String(row.id);
    detailedConfigData.assessmentId = String(row.id); // 传递考核ID
    detailedConfigData.taskId = String(row.taskId); // 传递任务ID
    detailedConfigData.submitting = false;
    return;
  }
  
  // 其他情况（应该不会发生）
  MessagePlugin.warning('未知的考核配置模式');
};

const handlePublishExam = (row: AssessmentInfo) => {
  // 验证考核数据
  if (!row || !row.id) {
    MessagePlugin.error('考核数据异常，无法发布');
    return;
  }

  // 验证考核状态
  if (row.assessmentStatus === 0) {
    MessagePlugin.warning('考核尚未配置完成，请先完成考核配置');
    return;
  }

  // 验证课程信息
  if (!row.courseId) {
    MessagePlugin.error('缺少课程信息，无法发布考核');
    return;
  }

  selectedAssessment.value = row;
  publishDialogVisible.value = true;
};

// 处理发布成功
const handlePublishSuccess = () => {
  try {
    publishDialogVisible.value = false;
    selectedAssessment.value = null;

    // 重新加载考核任务列表
    loadEvaluationTasks();

    MessagePlugin.success('考核发布成功！');
  } catch (error) {
    console.error('处理发布成功回调失败:', error);
    MessagePlugin.warning('发布成功，但刷新列表失败，请手动刷新页面');
  }
};

// 处理撤回考核任务
const handleWithdrawAssessment = (row: AssessmentInfo) => {
  // 验证考核数据
  if (!row || !row.id) {
    MessagePlugin.error('考核数据异常，无法撤回');
    return;
  }

  // 验证考核状态
  if (row.assessmentStatus !== 2) {
    MessagePlugin.warning('只有进行中的考核才能撤回');
    return;
  }

  // 显示确认对话框
  const confirmDialog = DialogPlugin.confirm({
    header: '确认撤回',
    body: '确定要撤回该考核任务吗？撤回后考核将停止，已录入的数据将保留。',
    confirmBtn: '确认撤回',
    cancelBtn: '取消',
    theme: 'warning',
    onConfirm: async () => {
      try {
        // 调用撤回API
        const response = await withdrawAssessment(row.id);

        if (response.code === 200) {
          MessagePlugin.success('考核任务撤回成功');
          // 重新加载考核任务列表
          loadEvaluationTasks();
        } else {
          MessagePlugin.error(response.message || '撤回失败，请稍后重试');
        }
      } catch (apiError) {
        console.error('调用撤回API失败:', apiError);

        if (apiError instanceof Error) {
          if (apiError.message.includes('timeout')) {
            MessagePlugin.error('请求超时，请检查网络连接后重试');
          } else if (apiError.message.includes('401') || apiError.message.includes('403')) {
            MessagePlugin.error('权限不足，请联系管理员');
          } else {
            MessagePlugin.error(`撤回失败: ${apiError.message}`);
          }
        } else {
          MessagePlugin.error('撤回失败，请稍后重试');
        }
      } finally {
        confirmDialog.destroy();
      }
    },
    onCancel: () => {
      confirmDialog.destroy();
    }
  });
};

// 处理删除考核任务
const handleDeleteAssessment = async (row: AssessmentInfo) => {
  try{
    console.log ('handleDeleteAssessment - row:', row);

    await deleteAssessment(row.id);
    MessagePlugin.success('考核任务删除成功');
    loadEvaluationTasks();
  } catch (error) {
    console.error('删除考核任务失败:', error);
  }
};

// 无需评价标准对话框相关处理函数，已迁移到父组件

// 获取考核状态标签主题
const getAssessmentStatusTheme = (status: number): 'success' | 'danger' | 'default' | 'primary' | 'warning' => {
  const themeMap: Record<number, 'success' | 'danger' | 'default' | 'primary' | 'warning'> = {
    3: 'success',
    2: 'primary',
    1: 'warning',
    0: 'default',
  };
  return themeMap[status] || 'default';
};

// 获取达成度计算状态相关函数
const getAchievementCalculationTheme = (achievement: boolean): 'success' | 'warning' | 'default' => {
  return achievement === true ? 'success' : 'warning';
};

const getAchievementCalculationVariant = (achievement: boolean): 'dark' | 'light' | 'outline' | 'light-outline' => {
  return achievement === true ? 'light' : 'light-outline';
};

const getAchievementCalculationIcon = (achievement: boolean): string => {
  return achievement === true ? 'check-circle' : 'close-circle';
};

const getAchievementCalculationLabel = (achievement: boolean): string => {
  return achievement === true ? '参与计算' : '不参与';
};

// 处理考核成绩管理
const handleScoreManagement = (row: AssessmentInfo) => {
  try {


    // 验证必要的数据
    if (!row || !row.id) {
      MessagePlugin.error('考核数据异常，无法进入成绩管理')
      return
    }

    if (!props.courseId) {
      MessagePlugin.error('缺少课程信息，无法进入成绩管理')
      return
    }

    // 构建路由参数
    const routeParams = {
      courseId: String(props.courseId),
      assessmentId: String(row.id)
    }

    // 构建查询参数
    const queryParams: Record<string, string> = {
      from: 'evaluation-tasks' // 标识来源页面
    }

    if (props.courseName) {
      queryParams.courseName = props.courseName
    }

    if (row.assessmentName) {
      queryParams.assessmentName = row.assessmentName
    }

    if (row.assessmentYear) {
      queryParams.year = String(row.assessmentYear)
    }

    if (row.assessmentTerm) {
      queryParams.term = String(row.assessmentTerm)
    }

    // 方式1: 标准GET请求跳转（推荐）
    router.push({
      name: 'AssessmentScoreManagement',
      params: routeParams,
      query: queryParams
    })

    // 方式2: 如果需要POST方式跳转（不推荐，仅作演示）
    // handleScoreManagementWithPost(routeParams, queryParams)


  } catch (error) {
    console.error('进入成绩管理失败:', error)
    MessagePlugin.error('进入成绩管理失败，请稍后重试')
  }
}


// 暴露给父组件的方法
const updateTask = (taskId: number, updates: Partial<AssessmentInfo>) => {
  const task = evaluationTasks.value.find(t => t.id === taskId);
  if (task) {
    Object.assign(task, updates);
  }
};

const updateTaskObjectiveScores = (taskId: number, _scores: { id: string; identifier: string; description: string; score: number }[]) => {
  const task = evaluationTasks.value.find(t => t.id === taskId);
  if (task) {
    // 注意：AssessmentInfo 类型中可能没有 objectiveScores 属性
    // 这里可以根据实际需要扩展类型或使用其他方式存储分数信息

    // (task as any).objectiveScores = scores;
  }
};

// 添加新的考核任务
const addNewAssessmentTask = (taskData: AssessmentInfo) => {

  // 加载考核任务数据
  loadEvaluationTasks();
  // 添加到数组前面，以便展示
  //evaluationTasks.value.unshift(taskData);
  
  // 更新统计数据 - 根据实际统计字段更新
  // if (taskStatistics.value) {
  //   if (taskData.examStatus === '未开始') {
  //     taskStatistics.value.pendingExams += 1;
  //   } else if (taskData.examStatus === '已结束') {
  //     taskStatistics.value.completedExams += 1;
  //   }
  //   // 可能还需要更新其他统计字段
  // }
  
  // 刷新数据，确保UI更新
  //evaluationTasks.value = [...evaluationTasks.value];
};

defineExpose({
  updateTask,
  updateTaskObjectiveScores,
  loadEvaluationTasks,
  addNewAssessmentTask
});

// 加载考核类型字典的函数
const loadAssessmentMethodDict = () => {
  console.log('EvaluationTasksTab - 加载考核类型字典');
  // 将 assessmentMethodList 转换为 Record<string | number, string>
  if (props.courseInfo && Array.isArray(props.courseInfo.assessmentMethodList)) {
    assessmentMethodDict.value = {};
    props.courseInfo.assessmentMethodList.forEach((item: any) => {
      // 假设每个 item 有 id 和 name 字段
      if (item && (item.id !== undefined || item.value !== undefined) && item.name) {
        const key = item.id !== undefined ? item.id : item.value;
        assessmentMethodDict.value[String(key)] = item.name;
      }
    });
   // console.log('EvaluationTasksTab - 考核类型字典加载完成:', assessmentMethodDict.value);
  } 
};

// 初始化筛选条件的函数
const initializeFilters = () => {

  if (!props.isReturn && props.fromModule !== 'current') {
    selectedExamYears.value = [];
    selectedExamSemesters.value = [];
    console.log('清空筛选条件');
  } else {
    // 当 isReturn 为 true 时，根据课程信息设置筛选条件
    if (props.courseInfo) {
      console.log('EvaluationTasksTab - 设置筛选条件，课程信息：', props.courseInfo);
      selectedExamYears.value = [String(props.courseInfo.courseVersion)];
      selectedExamSemesters.value = [String(props.courseInfo.courseSemester)];
    } else {
      console.log('EvaluationTasksTab - 课程信息为空，等待数据加载');
    }
  }
};

// 监听 courseInfo 的变化
watch(() => props.courseInfo, (newCourseInfo) => {
  console.log('EvaluationTasksTab - courseInfo 发生变化:', newCourseInfo);
  if (newCourseInfo) {
    // 重新初始化筛选条件
    initializeFilters();
    // 重新加载考核类型字典
    loadAssessmentMethodDict();
  }
}, { immediate: true });

onMounted(async () => {
  console.log('EvaluationTasksTab - 组件挂载');
  // 初始化筛选条件
  initializeFilters();

  // 加载考核任务数据
  loadEvaluationTasks();

  // 加载考核状态字典
  const statusArr = await getDictOptionsByTypeTitle('考核状态')
  assessmentStatusDict.value = {}
  statusArr.forEach(item => {
    assessmentStatusDict.value[item.value] = item.label
  })

  // 加载考核类型字典
  loadAssessmentMethodDict();

});
</script>

<style lang="less" scoped>
// 页面头部样式
.page-header {
  margin-bottom: var(--td-comp-margin-xl);
  padding: var(--td-comp-paddingTB-l) 0;
  border-bottom: 1px solid var(--td-border-level-1-color);

  .header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 100%;

    .header-left {
      flex: 0 0 auto;

      .back-button {
        display: flex;
        align-items: center;
        gap: var(--td-comp-margin-s);
        color: var(--td-text-color-secondary);
        transition: all var(--td-transition);

        &:hover {
          color: var(--td-brand-color);
          background-color: var(--td-brand-color-1);
        }

        :deep(.t-icon) {
          font-size: 16px;
        }
      }
    }

    .header-center {
      flex: 1;
      display: flex;
      justify-content: center;

      .page-title {
        display: flex;
        align-items: center;
        gap: 8px;

        .title-icon {
          font-size: 20px;
          color: var(--td-brand-color);
        }

        .title-text {
          font-size: 18px;
          font-weight: 600;
          color: var(--td-text-color-primary);
          line-height: 1.4;
        }
      }
    }

    .header-right {
      flex: 0 0 auto;
      width: 120px; // 与左侧保持平衡
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .header-content {
      .header-center {
        .page-title {
          .title-text {
            font-size: 16px;
          }
        }
      }
    }
  }
}

// 筛选区域样式
.filter-section {
  margin-bottom: var(--td-comp-margin-xl);

  .filter-card {
    background: linear-gradient(135deg, var(--td-bg-color-container) 0%, var(--td-brand-color-1) 100%);
    border: 1px solid var(--td-brand-color-3);

    .filter-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: var(--td-comp-paddingTB-l) var(--td-comp-paddingLR-l);

      .filter-title {
        display: flex;
        align-items: center;
        gap: var(--td-comp-margin-s);
        font-size: 16px;
        font-weight: 600;
        color: var(--td-text-color-primary);

        :deep(.t-icon) {
          color: var(--td-brand-color);
        }
      }

      .filter-controls {
        display: flex;
        align-items: center;
        gap: var(--td-comp-margin-l);

        .view-mode-group {
          :deep(.t-radio-button) {
            font-weight: 500;
          }
        }
      }
    }

    // 考核年份学期筛选区域样式
    .academic-filter {
      border-top: 1px solid var(--td-border-level-1-color);
      padding: var(--td-comp-paddingTB-l) var(--td-comp-paddingLR-l);
      margin-top: var(--td-comp-margin-l);

      .academic-filter-title {
        display: flex;
        align-items: center;
        gap: var(--td-comp-margin-s);
        font-size: 14px;
        font-weight: 600;
        color: var(--td-text-color-primary);
        margin-bottom: var(--td-comp-margin-l);

        :deep(.t-icon) {
          color: var(--td-brand-color);
          font-size: 16px;
        }
      }

      .academic-filter-controls {
        display: flex;
        align-items: flex-start; // 改为顶部对齐，确保所有筛选器在同一基线
        gap: var(--td-comp-margin-l);
        flex-wrap: wrap;
        min-height: 48px; // 设置容器最小高度

        .filter-item {
          display: flex;
          align-items: center;
          min-width: 160px; // 增加最小宽度以适应更多筛选器
          min-height: 48px; // 设置统一的最小高度

          .filter-main {
            display: flex;
            align-items: center;
            gap: var(--td-comp-margin-s);
            width: 100%;
            min-height: 32px; // 确保筛选器主体有统一高度

            .filter-label {
              font-size: 14px;
              color: var(--td-text-color-secondary);
              white-space: nowrap;
              font-weight: 500;
              min-width: 70px; // 确保标签宽度一致
              flex-shrink: 0;
            }

            .filter-control-wrapper {
              display: flex;
              align-items: center;
              gap: 6px;
              flex: 1;
              min-height: 32px; // 与筛选器组件高度一致

              .filter-disabled-icon {
                font-size: 14px;
                color: var(--td-warning-color);
                cursor: help;
                flex-shrink: 0;

                &:hover {
                  color: var(--td-warning-color-hover);
                }
              }
            }
          }

          .filter-select {
            min-width: 140px;
            flex: 1; // 让选择器自适应宽度

            // 确保所有筛选器具有统一的基础高度
            :deep(.t-select__wrap) {
              min-height: 32px;
              height: 32px; // 固定高度确保一致性
              max-height: none; // 移除最大高度限制，避免高度不一致
              display: flex;
              align-items: center;
            }

            // 多选标签样式优化
            :deep(.t-tag) {
              max-width: 80px;
              height: 20px; // 固定标签高度
              line-height: 18px;

              .t-tag__text {
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }
            }

            // 多选标签容器样式
            :deep(.t-select__multiple) {
              min-height: 30px;
              max-height: 30px;
              overflow: hidden;
              display: flex;
              align-items: center;
              flex-wrap: nowrap; // 防止换行影响高度
            }

            // 禁用状态样式
            &.filter-select--disabled {
              :deep(.t-select__wrap) {
                background-color: var(--td-bg-color-component-disabled);
                border-color: var(--td-border-level-1-color);
                cursor: not-allowed;
                height: 32px; // 保持统一高度

                .t-select__single-display {
                  color: var(--td-text-color-disabled);
                }

                .t-tag {
                  background-color: var(--td-bg-color-tag-disabled);
                  color: var(--td-text-color-disabled);
                  border-color: var(--td-border-level-1-color);
                  height: 20px; // 保持标签高度一致
                }
              }
            }
          }
        }

        .filter-actions {
          display: flex;
          align-items: center;
          gap: var(--td-comp-margin-s);
          margin-left: auto;
          flex-shrink: 0; // 防止按钮被压缩

          :deep(.t-button) {
            font-weight: 500;
          }
        }
      }

      // 响应式设计
      @media (max-width: 1200px) {
        .academic-filter-controls {
          .filter-item {
            min-width: 140px; // 中等屏幕减小宽度
            min-height: 44px; // 稍微减小高度

            .filter-main {
              .filter-label {
                min-width: 60px;
              }
            }
          }
        }
      }

      @media (max-width: 768px) {
        .academic-filter-controls {
          flex-direction: column;
          align-items: stretch;
          gap: var(--td-comp-margin-m);

          .filter-item {
            min-width: auto;
            min-height: 40px; // 移动端减小高度

            .filter-main {
              .filter-label {
                min-width: auto;
                flex-shrink: 0;
              }

              .filter-control-wrapper {
                .filter-select {
                  flex: 1;
                  min-width: auto;
                }
              }
            }
          }

          .filter-actions {
            margin-left: 0;
            justify-content: center;
            flex-direction: row;
            gap: var(--td-comp-margin-m);
          }
        }
      }

      @media (max-width: 480px) {
        .academic-filter-controls {
          .filter-item {
            min-height: 36px; // 小屏幕进一步减小高度

            .filter-main {
              flex-direction: column;
              align-items: flex-start;
              gap: 4px;

              .filter-label {
                align-self: flex-start;
              }

              .filter-control-wrapper {
                width: 100%;

                .filter-select {
                  width: 100%;
                }
              }
            }
          }

          .filter-actions {
            flex-direction: column;
            gap: var(--td-comp-margin-s);

            :deep(.t-button) {
              width: 100%;
            }
          }
        }
      }
    }
  }
}

// 学期显示样式
.semester-text {
  font-size: 14px;
  color: var(--td-text-color-primary);
  font-weight: 500;
  padding: 2px 8px;
  background-color: var(--td-bg-color-container-select);
  border-radius: var(--td-radius-small);
  border: 1px solid var(--td-border-level-2-color);
  display: inline-block;
  min-width: 80px;
  text-align: center;
}

// 考核分类区域样式
.achievement-section,
.normal-section {
  margin-bottom: var(--td-comp-margin-xl);

  .achievement-card,
  .normal-card {
    border-radius: var(--td-radius-large);
    overflow: hidden;
    box-shadow: var(--td-shadow-2);
    transition: all var(--td-transition);

    &:hover {
      box-shadow: var(--td-shadow-3);
    }
  }

  .achievement-card {
    border-left: 4px solid var(--td-success-color);

    :deep(.t-card__header) {
      background: linear-gradient(135deg, var(--td-success-color-1) 0%, var(--td-success-color-2) 100%);
      border-bottom: 1px solid var(--td-success-color-3);
    }
  }

  .normal-card {
    border-left: 4px solid var(--td-warning-color);

    :deep(.t-card__header) {
      background: linear-gradient(135deg, var(--td-warning-color-1) 0%, var(--td-warning-color-2) 100%);
      border-bottom: 1px solid var(--td-warning-color-3);
    }
  }

  .section-title {
    .title-content {
      display: flex;
      align-items: center;
      gap: var(--td-comp-margin-m);
      margin-bottom: var(--td-comp-margin-s);

      .title-icon {
        font-size: 20px;

        &.achievement-icon {
          color: var(--td-success-color);
        }

        &.normal-icon {
          color: var(--td-warning-color);
        }
      }

      span {
        font-size: 18px;
        font-weight: 600;
        color: var(--td-text-color-primary);
      }

      .count-tag {
        font-weight: 600;
      }
    }

    .title-description {
      font-size: var(--td-font-size-body-small);
      color: var(--td-text-color-secondary);
      margin-left: 32px;
    }
  }

  .achievement-table,
  .normal-table {
    :deep(.t-table) {
      border-radius: 0;
    }

    :deep(.t-table__header) {
      background: var(--td-bg-color-container-hover);
    }

    :deep(.t-table__body) {
      tr:hover {
        background: var(--td-bg-color-container-hover);
      }
    }
  }
}

// 统计指标区域
.statistics-section {
  margin-bottom: 24px;
  
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 20px;
    
    .stat-card {
      opacity: 0;
      transform: translateY(30px);
      animation: slideInUp 0.6s ease-out var(--delay) both;
      
      .stat-card-inner {
        background: white;
        border: 1px solid rgba(0, 0, 0, 0.08);
        border-radius: 16px;
        padding: 20px 16px;
        display: flex;
        align-items: center;
        gap: 16px;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
        height: 100%;
        
        &:hover {
          transform: translateY(-4px);
          box-shadow: 0 12px 32px rgba(0, 0, 0, 0.12);
          border-color: var(--td-brand-color-3);
        }
        
        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          height: 3px;
          background: linear-gradient(90deg, var(--td-brand-color), var(--td-brand-color-6));
          opacity: 0;
          transition: opacity 0.3s ease;
        }
        
        &:hover::before {
          opacity: 1;
        }
        
        .stat-icon-wrapper {
          width: 56px;
          height: 56px;
          border-radius: 14px;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-shrink: 0;
          transition: all 0.3s ease;
          
          &.icon-blue {
            background: linear-gradient(135deg, var(--td-brand-color-1), var(--td-brand-color-2));
            color: var(--td-brand-color);
          }
          
          &.icon-green {
            background: linear-gradient(135deg, var(--td-success-color-1), var(--td-success-color-2));
            color: var(--td-success-color);
          }
          
          &.icon-orange {
            background: linear-gradient(135deg, var(--td-warning-color-1), var(--td-warning-color-2));
            color: var(--td-warning-color);
          }
          
          &.icon-purple {
            background: linear-gradient(135deg, rgba(114, 46, 209, 0.1), rgba(114, 46, 209, 0.2));
            color: #722ed1;
          }
          
          :deep(.t-icon) {
            transition: transform 0.3s ease;
          }
        }
        
        &:hover .stat-icon-wrapper {
          transform: scale(1.1);
          
          :deep(.t-icon) {
            transform: rotate(5deg);
          }
        }
        
        .stat-content {
          flex: 1;
          
          .stat-number {
            font-size: 28px;
            font-weight: 700;
            color: var(--td-text-color-primary);
            margin-bottom: 4px;
            line-height: 1.2;
          }
          
          .stat-label {
            font-size: 14px;
            color: var(--td-text-color-secondary);
            margin-bottom: 8px;
            font-weight: 500;
          }
          
          .stat-trend {
            :deep(.t-tag) {
              font-size: 12px;
              border-radius: 12px;
              padding: 4px 8px;
              font-weight: 600;
            }
          }
        }
      }
    }
  }
}

// 动画效果
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .statistics-section {
    .stats-grid {
      grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
      gap: 16px;
      
      .stat-card {
        .stat-card-inner {
          padding: 18px 14px;
          gap: 14px;
          
          .stat-icon-wrapper {
            width: 48px;
            height: 48px;
            
            :deep(.t-icon) {
              font-size: 24px;
            }
          }
          
          .stat-content {
            .stat-number {
              font-size: 24px;
            }
            
            .stat-label {
              font-size: 13px;
            }
          }
        }
      }
    }
  }
}

// 表格单元格支持换行
:deep(.t-table) {
  td {
    white-space: normal;
  }
}

@media (max-width: 768px) {
  .statistics-section {
    .stats-grid {
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 12px;
      
      .stat-card {
        .stat-card-inner {
          padding: 16px 12px;
          gap: 12px;
          
          .stat-icon-wrapper {
            width: 40px;
            height: 40px;
            
            :deep(.t-icon) {
              font-size: 20px;
            }
          }
          
          .stat-content {
            .stat-number {
              font-size: 20px;
            }
            
            .stat-label {
              font-size: 12px;
            }
            
            .stat-trend {
              :deep(.t-tag) {
                font-size: 11px;
                padding: 2px 6px;
              }
            }
          }
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .statistics-section {
    .stats-grid {
      grid-template-columns: 1fr;
      
      .stat-card {
        .stat-card-inner {
          padding: 16px 12px;
          
          .stat-icon-wrapper {
            width: 48px;
            height: 48px;
            
            :deep(.t-icon) {
              font-size: 24px;
            }
          }
          
          .stat-content {
            .stat-number {
              font-size: 24px;
            }
            
            .stat-label {
              font-size: 14px;
            }
          }
        }
      }
    }
  }
}

// 达成度计算状态样式
.achievement-calculation-status {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--td-comp-margin-xs);

  :deep(.t-tag) {
    border-radius: var(--td-radius-medium);
    font-weight: var(--td-font-weight-medium);
  }

  .calculation-note {
    display: flex;
    align-items: center;
    gap: var(--td-comp-margin-xxs);
    font-size: var(--td-font-size-body-extra-small);
    color: var(--td-text-color-placeholder);
    opacity: 0.8;

    :deep(.t-icon) {
      font-size: var(--td-font-size-body-extra-small);
    }
  }
}
</style>
