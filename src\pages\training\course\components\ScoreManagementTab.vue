<template>
  <div>
    <t-card>
      <template #title>
        <div class="flex items-center w-full">
          <t-icon name="chart-bubble" class="mr-2 text-blue-500" />
          <span class="flex-1">成绩管理</span>
          <div class="flex items-center">
            <t-space size="small">
              <t-button theme="default">
                <template #icon>
                  <t-icon name="upload" />
                </template>
                导入成绩
              </t-button>
              <t-button theme="default">
                <template #icon>
                  <t-icon name="download" />
                </template>
                导出成绩
              </t-button>
            </t-space>
          </div>
        </div>
      </template>
      
      <div class="mb-4">
        <t-form layout="inline">
          <t-form-item label="班级">
            <t-select v-model="filterOptions.class" :options="classOptions" style="width: 180px;"></t-select>
          </t-form-item>
          <t-form-item label="学号/姓名">
            <t-input v-model="filterOptions.keyword" placeholder="请输入学号或姓名" style="width: 200px;"></t-input>
          </t-form-item>
          <t-form-item>
            <t-button theme="primary">查询</t-button>
            <t-button theme="default" style="margin-left: 8px;">重置</t-button>
          </t-form-item>
        </t-form>
      </div>
      
      <t-table
        :data="paginatedScores"
        :columns="scoreColumns"
        :bordered="true"
        :hover="true"
        :loading="loading"
        row-key="id"
      >
        <template #finalScore="{ row }">
          <div :class="getScoreClass(row.finalScore)">{{ row.finalScore }}</div>
        </template>
        <template #operation="{ row }">
          <t-space size="small">
            <t-button theme="primary" variant="text" size="small">
              <template #icon>
                <t-icon name="edit" />
              </template>
              编辑成绩
            </t-button>
            <t-button theme="primary" variant="text" size="small">
              <template #icon>
                <t-icon name="file-paste" />
              </template>
              成绩详情
            </t-button>
          </t-space>
        </template>
      </t-table>

      <!-- 分页组件 -->
      <div class="pagination-container">
        <t-pagination
          v-model:current="scorePagination.current"
          v-model:pageSize="scorePagination.pageSize"
          :total="scorePagination.total"
          :show-jumper="scorePagination.showJumper"
          :show-sizer="scorePagination.showSizer"
          :page-size-options="scorePagination.pageSizeOptions"
          theme="default"
          size="medium"
          @current-change="handleScorePageChange"
          @page-size-change="handleScorePageSizeChange"
        />
      </div>
    </t-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import {
  mockStudentScores,
  mockClassOptions,
  mockScoreColumns,
} from '../../../../../mock/assessment';

const loading = ref(false);

// 成绩管理分页配置
const scorePagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  showJumper: true,
  showSizer: true,
  pageSizeOptions: [5, 10, 20, 50]
});

// 成绩管理表格列配置
const scoreColumns = mockScoreColumns;

// 班级选项
const classOptions = mockClassOptions;

// 筛选选项
const filterOptions = ref({
  class: 'all',
  keyword: ''
});

// 学生成绩数据
const studentScores = ref(mockStudentScores);

// 计算分页显示的成绩数据
const paginatedScores = computed(() => {
  const start = (scorePagination.value.current - 1) * scorePagination.value.pageSize;
  const end = start + scorePagination.value.pageSize;
  return studentScores.value.slice(start, end);
});

// 处理成绩管理分页变化
const handleScorePageChange = (newPage: number) => {
  scorePagination.value.current = newPage;
};

const handleScorePageSizeChange = (newSize: number) => {
  scorePagination.value.pageSize = newSize;
  scorePagination.value.current = 1;
};

// 获取成绩样式类
const getScoreClass = (score: number) => {
  if (score >= 90) return 'score-excellent';
  if (score >= 80) return 'score-good';
  if (score >= 70) return 'score-medium';
  if (score >= 60) return 'score-pass';
  return 'score-fail';
};

onMounted(() => {
  scorePagination.value.total = studentScores.value.length;
});
</script>

<style lang="less" scoped>
// 成绩样式
.score-excellent {
  color: #ed7b2f;
  font-weight: 600;
}

.score-good {
  color: #00a870;
  font-weight: 600;
}

.score-medium {
  color: #0052d9;
}

.score-pass {
  color: #888;
}

.score-fail {
  color: #e34d59;
  font-weight: 600;
}

// 分页容器样式
.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}

// 表格单元格支持换行
:deep(.t-table) {
  td {
    white-space: normal;
  }
}
</style>
