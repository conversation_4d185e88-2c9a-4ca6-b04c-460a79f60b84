import { ref, computed, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useRoutesStore } from '@/store/modules/routes'
import type { ObeRouteRecord } from '@/types/router'
import type { MenuRoute } from '@/types/interface'
import { useHomeNavigation } from './useHomeNavigation'
import { useWorkspaceCaching } from './useWorkspaceCaching'

export function useDynamicSidebarLogic() {
  const route = useRoute()
  const router = useRouter()
  const routesStore = useRoutesStore()
  const { goToHome } = useHomeNavigation()
  const { getCachedWorkspace, goToCachedWorkspace } = useWorkspaceCaching()

  // 父路由信息
  const parentRoute = ref<string>('')

  // 来源路由缓存 - 缓存工作台路由信息
  const cachedFromRoute = ref<{
    path: string
    fullPath: string
    name?: string
    params?: any
    query?: any
  } | null>(null)

  // 查询参数键名常量
  const CACHE_QUERY_KEYS = {
    DASHBOARD_PATH: '_dashboardPath',
    DASHBOARD_PARAMS: '_dashboardParams', 
    DASHBOARD_QUERY: '_dashboardQuery',
    DASHBOARD_NAME: '_dashboardName'
  }

  // 将缓存信息编码到查询参数
  const encodeCacheToQuery = (cacheInfo: typeof cachedFromRoute.value) => {
    if (!cacheInfo) return {}
    
    const cacheQuery: Record<string, string> = {}
    
    // 编码路径
    if (cacheInfo.path) {
      cacheQuery[CACHE_QUERY_KEYS.DASHBOARD_PATH] = encodeURIComponent(cacheInfo.path)
    }
    
    // 编码路由名称
    if (cacheInfo.name) {
      cacheQuery[CACHE_QUERY_KEYS.DASHBOARD_NAME] = cacheInfo.name
    }
    
    // 编码参数
    if (cacheInfo.params && Object.keys(cacheInfo.params).length > 0) {
      cacheQuery[CACHE_QUERY_KEYS.DASHBOARD_PARAMS] = encodeURIComponent(JSON.stringify(cacheInfo.params))
    }
    
    // 编码查询参数
    if (cacheInfo.query && Object.keys(cacheInfo.query).length > 0) {
      cacheQuery[CACHE_QUERY_KEYS.DASHBOARD_QUERY] = encodeURIComponent(JSON.stringify(cacheInfo.query))
    }
    
    return cacheQuery
  }

  // 从查询参数解码缓存信息
  const decodeCacheFromQuery = () => {
    const query = route.query
    
    // 检查是否存在缓存查询参数
    if (!query[CACHE_QUERY_KEYS.DASHBOARD_PATH]) {
      return null
    }
    
    try {
      const cacheInfo: typeof cachedFromRoute.value = {
        path: decodeURIComponent(query[CACHE_QUERY_KEYS.DASHBOARD_PATH] as string),
        fullPath: '', // 将在后面重构
        params: {},
        query: {}
      }
      
      // 解码路由名称
      if (query[CACHE_QUERY_KEYS.DASHBOARD_NAME]) {
        cacheInfo.name = query[CACHE_QUERY_KEYS.DASHBOARD_NAME] as string
      }
      
      // 解码参数
      if (query[CACHE_QUERY_KEYS.DASHBOARD_PARAMS]) {
        cacheInfo.params = JSON.parse(decodeURIComponent(query[CACHE_QUERY_KEYS.DASHBOARD_PARAMS] as string))
      }
      
      // 解码查询参数
      if (query[CACHE_QUERY_KEYS.DASHBOARD_QUERY]) {
        cacheInfo.query = JSON.parse(decodeURIComponent(query[CACHE_QUERY_KEYS.DASHBOARD_QUERY] as string))
      }
      
      // 重构完整路径
      const queryString = new URLSearchParams(cacheInfo.query).toString()
      cacheInfo.fullPath = cacheInfo.path + (queryString ? `?${queryString}` : '')
      
      console.log('✅ 从查询参数恢复缓存信息:', cacheInfo)
      return cacheInfo
    } catch (error) {
      console.warn('❌ 解码缓存查询参数失败:', error)
      return null
    }
  }

  // 更新当前路由的查询参数以包含缓存信息
  const updateRouteWithCache = (cacheInfo: typeof cachedFromRoute.value) => {
    if (!cacheInfo) return
    
    const currentQuery = { ...route.query }
    const cacheQuery = encodeCacheToQuery(cacheInfo)
    
    // 合并缓存查询参数到当前查询参数
    Object.assign(currentQuery, cacheQuery)
    
    // 静默更新路由，不触发导航
    router.replace({
      path: route.path,
      query: currentQuery
    }).catch(() => {
      // 忽略导航错误（如重复导航）
    })
    
    console.log('✅ 已将缓存信息更新到查询参数')
  }

  // 生成动态菜单 - 根据父路由过滤子菜单
  const generateDynamicMenu = (parentPath: string): MenuRoute[] => {
    if (!parentPath) return []
    
    // 从路由缓存中查找匹配的父路由
    const allRoutes = routesStore.routes
    const targetRoute = findRouteByPath(allRoutes, parentPath)
    console.log('生成动态菜单:', { parentPath, targetRoute })
    if (!targetRoute || !targetRoute.children) return []
    
    // 过滤并返回子路由，只显示非隐藏的路由，转换为MenuRoute格式
    return targetRoute.children
      .filter(child => !child.meta?.hidden)
      .map(child => {
        // 处理相对路径，组合完整路径
        let fullPath: string
        if (child.path.startsWith('/')) {
          // 绝对路径，直接使用
          fullPath = child.path
        } else {
          // 相对路径，需要与父路径组合
          fullPath = `${parentPath}/${child.path}`
        }
        
        // 对于包含动态参数的路径，需要从当前路由获取参数值
        if (fullPath.includes(':')) {
          // 获取当前路由的所有参数
          const currentParams = route.params
          // 遍历所有参数，替换对应的占位符
          Object.keys(currentParams).forEach(key => {
            const paramValue = currentParams[key]
            if (paramValue) {
              fullPath = fullPath.replace(`:${key}`, paramValue as string)
            }
          })
        }
        
        return {
          path: fullPath,
          name: child.name,
          meta: child.meta,
          children: child.children ? child.children.map(grandChild => {
            let grandChildPath = grandChild.path.startsWith('/') ? grandChild.path : `${fullPath}/${grandChild.path}`
            
            // 处理孙子路由的动态参数
            if (grandChildPath.includes(':')) {
              const currentParams = route.params
              Object.keys(currentParams).forEach(key => {
                const paramValue = currentParams[key]
                if (paramValue) {
                  grandChildPath = grandChildPath.replace(`:${key}`, paramValue as string)
                }
              })
            }
            
            return {
              path: grandChildPath,
              name: grandChild.name,
              meta: grandChild.meta,
              children: grandChild.children || []
            }
          }) : []
        } as MenuRoute
      })
  }

  // 递归查找路由
  const findRouteByPath = (routes: ObeRouteRecord[], targetPath: string): ObeRouteRecord | null => {
    for (const route of routes) {
      // 匹配路径或名称
      if (route.path === targetPath || route.name === targetPath) {
        return route
      }
      // 如果有子路由，递归查找
      if (route.children && route.children.length > 0) {
        const found = findRouteByPath(route.children, targetPath)
        if (found) {
          // 如果在子路由中找到了目标，返回当前路由作为父路由
          return route
        }
      }
    }
    return null
  }

  // 缓存来源路由信息
  const cacheDashboardRoute = () => {
    // 优先尝试从查询参数恢复缓存
    const queryCache = decodeCacheFromQuery()
    if (queryCache) {
      cachedFromRoute.value = queryCache
      console.log('✅ 从查询参数恢复缓存成功:', cachedFromRoute.value)
      return
    }
    
    // 如果已经有内存缓存，且不是强制更新，则跳过
    if (cachedFromRoute.value) {
      console.log('已存在缓存的来源路由，跳过重复缓存:', cachedFromRoute.value)
      return
    }
    
    console.log('开始缓存来源路由信息，当前route.query.from:', route.query.from)
    
    // 优先从query参数获取from信息
    if (route.query.from) {
      const fromPath = route.query.from as string
      console.log('正在解析from参数:', fromPath)
      
      // 解析完整路径，提取路径、参数和查询参数
      try {
        const url = new URL(fromPath, window.location.origin)
        const pathSegments = url.pathname.split('/').filter(Boolean)
        
        // 构建缓存的路由信息
        cachedFromRoute.value = {
          path: url.pathname,
          fullPath: fromPath,
          params: {},
          query: Object.fromEntries(url.searchParams.entries())
        }
        
        // 如果是教师工作台路径，提取majorId参数
        if (pathSegments[0] === 'dashboard' && pathSegments[1] === 'teacher' && pathSegments[2]) {
          cachedFromRoute.value.params.majorId = pathSegments[2]
          cachedFromRoute.value.name = 'DashboardTeacher'
        }
        
        console.log('✅ 缓存来源路由信息成功:', cachedFromRoute.value)
        // 持久化到查询参数
        updateRouteWithCache(cachedFromRoute.value)
        return
      } catch (error) {
        console.warn('❌ 解析from参数失败:', error)
      }
    }
    
    // 如果没有from参数，尝试从当前路由推断工作台路径
    const currentPath = route.path
    console.log('尝试从当前路径推断工作台信息:', currentPath)
    
    if (currentPath.startsWith('/teacher/course')) {
      // 从路由参数或query中获取majorId
      const majorId = route.params.majorId || route.query.majorId
      console.log('检测到教师端课程路由，majorId:', majorId)
      
      if (majorId) {
        cachedFromRoute.value = {
          path: `/dashboard/teacher/${majorId}`,
          fullPath: `/dashboard/teacher/${majorId}`,
          name: 'DashboardTeacher',
          params: { majorId },
          query: {}
        }
        console.log('✅ 推断工作台路由信息成功:', cachedFromRoute.value)
        // 持久化到查询参数
        updateRouteWithCache(cachedFromRoute.value)
        return
      }
    }
    
    // 优先尝试从工作台缓存获取最后访问的工作台
    const cachedWorkspace = getCachedWorkspace()
    if (cachedWorkspace) {
      console.log('💾 使用工作台缓存:', cachedWorkspace)
      cachedFromRoute.value = {
        path: cachedWorkspace.path,
        fullPath: cachedWorkspace.fullPath,
        name: cachedWorkspace.name,
        params: cachedWorkspace.params || {},
        query: cachedWorkspace.query || {}
      }
      console.log('✅ 使用缓存的工作台路由信息:', cachedFromRoute.value)
      // 持久化到查询参数
      updateRouteWithCache(cachedFromRoute.value)
      return
    }
    
    // 降级：根据用户角色智能判断默认工作台
    console.log('📍 工作台缓存为空，根据用户角色确定默认工作台路径')
    const defaultHomePath = goToHome()
    console.log('📍 角色默认工作台路径:', defaultHomePath)
    
    // 解析路由路径以确定路由名称和参数
    let routeName = ''
    let routeParams = {}
    
    if (defaultHomePath.includes('/dashboard/major')) {
      routeName = 'DashboardMajor'
    } else if (defaultHomePath.includes('/dashboard/teacher')) {
      routeName = 'DashboardTeacher'
      // 如果路径包含参数，提取majorId
      const pathMatch = defaultHomePath.match(/\/dashboard\/teacher\/(.+)/)
      if (pathMatch) {
        routeParams = { majorId: pathMatch[1] }
      }
    } else if (defaultHomePath.includes('/dashboard/course-leader')) {
      routeName = 'DashboardCourseLeader'
    } else if (defaultHomePath.includes('/student/home')) {
      routeName = 'StudentHome'
    } else if (defaultHomePath.includes('/system/home')) {
      routeName = 'SystemHome'
    }
    
    cachedFromRoute.value = {
      path: defaultHomePath,
      fullPath: defaultHomePath,
      name: routeName,
      params: routeParams,
      query: {}
    }
    console.log('✅ 使用角色默认的工作台路由信息:', cachedFromRoute.value)
    // 持久化到查询参数
    updateRouteWithCache(cachedFromRoute.value)
  }

  // 从路由参数或query中获取父路由信息
  const initializeParentRoute = () => {
    // 优先从query参数获取
    if (route.query.parentRoute) {
      parentRoute.value = route.query.parentRoute as string
      return
    }
    
    // 从路由参数获取
    if (route.params.parentRoute) {
      parentRoute.value = route.params.parentRoute as string
      return
    }
    
    // 从路由meta获取
    if (route.meta.parentRoute) {
      parentRoute.value = route.meta.parentRoute as string
      return
    }
    
    // 默认根据当前路由推断父路由 - 修复路径匹配逻辑
    const currentPath = route.path
    // 对于教师端课程路由，父路由应该是 '/teacher/course'
    if (currentPath.startsWith('/teacher/course')) {
      parentRoute.value = '/teacher/course'
      return
    }
    
    // 通用逻辑：取路径的前两段作为父路由
    const pathSegments = currentPath.split('/').filter(Boolean)
    if (pathSegments.length >= 2) {
      parentRoute.value = `/${pathSegments[0]}/${pathSegments[1]}`
    }
  }

  // 清理缓存查询参数
  const clearCacheFromQuery = () => {
    const currentQuery = { ...route.query }
    
    // 移除所有缓存相关的查询参数
    Object.values(CACHE_QUERY_KEYS).forEach(key => {
      delete currentQuery[key]
    })
    
    // 如果查询参数发生了变化，更新路由
    const hasChanges = Object.values(CACHE_QUERY_KEYS).some(key => route.query[key])
    if (hasChanges) {
      router.replace({
        path: route.path,
        query: currentQuery
      }).catch(() => {
        // 忽略导航错误
      })
      console.log('✅ 已清理缓存查询参数')
    }
  }

  // 返回工作台处理函数
  const handleBackToDashboard = () => {
    console.log('🔄 开始执行返回工作台逻辑')
    
    // 优先使用缓存的来源路由信息
    if (cachedFromRoute.value) {
      console.log('✅ 使用缓存路由返回工作台:', cachedFromRoute.value)
      
      // 清理当前页面的缓存查询参数
      clearCacheFromQuery()
      
      if (cachedFromRoute.value.name) {
        // 如果有路由名称，使用name方式导航（更可靠）
        router.push({
          name: cachedFromRoute.value.name,
          params: cachedFromRoute.value.params,
          query: cachedFromRoute.value.query
        })
      } else {
        // 否则使用完整路径
        router.push(cachedFromRoute.value.fullPath)
      }
      return
    }
    
    // 降级：检查是否有from参数（从哪个页面跳转过来的）
    if (route.query.from) {
      console.log('📄 使用query.from参数返回:', route.query.from)
      router.push(route.query.from as string)
      return
    }
    
    // 降级：尝试从浏览器历史记录返回
    if (window.history.length > 1) {
      console.log('⬅️ 使用浏览器历史记录返回')
      router.go(-1)
      return
    }
    
    // 最后降级：优先尝试工作台缓存，其次使用角色智能判断
    console.log('🚀 开始最后降级跳转逻辑')
    
    // 尝试跳转到缓存的工作台
    if (goToCachedWorkspace()) {
      console.log('✅ 成功跳转到缓存的工作台')
      return
    }
    
    // 最终降级：使用角色智能判断
    console.log('⚡ 使用角色智能判断进行最终降级跳转')
    const fallbackHomePath = goToHome()
    router.push(fallbackHomePath)
  }

  // 动态菜单列表 - 这是与Layout的主要差异：过滤后的菜单而非全部菜单
  const dynamicMenu = computed(() => {
    return generateDynamicMenu(parentRoute.value)
  })

  // 计算当前激活的菜单项
  const activeMenu = computed(() => {
    const currentPath = route.path
    const currentName = route.name as string
    
   // console.log('🔍 计算动态菜单激活状态:', { currentPath, currentName, parentRoute: parentRoute.value })
    
    // 获取当前的动态菜单
    const currentDynamicMenu = generateDynamicMenu(parentRoute.value)
   // console.log('🔍 当前动态菜单:', currentDynamicMenu)
    
    // 优先使用路由名称匹配
    if (currentName) {
      // 检查当前路由名称是否在动态菜单中
      const foundByName = findMenuItemByName(currentDynamicMenu, currentName)
      if (foundByName) {
    //    console.log('✅ 通过路由名称找到激活菜单:', currentName)
        return currentName
      }
    }
    
    // 根据路径匹配最相近的菜单项
    const foundByPath = findMenuItemByPath(currentDynamicMenu, currentPath)
    if (foundByPath) {
    //  console.log('✅ 通过路径找到激活菜单:', foundByPath)
      return foundByPath
    }
    
    // 如果都没找到，尝试更宽松的匹配
   // console.log('🔍 尝试宽松匹配...')
    for (const item of currentDynamicMenu) {
     // console.log('🔍 检查菜单项:', { name: item.name, path: item.path, currentPath })
      
      // 检查路径包含关系（处理动态参数）
      if (item.path && currentPath.startsWith(item.path.split(':')[0])) {
      //  console.log('✅ 通过路径前缀匹配找到激活菜单:', item.name || item.path)
        return item.name || item.path
      }
      
      // 检查子菜单
      if (item.children) {
        for (const child of item.children) {
        //  console.log('🔍 检查子菜单项:', { name: child.name, path: child.path, currentPath })
          if (child.name === currentName || 
              (child.path && currentPath.includes(child.path)) ||
              (child.path && currentPath.startsWith(child.path.split(':')[0]))) {
         //   console.log('✅ 通过子菜单匹配找到激活菜单:', child.name || child.path)
            return child.name || child.path
          }
        }
      }
    }
    
    // 如果都没找到，返回空字符串
    console.log('❌ 未找到匹配的激活菜单')
    return ''
  })

  // 根据路由名称查找菜单项
  const findMenuItemByName = (menuItems: MenuRoute[], targetName: string): string | null => {
    for (const item of menuItems) {
      if (item.name === targetName) {
        return item.name || ''
      }
      
      // 递归检查子菜单
      if (item.children && item.children.length > 0) {
        const found = findMenuItemByName(item.children, targetName)
        if (found) return found
      }
    }
    return null
  }

  // 根据路径查找菜单项
  const findMenuItemByPath = (menuItems: MenuRoute[], targetPath: string): string | null => {
    for (const item of menuItems) {
      // 检查完整路径匹配
      if (item.path === targetPath) {
        return item.name || ''
      }
      
      // 检查路径包含关系（用于处理动态参数）
      if (item.path && targetPath.includes(item.path)) {
        return item.name || ''
      }
      
      // 递归检查子菜单
      if (item.children && item.children.length > 0) {
        const found = findMenuItemByPath(item.children, targetPath)
        if (found) return found
      }
    }
    return null
  }

  // 监听路由变化的特殊处理
  const watchDynamicRouteChange = () => {
    watch(
      () => route.path,
      (newPath, oldPath) => {
        console.log('路由变化:', { newPath, oldPath, hasCache: !!cachedFromRoute.value })
        
        // 只有当路径真的改变时才处理
        if (newPath !== oldPath) {
          initializeParentRoute()
          
          // 判断是否需要缓存来源路由：
          // 1. 还没有缓存 AND
          // 2. 新路径是课程详情页面 AND  
          // 3. 旧路径不是课程详情页面的子路径（即从外部跳转进来）
          const isEnteringCourseDetail = newPath.startsWith('/teacher/course')
          const isFromExternalPage = !oldPath || !oldPath.startsWith('/teacher/course')
          
          if (!cachedFromRoute.value && isEnteringCourseDetail && isFromExternalPage) {
            console.log('检测到从外部页面进入课程详情，开始缓存来源路由')
            cacheDashboardRoute()
          } else if (isEnteringCourseDetail && !isFromExternalPage) {
            console.log('课程详情页面内导航，保持现有缓存')
            // 页面内导航时，确保缓存查询参数不丢失
            if (cachedFromRoute.value) {
              updateRouteWithCache(cachedFromRoute.value)
            }
          }
        }
      },
      { immediate: true }
    )
  }

  // 初始化动态侧边栏逻辑
  const initDynamicSidebar = () => {
    initializeParentRoute()
    cacheDashboardRoute() // 组件挂载时立即缓存来源路由
  }

  return {
    parentRoute,
    dynamicMenu,
    activeMenu,
    handleBackToDashboard,
    initDynamicSidebar,
    watchDynamicRouteChange,
    cachedFromRoute
  }
} 