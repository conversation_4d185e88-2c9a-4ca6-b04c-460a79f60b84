import * as XLSX from 'xlsx';
import { MessagePlugin } from 'tdesign-vue-next';
import { StudentScore, Indicator } from '@/store/modules/indicator';
import { useIndicatorStore } from '@/store/modules/indicator';
import { readExcelFile, processScoreData } from '@/utils/excelHandler';

/**
 * 导出成绩模板
 * @param indicatorName 指标点名称，用于生成文件名
 * @returns 是否成功导出
 */
export const exportScoreTemplate = (indicatorName: string = '成绩'): boolean => {
  try {
    // 创建工作簿
    const workbook = XLSX.utils.book_new();
    
    // 创建表头
    const headers = ['学号', '姓名', '成绩'];
    
    // 创建示例数据
    const exampleData = [
      ['2021001', '张三', 85],
      ['2021002', '李四', 92],
      ['2021003', '王五', 78],
      ['2021004', '赵六', 65],
    ];
    
    // 创建工作表
    const worksheet = XLSX.utils.aoa_to_sheet([headers, ...exampleData]);
    
    // 设置列宽
    worksheet['!cols'] = [
      { width: 15 }, // 学号
      { width: 12 }, // 姓名
      { width: 10 }, // 成绩
    ];
    
    // 将工作表添加到工作簿
    XLSX.utils.book_append_sheet(workbook, worksheet, '成绩模板');
    
    // 导出工作簿
    const wbout = XLSX.write(workbook, {
      bookType: 'xlsx',
      type: 'array'
    });
    
    // 创建Blob并下载
    const blob = new Blob([wbout], { type: 'application/octet-stream' });
    const url = URL.createObjectURL(blob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = `${indicatorName}成绩导入模板.xlsx`;
    document.body.appendChild(link);
    link.click();
    
    // 清理资源
    setTimeout(() => {
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    }, 100);
    
    MessagePlugin.success('成绩导入模板下载成功');
    return true;
  } catch (error: any) {
    console.error('导出模板失败:', error);
    MessagePlugin.error(`导出模板失败: ${error.message}`);
    return false;
  }
};

/**
 * 导入成绩数据
 * @param file 上传的Excel文件
 * @param indicator 指标点对象
 * @param callback 回调函数集合
 * @returns 是否导入成功
 */
export const importScores = async (
  file: File,
  indicator: Indicator,
  callback: {
    loading: (status: boolean) => void,
    refresh: () => void,
    updateStore: (categories: any[]) => void,
    updateStudentsData?: () => void
  }
): Promise<boolean> => {
  try {
    callback.loading(true);
    
    // 读取Excel文件
    const { data, fileName } = await readExcelFile(file);
    
    // 处理成绩数据
    const scores = processScoreData(data, fileName);
    
    if (!scores) {
      return false;
    }
    
    // 更新指标点的成绩数据
    if (!indicator.scores) {
      indicator.scores = [];
    }
    
    // 合并或替换成绩数据
    const existingIds = new Set(indicator.scores.map(s => s.studentId));
    const newScores = scores.filter(s => !existingIds.has(s.studentId));
    const updatedScores = scores.filter(s => existingIds.has(s.studentId));
    
    // 更新已存在的成绩
    updatedScores.forEach(newScore => {
      const index = indicator.scores!.findIndex(s => s.studentId === newScore.studentId);
      if (index !== -1) {
        indicator.scores![index] = newScore;
      }
    });
    
    // 添加新成绩
    indicator.scores.push(...newScores);
    
    // 更新存储
    const indicatorStore = useIndicatorStore();
    indicatorStore.updateCategories([...indicatorStore.categories]);
    
    // 如果提供了更新学生数据的回调函数，则调用它
    if (callback.updateStudentsData) {
      callback.updateStudentsData();
    }
    
    // 刷新UI
    callback.refresh();
    
    MessagePlugin.success(`成功导入${scores.length}条成绩数据`);
    return true;
  } catch (error: any) {
    console.error('导入成绩失败:', error);
    MessagePlugin.error(`导入成绩失败: ${error.message}`);
    return false;
  } finally {
    callback.loading(false);
  }
}; 